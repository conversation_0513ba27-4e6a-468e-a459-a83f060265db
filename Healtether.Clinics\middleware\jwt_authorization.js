import jwt from "jsonwebtoken";
import {getUser, getUserAndPrevilege} from "../helpers/user/user.helper.js";

export const authorizationCheck = async(req, res, next) => {
    try {
        if (req.headers.authorization && req.headers.authorization.startsWith("Bearer")) {

            const token = req
                .headers
                .authorization
                .split(" ")[1];
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            if (typeof decoded !== "string") {
                const user = await getUserAndPrevilege(decoded.id);
                const userID = user._id;
                if (!user) {
                    res
                        .status(401)
                        .json({message: "Invalid token"});
                } else {
                    if (user.active === true) {

                        if (req.headers["x-notificationkey"] != null) {
                            req.Notificationkey = req.headers["x-notificationkey"];
                        }

                        req.user = {
                            id: user._id,
                            name: user.firstName + " " + user.lastName
                        };

                        req.linkedClinics=user.linkedClinics;
                        req.IsSuperAdmin = user.isSuperAdmin;
                        next();
                    } else {
                        res
                            .status(401)
                            .json({message: "User is Blocked", active: true});
                    }
                }
            }
        } else {
            res
                .status(401)
                .json({message: "No authorization"});
        }
    } catch (error) {
        console.log(error);
    }

}
