import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { upsertClient } from '../../../helpers/clinic/client.helper.js';
import { Client } from '../../../model/clinics.model.js';
import { User } from '../../../model/clinics.model.js'; 
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000); 

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('upsertClient function', () => {
  let data, testClientId, adminUser;

  beforeEach(async () => {
    await Client.deleteMany({});
    await User.deleteMany({});

    // Create admin user first
    adminUser = await User.create({
      firstName: 'Admin',
      lastName: 'User',
      mobile: '**********',
      prefix:"Mr.",
      email: '<EMAIL>',
      role: 'admin'
    });

    // Create test client
    const client = await Client.create({
      clinicName: 'Test Clinic',
      address: '123 Test Street',
      patientId: { prefix: "SD" },
      phonepeSetting: {
        merchantId: "marchant id",
        saltKey: "salt key",
        saltIndex: ""
      },
      staffId: { prefix: "CHENNAI" },
      timeSlots: [{
        startTime: { hours: 3, min: 0, tt: "PM" },
        endTime: { hours: 5, min: 59, tt: "PM" }
      }],
      adminUserId: adminUser._id
    });
    
    testClientId = client._id;

    data = {
      ClinicName: 'Test123 Clinic',
      Address: '123456 Test Street',
      PatientId_Prefix: 'PT',
      PatientId_Suffix: '001',
      StaffId_Prefix: 'ST',
      StaffId_Suffix: '001',
      Phonepe_MerchantId: 'merchantId',
      Phonepe_SaltKey: 'saltKey',
      Phonepe_SaltIndex: 1,
      TimeSlots: '[{"startTime": "09:00", "endTime": "10:00"}]',
      LogoName: 'logo.jpg',
      createdOn: new Date('2024-04-27T06:52:20.110Z'),
      AdminFirstName: 'Admin',
      AdminLastName: 'User',
      AdminMobile: '**********',
      AdminEmail: '<EMAIL>'
    };
  });

  afterEach(async () => {
    await Client.deleteMany({});
    await User.deleteMany({});
  });

  it('should upsert a client with the provided data', async () => {
    const result = await upsertClient(data, testClientId);
    expect(result._id).toStrictEqual(testClientId);
    expect(result.clinicName).toBe('Test123 Clinic');
    expect(result.address).toBe('123456 Test Street');
    expect(result.patientId.prefix).toBe('PT');
    expect(result.staffId.prefix).toBe('ST');
    expect(result.phonepeSetting.merchantId).toBe('merchantId');
  });

  it('should create a new client when id is null', async () => {
    const result = await upsertClient(data, null); 
    expect(result).toHaveProperty('_id'); 
    expect(result.clinicName).toBe('Test123 Clinic');
    expect(result.patientId.prefix).toBe('PT');
    expect(result.staffId.prefix).toBe('ST');
    expect(result.phonepeSetting.merchantId).toBe('merchantId');
    expect(result.address).toBe('123456 Test Street');
  });

  it('should return an error if the data is invalid', async () => {
    data.ClinicName = ''; 
    try {
      await upsertClient(data, testClientId);
      fail('Should have thrown an error');
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
    }
  });
});