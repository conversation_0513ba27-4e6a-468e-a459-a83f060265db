import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
const { mockAutoIdHelper } = await import("../mocks/clinicAutoId.helper.js");
mockClientHelper();
mockAutoIdHelper();
const { generateInvoiceId } = await import("../../controllers/clinic/client.controller.js");
const { getInvoiceNumber } = await import("../../helpers/clinicautoidentifier/clinicautoid.helper.js");


describe("generateInvoiceId", () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: { id: "randomid" }, // Use ObjectId directly
        };
        res = {
            json: jest.fn().mockReturnThis(),
            status: jest.fn().mockReturnThis(),
        };
        jest.clearAllMocks();
    });

    it("should return the current invoice ID with status 200 if invoice number is found", async () => {
        const mockInvoiceNo = {
            currentInvoiceId: "INV12345",
        };

        getInvoiceNumber.mockResolvedValueOnce(mockInvoiceNo);

        await generateInvoiceId(req, res);

        expect(getInvoiceNumber).toHaveBeenCalledWith(req.query.id); // Convert ObjectId to string before comparison
        expect(res.json).toHaveBeenCalledWith({ invoiceNumber: mockInvoiceNo.currentInvoiceId });
        expect(res.status).toHaveBeenCalledWith(200);
    });

    it("should return an empty string with status 404 if invoice number is not found", async () => {
        getInvoiceNumber.mockResolvedValueOnce(null);

        await generateInvoiceId(req, res);

        expect(getInvoiceNumber).toHaveBeenCalledWith(req.query.id);
        expect(res.json).toHaveBeenCalledWith("");
        expect(res.status).toHaveBeenCalledWith(404);
    });

    
    
});
