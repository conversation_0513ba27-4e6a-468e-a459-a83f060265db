import { jest } from "@jest/globals";

export async function mockPatientHelper() {
    await jest.unstable_mockModule("../../helpers/patient/patient.helper.js", async () => ({
        overview: jest.fn(),
        removePatient: jest.fn(),
        searchByMobile: jest.fn(),
        getPatientWithAllAppointmentDetails: jest.fn(),
        getPatientWithAllMedicalDetails: jest.fn(),
        getPatientDetail: jest.fn(),
        getPatientCurrentAppointment: jest.fn(),
        addUpdateDetails: jest.fn(),
        updateMedicalDetails:jest.fn(),
        createPatientWithAutoPatientIdInScheduleAppointment:jest.fn(),
        generatePatientAutoId:jest.fn(),
        modelSubmission:jest.fn()
    }));
}