import mongoose from "mongoose";
const userOTPSchema = new mongoose.Schema({
  mobile: {
    type: String,
    required:true,
    unique: true, 
    index:true,
    maxLength: 12
  },
  email: {
    type: String,
    index:true,
  },
  otp: {
    type: String,
    maxLength: 5
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
  "expireAt":{
    type: Date,
    expires: 10 
  }
});
//const UserOTPModel = new mongoose.model("UserOTP", userOTPSchema);
export { userOTPSchema };
