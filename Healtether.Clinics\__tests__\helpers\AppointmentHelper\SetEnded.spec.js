import { jest } from "@jest/globals";
import mongoose from 'mongoose';
import { setEnded } from '../../../helpers/appointment/appointment.helper.js';
import { Appointment } from '../../../model/clinics.model.js';
import { setup, teardown } from '../../../setup.js';
import { createInvoiceWithConsultationCharge } from "../../../helpers/payment/payment.helper.js";

jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe('setEnded', () => {
  let appointmentId;
  let user;
  let data;
  let patientId;

  beforeEach(async () => {
    jest.clearAllMocks();
    await Appointment.deleteMany({});
    appointmentId = new mongoose.Types.ObjectId();
    patientId = new mongoose.Types.ObjectId();

    user = { id: 'user123', name: 'Test User' };
    data = {
      medicalRecords: [
        {
          fileName: "updated_diabetes_report.pdf",
          blobName: "updated_45678_diabetes_report"
        }
      ],
      procedureRecords: [
        {
          fileName: "updated_procedurefile.pdf",
          blobName: "updated_45678_diabetes_report1"
        }
      ],
      prescriptionRecords: [
        {
          fileName: "updated_prescriptionRecords.pdf",
          blobName: "updated_45678_diabetes_report2"
        }
      ],
     
      clientId: new mongoose.Types.ObjectId()
    };

    await Appointment.create({
      _id: appointmentId,
      name: 'John Doe',
      mobile: '**********',
      gender: 'Male',
      age: 30,
      birthDate: '1993-05-15',
      doctorId: new mongoose.Types.ObjectId(),
      started: { yes: true },
      patientId: patientId,
      // address: {
      //   street: '123 Main St',
      //   city: 'New York',
      //   state: 'NY',
      //   zipCode: '12345',
      // },
      appointmentDate: '2024-09-30T10:00:00Z',
      timeSlot: '9:00 AM - 10:00 AM',
      rescheduled: {},
      modified: {},
      isDeleted: false,
      isCanceled: false,
      medicalRecords: [
        {
          fileName: "diabetes_report.pdf",
          blobName: "45678_diabetes_report"
        }
      ],
      procedureRecords: [
        {
          fileName: "procedurefile.pdf",
          blobName: "45678_diabetes_report1"
        }
      ],
      prescriptionRecords: [
        {
          fileName: "prescriptionRecords.pdf",
          blobName: "45678_diabetes_report2"
        }
      ],
      ended: {  
        yes: false,
      },
    });
  });

  it('should update appointment records, set ended and modified fields, and create an invoice', async () => {
    const result = await setEnded(data, appointmentId, user);

    expect(result.ended.yes).toBe(true);
    
    await createInvoiceWithConsultationCharge(
      user.id,
      user.name,
      appointmentId,
      data.clientId
    );

    const updatedAppointment = await Appointment.findById(appointmentId).exec();
    expect(updatedAppointment?.ended.yes).toBe(true);
    expect(updatedAppointment?.medicalRecords[0].fileName).toStrictEqual("updated_diabetes_report.pdf");
    expect(updatedAppointment?.procedureRecords[0].fileName).toStrictEqual("updated_procedurefile.pdf");
    expect(updatedAppointment?.prescriptionRecords[0].fileName).toStrictEqual("updated_prescriptionRecords.pdf");
 


  });

  it('should throw an error if the appointment is not found', async () => {
    await Appointment.deleteMany({}); 
    const nonexistentAppointmentId = new mongoose.Types.ObjectId();
    
    await expect(setEnded(data, nonexistentAppointmentId, user)).rejects.toThrow('appointment not found');
  });
  
});
