const patientValidator = {
  patient: {
    in: ["body"],
    exists: { errorMessage: "patient is required" },
    isObject: true,
  },
  "patient.id": {
    in: ["body"],
    notEmpty: true,
    errorMessage: "Invalid patient id",
  },
  "patient.abhaNumber": {
    in: ["body"],
    optional: true,
    isString: true,
    errorMessage: "abhaNumber must be a string",
  },
  "patient.abhaAddress": {
    in: ["body"],
    optional: true,
    isString: true,
    errorMessage: "abhaAddress must be a string",
  },
  "patient.name.text": {
    in: ["body"],
    notEmpty: true,
    isString: true,
    errorMessage: "Patient name.text must be a string",
  },
  "patient.name.prefix": {
    in: ["body"],
    notEmpty: true,
    isArray: true,
    // isIn: {
    //   options: [["Mr.", "Miss.", "Dr.", "Mrs.", "Prof."]],
    // },
    errorMessage: "Patient prefix must be a string",
  },
  "general.name.prefix.*": {
    isString: true,
    errorMessage: "Each prefix must be a string",
  },
  "patient.gender": {
    in: ["body"],
    isIn: { options: [["male", "female"]] },
    errorMessage: "Gender must be male, female ",
  },
  "patient.dob": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "Invalid date of birth",
  },
  "patient.address": {
    in: ["body"],
    isArray: true,
    errorMessage: "address must be an array",
  },
  "patient.address.*.use": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each address must have a non-empty use field",
  },
  "patient.address.*.type": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each address must have a non-empty type field",
  },
  "patient.address.*.postalCode": {
    in: ["body"],
    isString: true,
    errorMessage: "Each address must have a valid postal code",
  },
  "patient.address.*.country": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each address must have a country",
  },
  "patient.address.*.district": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each address must have a district",
  },
  "patient.address.*.city": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each address must have a city",
  },
  "patient.address.*.state": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each address must have a state",
  },
  "patient.address.*.text": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each address must have a full text description",
  },
  "patient.doctors": {
    in: ["body"],
    isArray: true,
    errorMessage: "Doctors must be an array of strings",
  },
  "patient.doctors.*": {
    isString: true,
    errorMessage: "Each doctor name must be a string",
  },
  "patient.allergyIntolerances": {
    in: ["body"],
    isArray: true,
    optional: true,
    errorMessage: "allergyIntolerances must be an array",
  },
  "patient.allergyIntolerances.*.type": {
    in: ["body"],
    isString: true,
    optional: true,
    errorMessage: "Each allergy must have a non-empty type",
  },
  "patient.allergyIntolerances.*.clinicalStatus": {
    in: ["body"],
    isString: true,
    optional: true,
    // isIn: {
    //   options: [["active", "inactive", "resolved"]],
    // },
    errorMessage: "clinicalStatus must be one of: active, inactive, resolved",
  },
  "patient.allergyIntolerances.*.verificationStatus": {
    in: ["body"],
    isString: true,
    optional: true,
    // isIn: {
    //   options: [["unconfirmed", "confirmed", "refuted", "entered-in-error"]],
    // },
    errorMessage:
      "verificationStatus must be one of: unconfirmed, confirmed, refuted, entered-in-error",
  },
  "patient.allergyIntolerances.*.notes": {
    in: ["body"],
    isArray: true,
    optional: true,
    errorMessage: "notes must be an array of strings",
  },
  "patient.allergyIntolerances.*.notes.*": {
    in: ["body"],
    isString: true,
    optional: true,
    errorMessage: "Each note must be a non-empty string",
  },
  "patient.allergyIntolerances.*.doctor": {
    in: ["body"],
    isString: true,
    optional: true,
    errorMessage: "doctor must be a non-empty string",
  },
  "patient.telecom": {
    in: ["body"],
    isArray: true,
    errorMessage: "telecom must be an array",
  },
  "patient.telecom.*.system": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    isIn: {
      options: [["phone", "email", "fax", "pager"]],
    },
    errorMessage: "system must be one of: phone, email, fax, pager",
  },
  "patient.telecom.*.value": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "value must be a non-empty string",
  },
  "patient.telecom.*.use": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    // isIn: {
    //   options: [["home", "work", "temp", "old", "mobile"]],
    // },
    errorMessage: "use must be one of: home, work, temp, old, mobile",
  },
};

export default patientValidator;
