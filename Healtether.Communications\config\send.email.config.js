import { EmailClient } from "@azure/communication-email";

const connectionString = process.env.AZURE_COMMUNICATION_URL;
const client = new EmailClient(connectionString);
const DEFAULT_SENDER = "<EMAIL>";

export const sendEmail = async (
  toEmail,
  subject,
  bodyHtml,
  bodyText = null,
  senderDomain = null
) => {
  var toAddress = getToEmailAddress(toEmail);
  if (toAddress == null) return;

  const emailMessage = {
    senderAddress: senderDomain != null ? senderDomain : DEFAULT_SENDER, // Replace with your verified sender address
    content: {
      subject: subject,
      plainText: bodyText,
      html: bodyHtml,
    },
    recipients: {
      to: [{ address: toAddress }],
    },
  };

  const poller = await client.beginSend(emailMessage);
  const result = await poller.pollUntilDone();
  return result;
};

const getToEmailAddress = (email) => {
  var toEmail = null;
  if (email.length > 1) {
    toEmail = email.join(";");
  } else if (email.length > 0) {
    toEmail = email[0];
  }
  return toEmail;
};
