import mongoose from "mongoose";
import { CLIENT_COLLECTION } from "../mongodb.collection.name.js";

const clientAutoIdSchema = new mongoose.Schema({
  currentPatientId: {
      type: Number,
      required: true,
      default:0,
  },
  currentStaffId: {
      type: Number,
      required: true,
      default:0,
  },
  currentInvoiceId: {
      type: Number,
      required: true,
      default:0,
  },
  clinic:{
      type: mongoose.Schema.Types.ObjectId,
      ref: CLIENT_COLLECTION,
      index:true,
      unique:true,
      required:true
  },

}, {versionKey: '1.2'});
export { clientAutoIdSchema };
