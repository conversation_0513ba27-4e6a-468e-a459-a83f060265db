/** @type {import('tailwindcss').Config} */
const { addDynamicIconSelectors } = require("@iconify/tailwind");
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    fontFamily: {
      primary: "Urbanist",
      secondary: "Roboto",
    },
    letterSpacing: {
      normal: "0.04rem",
    },
    extend: {
      colors: {
        primary_light: "#56D8B4",
        Primary: "#32856E", // for button green color
        Secondary: "#5351C7", // for text bule color
        text_primary: "#202741", // for entered text , titles inside each modles
        text_bg_primary: "#F8F7FC", // for bg textbox
        text_secondary: "#A1A1A1", // for text placeholder
        text_breadcrumb: "#6D6D6D", // for breadcrumb text , other label
        backcolor_primary: "#F7F4FA", // highlight bg, table body bg
        backcolor_secondary: "#7ad8c8", // table header bg
        backcolor_detailpage: "#F5F4FB",
        text_viewdetail: "#266A57",
        text_disabled: "#C2C2C2",
        backcolor_disabled: "#F5F5F5",
        text_grey: "#8E8E8E",
        table_row_border: "#D3D3D3",
        backcolor_light: "#F5F8F9",
        color_muted: "#808686",
        color_dark: "#0A0A0A",
        backcolor_grey: "#EFEFEF",
      },
      boxShadow: {
        sm: "0px 2px 11.3px 0px #BEBEBE40",
      },
    },
  },
  plugins: [
    addDynamicIconSelectors(),
    require("@tailwindcss/forms"),
  ],
  darkMode: "class",
};
