export function extractFhirBundleInfo(bundle) {
  const extractedData = {
    // Common
    bundle: {},
    composition: {},
    patient: {},
    practitioners: [],
    organization: {},
    encounter: {},

    // Consultation Specific
    chiefComplaints: [],
    allergies: [],
    medicalHistory: [],
    investigationAdvice: [],
    procedure: [],
    followUp: {},

    // Wellness Specific
    vitalSigns: [],
    observations: [], // General observations not in vital signs section

    // Prescription Specific
    medications: [],

    // Invoice Specific
    invoice: {}, // Details from the Invoice resource
    chargeItems: [], // Details from ChargeItem resources

    // Health Document Specific
    documentReferences: [], // Placing document references here

    immunizations: [],
    diagnosticReports: [],
    dischargeReports: [],
  };

  if (!bundle || !bundle.entry) {
    console.error("Invalid FHIR bundle provided.");
    return extractedData;
  }

  // Build a map of resources for easy lookup by fullUrl or id
  const resourceMap = new Map();
  bundle.entry.forEach((entry) => {
    if (entry.resource && entry.resource.id) {
      // Prefer fullUrl if available, otherwise use urn:uuid:id
      const key = entry.fullUrl || `urn:uuid:${entry.resource.id}`;
      resourceMap.set(key, entry.resource);
      // Also map by just the resource ID as a fallback or for simpler lookups
      // if (entry.resource.id && !resourceMap.has(entry.resource.id)) {
      //   resourceMap.set(entry.resource.id, entry.resource);
      // }
    }
  });
  // Iterate through entries to extract resources
  bundle.entry.forEach((entry) => {
    const resource = entry.resource;
    if (!resource) return; // Skip entries without a resource
    switch (resource.resourceType) {
      case "Composition":
        extractedData.composition = {
          id: resource.id,
          lastUpdated: resource.meta?.lastUpdated,
          versionId: resource.meta?.versionId,
          identifier: resource.identifier,
          language: resource.language,
          status: resource.status,
          type: resource.type?.text || resource.type?.coding?.[0]?.display,
          date: resource.date,
          title: resource.title,
          // Resolve references using the map
          subject: resource.subject?.reference
            ? resourceMap.get(resource.subject.reference)?.name?.[0]?.text ||
              resource.subject.display
            : resource.subject?.display,
          encounter: resource.encounter?.reference
            ? resourceMap.get(resource.encounter.reference)?.id ||
              resource.encounter.display
            : resource.encounter?.display,
          author: Array.from(
            new Set(
              resource.author?.map((a) =>
                a.reference
                  ? resourceMap.get(a.reference)?.name?.[0]?.text || a.display
                  : a.display
              )
            )
          ),

          attester: Array.from(
            new Map(
              resource.attester?.map((a) => {
                const party = a.party?.reference
                  ? resourceMap.get(a.party.reference)?.name?.[0]?.text ||
                    resourceMap.get(a.party.reference)?.name ||
                    a.party.display
                  : a.party?.display;

                const key = `${a.mode}-${party}`; // make a unique key

                return [key, { mode: a.mode, party }];
              }) || []
            ).values()
          ),

          custodian: resource.custodian?.reference
            ? resourceMap.get(resource.custodian.reference)?.name ||
              resource.custodian.display
            : resource.custodian?.display,
        };
        // Process sections by looking up referenced resources
        resource.section?.forEach((section) => {
          if (section.entry) {
            section.entry.forEach((sectionEntry) => {
              const referencedResource = resourceMap.get(
                sectionEntry.reference
              );
              if (referencedResource) {
                // --- Consultation Sections ---
                if (
                  section.title === "Chief complaints" &&
                  referencedResource.resourceType === "Condition"
                ) {
                  FilterDuplicates(
                    extractedData.chiefComplaints,
                    extractCondition(referencedResource)
                  );
                } else if (
                  section.title === "Allergies" &&
                  referencedResource.resourceType === "AllergyIntolerance"
                ) {
                  FilterDuplicates(
                    extractedData.allergies,
                    extractAllergyIntolerance(referencedResource)
                  );
                } else if (
                  section.title === "Medical History" &&
                  referencedResource.resourceType === "Condition"
                ) {
                  FilterDuplicates(
                    extractedData.medicalHistory,
                    extractCondition(referencedResource)
                  );
                } else if (
                  section.title === "Investigation Advice" &&
                  referencedResource.resourceType === "ServiceRequest"
                ) {
                  FilterDuplicates(
                    extractedData.investigationAdvice,
                    extractServiceRequest(referencedResource),
                    "ServiceRequest"
                  );
                } else if (section.title === "Medications") {
                  // Can contain MedicationRequest or MedicationStatement
                  if (
                    referencedResource.resourceType === "MedicationStatement"
                  ) {
                    FilterDuplicates(
                      extractedData.medications,
                      extractMedicationStatement(referencedResource),
                      "MedicationStatement"
                    );
                  } else if (
                    referencedResource.resourceType === "MedicationRequest"
                  ) {                                      FilterDuplicates(
                      extractedData.medications,
                      extractMedicationRequest(referencedResource),
                      "MedicationRequest"
                    );
                  }
                } else if (
                  section.title === "Procedure" &&
                  referencedResource.resourceType === "Procedure"
                ) {
                  FilterDuplicates(
                    extractedData.procedure,
                    extractProcedure(referencedResource)
                  );
                } else if (
                  section.title === "Follow Up" &&
                  referencedResource.resourceType === "Appointment"
                ) {
                  extractedData.followUp =
                    extractAppointment(referencedResource);
                }else if(                  
                  section.title === "Prescription record" &&
                  referencedResource.resourceType === "MedicationRequest"){
                    FilterDuplicates(
                      extractedData.medications,
                      extractMedicationRequest(referencedResource),
                      "MedicationRequest"
                    );
                  }
                // --- Wellness Section ---
                else if (
                  section.title === "Vital Signs" &&
                  referencedResource.resourceType === "Observation"
                ) {
                  const newObservation = extractObservation(referencedResource);
                  if (
                    !extractedData.vitalSigns.find(
                      (obs) => obs.code === newObservation.code
                    )
                  ) {
                    extractedData.vitalSigns.push(newObservation);
                  }
                } else if (
                  section.title === "Observations" &&
                  referencedResource.resourceType === "Observation"
                ) {
                  // Example general observations section
                  extractedData.observations.push(
                    extractObservation(referencedResource)
                  );
                }
                // --- Invoice Section ---
                else if (
                  section.title === "Line Items" &&
                  referencedResource.resourceType === "ChargeItem"
                ) {
                  extractedData.chargeItems.push(
                    extractChargeItem(referencedResource)
                  );
                }
              }
            });
          }
        });
        break;
      case "Patient":
        extractedData.patient = {
          id: resource.id,
          lastUpdated: resource.meta?.lastUpdated,
          versionId: resource.meta?.versionId,
          abhaId: resource.identifier?.find((id) => id.type?.text === "ABHAID")
            ?.value,
          abhaAddress: resource.identifier?.find(
            (id) => id.type?.text === "ABHAADDRESS"
          )?.value,
          name: resource.name?.[0]?.text,
          prefix: resource.name?.[0]?.prefix?.[0],
          telecom: resource.telecom?.map((t) => ({
            system: t.system,
            value: t.value,
            use: t.use,
          })),
          gender: resource.gender,
          birthDate: resource.birthDate,
          ...(resource.address && {
            address: {
              use: resource.address.use,
              type: resource.address.type,
              text: resource.address.text,
              postalCode: resource.address.postalCode,
              country: resource.address.country,
            },
          }),
        };
        break;
      case "Practitioner":
        {
          if (extractedData.practitioners.length === 0) {
            console.log("first", extractedData.practitioners);
            extractedData.practitioners.push({
              id: resource.id,
              lastUpdated: resource.meta?.lastUpdated,
              versionId: resource.meta?.versionId,
              medicalLicense: resource.identifier?.find(
                (id) => id.type?.coding?.[0]?.code === "MD"
              )?.value,
              name: resource.name?.[0]?.text,
              prefix: resource.name?.[0]?.prefix?.[0],
              telecom: resource.telecom?.map((t) => ({
                system: t.system,
                value: t.value,
                use: t.use,
              })),
              gender: resource.gender,
          ...(resource.address && {
            address: {
              use: resource.address.use,
              type: resource.address.type,
              text: resource.address.text,
              state: resource.address.state,
              district: resource.address.district,
              postalCode: resource.address.postalCode,
              country: resource.address.country,
            },
          }),
            });
            console.log(
              "Extracted Practitioner:",
              extractedData.practitioners[
                extractedData.practitioners.length - 1
              ]
            );
          } else {
            console.log(
              `Skipping duplicate Practitioner with ID: ${resource.id}`
            );
          }
        }
        break;
      case "Organization":
        // Assuming only one main organization per bundle, or the first one encountered is the primary
        if (!extractedData.organization.id) {
          extractedData.organization = {
            id: resource.id,
            lastUpdated: resource.meta?.lastUpdated,
            versionId: resource.meta?.versionId,
            identifier: resource.identifier?.map((i) => ({
              system: i.system,
              value: i.value,
              type: i.type?.text || i.type?.coding?.[0]?.display,
            })),
            providerNumber: resource.identifier?.find(
              (id) => id.type?.coding?.[0]?.code === "PRN"
            )?.value,
            name: resource.name, // Organization name is a single value, not an array of HumanName
            telecom: resource.telecom?.map((t) => ({
              system: t.system,
              value: t.value,
              use: t.use,
            })),
            address: resource.address?.map((a) => ({
              use: a.use,
              type: a.type,
              text: a.text,
              city: a.city,
              state: a.state,
              district: a.district,
              postalCode: a.postalCode,
              country: a.country,
            })),
          };
        }
        break;
      case "Encounter":
        // Assuming only one main encounter per bundle
        if (!extractedData.encounter.id) {
          extractedData.encounter = {
            id: resource.id,
            lastUpdated: resource.meta?.lastUpdated,
            identifier: resource.identifier?.map((i) => ({
              system: i.system,
              value: i.value,
            })),
            status: resource.status,
            class: resource.class?.display,
            subject: resource.subject?.display,
            period: resource.period,
            serviceType:
              resource.serviceType?.coding?.[0]?.display,
            reasonCode: resource.reasonCode?.map(
              (rc) => rc.text || rc.coding?.[0]?.display
            ),
            diagnosis: resource.diagnosis?.map((d) => ({
              condition: d.condition?.display,
              use: d.use?.coding?.[0]?.display,
              note:d.use?.text
            })),
          };
        }
        break;
      case "DocumentReference":
        {
          const docRefExists = extractedData.documentReferences.some(
            (dr) => dr.content[0].title === resource.content[0].attachment.title
          );
          if (!docRefExists) {
            extractedData.documentReferences.push(
              extractDocumentReference(resource)
            );
          }
        }
        break;
      case "Invoice":
        // Extract Invoice details
        // Assuming only one main Invoice resource per bundle
        if (!extractedData.invoice.id) {
          extractedData.invoice = {
            id: resource.id,
            lastUpdated: resource.meta?.lastUpdated,
            versionId: resource.meta?.versionId,
            identifier: resource.identifier?.map((i) => ({
              system: i.system,
              value: i.value,
              type: i.type?.text || i.type?.coding?.[0]?.display,
            })),
            status: resource.status,
            type: resource.type?.text || resource.type?.coding?.[0]?.display,
            subject: resource.subject?.display,
            recipient: resource.recipient?.display,
            date: resource.date,
            issuer: resource.issuer?.display,
            account: resource.account?.display,
            totalPriceComponent: resource.totalPriceComponent,
            totalNet: resource.totalNet,
            totalGross: resource.totalGross,
            paymentTerms: resource.paymentTerms,
            note: resource.note?.map((n) => n.text),
            supportingInformation: resource.supportingInformation?.map(
              (si) => si.display
            ),
            totalTax: resource.totalTax, // Add total tax if present
            // Add other relevant Invoice fields
          };
        }
        break;
      case "Immunization":
        {
          const immunizationExists = extractedData.immunizations.some(
            (i) => i.vaccineCode === resource?.vaccineCode?.coding?.[0]?.display
          );
          if (!immunizationExists) {
            extractedData.immunizations.push(extractImmunization(resource));
          }
        }
        break;
      case "DiagnosticReport":
        {
          const diagnosticReportExists = extractedData.diagnosticReports.some(
            (dr) => dr.id === resource.id
          );
          if (!diagnosticReportExists) {
            extractedData.diagnosticReports.push(
              extractDiagnosticReport(resource)
            );
          }
        }
        break;
      case "DischargeSummary":
        {
          const dischargeSummaryExists = extractedData.dischargeReports.some(
            (dr) => dr.id === resource.id
          );
          if (!dischargeSummaryExists) {
            extractedData.diagnosticReports.push(
              extractDischargeReport(resource)
            );
          }
        }
        break;
      default:
        console.log(
          `Unhandled resource type in bundle entries: ${resource.resourceType}`
        );
    }
  });

  // --- Helper functions ---

  function extractCondition(conditionResource) {
    if (!conditionResource) return null;
    return {
      id: conditionResource.id,
      clinicalStatus:
        conditionResource.clinicalStatus?.coding?.[0]?.display ||
        conditionResource.clinicalStatus?.text,
      code: conditionResource.code?.coding?.[0]?.display,
      recordedDate: conditionResource.recordedDate,
      onsetPeriod: conditionResource.onsetPeriod,
      subject: conditionResource.subject?.display,
      recorder: conditionResource.recorder?.display, // Add recorder
      asserter: conditionResource.asserter?.display, // Add asserter
      bodySite: conditionResource.bodySite?.map(
        (bs) => bs.text || bs.coding?.[0]?.display
      ), // Add body site
      note: conditionResource.code?.text, // Add notes
    };
  }

  function extractAllergyIntolerance(allergyResource) {
    if (!allergyResource) return null;
    return {
      id: allergyResource.id,
      clinicalStatus: allergyResource.clinicalStatus?.coding?.[0]?.display,
      verificationStatus:
        allergyResource.verificationStatus?.coding?.[0]?.display,
      code:
        allergyResource.code?.text ||
        allergyResource.code?.coding?.[0]?.display,
      recordedDate: allergyResource.recordedDate,
      patient: allergyResource.patient?.display,
      recorder: allergyResource.recorder?.display,
      asserter: allergyResource.asserter?.display, // Add asserter
      lastOccurrence: allergyResource.lastOccurrence, // Add last occurrence
      reaction: allergyResource.reaction?.map((r) => ({
        // Extract reactions
        substance: r.substance?.text || r.substance?.coding?.[0]?.display,
        manifestation: r.manifestation?.map(
          (m) => m.text || m.coding?.[0]?.display
        ),
        description: r.description,
        severity: r.severity, // e.g., 'mild', 'moderate', 'severe'
        onset: r.onset, // Date/DateTime/String
        duration: r.duration, // Duration
      })),
      note: allergyResource.note?.map((n) => n.text),
    };
  }

  function extractServiceRequest(serviceRequestResource) {
    if (!serviceRequestResource) return null;
    return {
      id: serviceRequestResource.id,
      status: serviceRequestResource.status,
      intent: serviceRequestResource.intent,
      category: serviceRequestResource.category?.map((c) => {
        return { displayName: c.coding?.[0]?.display, note: c.text };
      }),
      code: serviceRequestResource.code?.coding?.[0]?.display,
      subject: serviceRequestResource.subject?.display,
      encounter: serviceRequestResource.encounter?.display, // Add encounter display
      occurrenceDateTime: serviceRequestResource.occurrenceDateTime, // Add occurrenceDateTime
      occurrencePeriod: serviceRequestResource.occurrencePeriod,
      authoredOn: serviceRequestResource.authoredOn, // Add authoredOn
      requester: serviceRequestResource.requester?.display,
      performer: serviceRequestResource.performer?.map((p) => p.display), // Add performers
      reasonCode: serviceRequestResource.reasonCode?.map(
        (rc) => rc.text || rc.coding?.[0]?.display
      ), // Add reason code
      bodySite: serviceRequestResource.bodySite?.map(
        (bs) => bs.text || bs.coding?.[0]?.display
      ), // Add body site
    };
  }

  function extractMedicationStatement(medicationStatementResource) {
    if (!medicationStatementResource) return null;
    return {
      id: medicationStatementResource.id,
      status: medicationStatementResource.status,
      medication:
        medicationStatementResource.medicationCodeableConcept?.text ||
        medicationStatementResource.medicationCodeableConcept?.coding?.[0]
          ?.display,
      subject: medicationStatementResource.subject?.display,
      context: medicationStatementResource.context?.display, // Encounter or EpisodeOfCare context
      effectivePeriod: medicationStatementResource.effectivePeriod,
      effectiveDateTime: medicationStatementResource.effectiveDateTime, // Add effectiveDateTime
      dateAsserted: medicationStatementResource.dateAsserted, // Add date asserted
      informationSource: medicationStatementResource.informationSource?.display, // Who provided the information
      derivedFrom: medicationStatementResource.derivedFrom?.map(
        (df) => df.display
      ), // Resources statement was derived from
      reasonCode: medicationStatementResource.reasonCode?.map(
        (rc) => rc.text || rc.coding?.[0]?.display
      ), // Add reason code
      dosage: medicationStatementResource.dosage?.map((d) => ({
        text: d.text,
        timing: d.timing?.code?.text || d.timing?.code?.coding?.[0]?.display,
        route: d.route?.text || d.route?.coding?.[0]?.display,
        method: d.method?.text || d.method?.coding?.[0]?.display, // Add method
        doseAndRate: d.doseAndRate?.map((dr) => ({
          type: dr.type?.text || dr.type?.coding?.[0]?.display,
          doseQuantity: dr.doseQuantity,
          rateQuantity: dr.rateQuantity,
          rateRange: dr.rateRange, // Add rate range
          rateRatio: dr.rateRatio, // Add rate ratio
        })),
        maxDosePerPeriod: d.maxDosePerPeriod, // Add max dose per period
        asNeeded: d.asNeededBoolean || d.asNeededCodeableConcept?.text, // Add as needed
        site: d.site?.text || d.site?.coding?.[0]?.display, // Add site
        // Add other dosage details
      })),
      note: medicationStatementResource.note?.map((n) => n.text), // Add notes
      // Add more fields from MedicationStatement as needed
    };
  }

  function extractMedicationRequest(medicationRequestResource) {
    if (!medicationRequestResource) return null;
    return {
      id: medicationRequestResource.id,
      status: medicationRequestResource.status, // e.g., active, on-hold, cancelled, completed, entered-in-error, stopped, draft, unknown
      intent: medicationRequestResource.intent, // e.g., proposal, plan, order, original-order, reflex-order, filler-order, instance-order, option
      priority: medicationRequestResource.priority, // e.g., routine, urgent, asap, stat
      medication:
        medicationRequestResource.medicationCodeableConcept?.coding?.[0]
          ?.display,
      subject: medicationRequestResource.subject?.display,
      context: medicationRequestResource.context?.display, // Encounter or EpisodeOfCare context
      authoredOn: medicationRequestResource.authoredOn,
      requester: medicationRequestResource.requester?.display,
      courseOfTherapyType:
        medicationRequestResource.courseOfTherapyType?.text ||
        medicationRequestResource.courseOfTherapyType?.coding?.[0]?.display, // e.g., acute, continuous, seasonal
      dosageInstruction: medicationRequestResource.dosageInstruction?.map(
        (d) => ({
          text: d.text,
          timing: d.timing?.code?.text || d.timing?.code?.coding?.[0]?.display,
          route: d.route?.text || d.route?.coding?.[0]?.display,
          method: d.method?.text || d.method?.coding?.[0]?.display,
          doseAndRate: d.doseAndRate?.map((dr) => ({
            type: dr.type?.text || dr.type?.coding?.[0]?.display,
            doseQuantity: dr.doseQuantity,
            rateQuantity: dr.rateQuantity,
            rateRange: dr.rateRange,
            rateRatio: dr.rateRatio,
          })),
          maxDosePerPeriod: d.maxDosePerPeriod,
          asNeeded: d.asNeededBoolean || d.asNeededCodeableConcept?.text,
          site: d.site?.text || d.site?.coding?.[0]?.display,
          // Add other dosage details
        })
      ),
      dispenseRequest: medicationRequestResource.dispenseRequest
        ? {
            // Extract dispense request details
            initialFill: medicationRequestResource.dispenseRequest.initialFill
              ? {
                  quantity:
                    medicationRequestResource.dispenseRequest.initialFill
                      .quantity,
                  duration:
                    medicationRequestResource.dispenseRequest.initialFill
                      .duration,
                }
              : undefined,
            dispenseInterval:
              medicationRequestResource.dispenseRequest.dispenseInterval, // Duration
            validityPeriod:
              medicationRequestResource.dispenseRequest.validityPeriod, // Period
            numberOfRepeatsAllowed:
              medicationRequestResource.dispenseRequest.numberOfRepeatsAllowed, // Integer
            quantity: medicationRequestResource.dispenseRequest.quantity, // Quantity
            expectedSupplyDuration:
              medicationRequestResource.dispenseRequest.expectedSupplyDuration, // Duration
            performer:
              medicationRequestResource.dispenseRequest.performer?.display, // Dispense performer
          }
        : undefined,
      substitution: medicationRequestResource.substitution
        ? {
            // Extract substitution details
            allowed:
              medicationRequestResource.substitution.allowedBoolean ||
              medicationRequestResource.substitution.allowedCodeableConcept
                ?.text,
            reason:
              medicationRequestResource.substitution.reason?.text ||
              medicationRequestResource.substitution.reason?.coding?.[0]
                ?.display,
          }
        : undefined,
      priorPrescription: medicationRequestResource.priorPrescription?.display, // Link to prior prescription
      symptoms: medicationRequestResource.reasonCode?.map((s) => ({
        // Extract symptoms 
        code: s?.coding?.[0]?.display,
        note: s?.text
      })),
      note: medicationRequestResource.note?.map((n) => n.text),
    };
  }

  function extractProcedure(procedureResource) {
    if (!procedureResource) return null;
    return {
      id: procedureResource.id,
      status: procedureResource.status,
      code: procedureResource.code?.coding?.[0]?.display,
      subject: procedureResource.subject?.display,
      encounter: procedureResource.encounter?.display,
      performedPeriod: procedureResource.performedPeriod,
      performedDateTime: procedureResource.performedDateTime, // Add performedDateTime
      performedAge: procedureResource.performedAge, // Add performedAge
      performedRange: procedureResource.performedRange, // Add performedRange
      performer: procedureResource.performer?.map((p) => ({
        // Extract performers
        function: p.function?.coding?.[0]?.display,
        actor: p.actor?.display,
      })),
      location: procedureResource.location?.display, // Add location
      reasonCode: procedureResource.reasonCode?.map(
        (rc) => rc.coding?.[0]?.display
      ), // Add reason code
      reasonReference: procedureResource.reasonReference?.map((r) => r.display),
      bodySite: procedureResource.bodySite?.map(
        (bs) => bs.coding?.[0]?.display
      ), // Add body site
      outcome: procedureResource.outcome?.coding?.[0]?.display, // Add outcome
      report: procedureResource.report?.map((r) => r.display), // Add reports
      complication: procedureResource.complication?.map(
        (c) => c.text || c.coding?.[0]?.display
      ), // Add complications
      complicationDetail: procedureResource.complicationDetail?.map(
        (cd) => cd.display
      ), // Add complication details
      followUp: procedureResource.followUp?.map(
        (fu) => fu.text || fu.coding?.[0]?.display
      ), // Add follow up
      note: procedureResource.code?.text,
    };
  }

  function extractAppointment(appointmentResource) {
    if (!appointmentResource) return null;
    return {
      id: appointmentResource.id,
      status: appointmentResource.status,
      description: appointmentResource.description,
      start: appointmentResource.start,
      end: appointmentResource.end,
      created: appointmentResource.created,
      comment: appointmentResource.comment, // Add comment
      patientInstruction: appointmentResource.patientInstruction, // Add patient instruction
      serviceCategory: appointmentResource.serviceCategory?.map(
        (sc) => sc.text || sc.coding?.[0]?.display
      ),
      serviceType: appointmentResource.serviceType?.map(
        (st) => st.text || st.coding?.[0]?.display
      ),
      specialty: appointmentResource.specialty?.map(
        (s) => s.text || s.coding?.[0]?.display
      ),
      appointmentType:
        appointmentResource.appointmentType?.text ||
        appointmentResource.appointmentType?.coding?.[0]?.display,
      reasonCode: appointmentResource.reasonCode?.map(
        (rc) => rc.text || rc.coding?.[0]?.display
      ), // Add reason code
      reasonReference: appointmentResource.reasonReference[0],
      basedOn: appointmentResource.basedOn?.map((b) => b.display),
    };
  }

  function extractDocumentReference(documentReferenceResource) {
    if (!documentReferenceResource) return null;
    return {
      id: documentReferenceResource.id,
      description: documentReferenceResource.description,
      date: documentReferenceResource.date, // When the document reference was created
      identifier: documentReferenceResource.identifier?.map((i) => ({
        system: i.system,
        value: i.value,
        type: i.type?.text || i.type?.coding?.[0]?.display,
      })),
      status: documentReferenceResource.status, // e.g., current, superseeded, entered-in-error
      docStatus: documentReferenceResource.docStatus, // e.g., preliminary, final, appended, amended, entered-in-error
      type:
        documentReferenceResource.type?.text ||
        documentReferenceResource.type?.coding?.[0]?.display,
      category: documentReferenceResource.category?.map(
        (c) => c.text || c.coding?.[0]?.display
      ),
      subject: documentReferenceResource.subject?.display,
      author: documentReferenceResource.author?.map((a) => a.display),
      custodian: documentReferenceResource.custodian?.display,
      context: documentReferenceResource.context
        ? {
            // Encounter or EpisodeOfCare context
            encounter: documentReferenceResource.context.encounter?.map(
              (e) => e.display
            ),
            episodeOfCare: documentReferenceResource.context.episodeOfCare?.map(
              (eoc) => eoc.display
            ), // Add episode of care
            period: documentReferenceResource.context.period,
            facilityType:
              documentReferenceResource.context.facilityType?.text ||
              documentReferenceResource.context.facilityType?.coding?.[0]
                ?.display, // Add facility type
            practiceSetting:
              documentReferenceResource.context.practiceSetting?.text ||
              documentReferenceResource.context.practiceSetting?.coding?.[0]
                ?.display, // Add practice setting
            sourcePatientInfo:
              documentReferenceResource.context.sourcePatientInfo?.display, // Add source patient info
            related: documentReferenceResource.context.related?.map((r) => ({
              // Add related context
              // Handle related context details if needed
            })),
          }
        : undefined,
      content: documentReferenceResource.content?.map((c) => ({
        contentType: c.attachment?.contentType,
        language: c.attachment?.language, // Add language
        data: c.attachment?.data, // Base64 encoded data
        title: c.attachment?.title,
        creation: c.attachment?.creation, // Date created
      })),
    };
  }

  // --- Helper function for Observations ---
  function extractObservation(observationResource) {
    if (!observationResource) return null;

    const extracted = {
      id: observationResource.id,
      status: observationResource.status,
      category: observationResource.category?.map(
        (c) => c.text || c.coding?.[0]?.display
      ),
      code:
        observationResource.code?.text ||
        observationResource.code?.coding?.[0]?.display,
      effectiveDateTime: observationResource.effectiveDateTime,
      effectivePeriod: observationResource.effectivePeriod,
      issued: observationResource.issued,
      subject: observationResource.subject?.display,
      encounter: observationResource.encounter?.display, // Add encounter display
      components: [],
      interpretation: observationResource.interpretation?.map(
        (i) => i.text || i.coding?.[0]?.display
      ), // Add interpretation
      note: observationResource.note?.map((n) => n.text), // Add notes
      bodySite:
        observationResource.bodySite?.text ||
        observationResource.bodySite?.coding?.[0]?.display, // Add body site
      method:
        observationResource.method?.text ||
        observationResource.method?.coding?.[0]?.display, // Add method
      specimen: observationResource.specimen?.display, // Add specimen display
      device: observationResource.device?.display, // Add device display
    };

    // Extract single value
    if (observationResource.valueQuantity) {
      extracted.value = observationResource.valueQuantity.value;
      extracted.unit =
        observationResource.valueQuantity.unit ||
        observationResource.valueQuantity.code;
    } else if (observationResource.valueCodeableConcept) {
      extracted.value =
        observationResource.valueCodeableConcept.text ||
        observationResource.valueCodeableConcept.coding?.[0]?.display;
    } else if (observationResource.valueString) {
      extracted.value = observationResource.valueString;
    } else if (observationResource.valueBoolean !== undefined) {
      // Handle boolean values
      extracted.value = observationResource.valueBoolean ? "Yes" : "No";
    } else if (observationResource.valueInteger !== undefined) {
      // Handle integer values
      extracted.value = observationResource.valueInteger;
    } else if (observationResource.valueRange) {
      // Handle range values
      extracted.value = `${observationResource.valueRange.low?.value || ""} ${
        observationResource.valueRange.low?.unit || ""
      } - ${observationResource.valueRange.high?.value || ""} ${
        observationResource.valueRange.high?.unit || ""
      }`;
    }
    // Add support for other value types as needed

    // Extract components for grouped observations
    if (observationResource.component) {
      extracted.components = observationResource.component.map((comp) => ({
        code: comp.code?.text || comp.code?.coding?.[0]?.display,
        value:
          comp.valueQuantity?.value ||
          comp.valueCodeableConcept?.text ||
          comp.valueCodeableConcept?.coding?.[0]?.display ||
          comp.valueString ||
          comp.valueInteger, // Add other value types here
        unit: comp.valueQuantity?.unit || comp.valueQuantity?.code,
        interpretation: comp.interpretation?.map(
          (i) => i.text || i.coding?.[0]?.display
        ), // Add interpretation for components
        // Recursively extract nested components if necessary, though less common
        components: comp.component
          ? comp.component.map((nestedComp) => ({
              code:
                nestedComp.code?.text || nestedComp.code?.coding?.[0]?.display,
              value:
                nestedComp.valueQuantity?.value ||
                nestedComp.valueCodeableConcept?.text ||
                nestedComp.valueCodeableConcept?.coding?.[0]?.display ||
                nestedComp.valueString ||
                nestedComp.valueInteger,
              unit:
                nestedComp.valueQuantity?.unit ||
                nestedComp.valueQuantity?.code,
              // Stop recursion here for simplicity
            }))
          : [],
      }));
    }

    return extracted;
  }

  // --- NEW Helper function for ChargeItems ---
  function extractChargeItem(chargeItemResource) {
    if (!chargeItemResource) return null;
    return {
      id: chargeItemResource.id,
      identifier: chargeItemResource.identifier?.map((i) => ({
        system: i.system,
        value: i.value,
      })),
      status: chargeItemResource.status,
      code:
        chargeItemResource.code?.text ||
        chargeItemResource.code?.coding?.[0]?.display,
      subject: chargeItemResource.subject?.display,
      context: chargeItemResource.context?.display,
      occurrenceDateTime: chargeItemResource.occurrenceDateTime,
      occurrencePeriod: chargeItemResource.occurrencePeriod,
      performer: chargeItemResource.performer?.map((p) => ({
        actor: p.actor?.display,
        function: p.function?.text || p.function?.coding?.[0]?.display,
      })),
      performingOrganization:
        chargeItemResource.performingOrganization?.display,
      requestingOrganization:
        chargeItemResource.requestingOrganization?.display,
      costFactor: chargeItemResource.costFactor,
      priceOverride: chargeItemResource.priceOverride, // Money
      overrideReason:
        chargeItemResource.overrideReason?.text ||
        chargeItemResource.overrideReason?.coding?.[0]?.display, // Extract text/display
      quantity: chargeItemResource.quantity, // Quantity
      bodysite: chargeItemResource.bodysite?.map(
        (bs) => bs.text || bs.coding?.[0]?.display
      ),
      note: chargeItemResource.note?.map((n) => n.text),
      supportingInformation: chargeItemResource.supportingInformation?.map(
        (si) => si.display
      ),
      // Add other relevant ChargeItem fields
    };
  }

  // --- NEW Helper function for Immunization ---
  function extractImmunization(immunizationResource) {
    if (!immunizationResource) return null;
    return {
      id: immunizationResource.id,
      lotNumber: immunizationResource.lotNumber, // Lot number
      occurrenceDateTime: immunizationResource.occurrenceDateTime, // When administered
      patient: immunizationResource.patient?.display,
      status: immunizationResource.status,
      vaccineCode: immunizationResource.vaccineCode?.coding?.[0]?.display,
      notes: immunizationResource.vaccineCode?.text,
      primarySource: immunizationResource.primarySource, // Boolean
    };
  }

  // --- NEW Helper function for DiagnosticReport ---
  function extractDiagnosticReport(diagnosticReportResource) {
    if (!diagnosticReportResource) return null;
    return {
      id: diagnosticReportResource.id,
      identifier: diagnosticReportResource.identifier?.map((i) => ({
        system: i.system,
        value: i.value,
      })),
      status: diagnosticReportResource.status, // e.g., registered, partial, preliminary, final, amended, corrected, appended, cancelled, entered-in-error, unknown
      category: diagnosticReportResource.category?.map(
        (c) => c.text || c.coding?.[0]?.display
      ), // e.g., laboratory, cardiology
      code:
        diagnosticReportResource.code?.coding?.[0]?.display, // Type of report (e.g., "Complete Blood Count")
      subject: diagnosticReportResource.subject?.display, // Patient display
      encounter: diagnosticReportResource.encounter?.display, // Encounter display
      effectiveDateTime: diagnosticReportResource.effectiveDateTime, // When the report was relevant
      effectivePeriod: diagnosticReportResource.effectivePeriod,
      issued: diagnosticReportResource.issued, // When the report was issued
      performer: diagnosticReportResource.performer?.map((p) => p.display), // Who performed the analysis
      resultsInterpreter: diagnosticReportResource.resultsInterpreter?.map(
        (ri) => ri.display
      ), // Who interpreted the results
      specimen: diagnosticReportResource.specimen?.map((s) => s.display), // Specimen display
      result: diagnosticReportResource.result?.map((r) => r.display), // References to Observations containing results
      imagingStudy: diagnosticReportResource.imagingStudy?.map(
        (is) => is.display
      ), // References to ImagingStudy
      media: diagnosticReportResource.media?.map((m) => ({
        // Attached images/multimedia
        comment: m.comment,
        link: m.link?.url, // URL to the media
      })),
      conclusion: diagnosticReportResource.conclusion, // Clinical conclusion text
      conclusionCode: diagnosticReportResource.conclusionCode?.map(
        (cc) => cc.coding?.[0]?.display
      ), // Conclusion codes
      presentedForm: diagnosticReportResource.presentedForm?.map((pf) => ({
        // Primary report content (PDF, etc.)
        contentType: pf.contentType,
        language: pf.language,
        data: pf.data, // Base64 encoded data
        title: pf.title,
      })),
      // Add other relevant DiagnosticReport fields
    };
  }

  
  // --- NEW Helper function for CarePlan ---
     function extractDischargeReport(carePlanResource) {
         if (!carePlanResource) return null;
         return {
             id: carePlanResource.id,
             identifier: carePlanResource.identifier?.map(i => ({system: i.system, value: i.value})),
             status: carePlanResource.status, // e.g., draft, active, on-hold, completed, entered-in-error, cancelled, unknown
             intent: carePlanResource.intent, // e.g., proposal, plan, order, option
             category: carePlanResource.category?.map(c => c.text || c.coding?.[0]?.display), // e.g., obesity prevention, diabetes self management
             title: carePlanResource.title, // Plain text title
             description: carePlanResource.description, // Plain text description
             subject: carePlanResource.subject?.display, // Patient display
             encounter: carePlanResource.encounter?.display, // Encounter display
             period: carePlanResource.period, // Period plan covers
             created: carePlanResource.created, // Date created
             author: carePlanResource.author?.map(a => a.display), // Author(s) of the plan
             contributor: carePlanResource.contributor?.map(c => c.display), // Contributor(s) to the plan
             careTeam: carePlanResource.careTeam?.map(ct => ct.display), // Care team members
             addresses: carePlanResource.addresses?.map(a => a.display), // Conditions addressed by the plan
             supportingInfo: carePlanResource.supportingInfo?.map(si => si.display), // Supporting information
             goal: carePlanResource.goal?.map(g => g.display), // Goals addressed by the plan
             activity: carePlanResource.activity?.map(a => ({ // Activities planned
                 outcomeReference: a.outcomeReference?.map(or => or.display), // Outcomes expected
                 // Extract details from detail if present
                 detail: a.detail ? {
                     kind: a.detail.kind, // e.g., Appointment, MedicationRequest, ServiceRequest
                     code: a.detail.code?.text || a.detail.code?.coding?.[0]?.display, // What is to be done
                     status: a.detail.status, // e.g., not-started, scheduled, in-progress, completed
                     doNotPerform: a.detail.doNotPerform, // Boolean
                     scheduledPeriod: a.detail.scheduledPeriod, // When activity is scheduled
                     scheduledTiming: a.detail.scheduledTiming, // When activity is scheduled (Timing)
                     location: a.detail.location?.display, // Where activity is to occur
                     performer: a.detail.performer?.map(p => p.display), // Who is to perform activity
                     product: a.detail.productCodeableConcept?.text || a.detail.productCodeableConcept?.coding?.[0]?.display, // What is administered/supplied
                     dailyAmount: a.detail.dailyAmount, // Daily amount
                     quantity: a.detail.quantity, // Total quantity
                     description: a.detail.description, // Description of activity
                     // Add more detail fields as needed
                 } : undefined,
                 progress: a.progress?.map(p => ({ // Progress notes
                     text: p.text,
                     time: p.time
                 })),
                 reference: a.reference?.display // Reference to a resource defining the activity
             })),
             note: carePlanResource.note?.map(n => n.text) // Notes about the plan
             // Add more fields from CarePlan as needed
         };
     }

  //helper for deduplication
  function FilterDuplicates(record, extractedType, resourceType = "nothing") {
    const newRecord = extractedType;
    if (
      resourceType === "MedicationStatement" ||
      resourceType === "MedicationRequest"
    )
      if (!record.find((obs) => obs.medication === newRecord.medication)) {
        record.push({ ...newRecord, resourceType });
      }
    if(
      resourceType === "ServiceRequest"
    )
      if (!record.find((obs) => obs.category[0].displayName === newRecord.category[0].displayName)) {
        record.push({ ...newRecord, resourceType });
      }

    if (!record.find((obs) => obs.code === newRecord.code)) {
      record.push(newRecord);
    }
  }

  return extractedData;
}
