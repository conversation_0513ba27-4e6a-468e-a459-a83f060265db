import { Tooltip } from "react-tooltip";
import store from "../../../../store/store";
import { Link, useNavigate, useParams } from "react-router-dom";

export default function MedicalReportSidebar({ medicalRecords }) {
    const {appointmentId}= useParams();
    // console.log("medicalRecords idsss",data);
  var { currentClinic } = store.getState();
  const navigate = useNavigate();
  const handleUploadDoc = () => {
    // Navigate to consultation page with state information for tab and upload popup
    navigate(`/appointment/${appointmentId}/consultation_v2`, {
      state: {
        openMedicalRecordsTab: true,
        openUploadDialog: true
      }
    });
  };
  return (
    <div className="w-full rounded-lg gap-6 border bg-white drop-shadow-lg p-4">
      <div className="d-flex justify-content-between align-items-center">
        <span className="text-md text-center">Medical Reports</span>
        <span className="float-end">
          <button className="btn btn-Primary" onClick={handleUploadDoc}>Upload Doc</button>
        </span>
      </div>

      {/* <p className="text-md text-center">Medical Reports</p> */}
      <div className="flex flex-col gap-2">
        <div className="flex h-[134px] gap-3 flex-wrap flex-row overflow-x-hidden no-scrollbar">
          {medicalRecords.map((report, index) => (
            <Link
              key={index}
              to={`${import.meta.env.VITE_BLOB_URL}${
                import.meta.env.VITE_CLINICBLOB_CONTAINER_PREFIX
              }${currentClinic.clinic._id}/patient/${report.blobName}`}
              target="_blank"
            >
              <div
                data-tooltip-id={`tooltip-${index}`}
                className="w-[95px] h-[33px] rounded-sm bg-[#F7F7F7] text-[13px] font-normal cursor-pointer shrink-0"
              >
                <div className="flex justify-center align-middle mt-2 pt-2">
                  <span className="text-[15px] mr-2 text-center icon-[streamline--checkup-medical-report-clipboard]"></span>
                  <p>{report.fileName.substring(0, 6) + "..."}</p>
                </div>
                <p className="text-[#868686] text-start ms-2">{report.date}</p>
              </div>
              <Tooltip id={`tooltip-${index}`} content={report.fileName}>
                <div className="w-[150px]">
                  <p>{report.fileName}</p>
                </div>
              </Tooltip>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
