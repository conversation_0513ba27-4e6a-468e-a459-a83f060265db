import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");

mockPatientHelper();

const { getPatientWithAllAppointment } = await import('../../controllers/patients/patients.controller.js'); 

const { getPatientWithAllAppointmentDetails }= await import('../../helpers/patient/patient.helper.js'); 


const mockPatientData = {
    _id: 'patient123',
    firstName: 'test',
    lastName: 'test',
    age: 20,
    birthday: '2003-12-22',
    gender: 'Male',
    mobile: "**********",
    email: "<EMAIL>",
    patientId: 'SD_165',
    address: {
        house: "123",
        street: "Main St",
        city: "Test City",
        pincode: "12345"
    },
    appointments: [
        {
            started: "2024-08-23T09:00:00Z",
            ended: "2024-08-23T09:30:00Z",
            tokenNumber: 1,
            timeSlot: "09:00 AM - 09:30 AM",
            doctorName: "Dr. <PERSON>",
            appointmentDate: "2024-08-23",
            reason: "Regular checkup",
            isFollowUp: true,
            paymentStatus: "Paid",
            virtualConsultation: false,
            isCanceled: false
        }
    ]
};

let query = {
    query: {
        id: 'patient123',
    },
};

const res = {
    json: jest.fn().mockReturnThis(),
    status: jest.fn().mockReturnThis(),
};

describe('getPatientWithAllAppointment', () => {
    beforeEach(() => {
        jest.clearAllMocks(); 
    });

    it('should return patient data with all appointments successfully', async () => {
        getPatientWithAllAppointmentDetails.mockResolvedValueOnce(mockPatientData);

        await getPatientWithAllAppointment(query, res);

        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockPatientData);
    });




});
