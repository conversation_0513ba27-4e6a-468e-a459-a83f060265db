import mongoose from "mongoose";
import { CLIENT_COLLECTION, MAP_CLINIC_USER_COLLECTION, STAFF_COLLECTION, USER_COLLECTION } from "../mongodb.collection.name.js";

const userSchema = new mongoose.Schema({
  firstName:{
    type: String,
    index:true,
    required: true, 
    maxLength: 255
  },
  lastName:{
    type: String,
    index:true,
    required: true, 
    maxLength: 255
  },
  email: {
    type: String,
    index:true,
  },
  mobile: {
    type: String,
    required:true,
    unique: true, 
    index:true,
    maxLength: 12
  },
  countryCode:{
    type: String,
    default:'+91',
    maxLength: 5
  },
  password: {
    type: String,
    maxLength: 1000
  },
  active:{
    type:Boolean,
    default:true
  },
  isdeleted:{
    type:Boolean,
    default:false
  },
  isSuperAdmin:{
    type:Boolean,
    default:false
  },
  lastLoginAttempt:{
    type:Date,
  }
},
{versionKey:'1.2'});
userSchema.virtual('staffDetail', {
  ref: STAFF_COLLECTION,  
  localField: '_id', //userId
  foreignField: 'userId',
  justOne:true
});
userSchema.virtual('adminUserClinic', {
  ref: CLIENT_COLLECTION,
  localField: '_id',  //userId
  foreignField: 'adminUserId',
  justOne:true
});
userSchema.virtual('linkedClinics', {
  ref: MAP_CLINIC_USER_COLLECTION,
  localField: '_id',  // userId
  foreignField: 'userId', // field in clientUser

});

// Create the user model
//const User = new mongoose.model("User", userSchema);   
export { userSchema };

const mapClinicUserSchema=new mongoose.Schema({
 
  isAdmin:{
    type:Boolean,
    default:false
  },
  userId: { type: mongoose.Schema.Types.ObjectId,
         ref: USER_COLLECTION },   
  clinic: {
    type: mongoose.Schema.Types.ObjectId,
     ref: CLIENT_COLLECTION
  },
  isdeleted:{
    type:Boolean,
    default:false
  }
},{versionKey:'1.1'});
mapClinicUserSchema.index({ userId:1, clinic: 1}, { unique: true });
mapClinicUserSchema.virtual('linkedStaff', {
  ref: STAFF_COLLECTION,
  localField: 'userId',  // userId
  foreignField: 'userId', // field in clientUser

});

export { mapClinicUserSchema  };

