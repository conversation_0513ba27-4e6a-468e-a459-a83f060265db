import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
mockClientHelper();
const { clientUpsert } = await import("../../controllers/clinic/client.controller.js");
const { upsertClient } = await import("../../helpers/clinic/client.helper.js");
describe("clientUpsert", () => {
    let req, res;

    beforeEach(() => {
        req = {
            body: {
                value: {
                    ClinicName: "Test Clinic",
                    Address: "123 Main St",
                    PatientId_Prefix: "P-",
                    PatientId_Suffix: "-2024",
                    StaffId_Prefix: "S-",
                    StaffId_Suffix: "-2024",
                    Phonepe_MerchantId: "merchant123",
                    Phonepe_SaltKey: "saltkey123",
                    Phonepe_SaltIndex: "1",
                    TimeSlots: '["9am-11am", "1pm-3pm"]',
                    LogoName: "logo.png",
                    createdOn: "2024-09-01",
                    AdminFirstName: "<PERSON>",
                    AdminLastName: "Doe",
                    AdminMobile: "**********",
                    AdminEmail: "<EMAIL>",
                },
                id: "66d44d7aa657844e99fa411c",
            },
        };
        res = {
            json: jest.fn().mockReturnThis(),
            status: jest.fn().mockReturnThis(),
        };
        jest.clearAllMocks();
    });

    it("should call upsertClient with the correct parameters and return a status 200 with success true", async () => {
        upsertClient.mockResolvedValueOnce();

        await clientUpsert(req, res);

        expect(upsertClient).toHaveBeenCalledWith(req.body.value, req.body.id);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });

 

    it("should handle missing or invalid PatientId_Prefix", async () => {
        req.body.value.PatientId_Prefix = "";

        await clientUpsert(req, res);

        expect(upsertClient).toHaveBeenCalledWith(req.body.value, req.body.id);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });



    it("should handle missing AdminEmail", async () => {
        req.body.value.AdminEmail = null;

        await clientUpsert(req, res);

        expect(upsertClient).toHaveBeenCalledWith(req.body.value, req.body.id);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });

    it("should handle empty TimeSlots", async () => {
        req.body.value.TimeSlots = null;

        await clientUpsert(req, res);

        expect(upsertClient).toHaveBeenCalledWith(req.body.value, req.body.id);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });

    it("should handle missing LogoName", async () => {
        req.body.value.LogoName = null;

        await clientUpsert(req, res);

        expect(upsertClient).toHaveBeenCalledWith(req.body.value, req.body.id);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });

    it("should handle missing Phonepe settings", async () => {
        req.body.value.Phonepe_MerchantId = null;
        req.body.value.Phonepe_SaltKey = null;
        req.body.value.Phonepe_SaltIndex = null;

        await clientUpsert(req, res);

        expect(upsertClient).toHaveBeenCalledWith(req.body.value, req.body.id);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });

});
