import {initializeApp, cert} from 'firebase-admin/app';
import {getMessaging} from 'firebase-admin/messaging';

import {upsertUserNotification} from '../helpers/notification/notification.helper.js';
import { ServiceAccountJson } from './service-account-file.js';
export const SetFirebaseNotification = () => {
    initializeApp({credential: cert(ServiceAccountJson)});
}
export const sendNotificationViaToken =async (token, messagetxt, title, isSuccess, clinicId, userId) => {
    if (token != null) {
        var message = {
            "token": token,
            "notification": {
                "body": messagetxt,
                "title": title
            },
            "data": {
                "notificationType": isSuccess
                    ? "success"
                    : "error"
            }
        };

        var result = getMessaging()
            .send(message)
            .then((response) => {
                console.log('Firebase Message sent:', response);
            })
            .catch((error) => {
                console.error('Error sending message:', error);
            });
    }

    if(isSuccess)
    await upsertUserNotification(title, messagetxt, userId, clinicId);
}

export const sendNotificationViaTopic =async (topicName, message, title) => {
    if (to != null) {
        var message = {
            "topic": topicName,
            "notification": {
                "body": message,
                "title": title
            },
            "data": {
                "notificationType": "info"
            }
        };

        var result = getMessaging()
            .send(message)
            .then((response) => {
                console.log('Firebase Topic Message sent:', response);
            })
            .catch((error) => {
                console.error('Error sending message:', error);
            });
    }
}