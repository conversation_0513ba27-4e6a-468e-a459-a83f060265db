import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateSend = async (req, res, next) => {
  await checkSchema({
    "data.mobile": {
      in: ["body"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
    },
    "data.message": {
      in: ["body"],
      isString: {
        errorMessage: "message  must be a string",
      },
      notEmpty: {
        errorMessage: "message is required",
      },
    },
   "data.clientId": {
          in: ["body"],
          custom: {
            options: (value) => mongoose.Types.ObjectId.isValid(value),
            errorMessage: "clientId must be a valid ObjectId",
          },
        },
  
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSendAppointmentSummary = async (req, res, next) => {
  await checkSchema({
    "data.to": {
      in: ["body"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
    },
    "data.clinicName": {
      in: ["body"],
      isString: {
        errorMessage: "Clinic name must be a string",
      },
      notEmpty: {
        errorMessage: "Clinic name is required",
      },
    },
    "data.patientName": {
      in: ["body"],
      isString: {
        errorMessage: "Patient name must be a string",
      },
      notEmpty: {
        errorMessage: "Patient name is required",
      },
    },
    "data.doctorName": {
      in: ["body"],
      isString: {
        errorMessage: "Doctor name must be a string",
      },
      notEmpty: {
        errorMessage: "Doctor name is required",
      },
    },
    "data.scheduleDate": {
      in: ["body"],
      isString: {
        errorMessage: "Schedule date must be a valid date",
      },
      notEmpty: {
        errorMessage: "Schedule date is required",
      },
    },
    "data.timeSlots": {
      in: ["body"],
      isString: {
        errorMessage: "Time slot must be a string",
      },
      notEmpty: {
        errorMessage: "Time slot is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};


export const validateSendAppointmentLink = async (req, res, next) => {
    await checkSchema({
      "data.to": {
        in: ["body"],
        isMobilePhone: true,
        errorMessage: "Mobile must be a valid phone number",
      },
      "data.clinicName": {
        in: ["body"],
        isString: {
          errorMessage: "Clinic name must be a string",
        },
        notEmpty: {
          errorMessage: "Clinic name is required",
        },
      },
      "data.patientName": {
        in: ["body"],
        isString: {
          errorMessage: "Patient name must be a string",
        },
        notEmpty: {
          errorMessage: "Patient name is required",
        },
      },
      "data.doctorName": {
        in: ["body"],
        isString: {
          errorMessage: "Doctor name must be a string",
        },
        notEmpty: {
          errorMessage: "Doctor name is required",
        },
      },
      "data.scheduleDate": {
        in: ["body"],
        isString: {
          errorMessage: "Schedule date must be a valid date",
        },
        notEmpty: {
          errorMessage: "Schedule date is required",
        },
      },
      "data.timeSlots": {
        in: ["body"],
        isString: {
          errorMessage: "Time slot must be a string",
        },
        notEmpty: {
          errorMessage: "Time slot is required",
        },
      },
      "data.googleMeetLink": {
        in: ["body"],
        isString: {
          errorMessage: "googleMeetLink  must be a string",
        },
        notEmpty: {
          errorMessage: "googleMeetLink  is required",
        },
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };



  export const validateSendPaymentLink = async (req, res, next) => {
    await checkSchema({
      "data.to": {
        in: ["body"],
        isMobilePhone: true,
        errorMessage: "Mobile must be a valid phone number",
      },
      "data.clinicName": {
        in: ["body"],
        isString: {
          errorMessage: "Clinic name must be a string",
        },
        notEmpty: {
          errorMessage: "Clinic name is required",
        },
      },
      "data.patientName": {
        in: ["body"],
        isString: {
          errorMessage: "Patient name must be a string",
        },
        notEmpty: {
          errorMessage: "Patient name is required",
        },
      },
      "data.doctorName": {
        in: ["body"],
        isString: {
          errorMessage: "Doctor name must be a string",
        },
        notEmpty: {
          errorMessage: "Doctor name is required",
        },
      },
      "data.scheduleDate": {
        in: ["body"],
        isString: {
          errorMessage: "Schedule date must be a valid date",
        },
        notEmpty: {
          errorMessage: "Schedule date is required",
        },
      },
      "data.timeSlots": {
        in: ["body"],
        isString: {
          errorMessage: "Time slot must be a string",
        },
        notEmpty: {
          errorMessage: "Time slot is required",
        },
      },
      "data.paymentLink": {
        in: ["body"],
        isString: {
          errorMessage: "paymentLink  must be a string",
        },
        notEmpty: {
          errorMessage: "paymentLink  is required",
        },
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };


  export const validateSendOTP = async (req, res, next) => {
    await checkSchema({
      "data.mobile": {
        in: ["body"],
        isMobilePhone: true,
        errorMessage: "Mobile must be a valid phone number",
      },
      "data.email": {
        in: ["body"],
        isEmail: true,
        optional: true,
        errorMessage: "email must be a valid email id",
      },
      "data.otp": {
        in: ["body"],
        isString: true,
        errorMessage: "otp is missing",
      },
   
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };