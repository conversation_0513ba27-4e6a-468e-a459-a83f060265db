import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { removeStaff } from '../../../helpers/staff/staff.helper.js'; 
import { Staff } from '../../../model/clinics.model.js'; 
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); // Set up MongoDB connection
});

afterAll(async () => {
  await teardown(); // Tear down MongoDB connection
});

describe('removeStaff function', () => {
  let staffId;

  beforeEach(async () => {
    await Staff.deleteMany({});

    const staff = new Staff({
      firstName: 'Test',
      lastName: 'Staff',
      staffId:"testStaffId",
      mobile: '1234567890',
      specialization: 'General',
      prefix:"Mr.",
      isDoctor: true,
      deleted: false,
    });
    staffId = staff._id;
    await staff.save();
  });

  afterEach(async () => {
    await Staff.deleteMany({});
  });

  it('should set the deleted flag to true for a valid staff ID', async () => {
    const result = await removeStaff(staffId);

    expect(result.deleted).toBe(true);
    expect(result._id).toEqual(staffId); 
  });



});
