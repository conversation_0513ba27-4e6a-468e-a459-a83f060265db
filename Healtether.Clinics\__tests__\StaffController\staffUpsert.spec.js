import { jest } from "@jest/globals";
const { mockStaffHelper } = await import("../mocks/mock.staff.helper");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
mockStaffHelper();
mockCommonUtils();
mockFirebaseMethod();
const { staffUpsert } = await import('../../controllers/staffs/staffs.controller.js'); 
const { upsertStaff }= await import('../../helpers/staff/staff.helper.js');
const { buildNotificationText, resultObject, formatTodayDate } = await import("../../utils/common.utils.js");
const { sendNotificationViaToken } = await import("../../config/firebase.admin.js");

describe('staffUpsert', () => {
    let req, res;

    beforeEach(() => {
        req = {
            body: {
         
                data: {
                  staffId: "CHENNAI_160",
                  firstName: "test5",
                  lastName: "test",
                  specialisation: "",
                  isDoctor: "false",
                  age: "16",
                  birthday: "2007-12-05",
                  gender: "Female",
                  mobile: "**********",
                  email: "",
                  address: {
                    house: "",
                    street: "",
                    landmarks: "",
                    city: "",
                    pincode: ""
                  },
                  documentType: "",
                  documentNumber: "",
                  bankName: "",
                  account: "",
                  accountName: "",
                  ifsc: "",
                  isAdmin: "false",
                  createdOn: "10/09/2024, 17:38:15",
                  modifiedOn: "10/09/2024, 17:38:15",
                  profilepic: "",
                  documents: [],
                  clientId: "662ca0a41a2431e16c41ebaa",
                  availableTimeSlot: [
                    {
                      key: "*************",
                      timeSlot: [
                        {
                          _id: "*************",
                          start: "03:00 AM",
                          end: "04:00 AM"
                        }
                      ],
                      slotDuration: "27",
                      weekDay: ["Sun"]
                    }
                  ]
                }
              },
            user: {
                id: 'userId123',
                name: 'User Name'
            },
            Notificationkey: 'someNotificationKey'
        };

        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should successfully upsert staff  and send notification ', async () => {
        const upsertedData = { success: true };
        
        upsertStaff.mockResolvedValue(upsertedData);
        buildNotificationText.mockReturnValue('test5 test has been added');
        sendNotificationViaToken.mockResolvedValue();

        await staffUpsert(req, res);

        expect(upsertStaff).toHaveBeenCalledWith(req.body.data, req.body.data.id, req.user);
        expect(buildNotificationText).toHaveBeenCalledWith('test5', 'test', ' has been added', req.user);
        expect(sendNotificationViaToken).toHaveBeenCalledWith(req.Notificationkey, 'test5 test has been added', 'Staff Detail', true, '662ca0a41a2431e16c41ebaa', 'userId123');

        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(upsertedData);
    });


    it('should return mobile-duplicate error when duplicate mobile is detected', async () => {
        upsertStaff.mockResolvedValueOnce({
          code: 11000,
          message: 'mobile-duplicate',
          success: false,
        });
    
        await staffUpsert(req, res);
    
        expect(res.status).toHaveBeenCalledWith(200);
    
        expect(res.json).toHaveBeenCalledWith(
          expect.objectContaining({ code: 11000, message: 'mobile-duplicate', success: false })
        );
      });

      

      it('should return name-duplicate error when duplicate name is detected', async () => {
        upsertStaff.mockResolvedValueOnce({
          code: 11000,
          message: 'name-duplicate',
          success: false,
        });
    
        await staffUpsert(req, res);
    
        expect(res.status).toHaveBeenCalledWith(200);
    
        expect(res.json).toHaveBeenCalledWith(
          expect.objectContaining({ code: 11000, message: 'name-duplicate', success: false })
        );
      });

      it('should return staffId-duplicate error when duplicate staffId is detected', async () => {
        upsertStaff.mockResolvedValueOnce({
          code: 11000,
          message: 'staffId-duplicate',
          success: false,
        });
    
        await staffUpsert(req, res);
    
        expect(res.status).toHaveBeenCalledWith(200);
    
        expect(res.json).toHaveBeenCalledWith(
          expect.objectContaining({ code: 11000, message: 'staffId-duplicate', success: false })
        );
      });

});
