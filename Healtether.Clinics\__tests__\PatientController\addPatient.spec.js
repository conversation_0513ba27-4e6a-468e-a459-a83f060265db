
import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
mockCommonUtils();
mockFirebaseMethod();
mockPatientHelper();

const { addPatientDetails } = await import('../../controllers/patients/patients.controller.js'); 

const { addUpdateDetails }= await import('../../helpers/patient/patient.helper.js'); 
const { sendNotificationViaToken }= await import('../../config/firebase.admin.js'); 
const { buildNotificationText }= await import('../../utils/common.utils.js'); 

const mockPatientData = {
  firstName: "test",
  lastName: "test",
  patientId: "SD_173",
  age: "10",
  height: "120",
  weight: "30",
  birthday: "2014-07-17",
  gender: "Male",
  mobile: "**********",
  email: "",
  prefix:"Mr.",
  address: {
      house: "",
      street: "",
      landmarks: "",
      city: "",
      pincode: ""
  },
  documentType: "",
  documentNumber: "",
  documents: [],
  createdOn: "08/09/2024, 10:48:15",
  modifiedOn: "08/09/2024, 10:48:15",
  clientId: "662ca0a41a2431e16c41ebaa"
}

const req = {
  body: { patientData: mockPatientData },
  user: { id: 'user123', name: 'Test User' },
  Notificationkey: 'notification-key',
};

const res = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn(),
};

describe('addPatientDetails', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should add a new patient and send a notification', async () => {

    addUpdateDetails.mockResolvedValueOnce(mockPatientData);
    buildNotificationText.mockReturnValueOnce('Notification message');
    sendNotificationViaToken.mockResolvedValueOnce();
    await addPatientDetails(req, res);
    expect(addUpdateDetails).toHaveBeenCalledWith(req.body.patientData, null, req.user);
    expect(buildNotificationText).toHaveBeenCalledWith('Patient', 'test test', 'has been added successfully', req.user);
    expect(sendNotificationViaToken).toHaveBeenCalledWith(req.Notificationkey, 'Notification message', 'Patient Detail', true, req.body.patientData.clientId, req.user.id);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });


});
