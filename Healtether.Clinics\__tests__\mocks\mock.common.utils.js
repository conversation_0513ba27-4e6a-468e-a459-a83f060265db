import { jest } from "@jest/globals";

export async function mockCommonUtils() {
    await jest.unstable_mockModule("../../utils/common.utils.js", async () => ({
        buildNotificationText: jest.fn(),
        resultObject: jest.fn(),
        formatTodayDate: jest.fn(),
        getDecimal:jest.fn(),
        getFileTimeNow: jest.fn(),
        calculateDiscount: jest.fn(),
        calculateAmountAfterDisc: jest.fn(),
        getExpiryByMinutes: jest.fn(),
        getProperMobile: jest.fn(),
        getTodayDate: jest.fn(),
        getCurrentTime: jest.fn(),
        checkStringEmptyAndReturnNull: jest.fn(),
        lastPathInUrl: jest.fn(),
        getAppointmentInUtcDateWithTime: jest.fn(),
        toTitleCase: jest.fn(),
    }));
}
