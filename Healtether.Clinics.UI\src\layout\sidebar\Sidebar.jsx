import { Link, NavLink, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import defaultLogo from "assets/images/healtether-logo.png";
import secondLogo from "assets/images/HealTetherLogo.png";
import { useEffect, useState } from "react";
import PropTypes from "prop-types";

export default function Sidebar({ isOpen, toggleSiderbar,setViewSidebarxs }) {
  const [selectedMenu, setSelectedMenu] = useState(false);
  const location = useLocation();
  const { user } = useSelector((state) => state.user);
  const { clinic } = useSelector((state) => state.currentClinic);
  const [showText, setShowText] = useState(false);
  let SuperAdmin = user.isSuperAdmin;
  let admin = user.isSuperAdmin;
  for (let index = 0; index < user.linkedClinics.length && !admin; index++) {
    const linkedClinic = user.linkedClinics[index];

    var isCurrent = linkedClinic?.clinic?._id == clinic._id;

    if (isCurrent) {
      admin = linkedClinic.isAdmin;
      break;
    }
  }
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        setShowText(isOpen);
      }, 300);
    } else {
      setShowText(isOpen);
    }
  }, [isOpen]);
  /*const LogutUser= async(e) =>{

    confirm({
        show: true,
        title: 'Logout',
        proceed: async () => {
          dispatch(removeUser());
       return dispatch(logout());
        },
        confirmation: 'Are you sure you want to logout '
    })
    e.stopPropagation();
}*/

  const menuHtml = [];
  const menu = [
    {
      title: "Home",
      icon: "icon-[system-uicons--home] text-2xl",
      cssclass: "block  py-3  transition duration-200 ",
      route: "dashboard",
      path: "dashboard",
      role: "All",
    },
    {
      title: "Appointments",
      icon: "icon-[solar--calendar-linear] text-2xl",
      cssclass: "block py-3   transition duration-200 ",
      route: "appointment",
      path: "appointment",
      role: "All",
    },
    {
      title: "ABHA Queue",
      icon: "icon-[solar--calendar-linear] text-2xl",
      cssclass: "block py-3   transition duration-200 ",
      route: "appointmentoverview",
      path: "appointmentoverview",
      role: "All",
    },
    {
      title: "WhatsApp Chat",
      icon: "icon-[ion--logo-whatsapp] text-2xl",
      cssclass: "block py-3   transition duration-200 ",
      route: "chats",
      path: "chats",
      role: "All",
    },
    {
      title: "Patients Record",
      icon: "icon-[ooui--user-contributions-ltr] text-2xl",
      cssclass: "block  py-3   transition duration-200 ",
      route: "patient/managepatient",
      path: "patient",
      role: "All",
    },
    {
      title: "Manage Staff",
      icon: "icon-[medical-icon--i-care-staff-area] text-2xl ",
      cssclass: "block  py-3  transition duration-200  ",
      route: "staff/managestaffs",
      path: "staff",
      role: "Admin",
    },
    {
      title: "Payments",
      icon: "icon-[streamline--money-wallet-money-payment-finance-wallet] text-2xl",
      cssclass: "block py-3  transition duration-200 ",
      route: "payments",
      path: "payments",
      role: "All",
    },
    {
      title: "Analytics",
      icon: "icon-[ion--trending-up-outline] text-2xl ",
      cssclass: "block py-3  transition duration-200 ",
      role: "Admin",
      route: "analytics",
      path: "analytics",
    },
    /*  {
            title: "Logout",
            icon: "icon-[ant-design--logout-outlined] text-2xl",
            cssclass: "block py-3  transition duration-200 ",
            route: "",
            role:"All"
        }*/
  ];

  const selectedMenuCss = "bg-backcolor_primary border-r-2 border-r-[#110C2C]";
  menu.forEach((item, i) => {
    const isActive = location.pathname.includes(item.path);

    if (
      (item.role == "Admin" && admin) ||
      item.role == "All" ||
      (item.role == "SuperAdmin" && SuperAdmin)
    )
      menuHtml.push(
        /*     item.title !== "Logout" ?  */
        <Link
          to={item.route}
          className={
            item.cssclass +
            ` hover:bg-backcolor_primary hover:border-r-2 hover:border-r-[#110C2C]   ${
              isActive ? selectedMenuCss : ""
            } `
          }
          key={i}
          onClick={() => setSelectedMenu(item.route)}
        >
          <div className="flex justify-start pl-5 content-center">
            <span className={item.icon}></span>
            <span
              className={
                (!showText ? " hidden" : "") +
                " ml-3  transition-all ease-in-out duration-300"
              }
            >
              {item.title}
            </span>
          </div>
        </Link>
        /* :
            <div onClick={LogutUser} className={item.cssclass+" cursor-pointer hover:bg-backcolor_primary hover:border-r-2 hover:border-r-[#110C2C]"} key={i}>
        <div className='flex justify-start pl-5 content-center' >
            <span className={item.icon}></span>
            {isOpen? <span className=' ml-3 '>{item.title}</span>:<></>}
        </div>
            
            </div>*/
      );
  });
  return (
    <>
    <div className="absolute z-1000 right-0 top-0 mt-2 cursor-pointer rounded-full lg:hidden sm:flex "  onClick={()=>{
            setViewSidebarxs();
          }}>
    <span
            className="text-Primary icon-[gridicons--cross-circle] text-2xl  "
          ></span>
   
    </div>
      <div className="h-[13%] w-full grid justify-center content-center pt-2 relative">


        {isOpen ? (
          <img
            src={defaultLogo}
            className=" h-full max-h-[73px] transition-all ease-in-out duration-300 p-5"
            alt="main_logo"
          />
        ) : (
          <img
            src={secondLogo}
            className=" h-full max-h-[73px] p-2 transition-all ease-in-out duration-300"
            alt="main_logo"
          />
        )}
      </div>
      <div className="h-[87%] no-scrollbar overflow-y-auto  ">
        <nav className="text-text_primary space-y-6 mt-3 ">
          {menuHtml}
        </nav>
      </div>
      <div className="pr-3.5 mb-2 xl:flex justify-end md:flex xs:hidden">
        {isOpen ? (
          <span
            className="text-Primary icon-[solar--round-double-alt-arrow-left-bold] text-4xl cursor-pointer transition-all delay-700 "
            onClick={() => toggleSiderbar()}
          ></span>
        ) : (
          <span
            className="text-Primary icon-[solar--round-double-alt-arrow-right-bold] text-4xl cursor-pointer transition-all delay-700"
            onClick={() => toggleSiderbar()}
          ></span>
        )}
      </div>
    </>
  );
}

Sidebar.propTypes = {
  isOpen: PropTypes.bool,
  toggleSiderbar: PropTypes.func,
};
