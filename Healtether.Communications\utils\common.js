
export function getProperMobile(mobile){

    if(mobile!=null && mobile.toString().length>10)
    {
        return mobile;
    }
    else if(mobile!=null && mobile.toString().length==10)
    {
        return ("91"+mobile);
    }

    return null;
}

export function getExpiry(months)
{
  var today=new Date();
  var expiryDate= today.setMonth(today.getMonth()+ parseInt(months));
  return expiryDate;
}

export function getHoursDiff(sTime) {
    var isoDateString = new Date().toISOString();
    var startDate = new Date(sTime);
    var currentDate = new Date(isoDateString);
    
    var diff = currentDate.getTime() - startDate.getTime();
    var hh = Math.floor(diff / 1000 / 60 / 60);
    return hh;
}
export function buildTimeSlot(obj) {
    return formTimeText(obj.hours) + ":" + formTimeText(obj.min) + " " + obj.tt;
}

export function getExpiryByMinutes(mins) {
    var today = new Date();
    var expiryDate = today.setMinutes(today.getMinutes() + parseInt(mins));
    return expiryDate;
}

export function formTimeText(data){
  if(data.toString().length==1)
    {
        return "0"+data.toString();
    }
    return data;
}


// For Appointment TimeSlots
export function generateTimeSlots(startTime, endTime, duration) {
    let slots = [];
    let current = new Date("2024/04/01 "+startTime);
    let end = new Date("2024/04/01 "+endTime);
    let minutesToAdd=parseInt(duration)<0? 0:parseInt(duration);
    while (current < end) {
        let slotStart = new Date(current);

        current.setMinutes((current.getMinutes() + minutesToAdd));
        let slotEnd =minutesToAdd!==0? new Date(current) :end;

        if (slotEnd <= end) {
            slots.push({
                start: slotStart.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'}),
                end: slotEnd.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'})
            });
        }

        if(minutesToAdd===0)
            break;
    }
    return slots;
}

export function sortAndRemoveDuplicateTimeSlots(timeSlots) {
    // Step 1: Remove duplicates using a Set
    const uniqueTimeSlots = [...new Set(timeSlots)];

    // Step 2: Sort the time slots
   let result=uniqueTimeSlots.sort((a, b) => {
       // Extract the start times
       const startTimeA = parseTimeSlotsStart(a.split(" - ")[0]);
       const startTimeB = parseTimeSlotsStart(b.split(" - ")[0]);

       return startTimeA - startTimeB;
   });


    return result;
}

function parseTimeSlotsStart(timeStr) {
    let [time, modifier] = timeStr.split(' ');
    let [hours, minutes] = time.split(':').map(Number);

    // Convert 12 AM/PM to 24-hour format
    if (modifier.toLowerCase() === 'pm' && hours !== 12) {
        hours += 12;
    } else if (modifier.toLowerCase() === 'am' && hours === 12) {
        hours = 0;
    }

    // Return the time as the number of minutes from midnight
    return hours * 60 + minutes;
}
