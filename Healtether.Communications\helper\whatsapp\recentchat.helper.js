import mongoose from "mongoose";
import {whatsapplogDb} from "../../config/whatsapp.collections.config.js";
import { getPatientByMobileAndClinic } from "../patient/patient.helper.js";
import { getProperMobile } from "../../utils/common.js";

export const addRecentChat = async (mobile, clinicId) => {
  var toMobile = getProperMobile(mobile);
  var recentchatModel = whatsapplogDb.model("WhatsappRecentChat");
  var recent = await recentchatModel
    .findOne({ mobile: toMobile, clinicId: clinicId })
    .exec();

  if (recent != null) {
    // update as unreaded,
    (recent.timeStamp = new Date().toISOString()),
      (recent.expireAt = new Date());
    recent.unReaded = true;
    await recent.save();
  } else {
    var patientName = [];
    var patientDetail = await getPatientByMobileAndClinic(toMobile, clinicId);
    if (patientDetail != null && patientDetail.length > 0) {
      patientName = patientDetail.map((item, i) => {
        return { firstName: item.firstName, lastName: item.lastName };
      });
    }
    const messageLog = new recentchatModel({
      timeStamp: new Date().toISOString(),
      mobile: toMobile,
      patientName: patientName,
      clinicId: clinicId,
      expireAt: new Date(),
    });
    await messageLog.save();
  }
};

export const setChatReaded = async (mobile, clinicId) => {
  var toMobile = getProperMobile(mobile);
  var recentchatModel = whatsapplogDb.model("WhatsappRecentChat");

  var recent = await recentchatModel
    .findOne({ mobile: toMobile, clinicId: clinicId })
    .exec();
  if (recent != null) {
    // update as unreaded,
    recent.unReaded = false;
    await recent.save();
  }
};

export const getRecentChats = async (clinicId) => {
  var recentchatModel = whatsapplogDb.model("WhatsappRecentChat");

  var recent = await recentchatModel
    .find({ clinicId: clinicId })
    .limit(50)
    .sort({
      timeStamp: 1,
    })
    .select({
      timeStamp: 1,
      mobile: 1,
      patientName: 1,
      unReaded: 1,
    })
    .exec();
  return recent;
};
export const getRecentChatByMobile = async (mobile, clinicId) => {
  var toMobile = getProperMobile(mobile);
  var recentchatModel = whatsapplogDb.model("WhatsappRecentChat");

  var recent = await recentchatModel
    .findOne({ clinicId: clinicId, mobile: toMobile })
    .select({
      timeStamp: 1,
      mobile: 1,
      unReaded: 1,
    })
    .exec();
  return recent;
};
