import mongoose from "mongoose";
import {
  CLIENT_COLLECTION,
  PATIENT_COLLECTION,
  STAFF_COLLECTION,
} from "../../mongodb.collection.name.js";

const bookedAppointmentSchema = new mongoose.Schema(
  {
    mobile: {
      type: String,
      index: true,
      required: true,
      maxLength: 10,
    },
    name: {
      type: String,
      required: true,
      maxLength: 255,
    },
    gender: {
      type: String,
      maxLength: 20,
    },
    age: {
      type: Number,
      min: 1,
      max: 100,
    },
    birthDate: {
      type: Date,
    },
    appointmentDate: {
      type: Date,
      index: true,
    },
    timeSlot: {
      type: String,
      index: true,
      maxLength: 50,
    },
    reason: {
      type: String,
      maxLength: 1000,
    },
    abhaNumber: {
      type: String,
      maxLength: 50,
    },
    abhaAddress: {
      type: String,
      maxLength: 50,
    },
    virtualConsultation: {
      type: Boolean,
      default: false,
    },
    googleLink: {
      link: { type: String },
      id: { type: String },
    },

    created: {
      on: {
        type: Date,
        default: Date.Now,
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255,
        },
      },
    },
    modified: {
      on: {
        type: Date,
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255,
        },
      },
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    doctorName: {
      type: String,
      maxLength: 255,
    },
    clinicPatientId: {
      type: String,
      maxLength: 255,
    },
    doctorId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: STAFF_COLLECTION,
    },
    patientId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: PATIENT_COLLECTION,
    },
    clinic: {
      type: mongoose.Schema.Types.ObjectId,
      ref: CLIENT_COLLECTION,
      index: true,
    },
    isCancelled: {
      type: Boolean,
      default: false,
    },
    address: {
      type: String,
      maxLength: 225,
    },
    district: {
      type: String,
      maxLength: 250,
    },
    state: {
      type: String,
      maxLength: 100,
    },
    pincode: {
      type: String,
      maxLength: 10,
    },
    isFollowUp: {
      type: Boolean,
      default: false,
    },
    googleLink: {
      link: { type: String },
      id: { type: String },
    },
    tokenNumber: {
      type: Number,
    },
    type: {
      type: String,
      enum: ["PRE_BOOKED", "SCAN_SHARE", "WALK_IN"],
      default: "PRE_BOOKED",
    },
  },
  { versionKey: "1.0" }
);

bookedAppointmentSchema.index(
  { "created.on": 1 },
  {
    expireAfterSeconds: 3600, // 60 minutes in seconds
    partialFilterExpression: { type: "SCAN_SHARE" },
  }
);

export { bookedAppointmentSchema };
