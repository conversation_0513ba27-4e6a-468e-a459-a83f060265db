import mongoose from "mongoose";
import { <PERSON><PERSON><PERSON><PERSON>, User,Staff } from "../../model/clinics.model.js";
import { CreateOrUpdateByMobile, updateUserById } from "../user/user.helper.js";
import { resultObject } from "../../utils/common.utils.js";

export const staffOverview = async (
  clientId,
  pg,
  size,
  keyword,
  sortby,
  direction,
  status
) => {
  var dbClientId = new mongoose.Types.ObjectId(clientId);
  const regex = new RegExp(keyword, "i"); // i for case insensitive

  const findObj =
    keyword != null
      ? {
        $or: [
          {
            mobile: {
              $regex: regex,
            },
          },
          {
            firstName: {
              $regex: regex,
            },
          },
          {
            lastName: {
              $regex: regex,
            },
          },
          {
            staffId: {
              $regex: regex,
            },
          },
        ],
        deleted: false,
        clinic: new mongoose.Types.ObjectId(clientId),
      }
      : {
        deleted: false,
        clinic: new mongoose.Types.ObjectId(clientId),
      };
  const sortObj =
    sortby != null
      ? {
        sortby: direction == "desc" ? -1 : 1,
      }
      : {
        modified: -1,
        created: -1,
      };

  switch (status) {
    case "Doctor": findObj["isDoctor"] = true; break;
    case "Staff": findObj["isDoctor"] = false; break;
    default: break;
  }

  const count = await Staff.find(findObj).sort(sortObj).count();
  const staffCollection = await Staff.find(findObj)
    .sort(sortObj)
    .skip(pg * size)
    .limit(size)
    .select({
      _id: 1,
      staffId: 1,
      firstName: 1,
      lastName: 1,
      prefix:1,
      mobile: 1,
      isDoctor: 1
    })
    .exec();

  return { data: staffCollection, totalCount: count };
};

export const staffById = async (id) => {
    const staff = await Staff.findById(id).exec();
    return staff;
};
export const removeStaff = async (id) => {
    const staff = await Staff.findById(id).exec();
    staff.deleted = true;
    await staff.save();
    return staff;
};

export const getDoctorTimeSlotsById = async (id) => {
    const doctor = await Staff.findById(id).exec();
    var result = doctor != null ? {
      name: doctor?.firstName + " " + doctor?.lastName,
      availableTimeSlot: doctor?.availableTimeSlot
    } : null;
    return result;
}

export const getDoctorsWithTimeSlots = async (clinicId) => {
    const doctors = await Staff.find({
      clinic: new mongoose.Types.ObjectId(clinicId),
      deleted: false,
      isDoctor: true
    }).select({
      _id: 1,
      firstName: 1,
      lastName: 1,
      prefix:1,
      mobile: 1,
      specialization: 1,
      availableTimeSlot: 1
    }).exec();
    return doctors;
}

export const getDoctor = async (clinicId) => {
    const doctor = await Staff.aggregate([
      {
        $lookup: {
          from: "linkclientusers", // collection name in db
          localField: "userId",
          foreignField: "userId",
          as: "linkedClinics",
        },
      },
      {
        $match: {
          deleted: false,
          isDoctor: true,
          "linkedClinics.clinic": {
            $eq: new mongoose.Types.ObjectId(clinicId),
          },
        },
      },
      {
        $project: {
          _id: 1,
          firstName: 1,
          lastName: 1,
          prefix: 1,
          mobile: 1,
          specialization: 1,
        },
      },
    ]).exec();
    return doctor;
};


export const searchStaff = async (clientId, name, size) => {
    const staffCollection = await Staff.find({
      deleted: false,
      clinic: new mongoose.Types.ObjectId(clientId),
      $or: [
        {
          firstName: {
            $regex: ".*" + name + ".*",
            $options: "i", 
          },
        },
        {
          lastName: {
            $regex: ".*" + name + ".*",
            $options: "i", 
          },
        },
      ],
    })
      .limit(size)
      .sort({ modifiedOn: -1, createdOn: -1 })
      .select({
        _id: 1,
        firstName: 1,
        lastName: 1,
        mobile: 1,
        isAdmin: 1,
        isDoctor: 1,
        email: 1,
      })
      .exec();
    return { staffCollection };
};

export const checkMobileNumberPresent = async (mobile, id) => {
  let staffObj = null;
  staffObj = await Staff.findOne({ mobile: mobile }).exec();

  const staffCollection = await Staff.aggregate([
    {
      $lookup: {
        from: "linkclientusers", // collection name in db
        localField: "userId",
        foreignField: "userId",
        as: "linkedClinics",
      },
    },
    {
      $match: { mobile: mobile },
    },
    {
      $project: {
        _id: 1,
        firstName: 1,
        lastName: 1,
        mobile: 1,
        isDoctor: 1,
        linkedClinics: {
          isAdmin: 1,
        },
      },
    },
  ]).exec();

  if (id != null && staffObj != null && staffObj._id != id) {
    return true;
  }

  return staffObj?._id != null   // mobile (true)
}

export  const mapWeekDayWithTimeSlot=(slot)=>{

  const MapTimeSlot = (timeSlots) => {
    let slots = [];
    for (const timeSlotElement of timeSlots) {
      slots.push({
        start: timeSlotElement.start,
        end: timeSlotElement.end
      });
    }
    return slots;
  }
  let timeSlotModel = [];
  for (const item of JSON.parse(slot)) {
    timeSlotModel.push({
      weekDay: item.weekDay,
      timeSlot: MapTimeSlot(item.timeSlot),
      slotDuration: item.slotDuration,
    })
  }
  console.log("Mapped " + JSON.stringify(timeSlotModel));
  return timeSlotModel;
}

export const upsertStaff = async (data, id, user) => {
    let staffObj = null;
    try{
 
      if (id != null) {
        staffObj = await Staff.findById(new mongoose.Types.ObjectId(id)).exec();
      }
      if (staffObj != null) {
        staffObj.firstName = data.firstName;
        staffObj.lastName = data.lastName;
        staffObj.specialization = data.specialisation;
        staffObj.isDoctor = data.isDoctor === "true" || data.isDoctor === true;
        staffObj.age = data.age;
        staffObj.birthday = data.birthday;
        staffObj.gender = data.gender;
        staffObj.mobile = data.mobile;
        //whatsapp: data.whatsapp,
        staffObj.email = data.email;
        staffObj.address = data.address;
        staffObj.documentType = data.documentType;
        staffObj.documentNumber = data.documentNumber;
        staffObj.upiId = data.upiId;
        staffObj.bankName = data.bankName;
        staffObj.accountName = data.accountName;
        staffObj.accountNo = data.account;
        staffObj.ifsc = data.ifsc;
        staffObj.hprId=data.hprId;
        staffObj.prefix=data.prefix;
        staffObj.clinic = new mongoose.Types.ObjectId(data.clientId);
        staffObj.modified = {
          on: new Date().toISOString(),
          by: user,
        };
  
        staffObj.profilePic = data.profilepic;
        staffObj.documents = data.documents;
        staffObj.isAdmin = data.isAdmin === "true" || data.isAdmin === true;
        staffObj.availableTimeSlot =data.availableTimeSlot?mapWeekDayWithTimeSlot(data?.availableTimeSlot):[]
      } else {
        staffObj = new Staff({
          staffId: data.staffId,
          firstName: data.firstName,
          lastName: data.lastName,
          specialization: data.specialisation,
          isDoctor: data.isDoctor === "true" || data.isDoctor === true,
          age: data.age,
          birthday: data.birthday,
          gender: data.gender,
          mobile: data.mobile,
          //whatsapp: data.whatsapp,
          email: data.email,
          address: data.address,
          hprId:data.hprId,
          documentType: data.documentType,
          documentNumber: data.documentNumber,
          upiId: data.upiId,
          bankName: data.bankName,
          accountName: data.accountName,
          accountNo: data.account,
          prefix: data.prefix,
          ifsc: data.ifsc,
          // clients: data.clients,
          created: {
            on: new Date().toISOString(),
            by: user,
          },
          profilePic: data.profilepic,
          documents: data.documents,
          isAdmin: data.isAdmin === "true" || data.isAdmin === true,
          deleted: false,
          _id: id,
          clinic: new mongoose.Types.ObjectId(data.clientId),
          availableTimeSlot:data.availableTimeSlot?mapWeekDayWithTimeSlot(data?.availableTimeSlot):[]
        });
      }
      await staffObj.save();
      var user = await updateUserById({
        email: data.email,
        mobile: data.mobile,
        firstName: data.firstName,
        lastName: data.lastName,
        isAdmin: data.isDoctor === "true" || data.isAdmin === "true",
        id: staffObj.userId,
        clientId: new mongoose.Types.ObjectId(data.clientId),
      });
      if (staffObj.userId == null && (id == null || id == undefined)) {
        staffObj.userId = user._id;
        await staffObj.save();
      }
       return resultObject(0, "", true);
    }
      catch (error) {
        var mobileDuplicate = error?.code == 11000 && error?.keyPattern?.clinic == 1 && error?.keyPattern?.mobile == 1;
        var nameDuplicate = error?.code == 11000 && error?.keyPattern?.clinic == 1 && error?.keyPattern?.firstName == 1 && error?.keyPattern?.lastName == 1;
        var staffIDDuplicate = error?.code == 11000 && error?.keyPattern?.clinic == 1 && error?.keyPattern?.staffId == 1;
    
        if (mobileDuplicate || nameDuplicate || staffIDDuplicate) {
          return resultObject(400,
            mobileDuplicate ? "mobile-duplicate" : (nameDuplicate ? "name-duplicate" : (staffIDDuplicate ? "staffId-duplicate" : ""))
            , false);
        }
        return error;
      }
   
};

export const upsertUserByNameMobileEmail = async (
  firstName,
  lastName,
  prefix,
  mobile,
  email,
  userId,
  clinicStaffId,
  clientId
) => {
  var userObj = await updateUserById({
    email: email,
    mobile: mobile,
    firstName: firstName,
    lastName: lastName,
    prefix: prefix,
    isAdmin: true,
    id: userId,
    clientId: clientId,
  });

  if (
    (userObj?.staffDetail != null && userObj?.staffDetail?._id != null) ||
    (userObj?.staffDetail?.length > 0 &&
      userObj?.staffDetail[0]._id != null)
  ) {
    let _id = userObj?.staffDetail?._id || userObj?.staffDetail[0]._id;
    await Staff.findByIdAndUpdate(_id, {
      $set: {
        mobile: mobile,
        email: email,
        isAdmin: true,
        firstName: firstName,
        lastName: lastName,
        prefix: prefix
      },
    });

    return userObj;
  } else {
    const staffCollection = new Staff({
      mobile: mobile,
      email: email,
      isAdmin: true,
      firstName: firstName,
      lastName: lastName,
      userId: userObj.id,
      staffId: clinicStaffId,
      clinic: clientId,
      prefix: prefix
    });
    await staffCollection.save();
    return userObj;
  }
};
