import FollowUpAndCancel, { LoadAppointmentDetail } from "../../pages/appointment/FollowUpAndCancel";
import ScheduleAppointment, { AppointmentAction, EditAppointmentLoader } from "../../pages/appointment/ScheduleAppointment";
import ScheduleSuccessfully from "../../pages/appointment/ScheduleSuccessfully";
import AppointmentsOverview from "../../pages/appointment/v2/NewAppointment/AppointmentsOverview";
import ViewAppointmentAndPatient_V2, { EditAppointmentConsultationLoader } from "../../pages/appointment/v2/NewAppointment/ViewAppointmentAndPatient";
import ViewAppointmentAndPatient from "../../pages/appointment/ViewAppointmentAndPatient";
import Consultation from "../../pages/patient/Consultation";
//import { EditAppointmentConsultationLoader } from "../../pages/appointment/ViewAppointmentAndPatient";
import EditPatient, { PatientAction, PatientLoader } from "../../pages/patient/EditPatient";
import ViewPatient, { PatientWithAppointmentDetailLoader } from "../../pages/patient/ViewPatient";
import WritePrescription, { PrescriptionAction, PrescriptionLoader } from "../../pages/writePrescription/WritePrescription";
import WritePrescriptionForStaff from "../../pages/writePrescription/WritePrescriptionForStaff";
import ActionPropType from "../../utils/ActionPropType";
import { AuthorizeComponent } from "./AuthorizeComponent";
import { Dashboard } from "pages/home/<USER>";
import PatientsOverview from "pages/patient/PatientsOverview";
import StaffsOverview from "pages/staff/StaffsOverview";
import EditStaff from "../../pages/staff/EditStaff";
import ViewStaff from "../../pages/staff/ViewStaff";
import { StaffAction, StaffLoader } from "../../pages/staff/EditStaff";
import { ClinicLoader } from "../../pages/clinic/ClinicEdit";
import ClinicEdit from "../../pages/clinic/ClinicEdit";
import ClinicOverview from "../../pages/clinic/ClinicOverview";
import { ClinicAction } from "../../pages/clinic/ClinicEdit";
import Settings, { SettingAction, SettingLoader } from "../../pages/clinic/SettingsPage";
import Notification from "../../pages/Notification/Notification.jsx";
import PaymentDetails, { PaymentAction, PaymentLoader } from "../../pages/payment/PaymentDetails";
import PaymentOverview from "../../pages/payment/PaymentOverview.jsx";
import Analytics from "../../pages/analytics/Analytics.jsx";
import ChatOverview from "../../pages/chat/ChatOverview";

const DASHBOARD_ROUTES = [
  {
    path: "dashboard",
    element: <AuthorizeComponent component={Dashboard} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
        ],
      },
    },
  }
];

const PATIENT_ROUTES = [
  {
    path: "patient/addpatient",
    element: <AuthorizeComponent component={EditPatient} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "patient/managepatient",
            "Patient Records",
            () => { }
          ),
          new ActionPropType("noroute", "Add Patient", () => { }),
        ],
      },
    },
    action: PatientAction,
    loader: PatientLoader,
  },
  {
    path: "patient/viewpatient/:id",
    element: <AuthorizeComponent component={ViewPatient} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "patient/managepatient",
            "Patient Records",
            () => { }
          ),
          new ActionPropType("noroute", "View Patient", () => { }),
        ],
      },
    },
    loader: PatientWithAppointmentDetailLoader,
    //action: patientAction,// loader: patientLoader
  },
  {
    path: "patient/viewpatient/:id/consultation",
    element: <AuthorizeComponent component={Consultation} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "patient/managepatient",
            "Patient Records",
            () => { }
          ),
          new ActionPropType("noroute", "View Patient", () => { }),
        ],
      },
    },
    loader: PatientLoader,
    action: PatientAction,
  },
  {
    path: "patient/viewpatient/:id/edit",
    element: <AuthorizeComponent component={EditPatient} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "patient/managepatient",
            "Patient Records",
            () => { }
          ),
          new ActionPropType("noroute", "Edit Patient", () => { }),
        ],
      },
    },
    loader: PatientLoader,
    action: PatientAction,
  },
  {
    path: "patient/managepatient",
    element: <AuthorizeComponent component={PatientsOverview} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Patient Records", () => { }),
        ],
      },
    },
  },
  {
    path: "patient/viewpatient/:id",
    element: <AuthorizeComponent component={ViewPatient} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "patient/managepatient",
            "Patient Records",
            () => { }
          ),
          new ActionPropType("noroute", "Patient Details", () => { }),
        ],
      },
    },
    loader: PatientLoader,
  },

];

const APPOINTMENT_ROUTES = [
  {
    path: "appointment",
    element: <AuthorizeComponent component={AppointmentsOverview} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Appointments", () => { }),
        ],
      },
    },
  },
  {
    path: "appointmentoverview",
    element: <AuthorizeComponent component={AppointmentsOverview} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Appointments", () => { }),
        ],
      },
    },
  },
  {
    path: "appointment/:id/success",
    element: <AuthorizeComponent component={ScheduleSuccessfully} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Schedule Appointments", () => { }),
        ],
      },
    },
    // action: AppointmentAction,
    loader: EditAppointmentLoader,
  },
  {
    path: "appointment/:id/edit",
    element: <AuthorizeComponent component={ScheduleAppointment} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Schedule Appointments", () => { }),
        ],
      },
    },
    action: AppointmentAction,
    loader: EditAppointmentLoader,
  },
  {
    path: "appointment/:id/consultation",
    element: <AuthorizeComponent component={ViewAppointmentAndPatient} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Appointment Details", () => { }),
        ],
      },
    },
    // action: AppointmentAction,
    loader: EditAppointmentConsultationLoader,
  },
  {
    path: "appointment/:id/consultation_v2",
    element: <AuthorizeComponent component={ViewAppointmentAndPatient_V2} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Appointment Details", () => { }),
        ],
      },
    },
    // action: AppointmentAction,
    loader: EditAppointmentConsultationLoader,
  },
  {
    path: "patient/:id/appointment/:appointmentid/details",
    element: <AuthorizeComponent component={FollowUpAndCancel} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Appointment Details", () => { }),
        ],
      },
    },
    // action: AppointmentAction,
    loader: LoadAppointmentDetail,
  },
  {
    path: "scheduleappointment",
    element: <AuthorizeComponent component={ScheduleAppointment} />,
    handle: {
      crumb: {
        actionButton: null,
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Schedule Appointments", () => { }),
        ],
      },
    },
    action: AppointmentAction,
    loader: EditAppointmentLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/writeprescription",
    element: (
      <AuthorizeComponent
        component={WritePrescription}
        checkIsAdminOrDoctor={true}
      />
    ),
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/vitals",
    element: <AuthorizeComponent component={WritePrescription} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/medicalhistory",
    element: <AuthorizeComponent component={WritePrescription} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/vitalsforstaffs",
    element: <AuthorizeComponent component={WritePrescriptionForStaff} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/medicalhistoryforstaffs",
    element: <AuthorizeComponent component={WritePrescriptionForStaff} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/writeprescription",
    element: (
      <AuthorizeComponent
        component={WritePrescription}
        checkIsAdminOrDoctor={true}
      />
    ),
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/vitals",
    element: <AuthorizeComponent component={WritePrescription} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/medicalhistory",
    element: <AuthorizeComponent component={WritePrescription} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/vitalsforstaffs",
    element: <AuthorizeComponent component={WritePrescriptionForStaff} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },
  {
    path: "appointment/:appointmentId/:patientId/medicalhistoryforstaffs",
    element: <AuthorizeComponent component={WritePrescriptionForStaff} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("appointment", "Appointments", () => { }),
          new ActionPropType("noroute", "Prescription", () => { }),
        ],
      },
    },
    action: PrescriptionAction,
    loader: PrescriptionLoader,
  },

];

const STAFF_ROUTES = [
  {
    path: "staff/managestaffs",
    element: <AuthorizeComponent component={StaffsOverview} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Staff Details", () => { }),
        ],
      },
    },
  },
  {
    path: "staff/addmember",
    element: <AuthorizeComponent component={EditStaff} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "staff/managestaffs",
            "Staff Details",
            () => { }
          ),
          new ActionPropType("noroute", "Add Member", () => { }),
        ],
      },
    },
    action: StaffAction,
    loader: StaffLoader,
  },
  {
    path: "staff/viewmember/:id",
    element: <AuthorizeComponent component={ViewStaff} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "staff/managestaffs",
            "Staff Details",
            () => { }
          ),
          new ActionPropType("noroute", "Member Detail", () => { }),
        ],
      },
    },
    loader: StaffLoader,
    action: StaffAction, // loader: staffLoader
  },
  {
    path: "staff/viewmember/:id/edit",
    element: <AuthorizeComponent component={EditStaff} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "staff/managestaffs",
            "Staff Details",
            () => { }
          ),
          new ActionPropType("noroute", "Edit Member", () => { }),
        ],
      },
    },
    loader: StaffLoader,
    action: StaffAction, // loader: staffLoader
  },
]

const CLINIC_ROUTES = [
  {
    path: "clinic/manageclinic",
    element: <AuthorizeComponent component={ClinicOverview} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Manage Clinic", () => { }),
        ],
      },
    },
  },
  {
    path: "clinic/addclinic",
    element: <AuthorizeComponent component={ClinicEdit} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "clinic/manageclinic",
            "Manage Clinic",
            () => { }
          ),
          new ActionPropType("noroute", "Add Clinic", () => { }),
        ],
      },
    },
    action: ClinicAction,
    loader: ClinicLoader,
  },
  {
    path: "clinic/editclinic/:id",
    element: <AuthorizeComponent component={ClinicEdit} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "clinic/manageclinic",
            "Manage Clinic",
            () => { }
          ),
          new ActionPropType("noroute", "Edit Clinic", () => { }),
        ],
      },
    },
    action: ClinicAction,
    loader: ClinicLoader,
  },
  {
    path: "clinic/setting",
    element: <AuthorizeComponent component={Settings} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType(
            "clinic/manageclinic",
            "Manage Clinic",
            () => { }
          ),
          new ActionPropType("noroute", "Settings", () => { }),
        ],
      },
    },
    action: SettingAction,
    loader: SettingLoader,
  },
  {
    path: "clinic-detail",
    element: <AuthorizeComponent component={ClinicOverview} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Notifications", () => { }),
        ],
      },
    },
  },
];

const NOTIFICATION_ROUTES = [
  {
    path: "notification",
    element: <AuthorizeComponent component={Notification} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Notifications", () => { }),
        ],
      },
    },
  }
];

const CHAT_ROUTES = [{
  path: "chats",
  element: <AuthorizeComponent component={ChatOverview} />,
  handle: {
    crumb: {
      actionButton: new ActionPropType(
        "addpatient",
        "Add New Patient",
        () => { }
      ),
      breadcrumb: [
        new ActionPropType("dashboard", "Dashboard", () => { }),
        new ActionPropType("noroute", "Manage Patient", () => { }),
      ],
    },
  },
}];

const PAYMENT_ROUTES = [
  {
    path: "payments",
    element: <AuthorizeComponent component={PaymentOverview} />,
    handle: {
      crumb: {
        actionButton: new ActionPropType(
          "addpatient",
          "Add New Patient",
          () => { }
        ),
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Payments Overview", () => { }),
        ],
      },
    },
  },
  {
    path: "payments/:id/manage",
    element: <AuthorizeComponent component={PaymentDetails} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("payments", "Payments Overview", () => { }),
          new ActionPropType("noroute", "Proceed to Payments", () => { }),
        ],
      },
    },
    action: PaymentAction,
    loader: PaymentLoader,
  },
];

const ANALYTICS_ROUTES = [
  {
    path: "analytics",
    element: <AuthorizeComponent component={Analytics} />,
    handle: {
      crumb: {
        breadcrumb: [
          new ActionPropType("dashboard", "Dashboard", () => { }),
          new ActionPropType("noroute", "Analytics", () => { }),
        ],
      },
    },
  }
];

export const CHILD_ROUTES = [
  ...DASHBOARD_ROUTES,
  ...PATIENT_ROUTES,
  ...APPOINTMENT_ROUTES,
  ...STAFF_ROUTES,
  ...CLINIC_ROUTES,
  ...NOTIFICATION_ROUTES,
  ...CHAT_ROUTES,
  ...PAYMENT_ROUTES,
  ...ANALYTICS_ROUTES
];