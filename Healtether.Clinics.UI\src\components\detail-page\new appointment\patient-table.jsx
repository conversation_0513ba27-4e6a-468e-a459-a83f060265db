import React from "react";
import { Icons } from "../../../../components/detail-page/icons";
import { customer_support } from "../../../../components/detail-page/appointment/mock-data";
import { useNavigate } from "react-router-dom";
export default function PatientTable({
  appointments,
  MakeReceiptInAppointmentOverview,
  doctor,
  SuperAdmin,
  admin,
  alertBox,
}) {
  const navigation = useNavigate();

  return (
    <section className="mt-5 flex flex-col gap-4 font-roboto">
      {appointments?.map((appointment, i) => (
        <div className="w-full rounded-lg h-fit px-2 shadow-sm ">
          <article className=" overflow-x-auto w-full h-32 custom-scrollbar">
            <div
              // to={`/appointment/${appointment._id}/consultation`}
              className="h-full pl-0 pr-2 pb-2 flex items-center justify-evenly w-full gap-10"
            >
              <div className="pl-4 sticky w-max flex gap-6 items-center left-0 z-10 h-full bg-white">
                <section className=" text-xs font-normal text-color_muted flex items-center justify-center flex-col gap-3 h-full w-32">
                  {appointment.patientId}
                  <div className="">{appointment.timeSlot}</div>
                </section>
                <section className=" text-xs font-normal text-color_muted flex  justify-center flex-col gap-3">
                  <div className=" flex items-center gap-1 w-32">
                    <div className=" text-sm text-dark capitalize whitespace-nowrap">
                      {appointment?.name?.length > 10
                        ? `${appointment?.name.substring(0, 6)}...`
                        : appointment?.name}
                      ,
                    </div>
                    <div>{appointment.age},</div>
                    <div>{appointment.gender}</div>
                  </div>
                  <div className="flex items-center gap-1 text-dark w-24">
                    <div className="">
                      <Icons.profile />
                    </div>
                    <div className=" truncate">{appointment._id}</div>
                  </div>
                  <div className="flex items-center gap-1 text-dark w-fit">
                    <Icons.address /> ABHA address
                  </div>
                </section>
              </div>
              <section className=" text-xs font-normal text-color_muted flex  justify-center flex-col gap-3">
                <aside className="h-7 flex items-center justify-end gap-1 text-Primary text-sm cursor-pointer w-fit ml-auto">
                  <Icons.doctor_profile className="h-4 w-4" />
                  {appointment.doctorName}
                </aside>
                <main className=" flex gap-4">
                  <div className=" max-2xl:hidden">
                    <div className=" flex items-center gap-2 text-dark">
                      <Icons.phone className="h-5 w-5" />
                      {appointment.mobile}
                    </div>
                    <div className=" flex items-center gap-2 text-dark mt-2">
                      <Icons.abha_num className="h-5 w-5" />
                      <div className="mt-0.5">ABHA number</div>
                    </div>
                  </div>
                  {customer_support(
                    appointment,
                    navigation,
                    MakeReceiptInAppointmentOverview,
                    doctor,
                    SuperAdmin,
                    admin,
                    alertBox
                  ).map(
                    ({ label, Icon, handleClick }, idx) =>
                      idx < 6 && (
                        <div
                          key={idx}
                          onClick={handleClick}
                          className="text-center w-14 flex flex-col items-center gap-1"
                        >
                          <div className="h-7 w-8 rounded-md bg-light cursor-pointer flex items-center justify-center">
                            <Icon />
                          </div>
                          {label}
                        </div>
                      )
                  )}
                </main>
              </section>
            </div>
          </article>
        </div>
      ))}
    </section>
  );
}
