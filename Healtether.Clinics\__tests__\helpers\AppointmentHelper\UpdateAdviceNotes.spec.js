import { jest } from "@jest/globals";
import mongoose from 'mongoose';
import { setup, teardown } from '../../../setup.js'; 
import { updateAdviceNotes } from '../../../helpers/appointment/appointment.helper.js';
import { Appointment } from '../../../model/clinics.model.js';
jest.setTimeout(30000);


beforeAll(async () => {
    await setup(); 
  });
  
  afterAll(async () => {
    await teardown(); 
  });

describe('Appointment Model Test', () => {
  it('should insert and retrieve an appointment successfully', async () => {
    let user = { id: "user", name: 'Test User' }; 
    const testClientId = new mongoose.Types.ObjectId();
    const appointmentId= new mongoose.Types.ObjectId();
    const testAppointment = {
      _id:appointmentId,
      name: 'test',
      appointmentDate: new Date(),
      clinic: testClientId,
      paymentStatus: false,
      isDeleted: false,
      gender: "Male",
      age: 33,
      mobile: "**********"
    };
    const data = {
      advice: ['Take medication twice daily'],
      notes: ['Patient has a history of hypertension'],
    };

    await Appointment.create(testAppointment);
    const result = await updateAdviceNotes(data,appointmentId, user); 

    expect(result.advice).toEqual(data.advice);
    expect(result.notes).toEqual(data.notes);
    expect(result.name).toEqual("test");
    expect(result.clinic).toEqual(testClientId);
    expect(result.paymentStatus).toEqual(false);
    expect(result.gender).toEqual("Male");
    expect(result.age).toEqual(33);
    expect(result.mobile).toEqual("**********");
  });
});
