{"name": "healtether.external.communications", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "clear_jest": "jest --clear<PERSON>ache"}, "author": "Healtether healthcare", "license": "", "dependencies": {"@aws-sdk/client-sns": "^3.787.0", "@azure/communication-email": "^1.0.0", "@socket.io/mongo-adapter": "^0.3.2", "applicationinsights": "^2.9.6", "axios": "^1.8.4", "body-parser": "^1.20.3", "braces": "^3.0.3", "cors": "^2.8.5", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.0", "healtether.models": "^1.8.1", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.0.0", "nodemon": "^3.1.9", "socket.io": "^4.7.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@jest/globals": "^29.7.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "supertest": "^7.0.0"}}