import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
mockClientHelper();
const { getAllClinics } = await import("../../controllers/clinic/client.controller.js");
const { getAllClients } = await import("../../helpers/clinic/client.helper.js");


const mockClients = [
    { _id: '62a000000000000000000001', clinicName: 'Clinic A' },
    { _id: '62a000000000000000000002', clinicName: 'Clinic B' }
];

const res = {
    json: jest.fn().mockReturnThis(),
    status: jest.fn().mockReturnThis(),
};

describe('getAllClinics', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return all clinics successfully', async () => {
        getAllClients.mockResolvedValueOnce(mockClients);
        await getAllClinics({}, res);
        expect(getAllClients).toHaveBeenCalled();
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockClients);
    });

});


