import { jest } from "@jest/globals";
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockgooglemeetHelper } = await import("../mocks/mock.googlemeet.helper.js");
mockApointmentHelper();
mockgooglemeetHelper();
const { scheduleGoogleMeet } = await import("../../controllers/appointments/appointment.controller.js"); 
const { scheduleMeeting } = await import("../../helpers/googleMeet/googleMeet.js");

describe('scheduleGoogleMeet', () => {
    let req, res;

    beforeEach(() => {
        req = {
            body: {
                data: {
                    doctorEmail: '<EMAIL>',
                    patientEmail: '<EMAIL>',
                    doctorName: 'Dr. <PERSON>',
                    meetingDescription: 'Consultation',
                    scheduleTime: '2024-09-02T10:00:00Z',
                    scheduleDuration: 60,
                    appointmentID: 'appointment123'
                }
            }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

 

    it('should return 200 and meeting link if scheduling is successful', async () => {
        const mockResponse = {
            success: true,
            link: 'https://meet.google.com/test-meeting',
            id: 'meeting123'
        };

        scheduleMeeting.mockResolvedValue(mockResponse);

        await scheduleGoogleMeet(req, res);

        expect(scheduleMeeting).toHaveBeenCalledWith(
            req.body.data.doctorEmail,
            req.body.data.doctorName,
            req.body.data.patientEmail,
            req.body.data.meetingDescription,
            req.body.data.scheduleTime,
            req.body.data.scheduleDuration
        );

        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            link: mockResponse.link,
            id: mockResponse.id
        });
    });

    it('should return 400 if scheduling fails', async () => {
        const mockErrorResponse = {
            success: false,
            error: 'Failed to schedule meeting'
        };

        scheduleMeeting.mockResolvedValue(mockErrorResponse);

        await scheduleGoogleMeet(req, res);

        expect(scheduleMeeting).toHaveBeenCalledWith(
            req.body.data.doctorEmail,
            req.body.data.doctorName,
            req.body.data.patientEmail,
            req.body.data.meetingDescription,
            req.body.data.scheduleTime,
            req.body.data.scheduleDuration
        );

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({
            success: false,
            error: mockErrorResponse.error
        });
    });

});
