import React, { useEffect, useState } from "react";
import {
  useTable,
  useFilters,
  useGlobalFilter,
  useAsyncDebounce,
  useSortBy,
  usePagination,
} from "react-table";
import { Link } from "react-router-dom";
import { But<PERSON>, PageButton } from "./Button";
import { classNames } from "./Utils";
import { SortIcon, SortUpIcon, SortDownIcon } from "./Icons";
import { Tooltip } from "react-tooltip";
import { QueryClient, QueryClientProvider, useQuery } from "react-query";
import TablePagination from "./Pagination";
import ReactTablePagination from "utils/react-table-pagination";
import Loader from "../loader/Loader";
import GridFilter from "./GridFilter";
import DefaultTextboxClass from "utils/Classes";
import { Icons } from "../detail-page/icons";
import { customer_support } from "../detail-page/appointment/mock-data";
import SkeletonLoader from "../detail-page/skeleton";
import { useNavigate } from "react-router-dom";
import useFormatPhoneNumber from "./../../utils/hooks/useFormatPhoneNumber";

// Define a default UI for filtering
function GlobalFilter({
  preGlobalFilteredRows,
  globalFilter,
  setGlobalFilter,
}) {
  const count = preGlobalFilteredRows.length;
  const [value, setValue] = React.useState(globalFilter);
  const onChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 200);

  return (
    <label className="flex gap-x-2 items-baseline">
      <span className="text-gray-700">Search:</span>
      <input
        type="text"
        className={DefaultTextboxClass}
        // className="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring-3 focus:ring-indigo-200 focus:ring-opacity-50"
        value={value || ""}
        onChange={(e) => {
          setValue(e.target.value);
          onChange(e.target.value);
        }}
        placeholder={`${count} records...`}
      />
    </label>
  );
}

// This is a custom filter UI for selecting a unique option from a list
export function SelectColumnFilter({
  column: { filterValue, setFilter, preFilteredRows, id, render },
}) {
  // Calculate the options for filtering using the preFilteredRows
  const options = React.useMemo(() => {
    const options = new Set();
    preFilteredRows.forEach((row) => {
      options.add(row.values[id]);
    });
    return [...options.values()];
  }, [id, preFilteredRows]);

  // Render a multi-select box
  return (
    <label className="flex gap-x-2 items-baseline">
      <span className="text-gray-700">{render("Header")}:</span>
      <select
        className="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring-3 focus:ring-indigo-200 focus:ring-opacity-50"
        name={id}
        id={id}
        value={filterValue}
        onChange={(e) => {
          setFilter(e.target.value || undefined);
        }}
      >
        <option value="">All</option>
        {options.map((option, i) => (
          <option key={i} value={option}>
            {option}
          </option>
        ))}
      </select>
    </label>
  );
}

function Table({
  MakeReceiptInAppointmentOverview,
  alertBox,
  doctor,
  SuperAdmin,
  admin,
  headerColumn,
  rowClick,
  tableCaption,
  serverCall,
  showRow,
  tabButton,
  showFilter = true,
  showHeader = true,
  tableBg = "bg-backcolor_disabled",
  defaultKeywords,
  externalCall,
}) {
  const navigation = useNavigate();
  const [loading, setLoading] = useState(() => false);
  const [loaded, setLoaded] = useState(() => true);

  const initialState = {
    queryPageIndex: 0,
    queryPageSize: showRow,
    totalCount: 0,
    queryPageFilter: "",
    queryPageSortBy: [],
  };
  let columns = React.useMemo(() => headerColumn, []);

  const [keyword, setKeyword] = useState(
    defaultKeywords || { text: "", option: {} }
  );
  const [useFilter, setUseFilter] = useState(false);
  useEffect(() => {
    if (externalCall != null && externalCall.count > 0) {
      let resetKeyword = externalCall.SetKeywordDetails(keyword);
      if (resetKeyword != null) setUseFilter(true);
      setKeyword(resetKeyword);
    }
  }, [externalCall]);
  const onClickFilterCallback = (filter) => {
    console.log(filter);

    if (filter?.text?.trim() === "" && filter?.option?.status == null) {
      alert("Please enter a keyword to search!");
      return;
    }
    if (filter?.text === keyword.text && filter?.option == keyword?.option) {
      alert("No change in search");
      return;
    }
    setUseFilter(true);
    setKeyword({ text: filter.text, option: filter.option });
  };
  const reducer = (state, { type, payload }) => {
    switch (type) {
      case ReactTablePagination.PAGE_CHANGED:
        return {
          ...state,
          queryPageIndex: payload,
        };
      case ReactTablePagination.PAGE_SIZE_CHANGED:
        return {
          ...state,
          queryPageSize: payload,
        };
      case ReactTablePagination.PAGE_SORT_CHANGED:
        return {
          ...state,
          queryPageSortBy: payload,
        };
      case ReactTablePagination.PAGE_FILTER_CHANGED:
        return {
          ...state,
          queryPageFilter: payload,
        };
      case ReactTablePagination.TOTAL_COUNT_CHANGED:
        return {
          ...state,
          totalCount: payload,
        };
      default:
        throw new Error(`Unhandled action type: ${type}`);
    }
  };
  const [
    {
      queryPageIndex,
      queryPageSize,
      totalCount,
      queryPageFilter,
      queryPageSortBy,
    },
    dispatch,
  ] = React.useReducer(reducer, initialState);

  const { isLoading, error, data, isSuccess } = useQuery(
    ["users", queryPageIndex, queryPageSize, queryPageFilter, queryPageSortBy],
    () =>
      serverCall(
        queryPageIndex,
        queryPageSize,
        queryPageFilter,
        queryPageSortBy
      ),
    {
      keepPreviousData: false,
      staleTime: Infinity,
      cacheTime: 1000,
    }
  );
  const totalPageCount = Math.ceil(totalCount / queryPageSize);

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
    page,
    pageCount,
    pageOptions,
    gotoPage,
    previousPage,
    canPreviousPage,
    nextPage,
    canNextPage,
    setPageSize,
    state: { pageIndex, pageSize, sortBy },
  } = useTable(
    {
      columns,
      data: data?.results || [],
      initialState: {
        pageIndex: queryPageIndex,
        pageSize: queryPageSize,
        sortBy: queryPageSortBy,
      },
      manualPagination: true,
      pageCount: data ? totalPageCount : null,
      autoResetSortBy: false,
      autoResetExpanded: false,
      autoResetPage: false,
    },
    useSortBy,
    usePagination
  );
  const manualPageSize = [];
  useEffect(() => {
    dispatch({ type: ReactTablePagination.PAGE_CHANGED, payload: pageIndex });
  }, [pageIndex]);

  useEffect(() => {
    dispatch({
      type: ReactTablePagination.PAGE_SIZE_CHANGED,
      payload: pageSize,
    });
    gotoPage(0);
  }, [pageSize, gotoPage]);

  useEffect(() => {
    dispatch({ type: ReactTablePagination.PAGE_SORT_CHANGED, payload: sortBy });
    gotoPage(0);
  }, [sortBy, gotoPage]);

  useEffect(() => {
    if (useFilter) {
      dispatch({
        type: ReactTablePagination.PAGE_FILTER_CHANGED,
        payload: keyword,
      });
      gotoPage(0);
    }
  }, [keyword, gotoPage, useFilter]);

  useEffect(() => {
    setKeyword(defaultKeywords);
  }, [defaultKeywords]);

  useEffect(() => {
    if (data?.count) {
      dispatch({
        type: ReactTablePagination.TOTAL_COUNT_CHANGED,
        payload: data.count,
      });
    }
  }, [data?.count]);
  React.useEffect(() => {
    setLoaded(isSuccess);
    setLoading(isLoading);
  }, [isLoading, isSuccess]);

  const { formatNumber } = useFormatPhoneNumber();
  // Render the UI for your table
  return (
    <>
      {/* table */}
      <Tooltip id="grid-tooltip" />
      <article className="w-full mt-6">
        {showHeader && <></>}
        {loaded ? (
          <section className=" flex flex-col gap-2 py-4 px-2 font-roboto overflow-x-auto">
            <div className="pr-5 h-11 flex items-center justify-between min-w-max w-full gap-12 rounded-lg bg-Primary text-dark font-primary text-xl font-semibold">
              <div className="sticky left-0 top-0 pl-5 z-10 xl:max-w-[35%] xl:w-full flex items-center justify-between bg-Primary">
                <div className="w-40 text-center">Patient ID</div>
                <div className=" w-40 text-center">Patient </div>
              </div>
              <div className="w-32 text-center">Contact</div>
              <div className="w-28 text-center">Last visited</div>
              <div className="w-96 text-center">Action</div>
            </div>
            {page?.map(({ original }, i) => (
              <Link
                to={`/appointment/${original._id}/consultation`}
                className="h-24 pr-5 font-primary flex items-center justify-between min-w-max w-full gap-12 rounded-lg bg-light"
              >
                <div className="sticky left-0 top-0 pl-5 z-10 flex items-center justify-between xl:max-w-[35%]  xl:w-full h-full bg-light">
                  <div className="w-40 truncate text-center">
                    {" "}
                    {original.patientId}
                  </div>
                  <div className=" w-40 font-primary text-center">
                    <div className=" text-lg font-medium text-dark truncate">
                      {original.name}
                    </div>
                    <div className=" text-sm text-color_muted font-normal">
                      {original.age}yrs, {original.gender}
                    </div>
                  </div>
                </div>
                <div className="w-32 text-center text-lg font-medium text-dark">
                  {formatNumber(original.mobile)}
                </div>
                <div className=" text-lg text-color_muted font-medium text-center w-28">
                  24-08-22
                </div>
                <main className=" flex gap-4 w-96">
                  {customer_support(
                    original,
                    navigation,
                    MakeReceiptInAppointmentOverview,
                    doctor,
                    SuperAdmin,
                    admin,
                    alertBox
                  ).map(
                    ({ label, Icon, handleClick }, idx) =>
                      idx > 0 &&
                      idx < 5 && (
                        <div
                          key={idx}
                          onClick={handleClick}
                          className="text-center w-14 flex flex-col items-center gap-1 cursor-pointer text-xs text-color_muted font-semibold"
                        >
                          <div className="h-7 w-8 rounded-md bg-light flex items-center justify-center">
                            <Icon />
                          </div>
                          {label}
                        </div>
                      )
                  )}
                  <div className="text-center w-14 flex flex-col items-center gap-1 cursor-pointer text-xs text-color_muted font-semibold">
                    <div className="h-7 w-8 rounded-md bg-light flex items-center justify-center">
                      <Icons.delete />
                    </div>
                    Archieve Patient
                  </div>
                </main>
              </Link>
            ))}
          </section>
        ) : loading ? (
          ["", "", "", ""].map(() => <SkeletonLoader height={"h-32"} />)
        ) : (
          <tbody></tbody>
        )}
      </article>
    </>
  );
}

export default Table;
