import React, { useState, useEffect } from "react";
import { Records } from "./records";
import Record from "./record";
import AbhaRecord from "../abha-records/AbhaRecord";

export default function MedicalRecords({
  tab,
  type,
  setActiveTab,
  patientData,
  consultation,
  grantedConsentAbhaRecords,
}) {
  const [records, setRecords] = useState([]);
  const [selectedRecord, setSelectedRecord] = useState(null);

  const recordsProps = {
    records,
    type,
    setActiveTab,
    patientData,
    consultation,
    setSelectedRecord, // Only pass the setter to Records
  };
  
  // Record component only needs the selectedRecord
  const recordProps = {
    selectedRecord,
  };

  return (
    <div className="flex w-full h-full gap-2">
      <div className="w-3/12 bg-backcolor_detailpage">
        <Records
          {...recordsProps}
          grantedConsentAbhaRecords={grantedConsentAbhaRecords}
        />
      </div>
      <div className="w-9/12 bg-backcolor_detailpage">
        {type === "medical record" && (
          <Record {...recordProps} />
        )}
        {type === "abha record" && (
          <AbhaRecord {...recordProps} />
        )}
      </div>
    </div>
  );
}