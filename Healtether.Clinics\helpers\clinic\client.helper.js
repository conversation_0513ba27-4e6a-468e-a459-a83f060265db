import mongoose from "mongoose";
import {Client} from "../../model/clinics.model.js";
import { setInitialClientAutoId } from "../clinicautoidentifier/clinicautoid.helper.js";
import {upsertUserByNameMobileEmail} from "../staff/staff.helper.js";

export const upsertClient = async(data, id) => {
    let clientObj = {};
    if (id != null) {
        clientObj = await Client.findById(id).exec();
    }

    const clientCollection = new Client({
        clinicName: data.ClinicName,
        hfrId: data.hfrId,
        address: data.Address,
        patientId: {
            prefix: data.PatientId_Prefix,
            suffix: data.PatientId_Suffix
        },
        staffId: {
            prefix: data.StaffId_Prefix,
            suffix: data.StaffId_Suffix
        },
        consultationCharge: parseFloat(data.ConsultationCharge),
        phonepeSetting: {
            merchantId: data.Phonepe_MerchantId,
            saltKey: data.Phonepe_SaltKey,
            saltIndex: data.Phonepe_SaltIndex
        },
        logo: data.LogoName,
        googleMeetEmail: data.GoogleMeetEmail,
        created: {
            on: id != null ? data.createdOn : new Date()
        },
        modified: {
            on: id != null ? new Date() : null
        },
        isdeleted: false,
        _id: id
    });

    const upsertData = clientCollection.toObject();
    const result = await Client.findOneAndUpdate(
        { _id: upsertData._id },
        upsertData,
        { upsert: true, new: true }
    );

    const clinicId = result._id;
    
    // Pass the prefix from the admin user data
    const user = await upsertUserByNameMobileEmail(
        data.AdminFirstName,
        data.AdminLastName,
        data.AdminPrefix || "Mr.", // Default prefix if not provided
        data.AdminMobile,
        data.AdminEmail,
        clientObj?.adminUserId || null,
        `${data.StaffId_Prefix}1${data.StaffId_Suffix}`,
        clinicId
    );

    if (!id) {
        await Client.findByIdAndUpdate(clinicId, {
            adminUserId: user._id
        });
        await setInitialClientAutoId(clinicId);
    }
    
    return result;
};


export const updateSettingClient = async(data, id,user) => {
        let clientObj = {};
        if (id != null) {
            clientObj = await Client
                .findById(id)
                .exec();

            if(clientObj!=null)
            {
                clientObj.patientId= {
                    prefix: data.PatientId_Prefix,
                    suffix: data.PatientId_Suffix
                };
                
            clientObj.staffId= {
                prefix: data.StaffId_Prefix,
                suffix: data.StaffId_Suffix
                };

                clientObj.phonepeSetting= {
                    merchantId: data.Phonepe_MerchantId,
                    saltKey: data.Phonepe_SaltKey,
                    saltIndex: data.Phonepe_SaltIndex
                };
                clientObj.timeSlots=((data.TimeSlots!=''&& data.TimeSlots!=null)? JSON.parse(data.TimeSlots):[]);
                clientObj.modified = {
                    on: new Date().toISOString(),
                    by: user
                };
              await clientObj.save();
            }
        }
        return clientObj;
};
export const overview = async(pg, size,keyword) => {
        const searchCondition = keyword
        ? { clinicName: { $regex: keyword, $options: "i" } }: {};
        const clientCount = await Client
            .find({
                isdeleted: false,
                ...searchCondition,
            })
            .count();
        const clientCollection = await Client
            .find({
                isdeleted: false,
                ...searchCondition,
            })
            .populate({
                path: "adminUserId",
                select: {
                    firstName: 1,
                    lastName: 1,
                    email: 1,
                    mobile: 1
                }
            })
            .limit(size)
            .sort({modifiedOn: -1, createdOn: -1})
            .skip(pg * size)
            .select({_id: 1, clinicName: 1, adminUserId: 1})
            .exec();
        return {data: clientCollection, totalCount: clientCount};
};
export const getAllClients=async()=>{
        const clients = await Client
        .find()
        .where("isdeleted")
        .eq(false)
        .select({_id: 1, clinicName: 1})
        .exec();
        return clients;
}

export const getClientById = async(id) => {
        const client = await Client
            .findById(id)
            .populate({
                path: "adminUserId",
                select: {
                    firstName: 1,
                    lastName: 1,
                    email: 1,
                    mobile: 1
                }
            })
            .exec();
        return client;
};
export const getClientSetting = async(id) => {
        const client = await Client
            .findById(id)
          
            .select({
                clinicName: 1,
                timeSlots: 1,
                phonepeSetting: 1,
                patientId: 1,
                staffId: 1
            })
            .exec();
        // console.log("client",client);

        return client;
};
export const deleteClient = async(id) => {
        const client = await Client
            .findById(id)
            .exec();
            if (client) {
        client.isdeleted = true;
            }else{
                return "Client not found";
            }
        await client.save();
        return client;
};

export const updateClient = async(id, data,user) => {
        const updatedclient = {
            clinicName: data.clinicName,
            logo: data.logo,
            address: data.address,
            isActive: data.isActive,
            deleted: false,
            groupId:new mongoose.Types.ObjectId(data.groupId),
            adminUserId:new mongoose.Types.ObjectId(data.adminUserId),
            modified: {
                on: id != null
                    ? new Date()
                    : null,
                by:user
            },
            _id: id
        };
        const client = await Client.findByIdAndUpdate(id, updatedclient, {
            new: true,
            runValidators: true,
            useFindAndModify: false
        });

        return client;
};