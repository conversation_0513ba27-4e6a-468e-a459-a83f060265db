import { useNavigate } from "react-router-dom";
import { alertBox } from "../../../../../components/dialog/prompt.jsx";
import { Icons } from "../../../../../components/detail-page/icons.jsx";
import { MakeReceiptInAppointmentOverview } from "../../../../../services/appointment/writePrescription.js";
export const QueueActions = (
  appointment,
  isAdminOrDoctor,
  cancelHandler,
  noShowHandler,
  endConsultation,
  navigate 
  
) =>{
    const navigation = useNavigate();
  
 return ([
  {
    label: "Check out",
    Icon: Icons.check_out,
    handleClick: async (e) => {
      endConsultation(appointment._id);
      e.stopPropagation();
    }
  },
  {
    label:`${appointment.prescriptionFinished?"view Rx":"Write Rx"}`,
    Icon: Icons.write_x,
    handleClick: (e) => {
      navigation(
        `/appointment/${appointment._id}/${appointment.patientId}/writeprescription`
      );
      e.stopPropagation();
    },
  },
  {
    label: "Vitals and History",
    Icon: Icons.vitals,
    handleClick: (e) => {
      if (isAdminOrDoctor) {
        navigation(
          `/appointment/${appointment._id}/${appointment.patientId}/medicalhistory`
        );
      } else {
        navigation(
          `/appointment/${appointment._id}/${appointment.patientId}/medicalhistoryforstaffs`
        );
      }
      e.stopPropagation();
    },
  },
  {
    label: "Make Receipts",
    Icon: Icons.receipt,
    handleClick: async () => {
      let response = await MakeReceiptInAppointmentOverview(appointment._id);
    
      console.log("Appointment id:", appointment._id);
     
      if (response?.isValid) {

        console.log("Response from MakeReceiptInAppointmentOverview:", response);
      
        navigation(`/payments/${response?.invoiceId}/manage`, {
          state: {
            appointmentId: appointment._id,
          },
        });
      } else {
        alertBox({
          show: true,
          title: "Info.",
          proceed: undefined,
          confirmation: "Something went wrong",
        });
      }
    },
  },
  {
    label: "Chat",
    Icon: Icons.whatsapp,
    handleClick: () => navigation("/chats"),
  },
  {
    label: "Manage Apt.",
    Icon: Icons.settings,
    handleClick: (e) => {
      e.stopPropagation();
      navigate(
        `/patient/${appointment.patientId}/appointment/${appointment._id}/details`
      );
    },
  },
  {
    label: "Check in",
    Icon: Icons.check_in,
    handleClick: () => {},
  },
  {
    label: "No Show",
    Icon: Icons.no_show,
    handleClick: noShowHandler,
  },
  {
    label: "Cancel",
    Icon: Icons.cancel,
    handleClick: cancelHandler,
  },
  {
    label: "Reschedule",
    Icon: Icons.reschedule,
    handleClick: () => {},
  },
  {
    label: "Chat",
    Icon: Icons.whatsapp,
    handleClick: () => navigation("/chats"),
  },
])
};