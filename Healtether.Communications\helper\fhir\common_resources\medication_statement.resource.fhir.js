import { v4 as uuidv4 } from "uuid";
import { medicationStatementMetadata, medicationStatementDiv } from "../../../utils/fhir.constants.js";
import { getSnomedCtCode } from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

export const generateMedicationStatementResource = async (status, type, currentTime, patientResource) => {
    const id = uuidv4();

    const getSnomedData = await generateSnomedCtCode(type);

    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'MedicationStatement',
            id,
            meta: medicationStatementMetadata(),
            status: status,
            dateAsserted: currentTime,
            medicationCodeableConcept: getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term),
            subject: {
                reference: `urn:uuid:${patientResource.resource.id}`,
                display: patientResource.resource.resourceType
            }
            // text: medicationStatementDiv()
        }
    }
}