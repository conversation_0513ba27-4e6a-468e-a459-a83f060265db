import mongoose from "mongoose";
import { PATIENT_COLLECTION, CLIENT_COLLECTION, QUEUED_APPOINTMENT_COLLECTION } from "../mongodb.collection.name.js";
const dischargeSummarySchema = new mongoose.Schema(
    {
        clinicId: {
               type: mongoose.Schema.Types.ObjectId,
               ref: CLIENT_COLLECTION,
             required: true ,
             index:true,
           },
           patientId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: PATIENT_COLLECTION,
            required: true
        },
        symptoms: [
            {
                name: String,
                duration: Number,
                // duration: {
                //     value: Number,
                //     unit: String
                // },
                selectedDays: {
                    type: [String],
                    enum: ["Days", "Hours", "Weeks", "Months", "Years"],

                }
                // notes: {
                //     type: String,
                //     maxlength: 1500
                // },
            },
        ],

        historyOfIllness: {
            type: String,
            trim: true,
        },

        caseSummary: {
            type: String,
            trim: true,
        },

        conditionDischarge: {
            type: String,
            trim: true,
        },

        diagnosis: [
            {
                name: String,
                notes: {
                    type: String,
                    maxlength: 1500
                },
            },
        ],

        hospitalCourse: {
            type: String,
            trim: true,
        },

        operationDetails: [
            {
                operationDate: {
                    type: Date
                },
                notes: {
                    type: String,
                    maxlength: 1500
                },
            },
        ],

        adviceOnDischarge: [
            {
                isBeforeMeal: Boolean,
                drugName: String,
                dosage: String,
                frequency: String,
                duration: {
                    value: Number,
                    unit: String,
                },
                content: String,
                notes: String,
            },
        ],

        otherAdvice: {
            type: String,
            trim: true,
        },


        investigationAdvice: {
            type: String,
            trim: true,
        },

        dischargeDetails: {
            type: [String],
            enum: ["On request", "Death", "LAMA", "Normal discharge", "Referral"],
        },

        followUpAppointment: [
            {
                appointment: {
                    type: mongoose.Schema.Types.ObjectId,
                    ref: QUEUED_APPOINTMENT_COLLECTION
                },
                followUpDate: {
                    type: Date
                },
                followUpTimeSlot: {
                    type: String,
                    maxLength: 50
                },
            },
        ],

        created: {
            on: {
                type: Date,
                default: Date.now,
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255,
                },
            },
        },

        modified: {
            on: {
                type: Date,
                default: Date.now,
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255,
                },
            },
        },
    },
    {
        versionKey: "1.0",
        timestamps: true
    }
);
export {dischargeSummarySchema};