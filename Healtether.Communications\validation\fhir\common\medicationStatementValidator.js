
const medicationStatementValidator = {
  medicationStatements: {
    in: ["body"],
    isArray: true,
    errorMessage: "medicationStatements must be an array",
  },

  "medicationStatements.*.status": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each medicationStatement must have a non-empty status",
  },

  "medicationStatements.*.type": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each medicationStatement must have a non-empty type",
  },
}

export default medicationStatementValidator;
