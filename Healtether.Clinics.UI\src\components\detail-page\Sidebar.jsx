
function Sidebar({
    id,
    name,
    isActive,
    onClick,
    isVisible = true,
    isEnabled = false
}) {
  
    return (
        <div key={id + "_" + name}>
            <div
                className="grid grid-cols-1 font-semibold space-y-6  items-center font-primary">
                <div
                    onClick={() => {
                  const result=  onClick();
         
                }}
                    className={`${isVisible
                    ? ''
                    : 'hidden'}${isActive
                        ? ' bg-[#EEEEEE] shadow-2xs '
                        : ' bg-backcolor_disabled'} ${id} flex items-center pl-4 h-[55px] rounded-sm capitalize leading-[1.2rem] transition duration-300 ease-in-out ${isEnabled
                            ? ' cursor-pointer '
                            : ' bg-backcolor_disabled text-text_disabled cursor-none '}`}>
                    {name}
                </div>

            </div>
        </div>
    )
}

export default Sidebar