import React, { useEffect, useState } from 'react'
import DefaultTextboxClass from '../../utils/Classes'
import ButtonList from '../detail-page/ButtonList'
// import Select from 'react-select'

// const statusOptions = [
//     {'value':'', 'label':'All'},
//     {'value':'Active', 'label':'Active'},
//     {'value':'Inactive', 'label':'Inactive'},
//     {'value':'Pending', 'label':'Pending'},
// ]

function GridFilter({ onClickFilterCallback, defaultKeyword, tabButton, showFilter }) {
    const [keyword, setKeyword] = useState(defaultKeyword)

    const onKeywordChange = (e) => {
        var prev = keyword;
        setKeyword({ text: e.target.value, option: prev.option })
    }
    const onClickSearch = () => {
        onClickFilterCallback(keyword)
    }
    const onClear = async() => {
        var prev = keyword;
       await  onClickFilterCallback({ text: "ClearAllFilter", option: prev.option });
        setKeyword({ text: "", option:{} });

    }
    const [activeTab,
        setActiveTab] = useState(0);
    const handleTabClick = (tabNumber) => {
        setActiveTab(tabNumber);
    };

    const handleKeyPress = async (e) => {
        if (e.key === "Enter") {
            onClickSearch();
        }
    };
    useEffect(()=>{
      setKeyword(defaultKeyword)
    },[defaultKeyword])

    return (
        <>
            {tabButton != null ? <div className="mb-4 mt-2 font-primary font-semibold" >
                {tabButton.map((tab, i) => {
                    return <ButtonList key={i} id={i} name={tab.name} isActive={(activeTab === i)} change={() => {
                        handleTabClick(i);
                        var prev = keyword;
                        setKeyword({ text: prev.text, option: { ...prev.option, status: tab.setStatus?.status } });
                        onClickFilterCallback({ text: prev.text, option: { ...prev.option, status: tab.setStatus?.status } });
                    }} />
                })}
            </div> : <></>}
            {showFilter ?
                <div className="flex w-1/2 mb-2 font-primary font-medium">
                    {/* <div className="col-md-4 px-0">
                <Select
                    value={status}
                    onChange={onStatusChange}
                    options={statusOptions}
                    clearable={false}
                    className="react-select"
                    placeholder={statusPlaceholder}
                    classNamePrefix="react-select"
                />
            </div> */}
                    <div className="flex ml-1 w-3/5">
                        <input
                            value={keyword.text}
                            onChange={onKeywordChange}
                            type="text"
                            onKeyDown={handleKeyPress}
                            className={DefaultTextboxClass + " w-full py-1 leading-2 border text-sm shadow-2xs border-backcolor_secondary hover:border-backcolor_secondary rounded-full"}
                            //className="rounded-3xl text-[#484d63] outline-hidden shadow-2xs focus:border-teal-500 focus:ring-teal-500  "
                            placeholder="Search..."
                        />
                    </div>
                    <div className="flex gap-2 ml-2">
                        <button type='button' className="btn btn-soft btn-primary"  onClick={onClickSearch}>
                        <span className="icon-[tabler--search] size-4.5 shrink-0"></span> 
                                Search 
                        </button>
                        <button type='button' className="btn btn-soft btn-secondary"  onClick={onClear}>
                        <span className="icon-[tabler--zoom-cancel] size-4.5 shrink-0"></span> 
                        Clear 
                        </button>
                    </div>
                </div> : <></>}
        </>
    )
}

export default GridFilter;