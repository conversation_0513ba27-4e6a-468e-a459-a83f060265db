import swagger<PERSON>SDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'Healtether.External.Communications',
    version: '1.0.0',
    description: 'Documentation for your API',
  },
  servers: [
    {
      url: 'https://api-chats-tst-clinic.healtether.com',
    },
    {
      url:"http://localhost:3001"
    }
  ],
};

const options = {
    swaggerDefinition,
    apis: ['./routes/*.js'],
  };
  

// Initialize swagger-jsdoc
const swaggerSpec = swaggerJSDoc(options);

export { swaggerUi, swaggerSpec };