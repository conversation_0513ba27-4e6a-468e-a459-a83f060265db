import { jest } from "@jest/globals";

export async function mockClientHelper() {
    await jest.unstable_mockModule("../../helpers/clinic/client.helper.js", async () => ({
        upsertClient: jest.fn(),
        updateSettingClient: jest.fn(),
        overview: jest.fn(),
        getAllClients: jest.fn(),
        getClientById: jest.fn(),
        getClientSetting: jest.fn(),
        deleteClient: jest.fn(),
        updateClient: jest.fn(),
    }));
}