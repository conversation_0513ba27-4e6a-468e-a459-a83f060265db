
import { sendReply, sendTemplate } from "../helper/whatsapp/whatsapp.helper.js";
import { chooseClinicTemplate, chooseLanguageTemplate, chooseUserAction, forgotPasswordOTP,  notExistingTemplate, welcomeTemplate } from "../template/whatsapp/interactive.template.js";
import { getContentFromWebhookMessage } from "../utils/whatsapp.js";
import { addUpdateSessionWithCacheClinic, getSession, setLastWhatsappMessageId, setSessionLanguage, setSessionLive } from "../helper/whatsapp/whatsapp-session.helper.js";
import { searchByMobile } from "../helper/patient/patient.helper.js";
import { getHoursDiff } from "../utils/common.js";
import { saveChatMessage } from "../helper/whatsapp/messagelog.helper.js";
import { getAppointmentSummaryTemplate, getScheduleAppointmentTemplate } from "../template/whatsapp/appointment.template.js";
import { formTextJson } from "../template/whatsapp/text.message.js";

export const listen = async (req, res) => {
  res.sendStatus(200);

  let body = req?.body;
  console.log("listening WhatsappWebhook \n" + JSON.stringify(body, null, 2));
  var clinics = [];
  var receivedMessage = getContentFromWebhookMessage(body);
  var singleClinic = false;
  switch (receivedMessage?.type) {
    case "ScheduleReply":
      {
        var activeSession = await getSession(receivedMessage.mobile);
        console.log(
          "scenario appointment reply " + JSON.stringify(activeSession)
        );
        if (activeSession.lastWhatsappMessageId == receivedMessage.messageId) {
          var scheduleData = receivedMessage.reply;
          var summary = getAppointmentSummaryTemplate(
            receivedMessage.mobile,
            activeSession.language,
            activeSession.currentClinic.clinicName,
            scheduleData.patient.title,
            scheduleData.doctor.title,
            scheduleData.scheduleDate.toDateString(),
            scheduleData.timeslot,
            scheduleData.isOnline
          );
          await sendReply(summary);
        }
      }
      break;
    case "reply":
      {
        var activeSession = await getSession(receivedMessage.mobile);
        console.log("scenario reply " + JSON.stringify(activeSession));
        if (activeSession.lastWhatsappMessageId == receivedMessage.messageId) {
          switch (receivedMessage.replyText) {
            case "bookappointmant_button": {
              //   if (activeSession.language != null) {
              var gttemplate = await getScheduleAppointmentTemplate(
                receivedMessage?.mobile,
                activeSession.language,
                activeSession.currentClinic.clinicName,
                activeSession.currentClinic.id
              );
              var resultTemplate = await sendReply(gttemplate);

              if (resultTemplate.status == 200) {
                await setLastWhatsappMessageId(
                  receivedMessage.mobile,
                  resultTemplate.data
                );
              }
              return;
            }
            case "livechat_button": {
              var chatText = "You can start conversation with clinic: ";
              switch (activeSession?.language) {
                case "ta":
                  chatText = "நீங்கள் உரையாடலைத் தொடங்கலாம் கிளினி: ";
                  break;
                case "hi":
                  chatText = "आप क्लिनिक के साथ बातचीत शुरू कर सकते हैं: ";
                  break;
              }

              await setSessionLive(receivedMessage?.mobile);
              var textMessage = formTextJson(
                receivedMessage?.mobile,
                chatText + activeSession?.currentClinic?.clinicName
              );
              var resultTextMessage = await sendReply(textMessage);
              if (resultTextMessage.status == 200) {
                await setLastWhatsappMessageId(
                  receivedMessage.mobile,
                  resultTextMessage.data
                );
              }
              return;
            }
            case "changelanguage_button": {
              await sendChooseLanguage(
                receivedMessage?.mobile,
                activeSession.language
              );
              return;
            }
            case "tamil_button":
            case "english_button":
            case "hindi_button": {
              var lgCode =
                receivedMessage?.replyText == "tamil_button"
                  ? "ta"
                  : receivedMessage?.replyText == "hindi_button"
                  ? "hi"
                  : "en";

              await setSessionLanguage(receivedMessage?.mobile, lgCode);
              singleClinic = activeSession.cache?.clinic.length == 1;
              if (singleClinic) {
                await sendUserAction(
                  receivedMessage?.mobile,
                  lgCode,
                  receivedMessage?.displayName,
                  activeSession.currentClinic?.clinicName
                );
              } else {
                await sendChooseClinic(
                  receivedMessage?.mobile,
                  receivedMessage?.displayName,
                  activeSession.cache.clinic
                );
              }
              return;
            }
            default:
              return;
          }
        }
      }
      break;
    case "text":
      {
        var activeSession = await getSession(receivedMessage.mobile);
        console.log("scenario 1 " + JSON.stringify(activeSession));
        if (
          activeSession != null &&
          activeSession.isLive &&
          getHoursDiff(activeSession.timeStamp) < 6
        ) {
          // when session with live chat is present scenario 1 save to message log by
          // current clinic id
          await saveChatMessage(
            receivedMessage.message,
            activeSession.currentClinic.id,
            receivedMessage.mobile
          );
        } else if (activeSession != null) {
          //when Session is present
          if (activeSession?.language != null) {
            //Session language is present
            singleClinic = activeSession.cache?.clinic?.length == 1;
            if (
              singleClinic ||
              (activeSession.currentClinic != null &&
                getHoursDiff(activeSession.timeStamp) < 6)
            ) {
              await sendUserAction(
                receivedMessage?.mobile,
                activeSession.language,
                receivedMessage?.displayName,
                activeSession.currentClinic?.clinicName
              );
              return;
            }
            //  else if (activeSession.currentClinic != null && GetHoursDiff(activeSession.timeStamp) < 10) {
            //     var msgPredefine = receivedMessage.message
            //         ?.toLowerCase()
            //             ?.trim();
            //     //    if (receivedMessage.message == "predeined text") {} else if
            //     // (receivedMessage.) {} Session language & currentclinic is present send
            //     // options template
            // }
            else if (
              activeSession.cache != null &&
              activeSession.cache.clinic.length > 1
            ) {
              //Session cache clinic is present send choose clinic
              await sendChooseClinic(
                receivedMessage?.mobile,
                receivedMessage?.displayName,
                activeSession.cache.clinic
              );
              return;
            } else if (
              activeSession.cache?.clinic == null ||
              activeSession.cache?.clinic?.length == 0
            ) {
              //session cache clinic is  not present
              var patientClinic = await addWhatsappSession(
                receivedMessage.mobile
              );

              if (patientClinic != null && patientClinic.length == 1) {
                await sendUserAction(
                  receivedMessage?.mobile,
                  activeSession.language,
                  receivedMessage?.displayName,
                  patientClinic[0]?.clinicName
                );

                return;
              } else if (patientClinic != null && patientClinic.length > 1) {
                await sendChooseClinic(
                  receivedMessage?.mobile,
                  receivedMessage?.displayName,
                  activeSession.cache.clinic
                );
                return;
              }
              // send choose clinic template
            }
          } else {
            // send choose language template
            await sendChooseLanguage(
              receivedMessage?.mobile,
              activeSession?.language
            );
            return;
          }
        } else {
          var patientClinic = await addWhatsappSession(receivedMessage.mobile);
          if (patientClinic != null && patientClinic.length > 0) {
            var welcome = welcomeTemplate(receivedMessage?.mobile);
            await sendReply(welcome);
            // send choose language
            await sendChooseLanguage(
              receivedMessage?.mobile,
              activeSession?.language
            );
            return;
          } else {
            var notExisting = notExistingTemplate(
              receivedMessage?.mobile,
              receivedMessage?.displayName
            );
            await sendReply(notExisting);
            // send not existing customer template
            return;
          }
        }
      }
      break;
    default:
      break;
  }
};



const addWhatsappSession = async (mobile) => {
    var patientClinic = await searchByMobile(mobile);
    var clinics = [];
    if (patientClinic != null && patientClinic.length > 0) {
        for (let index = 0; index < patientClinic.length; index++) {
            const element = patientClinic[index];
            clinics.push(element);
        }
        var currentSession = await addUpdateSessionWithCacheClinic(mobile, clinics);

    }
    return clinics;
}

const sendChooseLanguage = async (mobile, code) => {
    var chooseLanguage = chooseLanguageTemplate(mobile, code);
    var resultChooseLanguage = await sendReply(chooseLanguage);
    if (resultChooseLanguage.status == 200) {
        await setLastWhatsappMessageId(mobile, resultChooseLanguage.data);
    }
}
const sendUserAction = async (mobile, code, displayName, clinicName) => {
    var userAction = chooseUserAction(mobile, code, displayName, clinicName);
    var resultChooseAction = await sendReply(userAction);
    if (resultChooseAction.status == 200) {
        await setLastWhatsappMessageId(mobile, resultChooseAction.data);
    }
}

const sendChooseClinic = async (mobile, displayName, clinic) => {
    var chooseClinic = chooseClinicTemplate(mobile, displayName, clinic);
    var resultChooseClinic = await sendReply(chooseClinic);
    if (resultChooseClinic.status == 200) {
        await setLastWhatsappMessageId(mobile, resultChooseClinic.data);
    }
}



export const verifyHook = (req, res) => {
  const verify_token = process.env.VERIFY_TOKEN;

  // Parse params from the webhook verification request
  let mode = req.query["hub.mode"];
  let token = req.query["hub.verify_token"];
  let challenge = req.query["hub.challenge"];

  // Check if a token and mode were sent
  if (mode && token) {
    // Check the mode and token sent are correct
    if (mode === "subscribe" && token === verify_token) {
      // Respond with 200 OK and challenge token from the request
      console.log("WEBHOOK_VERIFIED");
      res.status(200).send(challenge);
    } else {
      // Responds with '403 Forbidden' if verify tokens do not match
      res.sendStatus(403);
    }
  }
};
