import { jest } from "@jest/globals";
import mongoose from 'mongoose';
import { getAppointment } from "../../../helpers/appointment/appointment.helper.js";
import { Appointment } from "../../../model/clinics.model.js";
import { setup, teardown } from "../../../setup.js";

jest.setTimeout(30000);

describe('getAppointment function', () => {
  let appointmentId;
  let clientId;

  beforeAll(async () => {
    await setup();
    appointmentId = new mongoose.Types.ObjectId();
    clientId = new mongoose.Types.ObjectId();
    
    await Appointment.create({
      _id: appointmentId,
      name: 'first Patient',
      mobile: '**********',
      virtualConsultation: false,
      appointmentDate: new Date(),
      timeSlot: '08:30 pm - 08:50 pm',
      doctorName: 'Venkatesh Raja',
      patientId: new mongoose.Types.ObjectId(),
      clinic: clientId,
      isDeleted: false,
      isCanceled: true,
      paymentStatus: false,
      isFollowUp: false,
    });
  });

  afterAll(async () => {
    await Appointment.deleteMany({});
    await teardown();
  });

  it('should return the correct appointment by ID', async () => {
    const appointment = await getAppointment(appointmentId); 
    
    expect(appointment).toBeDefined(); 
    expect(appointment).toMatchObject({
      name: 'first Patient',
      doctorName: 'Venkatesh Raja',
      mobile: '**********',
      timeSlot: '08:30 pm - 08:50 pm',
      clinic: clientId
    });
  });

  it('should return null if no appointment is found', async () => {
    const invalidId = new mongoose.Types.ObjectId(); 
    const appointment = await getAppointment(invalidId); 

    expect(appointment).toBeNull(); 
  });
});