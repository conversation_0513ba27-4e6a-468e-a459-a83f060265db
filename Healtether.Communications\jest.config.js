import * as dotenv from "dotenv";
export default {
    clearMocks: true,
    collectCoverage: true,
    testRegex: './__tests__/.*\\.spec\\.js$',
    watchPlugins: [
      'jest-watch-typeahead/filename',
      'jest-watch-typeahead/testname',
    ],
   // testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/config/'],
    testEnvironment: 'node',
    transform: {
      //'^.+\\.jsx?$': 'babel-jest', // For JavaScript/JSX
    },
    setupFiles: ["<rootDir>/jest.setup.js"],
    verbose: true,
  };
  