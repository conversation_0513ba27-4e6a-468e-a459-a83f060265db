import { Router } from "express";
import {
  validateGetPaymentOverview,
  validateAddInvoice,
  validateGetInvoiceById,
  validateSendPaymentLink,
  validatePayByCash,
  validateAddPayment,
  validateFormAndSendPaymentLink,
} from "../../validation/payment/payment.validation.js";
import addInvoice,{
  formAndSendPaymentLink,
  getInvoiceById,
  getPaymentOverview,
  handlePhonePeCallback,
  payByCash,
  addInvoiceForMobile
} from "../../controllers/payment/payment.controller.js";


import { authorizationCheck } from "../../middleware/jwt_authorization.js";

const payment = Router();

/**
 * @swagger
 * /payment/getpayments:
 *   get:
 *     tags:
 *       - payment (mobile)
 *       - payment
 *     summary: Get payment overview
 *     description: Retrieves an overview of payments for patients based on the provided filters and pagination.
 *     parameters:
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *           example: "615d7bc134e8b5d88c1c5f11"
 *         description: The ID of the clinic for which the payment overview is being requested.
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           example: 1
 *         description: The page number for pagination (default is 1).
 *       - in: query
 *         name: size
 *         required: false
 *         schema:
 *           type: integer
 *           example: 10
 *         description: The number of records per page (default is 10).
 *       - in: query
 *         name: keyword
 *         required: false
 *         schema:
 *           type: string
 *           example: "Ramesh"
 *         description: A keyword to filter the payment overview by patient name or phone number.
 *       - in: query
 *         name: sortby
 *         required: false
 *         schema:
 *           type: string
 *           example: "lastVisited"
 *         description: The field by which to sort the results (e.g., lastVisited, totalFee).
 *       - in: query
 *         name: direction
 *         required: false
 *         schema:
 *           type: string
 *           example: "asc"
 *         description: The direction of the sort (asc for ascending, desc for descending).
 *     responses:
 *       200:
 *         description: A successful response containing the payment overview data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         example: "Ramesh"
 *                       phoneNo:
 *                         type: string
 *                         example: "0000000000"
 *                       lastVisited:
 *                         type: string
 *                         example: "15-07-23"
 *                       totalFee:
 *                         type: string
 *                         example: "556.00"
 *                       amtReceived:
 *                         type: string
 *                         example: "200.00"
 *                       isPaid:
 *                         type: boolean
 *                         example: true
 *                 totalCount:
 *                   type: integer
 *                   example: 2
 *       500:
 *         description: Unexpected error during the retrieval of payment overview.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Error message"
 */
payment.get(
  "/getpayments",
  authorizationCheck,
  validateGetPaymentOverview,
  async (req, res, next) => {
    try {
      return await getPaymentOverview(req, res);
    }
    catch (e) {
      next(e)
    }
  });

/**
 * @swagger
 * /payment/addinvoiceformobile:
 *   post:
 *     tags:
 *       - payment (mobile)
 *     summary: update invoice for mobile
 *     description: updates an existing invoice for a patient based on the provided data.
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           example: "615d7bc134e8b5d88c1c5f11"
 *         description: The ID of the invoice to be updated.
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *           example: "615d7bc134e8b5d88c1c5f11"
 *         description: The ID of the clinic for which the  updated.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   treatments:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         treatment:
 *                           type: string
 *                           example: "Consultation Charges"
 *                         quantity:
 *                           type: string
 *                           example: "1"
 *                         amount:
 *                           type: string
 *                           example: "96"
 *                         discRate:
 *                           type: string
 *                           example: "0"
 *                   discount:
 *                     type: string
 *                     example: ""
 *     responses:
 *       200:
 *         description: Successfully added or updated the invoice.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   description: The details of the updated or created invoice.
 *       500:
 *         description: Unexpected error during the invoice creation or update.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Error message"
 */

  
payment.post("/addinvoiceformobile", authorizationCheck, validateAddInvoice,
    async (req, res, next) => {
      try {
        return await addInvoiceForMobile(req, res);
      }
      catch (e) {
        next(e)
      }
    });
  
payment.post("/addinvoice", authorizationCheck, validateAddInvoice,
  async (req, res, next) => {
    try {
      return await addInvoice(req, res);
    }
    catch (e) {
      next(e)
    }
  });

/**
 * @swagger
 * /payment/getinvoicebyid:
 *   get:
 *     tags:
 *       - payment (mobile)
 *       - payment
 *     summary: Retrieve an invoice by ID
 *     description: Fetch an invoice by its ID and return patient and appointment details.
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         description: The ID of the invoice to retrieve.
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successfully retrieved the invoice.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 patient:
 *                   type: object
 *                   properties:
 *                     firstName:
 *                       type: string
 *                     lastName:
 *                       type: string
 *                     patientId:
 *                       type: string
 *                 appointment:
 *                   type: object
 *                   properties:
 *                     appointmentDate:
 *                       type: string
 *                       format: date-time
 *                 treatments:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       quantity:
 *                         type: number
 *                       amount:
 *                         type: number
 *                       discRate:
 *                         type: number
 *                 totalAmount:
 *                   type: number
 *                 discount:
 *                   type: number
 *                 totalTax:
 *                   type: number
 *                 totalCost:
 *                   type: number
 *       404:
 *         description: Invoice not found.
 *       500:
 *         description: Server error while retrieving the invoice.
 */
payment.get(
  "/getinvoicebyid",
  authorizationCheck,
  validateGetInvoiceById,
  async (req, res, next) => {
    try {
      return await getInvoiceById(req, res);
    }
    catch (e) {
      next(e)
    }
  });

/**
 * @swagger
 * /payment/setcashpayment:
 *   post:
 *     tags:
 *       - payment (mobile)
 *       - payment
 *     summary: Pay invoice by cash
 *     description: Process a cash payment for an invoice and return the updated invoice details.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               clientId:
 *                 type: string
 *                 required: true
 *                 description: The ID of the client making the payment.
 *                 example: "662ca0a41a2431e16c41ebaa"
 *               invoiceId:
 *                 type: string
 *                 description: The ID of the invoice being paid.
 *                 example: "64f112a3e5a8b8d30a1c4567"
 *               amount:
 *                 type: number
 *                 description: The amount being paid. If null, the remaining amount of the invoice will be calculated.
 *                 example: 500
 *               paymentMode:
 *                 type: string
 *                 description: The mode of payment used (e.g., cash, card).
 *                 example: "cash"
 *     responses:
 *       200:
 *         description: Payment processed successfully, and the updated invoice is returned.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 invoiceId:
 *                   type: string
 *                   description: The ID of the invoice.
 *                 totalAmount:
 *                   type: number
 *                   description: The total amount of the invoice.
 *                 paidAmount:
 *                   type: number
 *                   description: The total amount paid so far.
 *                 balanceAmount:
 *                   type: number
 *                   description: The remaining amount to be paid.
 *                 paymentStatus:
 *                   type: string
 *                   description: The status of the payment after processing (e.g., Paid, Partial, Pending).
 *                 modified:
 *                   type: object
 *                   properties:
 *                     on:
 *                       type: string
 *                       format: date-time
 *                       description: The date and time when the invoice was last modified.
 *                     by:
 *                       type: string
 *                       description: The user who modified the invoice.
 *       400:
 *         description: Invalid input data (e.g., amount exceeds invoice total).
 *       500:
 *         description: Server error while processing the payment.
 */
payment.post("/setcashpayment",authorizationCheck,validatePayByCash,
  async (req, res, next) => {
    try {
      return await payByCash(req, res);
    }
    catch (e) {
      next(e)
    }
  });


payment.post("/sendpaymentlink", authorizationCheck,validateFormAndSendPaymentLink,
  async (req, res, next) => {
    try {
      return await formAndSendPaymentLink(req, res);
    }
    catch (e) {
      next(e)
    }
  }
);

// dont swagger
payment.post("/phoneperesponse",
  async (req, res, next) => {
    try {
      return await handlePhonePeCallback(req, res);
    } catch (e) {
      next(e)
    }
  }
)

export default payment;
