import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateAddRecentUsers = async (req, res, next) => {
  await checkSchema({
    "mobile": {
      in: ["body"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
    },
    "name": {
        in: ["body"],
        isString: {
          errorMessage: "name must be a string",
        },
        notEmpty: {
          errorMessage: "name is required",
        },
      },
      "email": {
        in: ["body"],
        optional:true,
        isString: {
          errorMessage: "email must be a string",
        },
      },
    "clientId": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "clinicId must be a valid ObjectId",
      },
    },
    "userId": {
        in: ["body"],
        custom: {
          options: (value) => mongoose.Types.ObjectId.isValid(value),
          errorMessage: "userId must be a valid ObjectId",
        },
      },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
