import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateSearchAbha = async (req, res, next) => {
  await checkSchema({
    "mobile": {
      trim:true,
      escape:true,
      in: ["body"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateIndexMobileOtp = async (req, res, next) => {
    await checkSchema({
      "index": {
        trim:true,
        escape:true,
        in: ["body"],
        errorMessage: "Index is required",
      },
      "txnId": {
        trim:true,
        escape:true,
        isString:true,
        in: ["body"],
        errorMessage: "txnId is required",
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };
  
  export const validateVerifyMobileOtp = async (req, res, next) => {
    await checkSchema({
      "otp": {
        trim:true,
        escape:true,
        in: ["body"],
        errorMessage: "otp is required.",
      },
      "txnId": {
        trim:true,
        escape:true,
        isString:true,
        in: ["body"],
        errorMessage: "txnId is required.",
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };
  
  export const validateUserProfile = async (req, res, next) => {
    await checkSchema({
      "x-token": {
        trim: true,
        escape: true,
        in: ["headers"],
        errorMessage: "x-token is required in the headers.",
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const validateEnrollAbha = async (req, res, next) => {
    await checkSchema({
      "aadhaarNumber": {
        trim:true,
        escape:true,
        in: ["body"],
        errorMessage: "aadhaarNumber is required.",
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const validateEnrollByAadhaarOtp = async (req, res, next) => {
    await checkSchema({
      "otp": {
        trim:true,
        escape:true,
        in: ["body"],
        errorMessage: "otp is required.",
      },
      "mobileNumber": {
        trim:true,
        escape:true,
        in: ["body"],
        errorMessage: "mobileNumber is required.",
      },
      "txnId": {
        trim:true,
        escape:true,
        isString:true,
        in: ["body"],
        errorMessage: "txnId is required.",
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };
  
  
  export const validateEnrollByMobile = async (req, res, next) => {
    await checkSchema({
      "mobile": {
        trim:true,
        escape:true,
        in: ["body"],
        errorMessage: "mobile is required.",
      },
      "txnId": {
        trim:true,
        escape:true,
        isString:true,
        in: ["body"],
        errorMessage: "txnId is  required.",
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };
  export const validateSuggestion = async (req, res, next) => {
    await checkSchema({
      "transaction_id": {
        trim: true,
        escape: true,
        in: ["headers"],
        errorMessage: "transaction_id is required in the headers.",
      },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const validateEnrollByAbhaAddress = async (req, res, next) => {
    await checkSchema({
        "txnId": {
            trim:true,
            escape:true,
            isString:true,
            in: ["body"],
            errorMessage: "txnId is  required.",
          },
          "abhaAddress": {
            trim:true,
            escape:true,
            isString:true,
            in: ["body"],
            errorMessage: "abhaAddress is  required.",
          },
          "preferred": {
            trim:true,
            escape:true,
            in: ["body"],
            errorMessage: "preferred is  required.",
          },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const validateAbhaNumberAddressOtp = async (req, res, next) => {
    await checkSchema({
        "type": {
            trim:true,
            escape:true,
            isString:true,
            in: ["body"],
            errorMessage: "txnId is  required.",
          },
          "abhaNumberAddress": {
            trim:true,
            escape:true,
            isString:true,
            in: ["body"],
            errorMessage: "abhaNumber or Abha Address is  required.",
          }
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const validateVerifyAbhaNumberAddressOtp = async (req, res, next) => {
    await checkSchema({
        "type": {
            trim:true,
            escape:true,
            isString:true,
            in: ["body"],
            errorMessage: "txnId is  required.",
          },
          "txnId": {
            trim:true,
            escape:true,
            isString:true,
            in: ["body"],
            errorMessage: "txnId is  required.",
          },
          "otp": {
            trim:true,
            escape:true,
            in: ["body"],
            errorMessage: "otp is required.",
          },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };