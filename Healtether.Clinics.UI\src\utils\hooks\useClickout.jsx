import { useEffect, useRef, useState } from "react";

export const useClickOut = {
  auto: () => {
    const [isOpen, setIsOpen] = useState(false);
    const targetRef = useRef();

    useEffect(() => {
      const close = (e) => {
        if (targetRef.current && !targetRef.current.contains(e.target)) {
          setIsOpen(false);
        }
      };
      document.addEventListener("mousedown", close);
      return () => {
        document.removeEventListener("mousedown", close);
      };
    }, [targetRef]);

    return {
      isOpen,
      setIsOpen,
      targetRef,
    };
  },
  manual: ({ isOpen, setIsOpen }) => {
    const targetRef = useRef();
    useEffect(() => {
      const close = (e) => {
        if (targetRef.current && !targetRef.current.contains(e.target)) {
          setIsOpen(false);
        }
      };
      document.addEventListener("mousedown", close);
      return () => {
        document.removeEventListener("mousedown", close);
      };
    }, [targetRef]);

    return {
      isOpen,
      setIsOpen,
      targetRef,
    };
  },
};
