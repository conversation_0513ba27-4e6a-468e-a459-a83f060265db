import ContainerHeading from "components/detail-page/ContainerHeading"
import Required<PERSON>abel from 'components/detail-page/RequiredLabel';
import DefaultTextboxClass from 'utils/Classes';
function BankDetails({bankName,account,ifsc,accountName}) {
    return (
        <div className="flex flex-col space-y-2 ">
            <ContainerHeading heading={"Bank details"}/>
            <div className="flex flex-col gap-5">
                <div className='w-full'>
                    <label className="label-text" htmlFor="bankName">Bank name
                    </label>
                    <input
                        type="text"
                        id="bankName"
                        name="bankName"
                        placeholder="Bank name"
                        autoComplete="off"
                        defaultValue={bankName}
                        className={DefaultTextboxClass + " w-full text-md"}/>
                </div>
                <div className='w-full'>
                    <label className="label-text" htmlFor="account">Account no.
                    </label>
                    <input
                        type="text"
                        id="account"
                        name="account"
                        defaultValue={account}
                        placeholder="Account no."
                        autoComplete="off"
                        className={DefaultTextboxClass + " w-full text-md"}/>
                </div>
                <div className='w-full'>
                    <label className="label-text" htmlFor="ifsc">IFSC.
                    </label>
                    <input
                        type="text"
                        id="ifsc"
                        name="ifsc"
                        placeholder="IFSC"
                        autoComplete="off"
                        defaultValue={ifsc}
                        className={DefaultTextboxClass + " w-full text-md"}/>
                </div>
                <div className='w-full'>
                    <label className="label-text" htmlFor="accountName">Account holder's name
                    </label>
                    <input
                        type="text"
                        id="accountName"
                        name="accountName"
                        placeholder="Account holder's name"
                        autoComplete="off"
                        defaultValue={accountName}
                        className={DefaultTextboxClass + " w-full text-md"}/>
                </div>
            </div>
        </div>
    )
}

export default BankDetails