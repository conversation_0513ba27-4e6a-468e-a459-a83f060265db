// ConsultationDetails.jsx
import { useNavigate } from "react-router-dom";
import { Icons } from "../../../../../components/detail-page/icons";
import InfiniteScroll from "react-infinite-scroll-component";
import { useEffect, useState } from "react";
import Spinner from "../../../../../components/loader/Spinner";
import Modal from "../../../../../components/detail-page/modal";
import {
  BookedConsultationOverview,
  CancelConsultation,
} from "../../../../../services/booked-consultation/bookedConsultation";

export function AppointmentDetails({ status, setTab, filterDate }) {
  const [pg, setPg] = useState(1);
  const [data, setData] = useState([]);
  const [queueHasMore, setQueueHasMore] = useState(true);
  let loadingDetails = false;

  const fetchBookedConsultationData = async (reset = false) => {
    try {
      const currentPage = reset ? 1 : pg + 1;
      if (reset) {
        setPg(1);
        setData([]);
      }

      const res = await BookedConsultationOverview(
        currentPage,
        6,
        `&status=${status}`,
        filterDate
      );

      if (res.status === 200) {
        const fetchedData = res.data?.data || [];
        setData((prev) => {
          const mergedData = reset ? fetchedData : [...prev, ...fetchedData];
          const uniqueData = Array.from(
            new Map(mergedData.map((item) => [item._id, item])).values()
          );
          return uniqueData;
        });
        setPg(currentPage);
        setQueueHasMore(fetchedData.length === 6);
      }
    } catch (e) {
      console.error(`API error: ${e.message}`);
      setQueueHasMore(false);
    }
  };
  useEffect(() => {
    if (!loadingDetails) {  
      loadingDetails = true;
    fetchBookedConsultationData(true);
    }
  }, [status, filterDate]);

  return (
    <InfiniteScroll
      dataLength={data?.length}
      next={() => fetchBookedConsultationData(false)}
      hasMore={queueHasMore}
      loader={
        <p className="text-center m-5">
          <Spinner show={true} /> &nbsp;loading ...
        </p>
      }
      endMessage={<p className="text-center m-5"> no more appointments ...!</p>}
      scrollableTarget="bkdetails"
    >
      {data.map((info) => (
        <BookedDetails
          key={info._id}
          info={info}
          status={status}
          fetchBookedConsultationData={fetchBookedConsultationData}
          setTab={setTab}
        />
      ))}
    </InfiniteScroll>
  );
}

export function BookedDetails({
  status,
  info,
  fetchBookedConsultationData,
  setTab,
}) {
  const [appointment, setAppointment] = useState(info);
  const [cancelPopUp, setCancelPopUp] = useState(false);

  useEffect(() => {
    setAppointment(info);
  }, [info]);

  const toggleCancelPopup = () => {
    setCancelPopUp(!cancelPopUp);
  };

  const cancelConsultation = async (id) => {
    const result = await CancelConsultation(id);
    if (result) {
      toggleCancelPopup();
      fetchBookedConsultationData(true);
    }
  };

  return (
    <div className="flex flex-col bg-white w-full rounded-xl px-2 item-center border shadow-sm h-fit overflow-x-auto overflow-y-hidden custom-scrollbar mb-2">
      <section className="w-full flex flex-row items-center justify-center p-2 text-base font-primary">
        <div className="capitalize whitespace-nowrap flex flex-row font-medium">
          {appointment?.name} &nbsp;
          <div className="text-color_muted text-md">
            {appointment?.age} Years, {appointment?.gender}
          </div>
        </div>
      </section>

      <Modal
        isOpen={cancelPopUp}
        setIsOpen={toggleCancelPopup}
        classname="min-w-[350px]! "
        plain={false}
        isCard={true}
      >
        <div className="text-center p-4">
          <h2 className="text-lg font-bold">
            Are you sure you want to cancel this consultation?
          </h2>
          <button
            onClick={() => cancelConsultation(appointment._id)}
            className="mt-4 bg-Primary text-white px-4 py-2 rounded-sm"
          >
            Cancel
          </button>
        </div>
      </Modal>

      <div className="flex flex-row w-full gap-1 items-center h-full justify-evenly text-color_dark">
        <section className="flex flex-col text-color_muted">
          <div>#Token {appointment.tokenNumber&&appointment.tokenNumber}</div>
          <div className="text-wrap text-xs">{appointment?.timeSlot}</div>
          {appointment?.virtualConsultation && (
            <span
              className="text-Secondary text-sm cursor-pointer"
              onClick={(e) => {
                e.preventDefault();
                window.open(
                  appointment.googleLink.link,
                  "_blank",
                  "rel=noopener noreferrer"
                );
                e.stopPropagation();
              }}
            >
              Join call
            </span>
          )}
        </section>

        <section className="font-normal flex flex-col gap-1">
          <div className="flex items-center gap-2 truncate">
            <Icons.profile className="h-5 w-5" />
            <div className="truncate">{appointment?.clinicPatientId}</div>
          </div>
          <div className="flex items-center gap-2 truncate">
            <Icons.address className="h-5 w-5" /> {appointment.abhaAddress}
          </div>
        </section>

        <section className="font-normal flex flex-col mx-2">
          <div className="flex items-center gap-2">
            <Icons.phone className="h-5 w-5" />
            {appointment.mobile}
          </div>
          <div className="flex items-center gap-2 mt-2">
            <Icons.abha_num className="h-5 w-5" />
            <div className="mt-0.5">{appointment.abhaNumber}</div>
          </div>
        </section>
      </div>

      <div className="flex flex-row w-full justify-evenly p-2">
        <aside className="flex items-center gap-2 text-Primary cursor-pointer justify-start">
          <Icons.doctor_profile className="h-5 w-5" />
          Dr.{appointment?.doctorName}
        </aside>
        {BookedDetailActions(
          appointment,
          true,
          toggleCancelPopup,
          "",
          status,
          setTab
        ).map(
          ({ label, Icon, handleClick, className }, idx) =>
            idx < 6 && (
              <div
                key={idx}
                onClick={handleClick}
                className="text-center text-xs flex flex-col items-center gap-1 mx-2 font-primary fond-medium"
              >
                <div
                  className={`p-2 rounded-md bg-backcolor_primary cursor-pointer flex items-center justify-center mb-1 ${className}`}
                >
                  <Icon color={className} />
                </div>
                {label}
              </div>
            )
        )}
      </div>
    </div>
  );
}

export const BookedDetailActions = (
  appointment,
  isAdminOrDoctor,
  cancelHandler,
  noShowHandler,
  status,
  setTab
) => {
  const navigate = useNavigate();

  return [
    {
      label: "Check in",
      Icon: Icons.check_in,
      className: status === "Cancelled" && "#E5E5E5",
      handleClick: () => {
        navigate("/appointmentoverview", {
          state: { 
            appointmentDetails: appointment,
            defaultTab: 0  // Also pass the tab state in navigation
          },
        });
      },
    },
    {
      label: "Cancel",
      Icon: Icons.cancel,
      className: status === "Cancelled" && "#E5E5E5",
      handleClick: status === "Cancelled" ? null : cancelHandler,
    },
    {
      label: "Reschedule",
      Icon: Icons.reschedule,
      handleClick: () => {
        navigate("/appointmentoverview", {
          state: { appointmentDetails: appointment , defaultTab: 0  // Also pass the tab state in navigation},
          }
        });
      },
    },
    {
      label: "Chat",
      Icon: Icons.whatsapp,
      handleClick: () => navigate("/chats"),
    },
  ];
};
