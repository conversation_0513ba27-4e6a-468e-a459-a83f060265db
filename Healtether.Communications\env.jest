RECIPIENT_PHONE_NUMBER='6362106937'
FACEBOOK_ACCESS_TOKEN='EAARnEo0R1psBOyORefmLyulXHmzuxQ9aW9ufzCpIKVsFqJoQRqEzKbhmTiKoEt7xEhWDJxcsYsW5xYfUsM417GVZAzD4bSjQpTBL2W0pBGa4ohRd8ZBTaCGGKorDm92rbqIW77cPxP6k0v9Iwp4aLviNdrZASjz53exDw0SbDMDrIaAU7Azl0uJZBXgWJHreYAZDZD'
FACEBOOK_MSG_URL='https://graph.facebook.com/v17.0/463758466828252/messages'
AZURE_COMMUNICATION_URL="endpoint=https://healtether-tst-communication.india.communication.azure.com/;accesskey=CYBn0d7Rs2weIUxFuiaoUw7KIftIOgYMNQW4Lgmz51ANwJF6u0p9JQQJ99ALACULyCp7vaDGAAAAAZCSBY41"
MONGODB_PORTAL_URI='mongodb+srv://developer:<EMAIL>/tst-healtether?retryWrites=true&w=majority'
MONGODB_WHATSAPP_URI='mongodb+srv://developer:<EMAIL>/whatsapp_log?retryWrites=true&w=majority'
PORT=3001
ENV='jest'
APPLICATIONINSIGHTS_CONNECTION_STRING ="InstrumentationKey=7a5ec3e4-1e57-4c1d-9543-6174d7e01acc;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=1464e196-0a95-486e-bb70-485a2a31b700"
VERIFY_TOKEN='Happy'
WHATSAPP_TEMPLATE_INTRO='intro'
WHATSAPP_TEMPLATE_CHOOSE_LANGUAGE='heltether_choose_language_2024'
WHATSAPP_TEMPLATE_NOT_CUSTOMER='not_existing_customer'
WHATSAPP_TEMPLATE_USER_ACTION='healtether_welcome_choose_action'
WHATSAPP_FORGOT_PASSWORD_OTP='healtether_forgotpassword'
WHATSAPP_TEXT_MESSAGE='healtether_text_message'
WHATSAPP_TEMPLATE_PAYMENT_LINK='healtether_send_payment_link'
WHATSAPP_TEMPLATE_APPOINTMENT_SCHEDULE='healtether_schedule_appointment_summary'
WHATSAPP_SCHEDULE_APPOINTMENT_FLOWID='481933518260298'
WHATSAPP_CHOOSE_CLINIC_FLOWID='570318118955054'
CORS_URL='http://localhost:5173'
ABHA_BASE_URL="https://abhasbx.abdm.gov.in/abha/api/v3"
ABHA_M2_BASE_URL="https://dev.abdm.gov.in"
CLIENT_ID='SBX_003515'
CM_ID='sbx'
CLIENT_SECRET="85bb52e3-1a49-4227-b630-8e93dcd9e8bd"
GRANT_TYPE='client_credentials'
AUTH_TOKEN_URL="https://dev.abdm.gov.in/gateway/v0.5/sessions"
CALLBACK_URL="http://ec2-13-235-59-143.ap-south-1.compute.amazonaws.com"
ABDM_PAYLOAD_HEADER="https://dev.ndhm.gov.in/auth/realms/central-registry"
CLINIC_SERVER_URL='http://ec2-13-235-59-143.ap-south-1.compute.amazonaws.com:2222'