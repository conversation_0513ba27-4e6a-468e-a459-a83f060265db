{"name": "healtether-clinics-api", "type": "module", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon app.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "clear_jest": "jest --clear<PERSON>ache"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/identity": "^4.10.0", "@azure/storage-blob": "^12.27.0", "@date-fns/utc": "^2.1.0", "applicationinsights": "^2.9.6", "axios": "^1.7.7", "bcrypt": "^5.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-session": "^1.17.3", "express-validator": "^7.2.1", "firebase-admin": "^12.0.0", "googleapis": "^140.0.1", "healtether.models": "^1.7.9", "jest-watch-typeahead": "^2.2.2", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongodb": "^6.9.0", "mongoose": "^7.8.6", "mongoose-paginate-v2": "^1.8.2", "multer": "^1.4.5-lts.1", "node-cron": "^4.0.5", "node-fetch": "^3.3.2", "nodemon": "^3.1.9", "supertest": "^7.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^10.0.0", "zlib": "^1.0.5"}, "description": "", "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@jest/globals": "^29.7.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-watch-typeahead": "^2.2.2"}, "collectCoverage": false}