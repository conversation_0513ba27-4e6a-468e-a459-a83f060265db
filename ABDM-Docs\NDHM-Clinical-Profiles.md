# NDHM FHIR R4 Clinical Profiles - Detailed Implementation Guide

## Table of Contents
1. [Clinical Artifacts Overview](#clinical-artifacts-overview)
2. [Core Resource Profiles](#core-resource-profiles)
3. [Observation Profiles (Indian Context)](#observation-profiles-indian-context)
4. [Diagnostic Profiles](#diagnostic-profiles)
5. [Medication Profiles](#medication-profiles)
6. [Implementation Patterns](#implementation-patterns)

## Clinical Artifacts Overview

All clinical artifacts in NDHM FHIR R4 are based on the **Composition** resource, providing a consistent structure for clinical documents.

### DiagnosticReportRecord
**Purpose**: Represents diagnostic reports including Radiology and Laboratory reports

**Key Components**:
- References to DiagnosticReport resources
- Supporting observations and specimens
- Imaging studies (DICOM integration)
- Practitioner and organization information

**Use Cases**:
- Lab test results sharing
- Radiology report distribution
- Pathology findings
- Cardiology reports

### DischargeSummaryRecord
**Purpose**: Clinical document for hospital discharge summaries

**Key Components**:
- Admission and discharge details
- Procedures performed during stay
- Medications prescribed
- Follow-up instructions
- Condition at discharge

**Use Cases**:
- Hospital to primary care handoff
- Insurance claim documentation
- Continuity of care

### OPConsultRecord
**Purpose**: Outpatient visit consultation documentation

**Key Components**:
- Chief complaint and history
- Physical examination findings
- Assessment and plan
- Medications prescribed
- Follow-up recommendations

**Use Cases**:
- Doctor visit documentation
- Specialist consultation notes
- Telemedicine consultations

### PrescriptionRecord
**Purpose**: Medication advice compliant with PCI guidelines

**Key Components**:
- Medication requests
- Dosage instructions
- Duration of treatment
- Prescriber information
- Pharmacy dispensing instructions

**Use Cases**:
- Electronic prescriptions
- Medication history tracking
- Drug interaction checking

### ImmunizationRecord
**Purpose**: Vaccination records and certificates

**Key Components**:
- Vaccine administered
- Date and location
- Batch information
- Next dose recommendations
- Adverse reactions

**Use Cases**:
- Vaccination certificates
- Immunization tracking
- Travel documentation
- WHO DDCC compliance

### WellnessRecord
**Purpose**: Regular wellness information from PHR applications

**Key Components**:
- Vital signs monitoring
- Physical activity tracking
- Lifestyle assessments
- Women's health data
- General wellness metrics

**Use Cases**:
- Personal health tracking
- Preventive care monitoring
- Chronic disease management

### HealthDocumentRecord
**Purpose**: Unstructured historical health records

**Key Components**:
- Scanned documents
- Patient-uploaded files
- Historical medical records
- Insurance documents

**Use Cases**:
- Health Locker integration
- Legacy document storage
- Patient-contributed data

## Core Resource Profiles

### Patient Profile
**Mandatory Elements**:
- Identifier (ABHA ID integration)
- Name (supports Indian naming conventions)
- Gender
- Birth date

**Indian Context Features**:
- ABHA address support
- Multiple identifier types
- Multilingual name support
- Address format for Indian locations

### Practitioner Profile
**Mandatory Elements**:
- Identifier (MCI registration)
- Name
- Qualification details

**Indian Context Features**:
- Medical Council registration numbers
- Specialization codes
- Practice location information

### Organization Profile
**Mandatory Elements**:
- Identifier (facility registration)
- Name
- Type (hospital, clinic, pharmacy)

**Indian Context Features**:
- ROHINI ID support
- Facility type classifications
- Accreditation information

## Observation Profiles (Indian Context)

### ObservationVitalSigns
**Supported Measurements**:
- Blood pressure (systolic/diastolic)
- Heart rate
- Respiratory rate
- Body temperature
- Oxygen saturation

**LOINC Codes**: Standard vital signs codes
**Units**: Metric system (mmHg, bpm, °C, %)

### ObservationBodyMeasurement
**Supported Measurements**:
- Height and weight
- Body Mass Index (BMI)
- Waist circumference
- Mid-upper arm circumference
- Neck measurement

**Use Cases**:
- Growth monitoring
- Nutritional assessment
- Obesity management

### ObservationWomenHealth
**Supported Measurements**:
- Menstrual cycle tracking
- Age of menarche/menopause
- Last menstrual period
- Ovulation date
- Number of periods per year

**LOINC Codes**: Women's health specific codes
**Cultural Context**: Adapted for Indian healthcare practices

### ObservationLifestyle
**Supported Assessments**:
- Tobacco smoking status
- Tobacco chewing status
- Alcohol drinking status
- Diet type preferences

**SNOMED CT Codes**: Lifestyle finding codes
**Indian Context**: Includes tobacco chewing (common in India)

### ObservationPhysicalActivity
**Supported Metrics**:
- Step count
- Sleep duration
- Calories burned
- Activity level assessment

**Integration**: PHR app data, wearable devices
**Units**: Standard activity measurement units

### ObservationGeneralAssessment
**Supported Assessments**:
- Mental status
- General wellbeing
- Blood glucose levels
- ECG findings
- Body fat mass
- Metabolic rate

**Use Cases**:
- Comprehensive health assessments
- Chronic disease monitoring
- Preventive care screening

## Diagnostic Profiles

### DiagnosticReportLab
**Purpose**: Laboratory test results

**Key Components**:
- Test panels and individual tests
- Reference ranges
- Abnormal flags
- Specimen information
- Performing laboratory

**Supported Tests**:
- Complete Blood Count (CBC)
- Basic Metabolic Panel
- Lipid Panel
- Liver Function Tests
- Urinalysis

### DiagnosticReportImaging
**Purpose**: Radiology and imaging reports

**Key Components**:
- Imaging study references
- Radiologist interpretation
- Findings and impressions
- Recommendations

**Supported Modalities**:
- X-Ray
- CT Scan
- MRI
- Ultrasound
- Mammography

### ImagingStudy
**Purpose**: DICOM imaging study metadata

**Key Components**:
- Study identifiers
- Series information
- Instance details
- Modality information

**Integration**: PACS systems, DICOM viewers

### Specimen
**Purpose**: Sample collection information

**Key Components**:
- Specimen type
- Collection method
- Collection time
- Processing notes

**SNOMED CT Codes**: Specimen type classifications

## Medication Profiles

### Medication
**Purpose**: Drug information storage

**Key Components**:
- Drug identification
- Strength and form
- Manufacturer information
- Batch details

**Code Systems**:
- SNOMED CT International
- Common Drug Codes for India
- National Extension codes

### MedicationRequest
**Purpose**: Prescription orders

**Key Components**:
- Medication reference
- Dosage instructions
- Quantity and duration
- Prescriber information
- Substitution permissions

**PCI Compliance**: Follows Pharmacy Council of India guidelines

### MedicationStatement
**Purpose**: Medication history tracking

**Key Components**:
- Current medications
- Past medications
- Adherence information
- Effectiveness notes

**Use Cases**:
- Medication reconciliation
- Drug interaction checking
- Treatment history

## Implementation Patterns

### DocumentBundle Pattern
```json
{
  "resourceType": "Bundle",
  "type": "document",
  "entry": [
    {
      "resource": {
        "resourceType": "Composition",
        "section": [
          {
            "title": "Clinical Section",
            "entry": [
              {"reference": "Observation/vital-signs"},
              {"reference": "Condition/diagnosis"}
            ]
          }
        ]
      }
    }
  ]
}
```

### Reference Pattern
- **Internal References**: Within bundle using relative references
- **External References**: Across systems using absolute URLs
- **Contained Resources**: For small, context-specific resources

### Identifier Pattern
- **System URLs**: Use official NDHM identifier systems
- **Value Format**: Follow prescribed formats (ABHA, MCI, etc.)
- **Multiple Identifiers**: Support various ID types per resource

### Coding Pattern
- **Primary Coding**: Use preferred code system (SNOMED CT, LOINC)
- **Additional Codings**: Include local/regional codes
- **Text Fallback**: Always provide human-readable text

---

**Related Documents:**
- [NDHM FHIR R4 Overview](./NDHM-FHIR-R4-Overview.md)
- [Billing and Invoice Implementation](./NDHM-Billing-Implementation.md)
- [Terminology and Code Systems](./NDHM-Terminology.md)
