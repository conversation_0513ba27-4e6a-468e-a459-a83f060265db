import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { reschedule } from '../../../helpers/appointment/appointment.helper.js';
import { Appointment } from '../../../model/clinics.model.js';
import { setup, teardown } from '../../../setup.js';

jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe('reschedule', () => {
  let appointmentId, patientId;
  let user;
  let data;

  beforeEach(async () => {
    jest.clearAllMocks();
    await Appointment.deleteMany({});
    patientId = new mongoose.Types.ObjectId();
    appointmentId = new mongoose.Types.ObjectId();
    user = { id: 'user123', name: 'Test User' };
    data = {
      appointmentDate: new Date('2024-10-01T10:00:00Z'),
      timeSlot: '10:00 AM - 11:00 AM',
    };
  });

  it('should return "Already in Started" if the appointment has started', async () => {
    // Create appointment with started: true
    await Appointment.create({
      _id: appointmentId,
      name: '<PERSON> Doe',
      mobile: '**********',
      gender: 'Male',
      age: 30,
      doctorId: new mongoose.Types.ObjectId(),
      started: { yes: true },
      patientId: patientId,
      appointmentDate: new Date('2024-09-30T10:00:00Z'),
      timeSlot: '9:00 AM - 10:00 AM',
      isDeleted: false,
      isCanceled: false,
      paymentStatus: false,
    });

    const result = await reschedule(data, appointmentId, user);
    expect(result).toBe('Already in Started');
  });

  it('should reschedule the appointment and save changes if the appointment has not started', async () => {
    // Create appointment with started: false
    await Appointment.create({
      _id: appointmentId,
      name: 'John Doe',
      mobile: '**********',
      gender: 'Male',
      age: 30,
      doctorId: new mongoose.Types.ObjectId(),
      started: { yes: false },
      patientId: patientId,
      appointmentDate: new Date('2024-09-30T10:00:00Z'),
      timeSlot: '9:00 AM - 10:00 AM',
      isDeleted: false,
      isCanceled: false,
      paymentStatus: false,
    });

    const result = await reschedule(data, appointmentId, user);
    
    // Verify the appointment was updated correctly
    expect(result._id).toStrictEqual(appointmentId);
    expect(result.name).toBe("John Doe");
    expect(result.mobile).toBe("**********");
    expect(result.timeSlot).toBe("10:00 AM - 11:00 AM");
    // Or: Compare the ISO strings (if you need exact timezone matching)
    // expect(result.appointmentDate.toISOString()).toBe("2024-10-01T10:00:00.000Z");
    
    expect(result.gender).toBe("Male");
    expect(result.age).toBe(30);
    
    // Verify rescheduled fields were set
    expect(result.rescheduled).toBeDefined();
    expect(result.rescheduled.previousTimeSlot).toBe("9:00 AM - 10:00 AM");
    expect(result.rescheduled.previousDate.getTime()).toEqual(new Date("2024-09-30T10:00:00.000Z").getTime());
});

  it('should throw an error if the appointment is not found', async () => {
    await expect(reschedule(data, new mongoose.Types.ObjectId(), user))
      .rejects.toThrow('appointment not found');
  });
});