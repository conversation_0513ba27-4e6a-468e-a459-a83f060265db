import mongoose from "mongoose";
import portalDb from "../../config/clinics.collections.config.js";

export const searchByMobile = async(mobile) => {
    var code = mobile
        ?.substring(0, 2);
    var clinics = [];
    if (code == "91") {
        mobile = mobile.substring(2, mobile.length);
    }
    var PatientModel = portalDb.model("Patient");
    if (mobile != null) {
        const patientCollection = await PatientModel
            .find({mobile: mobile, deleted: false})
            .populate({
                path: "clinic",
                select: {
                    _id: 1,
                    clinicName: 1,
                    isActive: 1
                }
            })
            .select({clinic: 1, mobile: 1})
            .exec();

        if (patientCollection != null && patientCollection.length > 0) {
            for (let index = 0; index < patientCollection.length; index++) {
                clinics.push({id: patientCollection[index].clinic._id, clinicName: patientCollection[index].clinic.clinicName});
            }
            // Create a set to store unique clinic names
            let uniqueClinics = new Set();

            // Create a new array to store the filtered data
            let filteredclinics = [];

            // Iterate over each item in the original array
            for (let item of clinics) {
                // Get the clinic name
                let clinicName = item["clinic"];
                // If the clinic name is not already in the set, add it to the set and the
                // filtered list
                if (!uniqueClinics.has(clinicName)) {
                    uniqueClinics.add(clinicName);
                    filteredclinics.push(item);
                }
            }
            return filteredclinics;
        }

        return clinics;
    }
}

export const getPatientByMobileAndClinic = async(mobile, clinic) => {
    var code = mobile
        ?.substring(0, 2);
    if (code == "91") {
        mobile = mobile.substring(2, mobile.length);
    }
    var PatientModel = portalDb.model("Patient");
    if (mobile != null && clinic != null) {
        var clientId = new mongoose
            .Types
            .ObjectId(clinic);

        const patientCollection = await PatientModel
            .find({mobile: mobile, clinic: clientId, deleted: false})
            .select({patientId: 1, firstName: 1, lastName: 1, age: 1, gender: 1})
            .exec();
        return patientCollection;
    }
    return null;
}