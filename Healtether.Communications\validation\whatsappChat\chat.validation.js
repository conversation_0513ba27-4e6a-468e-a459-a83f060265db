import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateAddChatMessageForClinic = async (req, res, next) => {
  await checkSchema({
    "data.message": {
      in: ["body"],
      isString: {
        errorMessage: "message must be a string",
      },
      notEmpty: {
        errorMessage: "Message is required",
      },
    },
    "data.clinicId": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "clinicId must be a valid ObjectId",
      },
    },
    "data.mobile": {
      in: ["body"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetRecentChatMessageForClinic = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "clinicId must be a valid ObjectId",
      },
    },
    mobile: {
      in: ["query"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
    },
    pg: {
      in: ["query"],
      isInt: {
        errorMessage: "pg  must be a integer",
      },
      notEmpty: {
        errorMessage: "pg is required",
      },
    },
    pgSize: {
      in: ["query"],
      isInt: {
        errorMessage: "pgSize  must be a integer",
      },
      notEmpty: {
        errorMessage: "pgSize is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateAddRecent = async (req, res, next) => {
  await checkSchema({
    "data.clinicId": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "clinicId must be a valid ObjectId",
      },
    },
    "data.mobile": {
      in: ["body"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
      
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetRecentChatsForClinic = async (req, res, next) => {
  await checkSchema({
    "clinicId": {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "clinicId must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};


export const validateSetReaded = async (req, res, next) => {
    await checkSchema({
        "data.clinicId": {
          in: ["body"],
          custom: {
            options: (value) => mongoose.Types.ObjectId.isValid(value),
            errorMessage: "clinicId must be a valid ObjectId",
          },
        },
        "data.mobile": {
          in: ["body"],
          isMobilePhone: true,
          errorMessage: "Mobile must be a valid phone number",
        },
      }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };