import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
mockClientHelper();
const { clientDelete } = await import("../../controllers/clinic/client.controller.js");
const { deleteClient } = await import("../../helpers/clinic/client.helper.js");

const mockClient = { _id: '62a000000000000000000001', save: jest.fn() };

let query = { query: { id: '62a000000000000000000001' } };  
const res = {
  json: jest.fn(),
  status: jest.fn().mockReturnThis(),
};

describe('clientDelete', () => {  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should delete a client and return success', async () => {
    deleteClient.mockResolvedValueOnce(mockClient);

    await clientDelete(query, res);

    expect(deleteClient).toHaveBeenCalledWith(query.query.id);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });

  
});
