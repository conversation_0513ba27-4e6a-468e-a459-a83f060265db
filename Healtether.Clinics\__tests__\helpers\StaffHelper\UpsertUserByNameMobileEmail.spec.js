import { jest } from "@jest/globals";
import { Staff } from '../../../model/clinics.model.js';
import { User } from '../../../model/clinics.model.js'; 
import { upsertUserByNameMobileEmail } from '../../../helpers/staff/staff.helper.js'; 
import { setup, teardown } from "../../../setup.js"; 
import mongoose from 'mongoose';

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); // Set up MongoDB connection
});

afterAll(async () => {
  await teardown(); // Tear down MongoDB connection
});

describe('upsertUserByNameMobileEmail function', () => {
  const clientId = new mongoose.Types.ObjectId();
  const clinicStaffId = 'clinicStaffId';
  
  beforeEach(async () => {
    await Staff.deleteMany({});
    await User.deleteMany({}); 
  });

  afterEach(async () => {
    await Staff.deleteMany({}); 
    await User.deleteMany({}); 
  });

  it('should create a new user and staff member when no existing user is found', async () => {
    const result = await upsertUserByNameMobileEmail(
      'John',
      'Doe',
      "Mr.",
      '1234567890',
      '<EMAIL>',
      null, // No existing user ID
      clinicStaffId,
      clientId
    );

    const staff = await Staff.findOne({ mobile: '1234567890' });
    const user = await User.findOne({ mobile: '1234567890' });
    
    expect(staff).toBeTruthy(); 
    expect(staff.firstName).toBe('John'); 
    expect(user).toBeTruthy(); 
    expect(user.firstName).toBe('John');
    expect(result).toBeTruthy(); 
  });

  it('should update an existing user and staff member when found', async () => {
    const existingUser = new User({
      firstName: 'Jane',
      lastName: 'Doe',
      mobile: '0987654321',
      prefix:"Mr.",
      email: '<EMAIL>',
      password: 'password', 
    });
    await existingUser.save();

    const existingStaff = new Staff({
      firstName: 'Jane',
      lastName: 'Doe',
      mobile: '0987654321',
      email: '<EMAIL>',
      prefix:"Mr.",
      userId: existingUser._id,
      staffId: clinicStaffId,
      clinic: clientId,
    });
    await existingStaff.save();

    
    const result = await upsertUserByNameMobileEmail(
      'Jane',
      'Smith',
      "Mr.",
      '0987654321',
      '<EMAIL>',
      existingUser._id, // Existing user ID
      clinicStaffId,
      clientId
    );

    const updatedStaff = await Staff.findById(existingStaff._id);
    const updatedUser = await User.findById(existingUser._id);
    
    expect(updatedStaff).toBeTruthy(); 
    expect(updatedUser).toBeTruthy(); 
    expect(updatedStaff.lastName).toBe('Smith'); 
    expect(updatedUser.email).toBe('<EMAIL>'); 
    expect(result).toBeTruthy(); 
  });

  it('should return the user object after upserting', async () => {
    const result = await upsertUserByNameMobileEmail(
      'John',
      'Doe',
      "Mr.",
      '1234567890',
      '<EMAIL>',
      null, // No existing user ID
      clinicStaffId,
      clientId
    );

    expect(result).toBeTruthy(); 
    expect(result.firstName).toEqual('John'); 
  });
});
