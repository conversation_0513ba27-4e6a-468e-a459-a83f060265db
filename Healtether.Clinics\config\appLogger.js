import appInsights from 'applicationinsights';
export const initializeAppInsights = () => {

    if (process.env.ENV && process.env.APPLICATIONINSIGHTS_CONNECTION_STRING && process.env.ENV !== "dev") {
        console.log("into telemetry")
        appInsights.setup(process.env.APPLICATIONINSIGHTS_CONNECTION_STRING)
            .setAutoDependencyCorrelation(true)
            .setAutoCollectRequests(true)
            .setAutoCollectPerformance(true, true)
            .setAutoCollectExceptions(true)
            .setAutoCollectDependencies(true)
            .setAutoCollectConsole(true)
            .setUseDiskRetryCaching(true)
            .setSendLiveMetrics(true)
            .setDistributedTracingMode(appInsights.DistributedTracingModes.AI);

        appInsights.defaultClient.config.samplingPercentage = 99;
        appInsights.defaultClient.context.tags[appInsights.defaultClient.context.keys.cloudRole] = "Healtether.Portal.API";

        appInsights.start();
    }

};

export const LogTrace = (message) => {
    if (process.env.ENV === "dev") {
        console.log(message);
    }
    else {
        appInsights.defaultClient?.trackTrace({ message: `Log Application: ${message}` });
    }

}

export const LogException = (exception) => {
    if (process.env.ENV === "dev") {
        console.error(`Error in Application ${exception}\nStack: ${exception.stack}`);
    }
    else {
        try{
            appInsights.defaultClient?.trackException({exception:exception});
        }
       catch(e)
       {
        console.error(`Error in Application ${e}\nStack: ${e.stack}`);
       }
    }

}

export const LogEvent = (name, customProperty) => {
    appInsights.defaultClient?.trackEvent({ name: name, properties: { customProperty: customProperty } });
}

export const ConsoleLog = (message) => {
    if (process.env.ENV === "dev")
        console.log(message);
}
export const ConsoleLogError = (error) => {
    if (process.env.ENV === "dev")
        console.error(`Error in Application ${error}`);
}