import React from "react";
import { useClickOut } from "./../../utils/hooks/useClickout";
import { Icons } from "./icons";

export default function Modal({
  isOpen,
  setIsOpen,
  classname,
  plain,
  children,
  isCard
}) {
  return (
    <>
    <>
  <section
    className={`${
      isOpen ? "opacity-100 visible" : "opacity-0 invisible"
    } ${
      plain ? "fixed" : "absolute"
    } duration-150 bg-color_dark/10 backdrop-blur-xs top-0 left-0 w-full h-full z-50`}
  ></section>
  <main
    className={`${classname} custom-scrollbar ${
      isOpen ? "scale-100 opacity-100" : "scale-0 opacity-0"
    } ${
      plain ? "fixed" : "absolute"
    } duration-500 origin-top-right top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 z-50 max-h-[80vh] max-w-md bg-white rounded-lg shadow-sm ${
      isCard ? "p-0 rounded-none" : "p-5"
    }`}
  >
    {plain || (
      <Icons.cancel
        className="absolute top-2 right-3 cursor-pointer"
        onClick={setIsOpen}
      />
    )}
    <div className="overflow-y-auto max-h-[calc(80vh-2rem)] pr-1 ">
      {children}
    </div>
  </main>
</>
    </>
  );
}
