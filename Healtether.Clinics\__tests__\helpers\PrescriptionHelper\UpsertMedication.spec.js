import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { updateMedicalHistory } from "../../../helpers/appointment/write-prescription/medical-histories.helper.js";
import { Patient } from "../../../model/clinics.model.js";
import { Appointment } from "../../../model/clinics.model.js";
import { Client } from "../../../model/clinics.model.js";
import { setup, teardown } from "../../../setup.js";
import { Allergies } from "../../../model/clinics.model.js";
import { MedicationHistories } from "../../../model/clinics.model.js";

jest.setTimeout(30000);
beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe("UpsertVitals function", () => {
  let patientId;
  let appointmentId;
  let clinicId;
  // beforeEach(async () => {
  //   await Allergies.deleteMany({});
  //   await FamilyHistories.deleteMany({});
  //   await MedicationHistories.deleteMany({});
  //   await PastHistories.deleteMany({});
  //   await PastProcedure.deleteMany({});


  // });

  afterEach(async () => {
  });
  it("should add new vitals when no existing record is found", async () => {
    const patient = new Patient({
      firstName: "John",
      lastName: "Doe",
      mobile: "**********",
      patientId: "PAT001",
      prefix:"Mr.",
      deleted: false,
    });
    patientId = (await patient.save())._id;

    const clinic = await Client.create({
      clinicName: "TEST",
      address: "18 veera alagamman koil street",
      created: { on: new Date("2024-04-27T06:52:20.110Z") },
      isDeleted: false,
      logo: "",
      modified: { on: new Date("2024-09-17T09:15:18.594Z") },
      patientId: { prefix: "SD", suffix: "" },
    });
    clinicId = clinic._id;

    const appointment = new Appointment({
      mobile: "**********",
      name: "First Patient",
      gender: "Male",
      age: 33,
      patientId: new mongoose.Types.ObjectId(patientId),
      clinic: new mongoose.Types.ObjectId(clinicId),
      doctorName: "Dr. Smith",
      appointmentDate: new Date(),
      timeSlot: "10:00 AM",
      reason: "Checkup",
      paymentStatus: true,
      virtualConsultation: false,
      isCanceled: false,
    });
    appointmentId = (await appointment.save())._id;

    console.log(await Appointment.find({}))


   const data={
    medication:[
      {
        name: "medication",
        duration: {
          value:7,
          unit: "days"
        },
        notes: "Take after meals"
    }
    ],
     allergies:[
      {
        name: "Paracetamol",
        duration: {
          value:7,
          unit: "days"
        },
        notes: "Take after meals"
    }
    ],
    familyHistory:[
      {
        name: "Paracetamol",
        duration: {
          value:7,
          unit: "days"
        },
        notes: "Take after meals"
    }
    ],
    pastHistory:[
      {
        name: "Paracetamol",
        duration: {
          value:7,
          unit: "days"
        },
        notes: "Take after meals"
    }
    ],
    pastProcedureHistory:[
      {
        name: "Paracetamol",
        duration: {
          value:7,
          unit: "days"
        },
        notes: "Take after meals"
    }
    ]
   } 
    const user = { id: "user1", name: "User One" };
    const result = await updateMedicalHistory(
      data,
      user,
      patientId,
      clinicId,
    );
    let allergies=await Allergies.findOne({patient:patientId,clinic:clinicId})
    expect(allergies.name).toBe("Paracetamol");
    expect(allergies.notes).toBe("Take after meals");
    let medication=await MedicationHistories.findOne({patient:patientId,clinic:clinicId})
    expect(medication.name).toBe("medication");
    expect(medication.notes).toBe("Take after meals");
  });

});
