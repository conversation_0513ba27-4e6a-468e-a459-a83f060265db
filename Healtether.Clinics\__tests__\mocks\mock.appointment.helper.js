import { jest } from "@jest/globals";

export async function mockApointmentHelper() {
    await jest.unstable_mockModule("../../helpers/appointment/appointment.helper.js", async () => ({
        cancelled: jest.fn(),
        overview: jest.fn(),
        upsertAppointment: jest.fn(),
        getAppointment: jest.fn(),
        getAppointmentCount: jest.fn(),
        getAppointmentWithPatient: jest.fn(),
        getCurrentAppointmentRecord: jest.fn(),
        setStarted: jest.fn(),
        setEnded: jest.fn(),
        setFollowUp: jest.fn(),
        reschedule: jest.fn(),
        updateRecords: jest.fn(),
        updateAdviceNotes: jest.fn(),
        setPaymentStatus: jest.fn(),
    }));
}