import { format, parseISO } from "date-fns";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
// import { Buttons } from "../../../../../components/detail-page/appointment/button";

export default function Table({
  consent,
  tab,
  setActiveTab,
  consentList,
  setGrantedConsentAbhaRecords,
}) {
  const [expandedHiTypes, setExpandedHiTypes] = useState({});

  const toggleHiTypes = (id) => {
    setExpandedHiTypes((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };
  const formatHiTypes = (hiTypes, id) => {
    if (!hiTypes || hiTypes.length === 0) return "None";

    if (expandedHiTypes[id]) {
      return (
        <div className="flex flex-wrap gap-1 max-w-full justify-center">
          {hiTypes.map((hi, index) => (
            <span
              key={index}
              className="bg-gray-100 px-2 py-0.5 rounded-sm text-xs"
            >
              {hi}
            </span>
          ))}
          <span
            className="text-xs text-blue-600 cursor-pointer underline mt-1"
            onClick={(e) => {
              e.stopPropagation();
              toggleHiTypes(id);
            }}
          >
            Show less
          </span>
        </div>
      );
    } else {
      // Show limited types with "Show more" option
      const displayCount = 2;
      const displayTypes = hiTypes.slice(0, displayCount);
      const remaining = hiTypes.length - displayCount;

      return (
        <div className="flex flex-wrap gap-1 max-w-full justify-center">
          {displayTypes.map((hi, index) => (
            <span
              key={index}
              className="bg-gray-100 px-2 py-0.5 rounded-sm text-xs"
            >
              {hi}
            </span>
          ))}
          {remaining > 0 && (
            <span
              className="text-xs text-blue-600 cursor-pointer underline"
              onClick={(e) => {
                e.stopPropagation();
                toggleHiTypes(id);
              }}
            >
              +{remaining} more
            </span>
          )}
        </div>
      );
    }
  };

  return (
    <main className="overflow-x-auto w-auto font-primary scroll flex flex-col gap-4 custom-scrollbar">
      <header className="bg-Primary h-auto w-[1260px] grid grid-cols-12 items-center text-xl font-semibold text-dark">
        <div className="pl-8 col-span-2 sticky left-0 bg-Primary z-10">
          Consent ID
        </div>
        <div className="text-center col-span-1">Name</div>
        <div className="text-center col-span-2">ABHA Address</div>
        <div className="text-center col-span-1">Requested on</div>
        <div className="text-center col-span-1">Shared for</div>
        <div className="text-center col-span-1">Expires in</div>
        <div className="text-center col-span-1">Requested HI Types</div>
        <div className="text-center col-span-1">Granted HI Types</div>
        <div className="text-center col-span-1">Granted On</div>
        <div className="text-center col-span-1">Status</div>
        <div className="text-center col-span-1">Action</div>
      </header>

      {consentList?.length > 0 &&
        consentList.map((data, idx) => (
          <section
            key={idx}
            className="bg-white shadow-sm h-auto min-h-14 py-2 w-[1260px] grid grid-cols-12 items-center text-sm font-semibold text-dark"
          >
            <div className="text-center col-span-2 truncate px-2">
              {data?.consentRequestId}
            </div>
            <div className="text-center col-span-1 truncate px-2">
              {data?.patientName}
            </div>
            <div className="text-center col-span-2 truncate px-2">
              {data?.abhaAddress}
            </div>
            <div className="text-center col-span-1">
              {data?.createdAt
                ? format(parseISO(data.createdAt), "do MMM, yyyy")
                : "N/A"}
            </div>
            <div className="text-center col-span-1">
              {data?.permission?.dateRange?.from
                ? format(
                    parseISO(data?.permission?.dateRange?.from),
                    "do MMM, yyyy"
                  )
                : "N/A"}
              <br />
              {data?.permission?.dateRange?.to
                ? format(
                    parseISO(data?.permission?.dateRange?.to),
                    "do MMM, yyyy"
                  )
                : "N/A"}
            </div>
            <div className="text-center col-span-1">
              {data?.permission?.dataEraseAt
                ? format(
                    parseISO(data?.permission?.dataEraseAt),
                    "do MMM, yyyy"
                  )
                : "N/A"}
            </div>
            <div className="text-center col-span-1 px-2">
              {formatHiTypes(data.hiTypes, data.consentRequestId)}
            </div>
            <div className="text-center col-span-1 px-2">
              {formatHiTypes(data.grantedHiTypes, data.consentRequestId)}
            </div>
             <div className="text-center col-span-1 px-2">
              {data?.consentGrantedOn && (
                  <div className="text-xs mt-1">
                    {format(
                      new Date(data.consentGrantedOn),
                      "do MMM, yyyy, hh:mm a"
                    )}
                  </div>)}
            </div>
            <div className="col-span-1">
              <div className="flex flex-col items-center justify-center mx-auto h-auto cursor-pointer">
                <div
                  className={`text-center py-1 px-4 mt-1 rounded-md ${
                    data?.consentStatus === "DENIED"
                      ? "bg-red-500 text-white"
                      : data?.consentStatus === "REVOKED"
                      ? "bg-red-200 text-white"
                      : data?.consentStatus === "REQUESTED"
                      ? "bg-blue-400 text-white"
                      : data?.consentStatus === "GRANTED"
                      ? "bg-Primary text-white"
                      : "bg-gray-200"
                  }`}
                >
                  {data?.consentStatus}
                </div>
                {data?.consentStatusUpdatedOn && (
                  <div className="text-xs mt-1">
                    {format(
                      new Date(data.consentStatusUpdatedOn),
                      "do MMM, yyyy, hh:mm a"
                    )}
                  </div>)}
              </div>
            </div>
            <div
              className={`col-span-1 ${
                data?.consentStatus === "GRANTED" ? "" : "hidden"
              }`}
            >
              <div
                className="flex items-center text-[#740ac7] justify-center mx-auto w-20 h-7 rounded-md bg-gray cursor-pointer"
                onClick={() => {
                  setActiveTab(2);
                  setGrantedConsentAbhaRecords(data);
                }}
              >
                View
              </div>
            </div>
          </section>
        ))}
    </main>
  );
}
