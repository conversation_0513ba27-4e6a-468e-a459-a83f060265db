import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { overview } from '../../../helpers/clinic/client.helper.js';
import { Client } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js";

jest.setTimeout(30000); 
beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('overview function', () => {
  beforeEach(async () => {
    await Client.deleteMany({});
    await Client.create({
      clinicName: 'TEST',
      address: '18 veera alagamman koil street',
      created: { on: new Date('2024-04-27T06:52:20.110Z') },
      isDeleted: false,
      logo: '',
      modified: { on: new Date('2024-09-17T09:15:18.594Z') },
      patientId: { prefix: 'SD', suffix: '' },
      phonepeSetting: { merchantId: '', saltKey: '', saltIndex: '' },
      staffId: { prefix: 'CHENNAI', suffix: '' },
      timeSlots: [
        {
          startTime: { hours: 3, min: 0, tt: 'PM' },
          endTime: { hours: 5, min: 59, tt: 'PM' },
        },
        {
          startTime: { hours: 7, min: 50, tt: 'AM' },
          endTime: { hours: 8, min: 20, tt: 'AM' },
        },
      ],
    });
  });

  it('should return client data with default sorting and correct pagination', async () => {
    const result = await overview(0, 10);

    // Assertions
    console.log(result.data[0]);

    expect(result.data).toHaveLength(1); 
    expect(result.data[0].clinicName).toBe('TEST'); 
    expect(result.totalCount).toBe(1);
  });

  it('should return an empty array if no clients are available', async () => {
    await Client.deleteMany({});

    const result = await overview(0, 10); 

    expect(result.data).toHaveLength(0); 
    expect(result.totalCount).toBe(0); 
  });

});
