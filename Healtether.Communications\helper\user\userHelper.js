// import mongoose from "mongoose";
// import {RecentChatsModel} from "../../schemas/recentchatschema.js";
// import {UserModel} from "../../schemas/userschema.js";
// import {v4 as uuidv4} from 'uuid';

// export const getAllUserChat = async(clientId, size, pg) => {
//     try {
//         const user = await RecentChatsModel
//             .find({clientId: clientId})
//             .populate('userDetail')
//             .limit(size)
//             .skip(pg * size)
//             .exec()

//         return user;

//     } catch (error) {
//         console.log(error);
//         return error;
//     }
// };

// export const saveUser = async(userId, clientId, name, mobile, email, whatsapp, profilePic) => {
//     try {
//         const userIsExists = await UserModel
//             .findOne({
//             _id: new mongoose
//                 .Types
//                 .ObjectId(userId)
//         })
//             .exec();

//         let loggerName = "";
//         if (userIsExists && userIsExists.logger) {
//             loggerName = userIsExists.logger;
//         } else {
//             loggerName = uuidv4();
//         }

//         const userCollection = new UserModel({
//             _id: new mongoose
//                 .Types
//                 .ObjectId(userId),
//             email: email,
//             name: name,
//             userId: userId,
//             mobile: mobile,
//             whatsapp: whatsapp,
//             profilePic: profilePic,
//             logger: loggerName
//         });
//         var upsertData = userCollection.toObject();
//         await UserModel.findOneAndUpdate({
//             _id: upsertData._id
//         }, upsertData, {upsert: true});

//         const chat = new RecentChatsModel({
//             clientId: clientId,
//             userDetail: new mongoose
//                 .Types
//                 .ObjectId(userId)

//         });

//         var upsertData = chat.toObject();
//         var recents = await RecentChatsModel
//             .findOne({
//             clientId: clientId,
//             userDetail: new mongoose
//                 .Types
//                 .ObjectId(userId)
//         })
//             .exec();

//         if (recents) {
//           recents.timeStamp=Date.now()
//           recents.save()

//         } else {
//             await RecentChatsModel.findOneAndUpdate({
//                 clientId: clientId,
//                 userDetail: new mongoose
//                     .Types
//                     .ObjectId(userId)
//             }, upsertData, {upsert: true});
//         }

//     } catch (error) {
//         console.log(error);
//         return error;
//     }
// };