import { ClinicGroup } from "../../model/clinics.model.js";

export const createGroup = async (data, user) => {
    var clinicGroup = {};
    clinicGroup = new ClinicGroup({
      groupName: data.groupName,
      deleted: false,
      created: {
        on: new Date().toISOString(),
        by: user,
      },
    });
    clinicGroup.save();
    return clinicGroup;
};

export const getAllGroup = async () => {
    const groupName = await ClinicGroup.find().select("groupName").exec();
    return groupName;
};
