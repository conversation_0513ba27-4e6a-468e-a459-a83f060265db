import Chart from "react-apexcharts";
export default function PaymentAnalytics() {

    const stdLegend = {
        show: true,
        position: 'top',
        horizontalAlign: 'center',
        fontSize: '14px',
        fontFamily: "Urbanist",
        fontWeight: 400,
        labels: {
          useSeriesColors: true,
        }
      };
      const stdTooltip = {
        style: {
          fontSize: '12px',
          fontFamily: "Urbanist",
        },
      };
      const stdFill = {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          gradientToColors: ['var(--color-base-100)'],
          opacityTo: 0.3,
          stops: [0, 90, 100]
        }
      };
    
      const stdResponsive = [{
        breakpoint: 568,
        options: {
          chart: {
            height: 300
          },
          labels: {
            style: {
              fontSize: '10px',
              colors: 'var(--color-base-content)',
              fontFamily: 'Urbanist'
            },
            offsetX: -2,
            formatter: title => title.slice(0, 3)
          },
          yaxis: {
            labels: {
              align: 'left',
              minWidth: 0,
              maxWidth: 140,
              style: {
                fontSize: '10px',
                colors: 'var(--color-base-content)',
                fontFamily: 'Urbanist'
              },
              formatter: value => (value >= 1000 ? `${value / 1000}k` : value)
            }
          }
        }
      }];
    
     
    
      const modeOption = {
        chart: {
          height: 350,
          type: 'bar',
          toolbar: {
            show: false
          },
          zoom: {
            enabled: false
          }
        },
        legend: stdLegend,
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '60px',
            borderRadius: 4,
            borderRadiusApplication: 'end'
          }
        },
        dataLabels: {
          enabled: true,
          formatter: function (val, opts) {
            return val + "%"
          },
        },
        tooltip: {
          enabled: false,
        },
        markers: {
          size: [5, 5]
        },
        stroke: {
          curve: 'smooth',
          width: 2
        },
        grid: {
          strokeDashArray: 2,
          borderColor: 'color-mix(in oklab, var(--color-base-content) 40%, transparent)'
        },
        colors: ['var(--color-accent)'],
        xaxis: {
          categories: ["Cash","UPI","Card"],
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: true
          },
          tooltip: {
            enabled: false
          },
          labels: {
            style: {
              colors: 'var(--color-base-content)',
              fontSize: '12px',
              fontFamily: "Urbanist",
              fontWeight: 400
            },
          }
        },
        yaxis: {
          labels: {
            align: 'left',
            minWidth: 0,
            maxWidth: 140,
            style: {
              colors: 'var(--color-base-content)',
              fontSize: '12px',
              fontFamily: "Urbanist",
              fontWeight: 400
            },
            formatter: value => (value + "%")
          }
        },
        responsive: stdResponsive
      }
      const paymentsOption = {
        chart: {
          height: 350,
          type: 'bar',
          toolbar: {
            show: false
          },
          zoom: {
            enabled: false
          }
        },
        plotOptions: {
          bar: {
            horizontal: true,
            columnWidth: '16px',
            borderRadius: 4,
            borderRadiusApplication: 'end'
          }
        },
        legend: stdLegend,
        dataLabels: {
          enabled: true,
          formatter: function (val, opts) {
            return val + "%"
          }
        },
        tooltip: {
          enabled: false,
        },
        grid: {
          strokeDashArray: 2,
          borderColor: 'color-mix(in oklab, var(--color-base-content) 40%, transparent)'
        },
        colors: ['var(--color-accent)'],
        xaxis: {
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: true
          },
          tooltip: {
            enabled: false
          },
          labels: {
            style: {
              colors: 'var(--color-base-content)',
              fontSize: '12px',
              fontFamily: "Urbanist",
              fontWeight: 400
            },
            formatter: value => (value + "%")
          },
    
        },
        yaxis: {
          labels: {
            align: 'left',
            minWidth: 0,
            maxWidth: 140,
            style: {
              colors: 'var(--color-base-content)',
              fontSize: '12px',
              fontFamily: "Urbanist",
              fontWeight: 400
            }
          },
        },
        responsive: stdResponsive
      }
    return (<>
        <div className="flex gap-4 ml-3">
            <div className="stats xs:w-full w-4/12">
                <div className="stat">
                    <div className="avatar avatar-placeholder">
                        <div className="bg-success/20 text-success size-10 rounded-full">
                            <span className="icon-[tabler--package] size-6"></span>
                        </div>
                    </div>
                    <div className="stat-value mb-1 font-primary">Total Revenue Collected </div>
                    <div className="stat-title">7,500 of 10,000 orders</div>
                    <div className="progress bg-success/10 h-2" role="progressbar" aria-label="Order Progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">
                        <div className="progress-bar progress-success w-3/4"></div>
                    </div>
                </div>
            </div>

            <div className="stats xs:w-full w-4/12">
                <div className="stat">
                    <div className="avatar avatar-placeholder">
                        <div className="bg-warning/20 text-warning size-10 rounded-full">
                            <span className="icon-[tabler--cash] size-6"></span>
                        </div>
                    </div>
                    <div className="stat-value mb-1 font-primary">Online Payments</div>
                    <div className="stat-title">45,000 of 100,000</div>
                    <div className="progress bg-warning/10 h-2" role="progressbar" aria-label="Revenue Progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100">
                        <div className="progress-bar progress-warning w-2/5"></div>
                    </div>
                </div>
            </div>
        </div>
        <div className="flex sm:flex-col md:flex-row gap-4 mt-4 ml-3">
              <div className=" sm:w-full md:w-1/2">
                <div className="stats w-full">
                  <div className="stat overflow-hidden">
                    <div className="stat-title font-primary text-lg font-semibold">Mode of Payments</div>
                    <div className="stat-value">
                      <div className="app">
                        <div className="row">
                          <div className="mixed-chart">
                            <Chart
                              options={modeOption}
                              series={[
                                {
                                  name: 'PaymentMode',
                                  data: [25,67,10]
                                },
                              ]}
                              type="bar"
                              height="350"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className=" sm:w-full md:w-1/2">
                <div className="stats w-full">
                  <div className="stat overflow-hidden">
                    <div className="stat-title font-primary text-lg font-semibold">Payment Analysis</div>
                    <div className="stat-value">
                      <div className="app">
                        <div className="row">
                          <div className="mixed-chart">
                            <Chart
                              options={paymentsOption}
                              series={[
                                {
                                  data: [{
                                    x:"Consultation Fee",
                                    y:85,
                                  },
                                  {
                                    x:"others",
                                    y:15,
                                  }]
                                }
                              ]}
                              type="bar"
                              height="350"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
    </>)
}