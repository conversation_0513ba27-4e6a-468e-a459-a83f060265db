import React, { useEffect, useRef, useState } from "react";
import domtoimage from "dom-to-image-more";
import NH<PERSON>OG<PERSON> from "../../../assets/images/national-health-authority2.png";
import ABHAIMG from "../../../assets/images/abha.svg";

const ABHACard = ({ abhaCard, onCancel }) => {
  const cardRef = useRef(null);
  const [imageSource, setImageSource] = useState(null);

  useEffect(() => {
    // Check if abhaCardData is a Blob
    if (abhaCard instanceof Blob) {
      setImageSource(URL.createObjectURL(abhaCard));
    } else if (typeof abhaCard === 'string') {
      // If it's already a string URL or base64, use it directly
      setImageSource(abhaCard);
    }

    // Cleanup function
    return () => {
      if (imageSource && imageSource.startsWith('blob:')) {
        URL.revokeObjectURL(imageSource);
      }
    };
  }, [abhaCard]);

  const handleDownload = () => {
    const cardElement = cardRef.current;

    domtoimage.toPng(cardElement, {
      quality: 1.0,
      scale: 2,
      bgcolor: 'white'
    })
    .then((dataUrl) => {
      const link = document.createElement("a");
      link.href = dataUrl;
      link.download = "abhacard.png";
      link.click();
    })
    .catch((error) => {
      console.error("Error generating ABHA card image:", error);
    });
  };

  return (
    <div className="w-full max-w-md mx-auto bg-white shadow-lg font-sans">
      <div>
        {/* Header Section */}
        <div className="bg-[#20c997] text-black p-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <img src={NHALOGO} alt="NHA Logo" className="h-10" />
            <h1 className="text-md font-medium">
              Ayushman Bharat Health Account (ABHA)
            </h1>
          </div>
          <img src={ABHAIMG} alt="ABHA Logo" className="h-10" />
        </div>

        {/* Content Section */}
        <div className="p-6 space-y-6"  >
          {imageSource ? (
            <img
            ref={cardRef}
              src={imageSource}
              alt="ABHA Card"
              className="w-full h-auto"
              onError={(e) => console.error("Image loading error:", e)}
            />
          ) : (
            <div className="w-full h-32 flex items-center justify-center bg-gray-100">
              Loading ABHA Card...
            </div>
          )}
        </div>
      </div>

      {/* Footer Buttons */}
      <div className="flex justify-center gap-4 p-6 border-t">
        <button
          className="px-8 py-2 border-2 border-[#20c997] text-[#20c997] rounded-md font-medium hover:bg-gray-50"
          onClick={onCancel}
        >
          Cancel
        </button>
        <button
          className="px-8 py-2 bg-[#20c997] text-white rounded-md font-medium hover:bg-[#1db386]"
          onClick={handleDownload}
        >
          Download
        </button>
      </div>
    </div>
  );
};

export default ABHACard;