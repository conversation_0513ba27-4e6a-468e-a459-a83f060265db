{"name": "healtether-clinics-api", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "healtether-clinics-api", "version": "1.0.0", "license": "ISC", "dependencies": {"@azure/identity": "^4.10.0", "@azure/storage-blob": "^12.27.0", "@date-fns/utc": "^2.1.0", "applicationinsights": "^2.9.6", "axios": "^1.7.7", "bcrypt": "^5.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-session": "^1.17.3", "express-validator": "^7.2.1", "firebase-admin": "^12.0.0", "googleapis": "^140.0.1", "healtether.models": "^1.7.9", "jest-watch-typeahead": "^2.2.2", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongodb": "^6.9.0", "mongoose": "^7.8.6", "mongoose-paginate-v2": "^1.8.2", "multer": "^1.4.5-lts.1", "node-cron": "^4.0.5", "node-fetch": "^3.3.2", "nodemon": "^3.1.9", "supertest": "^7.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^10.0.0", "zlib": "^1.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@jest/globals": "^29.7.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-watch-typeahead": "^2.2.2"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@apidevtools/json-schema-ref-parser": {"version": "9.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@apidevtools/json-schema-ref-parser/-/json-schema-ref-parser-9.1.2.tgz", "integrity": "sha1-j/U4azZdTJ+qfItWb/FqRqV32bg=", "license": "MIT", "dependencies": {"@jsdevtools/ono": "^7.1.3", "@types/json-schema": "^7.0.6", "call-me-maybe": "^1.0.1", "js-yaml": "^4.1.0"}}, "node_modules/@apidevtools/json-schema-ref-parser/node_modules/argparse": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/argparse/-/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "license": "Python-2.0"}, "node_modules/@apidevtools/json-schema-ref-parser/node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@apidevtools/openapi-schemas": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@apidevtools/openapi-schemas/-/openapi-schemas-2.1.0.tgz", "integrity": "sha1-n6CAF/tZ2AU4gS8D/HysWZLKqhc=", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@apidevtools/swagger-methods": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@apidevtools/swagger-methods/-/swagger-methods-3.0.2.tgz", "integrity": "sha1-t4mjYuBVsDQNBHEur+cCfdwawmc=", "license": "MIT"}, "node_modules/@apidevtools/swagger-parser": {"version": "10.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@apidevtools/swagger-parser/-/swagger-parser-10.0.3.tgz", "integrity": "sha1-MgV66ZSHhyxN2WsxShq0uV2J6vU=", "license": "MIT", "dependencies": {"@apidevtools/json-schema-ref-parser": "^9.0.6", "@apidevtools/openapi-schemas": "^2.0.4", "@apidevtools/swagger-methods": "^3.0.2", "@jsdevtools/ono": "^7.1.3", "call-me-maybe": "^1.0.1", "z-schema": "^5.0.1"}, "peerDependencies": {"openapi-types": ">=7"}}, "node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha1-Qv4MyrI4QdmQWBLFjxCC0neEVm0=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-auth": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-auth/-/core-auth-1.9.0.tgz", "integrity": "sha1-rHJbA/q+PIkjcQZe6eIEG+4P0aw=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.11.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-client": {"version": "1.9.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-client/-/core-client-1.9.4.tgz", "integrity": "sha1-u5u4Xtx4D8ZWMLbY/6Fyw2M8qP4=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.4.0", "@azure/core-rest-pipeline": "^1.20.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.6.1", "@azure/logger": "^1.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-http-compat": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-http-compat/-/core-http-compat-2.3.0.tgz", "integrity": "sha1-6dOWKZIR50IwiCdnQILBO9Y4xr8=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-client": "^1.3.0", "@azure/core-rest-pipeline": "^1.20.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-lro": {"version": "2.7.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-lro/-/core-lro-2.7.2.tgz", "integrity": "sha1-eHEFAnog5Fx3ZRqYsBpNOwG3Wgg=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.2.0", "@azure/logger": "^1.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-paging": {"version": "1.6.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-paging/-/core-paging-1.6.2.tgz", "integrity": "sha1-QNOGDcLffykdZjULLP2RcVJkM+c=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-rest-pipeline": {"version": "1.20.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-rest-pipeline/-/core-rest-pipeline-1.20.0.tgz", "integrity": "sha1-kW2NbJz/a1VvCwv9W5I1JtWQ4tk=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.8.0", "@azure/core-tracing": "^1.0.1", "@azure/core-util": "^1.11.0", "@azure/logger": "^1.0.0", "@typespec/ts-http-runtime": "^0.2.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-tracing": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-tracing/-/core-tracing-1.2.0.tgz", "integrity": "sha1-e+XVPDUi1jnPGQQsvNsZ9xvDWrI=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-util": {"version": "1.12.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-util/-/core-util-1.12.0.tgz", "integrity": "sha1-C4woN+bWfD+66uIN80zwf2azSA0=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@typespec/ts-http-runtime": "^0.2.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-xml": {"version": "1.4.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-xml/-/core-xml-1.4.5.tgz", "integrity": "sha1-br/6hgeZy2V/DKY6WZLTWdSqSy0=", "license": "MIT", "dependencies": {"fast-xml-parser": "^5.0.7", "tslib": "^2.8.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/identity": {"version": "4.10.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/identity/-/identity-4.10.0.tgz", "integrity": "sha1-EM/kkge00RHr46qzreR1YcKFLG0=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.9.0", "@azure/core-client": "^1.9.2", "@azure/core-rest-pipeline": "^1.17.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.11.0", "@azure/logger": "^1.0.0", "@azure/msal-browser": "^4.2.0", "@azure/msal-node": "^3.5.0", "open": "^10.1.0", "tslib": "^2.2.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/logger": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/logger/-/logger-1.2.0.tgz", "integrity": "sha1-p5rvzdV9KpZgP6tZyaZuDZAipWQ=", "license": "MIT", "dependencies": {"@typespec/ts-http-runtime": "^0.2.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/msal-browser": {"version": "4.12.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/msal-browser/-/msal-browser-4.12.0.tgz", "integrity": "sha1-D2VoxA/BvvQVOh8p/OH8b/CddbQ=", "license": "MIT", "dependencies": {"@azure/msal-common": "15.6.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-common": {"version": "15.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/msal-common/-/msal-common-15.6.0.tgz", "integrity": "sha1-B2TWRG7v85cCIZleJfJl/bIY2mY=", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-node": {"version": "3.5.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/msal-node/-/msal-node-3.5.3.tgz", "integrity": "sha1-AveiNEosKZQ1SgzsElue+ajnEJs=", "license": "MIT", "dependencies": {"@azure/msal-common": "15.6.0", "jsonwebtoken": "^9.0.0", "uuid": "^8.3.0"}, "engines": {"node": ">=16"}}, "node_modules/@azure/msal-node/node_modules/uuid": {"version": "8.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uuid/-/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@azure/opentelemetry-instrumentation-azure-sdk": {"version": "1.0.0-beta.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/opentelemetry-instrumentation-azure-sdk/-/opentelemetry-instrumentation-azure-sdk-1.0.0-beta.8.tgz", "integrity": "sha1-erxrBWNUQU5rrMNmhzxn+MvnGKo=", "license": "MIT", "dependencies": {"@azure/core-tracing": "^1.2.0", "@azure/logger": "^1.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/core": "^1.30.1", "@opentelemetry/instrumentation": "^0.57.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/storage-blob": {"version": "12.27.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/storage-blob/-/storage-blob-12.27.0.tgz", "integrity": "sha1-MGKTBBEXOihGi9OA4K0sYyjXKIo=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.1.2", "@azure/core-auth": "^1.4.0", "@azure/core-client": "^1.6.2", "@azure/core-http-compat": "^2.0.0", "@azure/core-lro": "^2.2.0", "@azure/core-paging": "^1.1.1", "@azure/core-rest-pipeline": "^1.10.1", "@azure/core-tracing": "^1.1.2", "@azure/core-util": "^1.6.1", "@azure/core-xml": "^1.4.3", "@azure/logger": "^1.0.0", "events": "^3.0.0", "tslib": "^2.2.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/compat-data/-/compat-data-7.27.2.tgz", "integrity": "sha1-QYP55kL9hOdOPup/+pOkEuOxAsk=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/core/-/core-7.27.1.tgz", "integrity": "sha1-id5R6GvRIkYAPjUkcExJVBsWw+Y=", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-module-transforms": "^7.27.1", "@babel/helpers": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/generator": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/generator/-/generator-7.27.1.tgz", "integrity": "sha1-hi1PrYWPcgjt1IfCi1gUQDa3YjA=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.27.1", "@babel/types": "^7.27.1", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.1.tgz", "integrity": "sha1-Q0XYGppGpkhuJNBpRp8T5gRFwF0=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha1-RqD276uAjVHSnOloWN0Qzocycz0=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz", "integrity": "sha1-W+5CYqbqXdyFLQgGGZ6xfKPekoE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz", "integrity": "sha1-BbCILZe6HU0DUZ5LzmFdcK+hjFM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-define-polyfill-provider": {"version": "0.6.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz", "integrity": "sha1-Feh0Y2i/pnF4X1km/3SzBkwpH6s=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz", "integrity": "sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.27.1.tgz", "integrity": "sha1-4WY7i3HS3pSNpcT7KiDKTz7Cem8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz", "integrity": "sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz", "integrity": "sha1-RgHVx84usq6lgyjUNyVSP802LOY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz", "integrity": "sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz", "integrity": "sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz", "integrity": "sha1-uIKFAJwxQnrzGNT+N2Uc1ioUJAk=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/helpers/-/helpers-7.27.1.tgz", "integrity": "sha1-/8JwEwOGB826MojmksNhHAahiqQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/parser/-/parser-7.27.2.tgz", "integrity": "sha1-V3UYvtsXos5CEq/QUuAfffCUESc=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-bugfix-firefox-class-in-computed-class-key": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz", "integrity": "sha1-Yd2KjmH361aCaNG18SnaPu42S/k=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-class-field-initializer-scope": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz", "integrity": "sha1-Q/cKbX79UjcO7731WuA9kbKThW0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz", "integrity": "sha1-vrYjvVc7i28wR70EwyUGrcPlinI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz", "integrity": "sha1-4TSlR56yupwCcU6MHr8eyQdhJP0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.13.0"}}, "node_modules/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.27.1.tgz", "integrity": "sha1-uxwlrzTXURXOIpod5/pEv4+VVnA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0-placeholder-for-preset-env.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz", "integrity": "sha1-eET5KJVG76n+usLeTP41igUL1wM=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "integrity": "sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "integrity": "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz", "integrity": "sha1-iIlK79KwO17mrRVip8jhWHSWrs0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "integrity": "sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "integrity": "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "integrity": "sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "integrity": "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "integrity": "sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-unicode-sets-regex": {"version": "7.18.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz", "integrity": "sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz", "integrity": "sha1-biBhBnujqwJm2DSp+UgRGW8qupo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-generator-functions": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.27.1.tgz", "integrity": "sha1-ykM9+YPWjhN1OY58pxvypPb9idc=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz", "integrity": "sha1-mpOJO5N5s5Rmx0R09VrwPeeMZuc=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.27.1.tgz", "integrity": "sha1-VYqdbiTPcoAt07YqS1Hg1iwPV/k=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.1.tgz", "integrity": "sha1-vA2+isbeVgKYG6WO9oxt+O+b+7M=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-properties": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz", "integrity": "sha1-3UCmo3Df1J0yNiriBt2vK7CCqSU=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-static-block": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.27.1.tgz", "integrity": "sha1-fpINViWyW7zNMGGu+8wFgF7VbOQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.1.tgz", "integrity": "sha1-A7sEvqLHsvcR8NtzBKjaRqhcztQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.27.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz", "integrity": "sha1-gWYueL9ec0qXmCwrfwp5MojvPKo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/template": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.27.1.tgz", "integrity": "sha1-1ZFu9wicslTfBBiuUkUzwbcrplY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.27.1.tgz", "integrity": "sha1-qmgh3oZMUosf7PKG8KF0446Cb00=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.27.1.tgz", "integrity": "sha1-8fv2KOzhjhLnsysXWUDmg1j1RtE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-named-capturing-groups-regex": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz", "integrity": "sha1-UEOFTKYgqUFJNy5pAw/4y2qesOw=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-dynamic-import": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.27.1.tgz", "integrity": "sha1-THjzVVKsDgaqH248Vz1naV6K9aQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.27.1.tgz", "integrity": "sha1-/El7EtgnflWXR/Wj7YaN2AZPg+E=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-export-namespace-from": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz", "integrity": "sha1-ccpp00ce3W2qcRz038NABBXfnCM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz", "integrity": "sha1-vCT3CA6f9yG2OnCseyVkyhW2xAo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz", "integrity": "sha1-TQvzB3IOTc5tfDD8sf1sp3ves6c=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-json-strings": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.27.1.tgz", "integrity": "sha1-ouDObvJWN2vVJ/KQ2gI5g1J6T0w=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz", "integrity": "sha1-uq76TRCh1CBvnc3aUNfVgnu3CyQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-logical-assignment-operators": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz", "integrity": "sha1-iQyyDgJw4OW+vj8CW0NIQcMtW6o=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.27.1.tgz", "integrity": "sha1-N7iLpZTYUkGOmVNvVhL3lfI66vk=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.27.1.tgz", "integrity": "sha1-pBRfnYfCKR/i0F+ZS2XbpOPnGW8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz", "integrity": "sha1-jkTtN8J4fswjvcNn9Jl3R2YU6DI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.27.1.tgz", "integrity": "sha1-AOBbYYYwcNDzKSoAEmwWwOAkxO0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.27.1.tgz", "integrity": "sha1-Y/LPT23BXevBL2lORHFIY9NM0zQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz", "integrity": "sha1-8yuPeBjY/AzEbuIKjvdfBxr5duE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.27.1.tgz", "integrity": "sha1-JZxDk5coytFwasFzUbfmp76hq+s=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz", "integrity": "sha1-T50xU79ngtc91CeFqdItAxl7yR0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-numeric-separator": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz", "integrity": "sha1-YU4LFcyADlmX2t2b1upSTtbIGcY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-rest-spread": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.27.2.tgz", "integrity": "sha1-Z/mrgiNHqivO6R6JlnY9p5veqXM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.27.1.tgz", "integrity": "sha1-HJMs0nvzh0xDpcrE9D6/lwyYcbU=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-catch-binding": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz", "integrity": "sha1-hMc0Hr3jXM02sTfp5FhmglByoww=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz", "integrity": "sha1-h0zjxPBrd4BZLpRgJut2oygwRU8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.1.tgz", "integrity": "sha1-gDNLVLmxrFJEFVoMgwShh6YY1ac=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-methods": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.27.1.tgz", "integrity": "sha1-/ay6scXtgexw39u4shPWXaFItq8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-property-in-object": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz", "integrity": "sha1-TbvvKDtbLwGiHoHimfduNfkA+xE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-property-literals": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.27.1.tgz", "integrity": "sha1-B+r9YYgAWR6IBzoK8blA2aQsZCQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.1.tgz", "integrity": "sha1-Ckcd+SE0FuRM1mv2cXa2b2V2hAE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regexp-modifiers": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.27.1.tgz", "integrity": "sha1-35ulV3yXTj8USYiLcLdhaZmKbQk=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-reserved-words": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.27.1.tgz", "integrity": "sha1-QPukh4zL0cVmBaRHmjqJGsAnS7Q=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz", "integrity": "sha1-Uyq9rN7Ie/7h4O+OL83uVD/jK5A=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz", "integrity": "sha1-GiZNX8EnUJGPUOP+PiTkNxeKuwg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz", "integrity": "sha1-GJhJNdnSKWhDpJHXigFJOffc0oA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz", "integrity": "sha1-Gg6zXYuz5u/AbJ/UDrC871SDKLg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.27.1.tgz", "integrity": "sha1-cOlmu0kuA1Cc836vptzDBR+EQ2k=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-escapes": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.27.1.tgz", "integrity": "sha1-PjFD+EOK74Qt4ogW7OWHgBkM+AY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-property-regex": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.27.1.tgz", "integrity": "sha1-vf4tMXDHjFaRo8O+k0yMAIdSWVY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz", "integrity": "sha1-JZSPXDldsV9gkCjjcGZ+2Lrpr5c=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-sets-regex": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.27.1.tgz", "integrity": "sha1-arcG0Q+AG1xy2ouyVIVh+gQZPNE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/preset-env": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/preset-env/-/preset-env-7.27.2.tgz", "integrity": "sha1-EG5r+tkrWRsfb3b9TPE7dyWnv5o=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "^7.27.1", "@babel/plugin-bugfix-safari-class-field-initializer-scope": "^7.27.1", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.27.1", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.27.1", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "^7.27.1", "@babel/plugin-proposal-private-property-in-object": "7.21.0-placeholder-for-preset-env.2", "@babel/plugin-syntax-import-assertions": "^7.27.1", "@babel/plugin-syntax-import-attributes": "^7.27.1", "@babel/plugin-syntax-unicode-sets-regex": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-async-generator-functions": "^7.27.1", "@babel/plugin-transform-async-to-generator": "^7.27.1", "@babel/plugin-transform-block-scoped-functions": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-class-static-block": "^7.27.1", "@babel/plugin-transform-classes": "^7.27.1", "@babel/plugin-transform-computed-properties": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.1", "@babel/plugin-transform-dotall-regex": "^7.27.1", "@babel/plugin-transform-duplicate-keys": "^7.27.1", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-dynamic-import": "^7.27.1", "@babel/plugin-transform-exponentiation-operator": "^7.27.1", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-function-name": "^7.27.1", "@babel/plugin-transform-json-strings": "^7.27.1", "@babel/plugin-transform-literals": "^7.27.1", "@babel/plugin-transform-logical-assignment-operators": "^7.27.1", "@babel/plugin-transform-member-expression-literals": "^7.27.1", "@babel/plugin-transform-modules-amd": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-modules-systemjs": "^7.27.1", "@babel/plugin-transform-modules-umd": "^7.27.1", "@babel/plugin-transform-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-new-target": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-numeric-separator": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.2", "@babel/plugin-transform-object-super": "^7.27.1", "@babel/plugin-transform-optional-catch-binding": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-property-literals": "^7.27.1", "@babel/plugin-transform-regenerator": "^7.27.1", "@babel/plugin-transform-regexp-modifiers": "^7.27.1", "@babel/plugin-transform-reserved-words": "^7.27.1", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-spread": "^7.27.1", "@babel/plugin-transform-sticky-regex": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/plugin-transform-typeof-symbol": "^7.27.1", "@babel/plugin-transform-unicode-escapes": "^7.27.1", "@babel/plugin-transform-unicode-property-regex": "^7.27.1", "@babel/plugin-transform-unicode-regex": "^7.27.1", "@babel/plugin-transform-unicode-sets-regex": "^7.27.1", "@babel/preset-modules": "0.1.6-no-external-plugins", "babel-plugin-polyfill-corejs2": "^0.4.10", "babel-plugin-polyfill-corejs3": "^0.11.0", "babel-plugin-polyfill-regenerator": "^0.6.1", "core-js-compat": "^3.40.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-modules": {"version": "0.1.6-no-external-plugins", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz", "integrity": "sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/template/-/template-7.27.2.tgz", "integrity": "sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/traverse/-/traverse-7.27.1.tgz", "integrity": "sha1-TbdykCsTO73dHE96fuR3YcG58pE=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/template": "^7.27.1", "@babel/types": "^7.27.1", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@babel/types/-/types-7.27.1.tgz", "integrity": "sha1-ne/FPBb8iZ5GlB/GkBqe6hydhWA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "integrity": "sha1-daLotRy3WKdVPWgEpZMteqznXDk=", "dev": true, "license": "MIT"}, "node_modules/@date-fns/utc": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@date-fns/utc/-/utc-2.1.0.tgz", "integrity": "sha1-kjiWFgvTMgnXR1A+7tWQBe5EJdI=", "license": "MIT"}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "integrity": "sha1-YHCEYwxsAzmSoILebm+8GotSF1o=", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.20.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/config-array/-/config-array-0.20.0.tgz", "integrity": "sha1-ehIy6CN2cS0zQAEqL1YaJ2TRmI8=", "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-helpers": {"version": "0.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/config-helpers/-/config-helpers-0.2.2.tgz", "integrity": "sha1-N3n3a4lN46jsR2O3lmDm1U1bEBA=", "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.13.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/core/-/core-0.13.0.tgz", "integrity": "sha1-vwLyCYRtO/mW+egAnbYt8nObRYw=", "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "integrity": "sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=", "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/argparse": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/argparse/-/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "license": "Python-2.0"}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "14.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/globals/-/globals-14.0.0.tgz", "integrity": "sha1-iY10E8Kbq89rr+Vvyt3thYrack4=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/eslintrc/node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@eslint/js": {"version": "9.26.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/js/-/js-9.26.0.tgz", "integrity": "sha1-HhMSa2ej2xURHS3MYfaaKs/3C9U=", "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/object-schema/-/object-schema-2.1.6.tgz", "integrity": "sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=", "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.2.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz", "integrity": "sha1-R0iNj4FxtdRhPoMzE/POcI41Jfg=", "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.13.0", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@fastify/busboy": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@fastify/busboy/-/busboy-3.1.1.tgz", "integrity": "sha1-rzrqfx5S7JFti1ydzA8J1MBgo/w=", "license": "MIT"}, "node_modules/@firebase/app-check-interop-types": {"version": "0.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/app-check-interop-types/-/app-check-interop-types-0.3.2.tgz", "integrity": "sha1-RVtlYsej3j73XqUfct/sWCmtaZc=", "license": "Apache-2.0"}, "node_modules/@firebase/app-types": {"version": "0.9.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/app-types/-/app-types-0.9.2.tgz", "integrity": "sha1-jLzOunhHU6fABmpICbwi+Tre4IA=", "license": "Apache-2.0"}, "node_modules/@firebase/auth-interop-types": {"version": "0.2.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/auth-interop-types/-/auth-interop-types-0.2.3.tgz", "integrity": "sha1-kn8fITmmgLVf7wvdv/LJgrCFh+g=", "license": "Apache-2.0"}, "node_modules/@firebase/component": {"version": "0.6.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/component/-/component-0.6.9.tgz", "integrity": "sha1-QkjP6rIiJFraDX947Olah1dFMrQ=", "license": "Apache-2.0", "dependencies": {"@firebase/util": "1.10.0", "tslib": "^2.1.0"}}, "node_modules/@firebase/database": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/database/-/database-1.0.8.tgz", "integrity": "sha1-AbsNDLVlOuamZBUj9vCFtMG+nC8=", "license": "Apache-2.0", "dependencies": {"@firebase/app-check-interop-types": "0.3.2", "@firebase/auth-interop-types": "0.2.3", "@firebase/component": "0.6.9", "@firebase/logger": "0.4.2", "@firebase/util": "1.10.0", "faye-websocket": "0.11.4", "tslib": "^2.1.0"}}, "node_modules/@firebase/database-compat": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/database-compat/-/database-compat-1.0.8.tgz", "integrity": "sha1-aasD0A4nqJ9lSGiW6iGQlKo4wn8=", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.9", "@firebase/database": "1.0.8", "@firebase/database-types": "1.0.5", "@firebase/logger": "0.4.2", "@firebase/util": "1.10.0", "tslib": "^2.1.0"}}, "node_modules/@firebase/database-types": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/database-types/-/database-types-1.0.5.tgz", "integrity": "sha1-LZI/QuPZkRue7FN+2LXsqgzpXDc=", "license": "Apache-2.0", "dependencies": {"@firebase/app-types": "0.9.2", "@firebase/util": "1.10.0"}}, "node_modules/@firebase/logger": {"version": "0.4.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/logger/-/logger-0.4.2.tgz", "integrity": "sha1-dN/P7t7oEN64pwgNW366VqoW/6I=", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/@firebase/util": {"version": "1.10.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@firebase/util/-/util-1.10.0.tgz", "integrity": "sha1-nsirVNqCv8Mbr/DEPLKBmYy+3as=", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/@google-cloud/firestore": {"version": "7.11.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@google-cloud/firestore/-/firestore-7.11.0.tgz", "integrity": "sha1-qKK2HVBpuiP/KhuY6seNFakEpA8=", "license": "Apache-2.0", "optional": true, "dependencies": {"@opentelemetry/api": "^1.3.0", "fast-deep-equal": "^3.1.1", "functional-red-black-tree": "^1.0.1", "google-gax": "^4.3.3", "protobufjs": "^7.2.6"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@google-cloud/paginator": {"version": "5.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@google-cloud/paginator/-/paginator-5.0.2.tgz", "integrity": "sha1-hq13MmbOnzuClVqPdeIs0BLMyIk=", "license": "Apache-2.0", "optional": true, "dependencies": {"arrify": "^2.0.0", "extend": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@google-cloud/projectify": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@google-cloud/projectify/-/projectify-4.0.0.tgz", "integrity": "sha1-1gDgQz2vUbiMH6lax/AuOOgKB74=", "license": "Apache-2.0", "optional": true, "engines": {"node": ">=14.0.0"}}, "node_modules/@google-cloud/promisify": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@google-cloud/promisify/-/promisify-4.0.0.tgz", "integrity": "sha1-qQblM+vdD3VNyiUJkzM0zli4yLE=", "license": "Apache-2.0", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@google-cloud/storage": {"version": "7.16.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@google-cloud/storage/-/storage-7.16.0.tgz", "integrity": "sha1-YsBO5PgBkJku8GywM6kMBUvOpXU=", "license": "Apache-2.0", "optional": true, "dependencies": {"@google-cloud/paginator": "^5.0.0", "@google-cloud/projectify": "^4.0.0", "@google-cloud/promisify": "<4.1.0", "abort-controller": "^3.0.0", "async-retry": "^1.3.3", "duplexify": "^4.1.3", "fast-xml-parser": "^4.4.1", "gaxios": "^6.0.2", "google-auth-library": "^9.6.3", "html-entities": "^2.5.2", "mime": "^3.0.0", "p-limit": "^3.0.1", "retry-request": "^7.0.0", "teeny-request": "^9.0.0", "uuid": "^8.0.0"}, "engines": {"node": ">=14"}}, "node_modules/@google-cloud/storage/node_modules/fast-xml-parser": {"version": "4.5.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-xml-parser/-/fast-xml-parser-4.5.3.tgz", "integrity": "sha1-xU1rNaoPI9wepgtsiENAwAbcbvs=", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT", "optional": true, "dependencies": {"strnum": "^1.1.1"}, "bin": {"fxparser": "src/cli/cli.js"}}, "node_modules/@google-cloud/storage/node_modules/strnum": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/strnum/-/strnum-1.1.2.tgz", "integrity": "sha1-V7yk+6pvJxCBcV28ntfO5Uk+KOQ=", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT", "optional": true}, "node_modules/@google-cloud/storage/node_modules/uuid": {"version": "8.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uuid/-/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "license": "MIT", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@grpc/grpc-js": {"version": "1.13.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@grpc/grpc-js/-/grpc-js-1.13.3.tgz", "integrity": "sha1-atCNGGwqhlFpcIX3kMXGjqykWQQ=", "license": "Apache-2.0", "optional": true, "dependencies": {"@grpc/proto-loader": "^0.7.13", "@js-sdsl/ordered-map": "^4.4.2"}, "engines": {"node": ">=12.10.0"}}, "node_modules/@grpc/proto-loader": {"version": "0.7.15", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@grpc/proto-loader/-/proto-loader-0.7.15.tgz", "integrity": "sha1-TN+/NaNUYfyEOr6LniwHcLUJXmA=", "license": "Apache-2.0", "optional": true, "dependencies": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}, "bin": {"proto-loader-gen-types": "build/bin/proto-loader-gen-types.js"}, "engines": {"node": ">=6"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanfs/core/-/core-0.19.1.tgz", "integrity": "sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=", "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanfs/node/-/node-0.16.6.tgz", "integrity": "sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=", "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanwhocodes/retry/-/retry-0.3.1.tgz", "integrity": "sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=", "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanwhocodes/retry/-/retry-0.4.2.tgz", "integrity": "sha1-GGBHPeffoVRnZ0SPMz24DLD/IWE=", "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "integrity": "sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=", "dev": true, "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@istanbuljs/schema/-/schema-0.1.3.tgz", "integrity": "sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/console": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/console/-/console-29.7.0.tgz", "integrity": "sha1-zUgi29uEUpJlxaK9tSmjycyVD/w=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/core": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/core/-/core-29.7.0.tgz", "integrity": "sha1-tszMI58w/zZglljFpeIpF1fORI8=", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/reporters": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^29.7.0", "jest-config": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-resolve-dependencies": "^29.7.0", "jest-runner": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "jest-watcher": "^29.7.0", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/environment": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/environment/-/environment-29.7.0.tgz", "integrity": "sha1-JNYfVP8feG881Ac7S5RBY4O68qc=", "dev": true, "license": "MIT", "dependencies": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/expect/-/expect-29.7.0.tgz", "integrity": "sha1-dqPtsMt1O3Dfv+Iyg1ENPUVDK/I=", "dev": true, "license": "MIT", "dependencies": {"expect": "^29.7.0", "jest-snapshot": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect-utils": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/expect-utils/-/expect-utils-29.7.0.tgz", "integrity": "sha1-Aj7+XSaopw8hZ30KGvwPCkTjocY=", "dev": true, "license": "MIT", "dependencies": {"jest-get-type": "^29.6.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/fake-timers": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "integrity": "sha1-/ZG/H/+xbX0NJKQmqxpHpJiBpWU=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/globals": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/globals/-/globals-29.7.0.tgz", "integrity": "sha1-jZKQ+exH/3cmB/qGTKHVou+uHU0=", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/types": "^29.6.3", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/reporters": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/reporters/-/reporters-29.7.0.tgz", "integrity": "sha1-BLJi7LO4+qg7Cz0yFiOXI5Po9Mc=", "dev": true, "license": "MIT", "dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/reporters/node_modules/istanbul-lib-instrument": {"version": "6.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz", "integrity": "sha1-+hVAHfbBWHS8shBfdzMl14xmZ2U=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0", "semver": "^7.5.4"}, "engines": {"node": ">=10"}}, "node_modules/@jest/reporters/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@jest/schemas": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/schemas/-/schemas-29.6.3.tgz", "integrity": "sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=", "dev": true, "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/source-map": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/source-map/-/source-map-29.6.3.tgz", "integrity": "sha1-2Quncglc83o0peuUE/G1YqCFVMQ=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.18", "callsites": "^3.0.0", "graceful-fs": "^4.2.9"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-result": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/test-result/-/test-result-29.7.0.tgz", "integrity": "sha1-jbmoCqGgl7siYlcmhnNLrtmxZXw=", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/types": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-sequencer": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz", "integrity": "sha1-bO+XfOHTmDSjrqiHoXJmKKbwcs4=", "dev": true, "license": "MIT", "dependencies": {"@jest/test-result": "^29.7.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/transform": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/transform/-/transform-29.7.0.tgz", "integrity": "sha1-3y3Zw0bH13aLigZjmZRkDGQuKEw=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/types": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jest/types/-/types-29.6.3.tgz", "integrity": "sha1-ETH4z2NOfoTF53urEvBSr1hfulk=", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@js-sdsl/ordered-map": {"version": "4.4.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@js-sdsl/ordered-map/-/ordered-map-4.4.2.tgz", "integrity": "sha1-kpn4KHS6ueTH+cSNhlvsv+jWkHw=", "license": "MIT", "optional": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/js-sdsl"}}, "node_modules/@jsdevtools/ono": {"version": "7.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@jsdevtools/ono/-/ono-7.1.3.tgz", "integrity": "sha1-nfA7vXxpalxYiFw0qgbaQchUN5Y=", "license": "MIT"}, "node_modules/@mapbox/node-pre-gyp": {"version": "1.0.11", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11.tgz", "integrity": "sha1-QX20K39TI9eek7NKbXoqEsDfQ/o=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.11"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/agent-base": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/@mapbox/node-pre-gyp/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/tr46": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT"}, "node_modules/@mapbox/node-pre-gyp/node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@mapbox/node-pre-gyp/node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/@microsoft/applicationinsights-web-snippet": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@microsoft/applicationinsights-web-snippet/-/applicationinsights-web-snippet-1.0.1.tgz", "integrity": "sha1-a7eIspAuSL9dRgw4xrt/7daG3dc=", "license": "MIT"}, "node_modules/@modelcontextprotocol/sdk": {"version": "1.11.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@modelcontextprotocol/sdk/-/sdk-1.11.0.tgz", "integrity": "sha1-nxdi7+bzNl8L87AZzJvRYp0ZvFA=", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "eventsource": "^3.0.2", "express": "^5.0.1", "express-rate-limit": "^7.5.0", "pkce-challenge": "^5.0.0", "raw-body": "^3.0.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.1"}, "engines": {"node": ">=18"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/accepts": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/accepts/-/accepts-2.0.0.tgz", "integrity": "sha1-u89LpQdUZ/PyEx6rPP/HPC9deJU=", "license": "MIT", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/body-parser": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/body-parser/-/body-parser-2.2.0.tgz", "integrity": "sha1-96llbeMFJJpxW1Sbe4/Rq5393Po=", "license": "MIT", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/content-disposition": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/content-disposition/-/content-disposition-1.0.0.tgz", "integrity": "sha1-hEQmyzmPk0yu/LsXIgASa8fOrOI=", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/cookie-signature": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cookie-signature/-/cookie-signature-1.2.2.tgz", "integrity": "sha1-V8f8PMKTrKuf7FTXPhVpDr5KF5M=", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/express": {"version": "5.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/express/-/express-5.1.0.tgz", "integrity": "sha1-0xvq9xWgAW8NU/R9O016zyjHXMk=", "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/finalhandler": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/finalhandler/-/finalhandler-2.1.0.tgz", "integrity": "sha1-cjBjc6qJ0FqCQu1WnthqG/98Vh8=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/fresh": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fresh/-/fresh-2.0.0.tgz", "integrity": "sha1-jdffahs6Gzpc8YbAWl3SZ2ImNaQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/media-typer": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/media-typer/-/media-typer-1.1.0.tgz", "integrity": "sha1-ardLjy0zIPIGSyqHo455Mf86VWE=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/merge-descriptors": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "integrity": "sha1-6pIvZgY1oiSe5WXgRJ+VHmtgOAg=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/mime-db": {"version": "1.54.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mime-db/-/mime-db-1.54.0.tgz", "integrity": "sha1-zds+5PnGRTDf9kAjZmHULLajFPU=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/mime-types": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mime-types/-/mime-types-3.0.1.tgz", "integrity": "sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/negotiator": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/negotiator/-/negotiator-1.0.0.tgz", "integrity": "sha1-tskbtHFy1p+Tz9fDV7u1KQGbX2o=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/qs": {"version": "6.14.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/qs/-/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/raw-body": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/raw-body/-/raw-body-3.0.0.tgz", "integrity": "sha1-JbNHbwelFgBhna4/6C3cKKNuXg8=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/send": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/send/-/send-1.2.0.tgz", "integrity": "sha1-MqdVT7d3uDHfqCg3D3c6OAjTchI=", "license": "MIT", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/serve-static": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/serve-static/-/serve-static-2.2.0.tgz", "integrity": "sha1-nAJWTuJZvdIlG4LWWaLn4ZONZvk=", "license": "MIT", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/@modelcontextprotocol/sdk/node_modules/type-is": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/type-is/-/type-is-2.0.1.tgz", "integrity": "sha1-ZPbPA/kvzkAVwrIkeT9r3UsGjJc=", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/@mongodb-js/saslprep": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@mongodb-js/saslprep/-/saslprep-1.2.2.tgz", "integrity": "sha1-CVBvKcwqmdnXuVHKp//8h+UiptM=", "license": "MIT", "dependencies": {"sparse-bitfield": "^3.0.3"}}, "node_modules/@noble/hashes": {"version": "1.8.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@noble/hashes/-/hashes-1.8.0.tgz", "integrity": "sha1-zuQ9gB/O+WRLEbgZSFdpWs1fgVo=", "license": "MIT", "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@opentelemetry/api": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/api/-/api-1.9.0.tgz", "integrity": "sha1-0D66aCc9wPdQnio9XLoh6uEDef4=", "license": "Apache-2.0", "engines": {"node": ">=8.0.0"}}, "node_modules/@opentelemetry/api-logs": {"version": "0.57.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/api-logs/-/api-logs-0.57.2.tgz", "integrity": "sha1-1AAbmqNYA2e0D+iJ81QAFPdmzIc=", "license": "Apache-2.0", "dependencies": {"@opentelemetry/api": "^1.3.0"}, "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/core": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/core/-/core-1.30.1.tgz", "integrity": "sha1-oLRouzljWN+AGIFwnqOCmfwwqyc=", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/semantic-conventions/-/semantic-conventions-1.28.0.tgz", "integrity": "sha1-M3+yvKBFPQcmaW50X1AGRBH2RtY=", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation": {"version": "0.57.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/instrumentation/-/instrumentation-0.57.2.tgz", "integrity": "sha1-iSRUnXlBuhtcbwTVUpz0gzBFbR0=", "license": "Apache-2.0", "dependencies": {"@opentelemetry/api-logs": "0.57.2", "@types/shimmer": "^1.2.0", "import-in-the-middle": "^1.8.1", "require-in-the-middle": "^7.1.1", "semver": "^7.5.2", "shimmer": "^1.2.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/resources": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/resources/-/resources-1.30.1.tgz", "integrity": "sha1-pOrhfr2WlH/cemT5McpLceGM6WQ=", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.30.1", "@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/semantic-conventions/-/semantic-conventions-1.28.0.tgz", "integrity": "sha1-M3+yvKBFPQcmaW50X1AGRBH2RtY=", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/sdk-trace-base": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/sdk-trace-base/-/sdk-trace-base-1.30.1.tgz", "integrity": "sha1-QaQiNAltyY6PRU0kVR/IC4Fv6zQ=", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.30.1", "@opentelemetry/resources": "1.30.1", "@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/semantic-conventions/-/semantic-conventions-1.28.0.tgz", "integrity": "sha1-M3+yvKBFPQcmaW50X1AGRBH2RtY=", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/semantic-conventions": {"version": "1.32.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@opentelemetry/semantic-conventions/-/semantic-conventions-1.32.0.tgz", "integrity": "sha1-oV6PePMjiKfkZV5/U5Vw5AlYyj8=", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@paralleldrive/cuid2": {"version": "2.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@paralleldrive/cuid2/-/cuid2-2.2.2.tgz", "integrity": "sha1-f5E2TVO4niycueAujdDxKeg0RV8=", "license": "MIT", "dependencies": {"@noble/hashes": "^1.1.5"}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "integrity": "sha1-m4sMxmPWaafY9vXQiToU00jzD78=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/base64/-/base64-1.1.2.tgz", "integrity": "sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/codegen/-/codegen-2.0.4.tgz", "integrity": "sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "integrity": "sha1-NVy8mLr61ZePntCV85diHx0Ga3A=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/fetch/-/fetch-1.1.0.tgz", "integrity": "sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/float/-/float-1.0.2.tgz", "integrity": "sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/inquire/-/inquire-1.1.0.tgz", "integrity": "sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/path": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/path/-/path-1.1.2.tgz", "integrity": "sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/pool/-/pool-1.1.0.tgz", "integrity": "sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@protobufjs/utf8/-/utf8-1.1.0.tgz", "integrity": "sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=", "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/@scarf/scarf": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@scarf/scarf/-/scarf-1.4.0.tgz", "integrity": "sha1-O7uYQIXb1tmCSUU4tSO+HOZWKXI=", "hasInstallScript": true, "license": "Apache-2.0"}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@sinclair/typebox/-/typebox-0.27.8.tgz", "integrity": "sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=", "dev": true, "license": "MIT"}, "node_modules/@sinonjs/commons": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@sinonjs/commons/-/commons-3.0.1.tgz", "integrity": "sha1-ECk1fkTKkBphVYX20nc428iQhM0=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "10.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "integrity": "sha1-Vf3/Hsq581QBkSna9N8N1Nkj6mY=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^3.0.0"}}, "node_modules/@tootallnate/once": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@tootallnate/once/-/once-2.0.0.tgz", "integrity": "sha1-9UShSNOrNYAcH2M6dEH9h8LkhL8=", "license": "MIT", "optional": true, "engines": {"node": ">= 10"}}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "integrity": "sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/body-parser": {"version": "1.19.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/body-parser/-/body-parser-1.19.5.tgz", "integrity": "sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/caseless": {"version": "0.12.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/caseless/-/caseless-0.12.5.tgz", "integrity": "sha1-25RoyxsbWpJbjzSCLxZp3wxUcvU=", "license": "MIT", "optional": true}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/connect/-/connect-3.4.38.tgz", "integrity": "sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/estree": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/estree/-/estree-1.0.7.tgz", "integrity": "sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=", "license": "MIT"}, "node_modules/@types/express": {"version": "4.17.21", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/express/-/express-4.17.21.tgz", "integrity": "sha1-wm1KFR5g7+AISyPcM2nrxjHtGS0=", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.19.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz", "integrity": "sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/graceful-fs": {"version": "4.1.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "integrity": "sha1-Kga8D2iiCrN7PjaqI4vmq99J6LQ=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/http-errors": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/http-errors/-/http-errors-2.0.4.tgz", "integrity": "sha1-frR3JsORtzRabsNa1/TeRpz1uk8=", "license": "MIT"}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "integrity": "sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=", "dev": true, "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "integrity": "sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "integrity": "sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "license": "MIT"}, "node_modules/@types/jsonwebtoken": {"version": "9.0.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/jsonwebtoken/-/jsonwebtoken-9.0.9.tgz", "integrity": "sha1-pMOkRsDrqvRnpYOYOCYW9BY0X7M=", "license": "MIT", "dependencies": {"@types/ms": "*", "@types/node": "*"}}, "node_modules/@types/long": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/long/-/long-4.0.2.tgz", "integrity": "sha1-t0EpcZ/I0RwBhoAQCC1IO3VFWRo=", "license": "MIT", "optional": true}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/mime/-/mime-1.3.5.tgz", "integrity": "sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=", "license": "MIT"}, "node_modules/@types/ms": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/ms/-/ms-2.1.0.tgz", "integrity": "sha1-BSqmekjszEMJ1/AZG35BQ0uQu3g=", "license": "MIT"}, "node_modules/@types/node": {"version": "22.15.14", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/node/-/node-22.15.14.tgz", "integrity": "sha1-iJ/TVqBNADptVlDMwAPvTXEkMNc=", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/qs": {"version": "6.9.18", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/qs/-/qs-6.9.18.tgz", "integrity": "sha1-h3KSyqkffBshMDKzRiZQW3RmJMI=", "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/range-parser/-/range-parser-1.2.7.tgz", "integrity": "sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=", "license": "MIT"}, "node_modules/@types/request": {"version": "2.48.12", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/request/-/request-2.48.12.tgz", "integrity": "sha1-D1kPYVoQ+H2hjpeQrJTCnsTF7zA=", "license": "MIT", "optional": true, "dependencies": {"@types/caseless": "*", "@types/node": "*", "@types/tough-cookie": "*", "form-data": "^2.5.0"}}, "node_modules/@types/request/node_modules/form-data": {"version": "2.5.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/form-data/-/form-data-2.5.3.tgz", "integrity": "sha1-+bz4dBjOdIUTwMNJS7SOwnDJesw=", "license": "MIT", "optional": true, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.35", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.12"}}, "node_modules/@types/send": {"version": "0.17.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/send/-/send-0.17.4.tgz", "integrity": "sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=", "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/serve-static/-/serve-static-1.15.7.tgz", "integrity": "sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=", "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/shimmer": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/shimmer/-/shimmer-1.2.0.tgz", "integrity": "sha1-m3Bq+W+gZBaCiEI5enDfu/HBTe0=", "license": "MIT"}, "node_modules/@types/stack-utils": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/stack-utils/-/stack-utils-2.0.3.tgz", "integrity": "sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=", "dev": true, "license": "MIT"}, "node_modules/@types/tough-cookie": {"version": "4.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/tough-cookie/-/tough-cookie-4.0.5.tgz", "integrity": "sha1-y24qaRtwyxd8bjrpwdLosuqM0wQ=", "license": "MIT", "optional": true}, "node_modules/@types/webidl-conversions": {"version": "7.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz", "integrity": "sha1-Ewbb+lN2i8vPyVocjN42eXVYGFk=", "license": "MIT"}, "node_modules/@types/whatwg-url": {"version": "11.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/whatwg-url/-/whatwg-url-11.0.5.tgz", "integrity": "sha1-qqJUbmDwyZIJyhM2DDLHjK8sQJ8=", "license": "MIT", "dependencies": {"@types/webidl-conversions": "*"}}, "node_modules/@types/yargs": {"version": "17.0.33", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/yargs/-/yargs-17.0.33.tgz", "integrity": "sha1-jDIwPag+7AUKhLPHrnufki0T4y0=", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "integrity": "sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=", "dev": true, "license": "MIT"}, "node_modules/@typespec/ts-http-runtime": {"version": "0.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@typespec/ts-http-runtime/-/ts-http-runtime-0.2.2.tgz", "integrity": "sha1-oMdFjtmarm1+si78F6g5zsC0obM=", "license": "MIT", "dependencies": {"http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=", "license": "ISC"}, "node_modules/abort-controller": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/abort-controller/-/abort-controller-3.0.0.tgz", "integrity": "sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=", "license": "MIT", "optional": true, "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/accepts/-/accepts-1.3.8.tgz", "integrity": "sha1-C/C+EltnAUrcsLCSHmLbe//hay4=", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.14.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/acorn/-/acorn-8.14.1.tgz", "integrity": "sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-import-attributes": {"version": "1.9.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/acorn-import-attributes/-/acorn-import-attributes-1.9.5.tgz", "integrity": "sha1-frFVexugXvGLXtDsZ1kb+rBGiO8=", "license": "MIT", "peerDependencies": {"acorn": "^8"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/agent-base": {"version": "7.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/agent-base/-/agent-base-7.1.3.tgz", "integrity": "sha1-KUNeuCG8QZRjOluJ5bxHA7r8JaE=", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/append-field": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/append-field/-/append-field-1.0.0.tgz", "integrity": "sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY=", "license": "MIT"}, "node_modules/applicationinsights": {"version": "2.9.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/applicationinsights/-/applicationinsights-2.9.6.tgz", "integrity": "sha1-Z1KOZn15U8jdV7X7FuCkcU/Aeu0=", "license": "MIT", "dependencies": {"@azure/core-auth": "1.7.2", "@azure/core-rest-pipeline": "1.16.3", "@azure/opentelemetry-instrumentation-azure-sdk": "^1.0.0-beta.5", "@microsoft/applicationinsights-web-snippet": "1.0.1", "@opentelemetry/api": "^1.7.0", "@opentelemetry/core": "^1.19.0", "@opentelemetry/sdk-trace-base": "^1.19.0", "@opentelemetry/semantic-conventions": "^1.19.0", "cls-hooked": "^4.2.2", "continuation-local-storage": "^3.2.1", "diagnostic-channel": "1.1.1", "diagnostic-channel-publishers": "1.0.8"}, "engines": {"node": ">=8.0.0"}, "peerDependencies": {"applicationinsights-native-metrics": "*"}, "peerDependenciesMeta": {"applicationinsights-native-metrics": {"optional": true}}}, "node_modules/applicationinsights/node_modules/@azure/core-auth": {"version": "1.7.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-auth/-/core-auth-1.7.2.tgz", "integrity": "sha1-VYt8t90SsAvuwHrl31kH103x69k=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.1.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/applicationinsights/node_modules/@azure/core-rest-pipeline": {"version": "1.16.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@azure/core-rest-pipeline/-/core-rest-pipeline-1.16.3.tgz", "integrity": "sha1-veO8PrrX+IXd2d5q9eWo/CVLKH4=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.4.0", "@azure/core-tracing": "^1.0.1", "@azure/core-util": "^1.9.0", "@azure/logger": "^1.0.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/aproba": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/aproba/-/aproba-2.0.0.tgz", "integrity": "sha1-UlILiuW1aSFbNU78DKo/4eRaitw=", "license": "ISC"}, "node_modules/are-we-there-yet": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz", "integrity": "sha1-Ny4Oe9J52OlMZTqqH2cgCIS/Phw=", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">=10"}}, "node_modules/argparse": {"version": "1.0.10", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/argparse/-/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "license": "MIT"}, "node_modules/arrify": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/arrify/-/arrify-2.0.1.tgz", "integrity": "sha1-yWVekzHgq81YjSp8rX6ZVvZnAfo=", "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=", "license": "MIT"}, "node_modules/async-hook-jl": {"version": "1.7.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/async-hook-jl/-/async-hook-jl-1.7.6.tgz", "integrity": "sha1-T9JcL4ZNuvJ5xhDXO/l7GyhZXmg=", "license": "MIT", "dependencies": {"stack-chain": "^1.3.7"}, "engines": {"node": "^4.7 || >=6.9 || >=7.3"}}, "node_modules/async-listener": {"version": "0.6.10", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/async-listener/-/async-listener-0.6.10.tgz", "integrity": "sha1-p8l6vlcLpgLXgic8DeYKUePhfLw=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"semver": "^5.3.0", "shimmer": "^1.1.0"}, "engines": {"node": "<=0.11.8 || >0.11.10"}}, "node_modules/async-listener/node_modules/semver": {"version": "5.7.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/async-retry": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/async-retry/-/async-retry-1.3.3.tgz", "integrity": "sha1-Dn82wE2EeOeli9vtgM7fl3eF8oA=", "license": "MIT", "optional": true, "dependencies": {"retry": "0.13.1"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/axios": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/axios/-/axios-1.9.0.tgz", "integrity": "sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/babel-jest": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/babel-jest/-/babel-jest-29.7.0.tgz", "integrity": "sha1-9DaZGSJbaExWCFmYrGPb0FvgINU=", "dev": true, "license": "MIT", "dependencies": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "integrity": "sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-jest-hoist": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "integrity": "sha1-qtvpQ0ZBgqiSLDySfDBn/0DSRiY=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/babel-plugin-polyfill-corejs2": {"version": "0.4.13", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.13.tgz", "integrity": "sha1-fURfDgYH68j7awHX6PsCBpuR3Ys=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.4", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-corejs3": {"version": "0.11.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.11.1.tgz", "integrity": "sha1-Tk4YLxuzfHumLir4HY3QnfMTRPY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.3", "core-js-compat": "^3.40.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-regenerator": {"version": "0.6.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.4.tgz", "integrity": "sha1-QoxhXTwXcpKiK0+T7ZnjWNeQaps=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.4"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-preset-current-node-syntax": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "integrity": "sha1-mpKer+zkGWEu9K5PYLGGLrrY7zA=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-preset-jest": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "integrity": "sha1-+gX6UQ59STiW17DdIDNgHIQPFxw=", "dev": true, "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bcrypt": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/bcrypt/-/bcrypt-5.1.1.tgz", "integrity": "sha1-D3MsbctOEuW3CiXjJqcpZYebpuI=", "hasInstallScript": true, "license": "MIT", "dependencies": {"@mapbox/node-pre-gyp": "^1.0.11", "node-addon-api": "^5.0.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/bignumber.js": {"version": "9.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/bignumber.js/-/bignumber.js-9.3.0.tgz", "integrity": "sha1-vbp+KkwaLroIKQ6NytTzY5PJKs0=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/binary-extensions/-/binary-extensions-2.3.0.tgz", "integrity": "sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/body-parser": {"version": "1.20.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/body-parser/-/body-parser-1.20.3.tgz", "integrity": "sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/braces/-/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.24.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/browserslist/-/browserslist-4.24.5.tgz", "integrity": "sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001716", "electron-to-chromium": "^1.5.149", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bser": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/bser/-/bser-2.1.1.tgz", "integrity": "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=", "dev": true, "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/bson": {"version": "6.10.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/bson/-/bson-6.10.3.tgz", "integrity": "sha1-X5pGOva4PiZL7dCLI20TVqMO2kc=", "license": "Apache-2.0", "engines": {"node": ">=16.20.1"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=", "license": "MIT"}, "node_modules/bundle-name": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/bundle-name/-/bundle-name-4.1.0.tgz", "integrity": "sha1-87lrNBYNZDGhnXaIE1r3z7h5eIk=", "license": "MIT", "dependencies": {"run-applescript": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/busboy": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/busboy/-/busboy-1.6.0.tgz", "integrity": "sha1-lm6japUC5DzbkUaWJSO5L1MfaJM=", "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-me-maybe": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/call-me-maybe/-/call-me-maybe-1.0.2.tgz", "integrity": "sha1-A/lk8ZUiumQ7GwaTrLkVL+IHS6o=", "license": "MIT"}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001717", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001717.tgz", "integrity": "sha1-XZ/sXOCXlqGJMBOCVRBniSisoSk=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/char-regex": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/char-regex/-/char-regex-1.0.2.tgz", "integrity": "sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/chokidar": {"version": "3.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/chokidar/-/chokidar-3.6.0.tgz", "integrity": "sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=", "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/chownr": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/chownr/-/chownr-2.0.0.tgz", "integrity": "sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/ci-info": {"version": "3.9.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ci-info/-/ci-info-3.9.0.tgz", "integrity": "sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cjs-module-lexer": {"version": "1.4.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz", "integrity": "sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=", "license": "MIT"}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cliui/-/cliui-8.0.1.tgz", "integrity": "sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=", "devOptional": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/cls-hooked": {"version": "4.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cls-hooked/-/cls-hooked-4.2.2.tgz", "integrity": "sha1-rS6aQJJoDNr/6y01UdoOIl6uGQg=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"async-hook-jl": "^1.7.6", "emitter-listener": "^1.0.1", "semver": "^5.4.1"}, "engines": {"node": "^4.7 || >=6.9 || >=7.3 || >=8.2.1"}}, "node_modules/cls-hooked/node_modules/semver": {"version": "5.7.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/co": {"version": "4.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "dev": true, "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/collect-v8-coverage": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "integrity": "sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=", "dev": true, "license": "MIT"}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "license": "MIT"}, "node_modules/color-support": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/color-support/-/color-support-1.1.3.tgz", "integrity": "sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=", "license": "ISC", "bin": {"color-support": "bin.js"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "6.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/commander/-/commander-6.2.0.tgz", "integrity": "sha1-uZC/uKwDCu3G0RvATRSI/+9W23U=", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/component-emitter": {"version": "1.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/component-emitter/-/component-emitter-1.3.1.tgz", "integrity": "sha1-7x1XlvfZPxNe5vtoQ0CyZAPJfRc=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "license": "MIT"}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/concat-stream/node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/concat-stream/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/concat-stream/node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/console-control-strings": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "license": "ISC"}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha1-i4K076yCUSoCuwsdzsnSxejrW/4=", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/content-type/-/content-type-1.0.5.tgz", "integrity": "sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/continuation-local-storage": {"version": "3.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/continuation-local-storage/-/continuation-local-storage-3.2.1.tgz", "integrity": "sha1-EfYT906RT+mzTJKtLSj+auHbf/s=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"async-listener": "^0.6.0", "emitter-listener": "^1.1.1"}}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=", "dev": true, "license": "MIT"}, "node_modules/cookie": {"version": "0.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cookie/-/cookie-0.7.1.tgz", "integrity": "sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "license": "MIT"}, "node_modules/cookiejar": {"version": "2.1.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cookiejar/-/cookiejar-2.1.4.tgz", "integrity": "sha1-7macH+os9C3DFYVGnRk/7w1ldxs=", "license": "MIT"}, "node_modules/core-js-compat": {"version": "3.42.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/core-js-compat/-/core-js-compat-3.42.0.tgz", "integrity": "sha1-zhnClwbuWAbibTyzxULUz8DtUbs=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.24.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cors/-/cors-2.8.5.tgz", "integrity": "sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/create-jest": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/create-jest/-/create-jest-29.7.0.tgz", "integrity": "sha1-o1XFs8seGvAroXf+ev1/7uSaUyA=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "prompts": "^2.0.1"}, "bin": {"create-jest": "bin/create-jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/data-uri-to-buffer": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "integrity": "sha1-2P6ysogeak9YwuCKz9Dig04mIi4=", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/date-fns": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/date-fns/-/date-fns-4.1.0.tgz", "integrity": "sha1-ZLPYP/9aqAQ49bGmM8LoO4ocLRQ=", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}}, "node_modules/debug": {"version": "4.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/debug/-/debug-4.4.0.tgz", "integrity": "sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/dedent": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/dedent/-/dedent-1.6.0.tgz", "integrity": "sha1-edUtY4mx/6Z9K871m6UYR6nVA7I=", "dev": true, "license": "MIT", "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "license": "MIT"}, "node_modules/deepmerge": {"version": "4.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/deepmerge/-/deepmerge-4.3.1.tgz", "integrity": "sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/default-browser": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/default-browser/-/default-browser-5.2.1.tgz", "integrity": "sha1-e3umEgT/PkJbVWhprm0+nZ8XEs8=", "license": "MIT", "dependencies": {"bundle-name": "^4.1.0", "default-browser-id": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser-id": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/default-browser-id/-/default-browser-id-5.0.0.tgz", "integrity": "sha1-odmL+WDBUILYo/pp6DFQzMzDryY=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-lazy-prop": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz", "integrity": "sha1-27Ga37dG1/xtc0oGty9KANAhJV8=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/delegates": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/delegates/-/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=", "license": "MIT"}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/depd/-/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/destroy/-/destroy-1.2.0.tgz", "integrity": "sha1-SANzVQmti+VSk0xn32FPlOZvoBU=", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-libc": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/detect-libc/-/detect-libc-2.0.4.tgz", "integrity": "sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=", "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/detect-newline": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/detect-newline/-/detect-newline-3.1.0.tgz", "integrity": "sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/dezalgo": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/dezalgo/-/dezalgo-1.0.4.tgz", "integrity": "sha1-dRI1JgRpCEwTIVffqFfzhtTDPYE=", "license": "ISC", "dependencies": {"asap": "^2.0.0", "wrappy": "1"}}, "node_modules/diagnostic-channel": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/diagnostic-channel/-/diagnostic-channel-1.1.1.tgz", "integrity": "sha1-RLYJct6e4FXBYhZTWw6ds/ag79A=", "license": "MIT", "dependencies": {"semver": "^7.5.3"}}, "node_modules/diagnostic-channel-publishers": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/diagnostic-channel-publishers/-/diagnostic-channel-publishers-1.0.8.tgz", "integrity": "sha1-cAVXqQLEQ8sR+Znxn1CouzvkkKA=", "license": "MIT", "peerDependencies": {"diagnostic-channel": "*"}}, "node_modules/diagnostic-channel/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/diff-sequences": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/diff-sequences/-/diff-sequences-29.6.3.tgz", "integrity": "sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dotenv": {"version": "16.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/dotenv/-/dotenv-16.5.0.tgz", "integrity": "sha1-CStJ8l+AjwIAUAUdH/JY5ATHhpI=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/duplexify": {"version": "4.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/duplexify/-/duplexify-4.1.3.tgz", "integrity": "sha1-oH4cDQosABFYVj0yWSuli92wI28=", "license": "MIT", "optional": true, "dependencies": {"end-of-stream": "^1.4.1", "inherits": "^2.0.3", "readable-stream": "^3.1.1", "stream-shift": "^1.0.2"}}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "integrity": "sha1-rg8PothQRe8UqBfao86azQSJ5b8=", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.150", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.150.tgz", "integrity": "sha1-MSC/NEU6eoLLTZM13yBoCyu0Bkk=", "dev": true, "license": "ISC"}, "node_modules/emitter-listener": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/emitter-listener/-/emitter-listener-1.1.2.tgz", "integrity": "sha1-VrFA6PaZI3Wz18ssqxzHQy2WMug=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"shimmer": "^1.2.0"}}, "node_modules/emittery": {"version": "0.13.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/emittery/-/emittery-0.13.1.tgz", "integrity": "sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha1-e46omAd9fkCdOsRUdOo46vCFelg=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "license": "MIT", "optional": true, "dependencies": {"once": "^1.4.0"}}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/escalade/-/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "devOptional": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.26.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eslint/-/eslint-9.26.0.tgz", "integrity": "sha1-l4/gKa3CrO7SirQ3vKh26DRhw7Q=", "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.13.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.26.0", "@eslint/plugin-kit": "^0.2.8", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@modelcontextprotocol/sdk": "^1.8.0", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "zod": "^3.24.2"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-scope": {"version": "8.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eslint-scope/-/eslint-scope-8.3.0.tgz", "integrity": "sha1-EM06kY/91yL18/e1uD25sjyHNA0=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz", "integrity": "sha1-aHussq+IT83aim59ZcYG9GoUzUU=", "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/find-up": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/find-up/-/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/locate-path": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/p-locate": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/espree": {"version": "10.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/espree/-/espree-10.3.0.tgz", "integrity": "sha1-KSZ89bDLmHNbZeZLoH4O1J0e7Yo=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.14.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/esprima/-/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/esquery/-/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=", "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/events/-/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/eventsource": {"version": "3.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eventsource/-/eventsource-3.0.6.tgz", "integrity": "sha1-XEskzXDAMj7tJlGl7ge9S8OR5lY=", "license": "MIT", "dependencies": {"eventsource-parser": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/eventsource-parser": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eventsource-parser/-/eventsource-parser-3.0.1.tgz", "integrity": "sha1-XjWNuppVumTKkNqIPEyjW9gkZ70=", "license": "MIT", "engines": {"node": ">=18.0.0"}}, "node_modules/execa": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/execa/-/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/exit": {"version": "0.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/exit/-/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/expect": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/expect/-/expect-29.7.0.tgz", "integrity": "sha1-V4h0WQ3LMhRRQITAgRXYruYeEbw=", "dev": true, "license": "MIT", "dependencies": {"@jest/expect-utils": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/express": {"version": "4.21.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/express/-/express-4.21.2.tgz", "integrity": "sha1-zyUOSDYhdOrWzqSlZqvvAWLB7DI=", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express-rate-limit": {"version": "7.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/express-rate-limit/-/express-rate-limit-7.5.0.tgz", "integrity": "sha1-ameZCnJLT7vGkRlBn+71DFHoso8=", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/express-rate-limit"}, "peerDependencies": {"express": "^4.11 || 5 || ^5.0.0-beta.1"}}, "node_modules/express-session": {"version": "1.18.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/express-session/-/express-session-1.18.1.tgz", "integrity": "sha1-iNC71Bh4iChA8k7GInST/LFn6NU=", "license": "MIT", "dependencies": {"cookie": "0.7.2", "cookie-signature": "1.0.7", "debug": "2.6.9", "depd": "~2.0.0", "on-headers": "~1.0.2", "parseurl": "~1.3.3", "safe-buffer": "5.2.1", "uid-safe": "~2.1.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/express-session/node_modules/cookie": {"version": "0.7.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cookie/-/cookie-0.7.2.tgz", "integrity": "sha1-VWNpxHKiupEPKXmJG1JrNDYjftc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express-session/node_modules/cookie-signature": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cookie-signature/-/cookie-signature-1.0.7.tgz", "integrity": "sha1-q13Xq3V8VOYPN+9lUPSBxCbRBFQ=", "license": "MIT"}, "node_modules/express-session/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express-session/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/express-validator": {"version": "7.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/express-validator/-/express-validator-7.2.1.tgz", "integrity": "sha1-hAPer4EPm+3toKT9cRaAPkbxIsI=", "license": "MIT", "dependencies": {"lodash": "^4.17.21", "validator": "~13.12.0"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/extend": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/extend/-/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=", "license": "MIT"}, "node_modules/farmhash-modern": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/farmhash-modern/-/farmhash-modern-1.1.0.tgz", "integrity": "sha1-w2s0rRlikNV7C0gtyJ5jfQtZg18=", "license": "MIT", "engines": {"node": ">=18.0.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "license": "MIT"}, "node_modules/fast-safe-stringify": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz", "integrity": "sha1-xAaoO25w2eNc47MKgRQd8wrrqIQ=", "license": "MIT"}, "node_modules/fast-xml-parser": {"version": "5.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-xml-parser/-/fast-xml-parser-5.2.2.tgz", "integrity": "sha1-3ah0RrYj2BKQih78Xz+CRaj2pks=", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT", "dependencies": {"strnum": "^2.1.0"}, "bin": {"fxparser": "src/cli/cli.js"}}, "node_modules/faye-websocket": {"version": "0.11.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/faye-websocket/-/faye-websocket-0.11.4.tgz", "integrity": "sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=", "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/fb-watchman": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fb-watchman/-/fb-watchman-2.0.2.tgz", "integrity": "sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=", "dev": true, "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "node_modules/fetch-blob": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha1-8JuNS71Frcbwwgt+eH55PjCdzOk=", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/file-entry-cache": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/file-entry-cache/-/file-entry-cache-8.0.0.tgz", "integrity": "sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=", "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/finalhandler/-/finalhandler-1.3.1.tgz", "integrity": "sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/find-up": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/firebase-admin": {"version": "12.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/firebase-admin/-/firebase-admin-12.7.0.tgz", "integrity": "sha1-WGye2FLEuy1NcvDVLBxIorbe55M=", "license": "Apache-2.0", "dependencies": {"@fastify/busboy": "^3.0.0", "@firebase/database-compat": "1.0.8", "@firebase/database-types": "1.0.5", "@types/node": "^22.0.1", "farmhash-modern": "^1.1.0", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.1.0", "node-forge": "^1.3.1", "uuid": "^10.0.0"}, "engines": {"node": ">=14"}, "optionalDependencies": {"@google-cloud/firestore": "^7.7.0", "@google-cloud/storage": "^7.7.0"}}, "node_modules/flat-cache": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/flat-cache/-/flat-cache-4.0.1.tgz", "integrity": "sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=", "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/flatted/-/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/form-data/-/form-data-4.0.2.tgz", "integrity": "sha1-Ncq73TDDznPessQtPI0+2cpReUw=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/formdata-polyfill": {"version": "4.0.10", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha1-JIB8McnUAuACqz2McgFEzriEhCM=", "license": "MIT", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/formidable": {"version": "3.5.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/formidable/-/formidable-3.5.4.tgz", "integrity": "sha1-rJpZO5UegpsymPIaqaIkOTLzLtk=", "license": "MIT", "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "dezalgo": "^1.0.4", "once": "^1.4.0"}, "engines": {"node": ">=14.0.0"}, "funding": {"url": "https://ko-fi.com/tunnckoCore/commissions"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-minipass": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fs-minipass/-/fs-minipass-2.1.0.tgz", "integrity": "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/minipass/-/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs-minipass/node_modules/yallist": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yallist/-/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "license": "ISC"}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functional-red-black-tree": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "license": "MIT", "optional": true}, "node_modules/gauge": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/gauge/-/gauge-3.0.2.tgz", "integrity": "sha1-A79EQcBEODkIvPoGVq2RgDJZs5U=", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.2", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.1", "object-assign": "^4.1.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2"}, "engines": {"node": ">=10"}}, "node_modules/gaxios": {"version": "6.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/gaxios/-/gaxios-6.7.1.tgz", "integrity": "sha1-69n3CT7eO6UCaF5zOQJIu1t/cfs=", "license": "Apache-2.0", "dependencies": {"extend": "^3.0.2", "https-proxy-agent": "^7.0.1", "is-stream": "^2.0.0", "node-fetch": "^2.6.9", "uuid": "^9.0.1"}, "engines": {"node": ">=14"}}, "node_modules/gaxios/node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/gaxios/node_modules/tr46": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT"}, "node_modules/gaxios/node_modules/uuid": {"version": "9.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uuid/-/uuid-9.0.1.tgz", "integrity": "sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/gaxios/node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/gaxios/node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/gcp-metadata": {"version": "5.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/gcp-metadata/-/gcp-metadata-5.3.0.tgz", "integrity": "sha1-b0XrRz0MtH0VABR2tItmN0TSVAg=", "license": "Apache-2.0", "optional": true, "peer": true, "dependencies": {"gaxios": "^5.0.0", "json-bigint": "^1.0.0"}, "engines": {"node": ">=12"}}, "node_modules/gcp-metadata/node_modules/agent-base": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/gcp-metadata/node_modules/gaxios": {"version": "5.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/gaxios/-/gaxios-5.1.3.tgz", "integrity": "sha1-9/qS2g/hl8hGRB5erSVz1JeekBM=", "license": "Apache-2.0", "optional": true, "peer": true, "dependencies": {"extend": "^3.0.2", "https-proxy-agent": "^5.0.0", "is-stream": "^2.0.0", "node-fetch": "^2.6.9"}, "engines": {"node": ">=12"}}, "node_modules/gcp-metadata/node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/gcp-metadata/node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/gcp-metadata/node_modules/tr46": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT", "optional": true, "peer": true}, "node_modules/gcp-metadata/node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>", "optional": true, "peer": true}, "node_modules/gcp-metadata/node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "devOptional": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-package-type": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/get-package-type/-/get-package-type-0.1.0.tgz", "integrity": "sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=", "dev": true, "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/glob/-/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "deprecated": "Glob versions prior to v9 are no longer supported", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/globals": {"version": "11.12.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/google-auth-library": {"version": "9.15.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/google-auth-library/-/google-auth-library-9.15.1.tgz", "integrity": "sha1-DF2E7RiQsjdfHNdPA6x7gGs5KSg=", "license": "Apache-2.0", "dependencies": {"base64-js": "^1.3.0", "ecdsa-sig-formatter": "^1.0.11", "gaxios": "^6.1.1", "gcp-metadata": "^6.1.0", "gtoken": "^7.0.0", "jws": "^4.0.0"}, "engines": {"node": ">=14"}}, "node_modules/google-auth-library/node_modules/gcp-metadata": {"version": "6.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/gcp-metadata/-/gcp-metadata-6.1.1.tgz", "integrity": "sha1-9lqmn1RrxW4RYGHRN9P1+QvexJQ=", "license": "Apache-2.0", "dependencies": {"gaxios": "^6.1.1", "google-logging-utils": "^0.0.2", "json-bigint": "^1.0.0"}, "engines": {"node": ">=14"}}, "node_modules/google-gax": {"version": "4.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/google-gax/-/google-gax-4.6.0.tgz", "integrity": "sha1-RVyUSW2A7PDLoROwRRqIO3vO4P0=", "license": "Apache-2.0", "optional": true, "dependencies": {"@grpc/grpc-js": "^1.10.9", "@grpc/proto-loader": "^0.7.13", "@types/long": "^4.0.0", "abort-controller": "^3.0.0", "duplexify": "^4.0.0", "google-auth-library": "^9.3.0", "node-fetch": "^2.7.0", "object-hash": "^3.0.0", "proto3-json-serializer": "^2.0.2", "protobufjs": "^7.3.2", "retry-request": "^7.0.0", "uuid": "^9.0.1"}, "engines": {"node": ">=14"}}, "node_modules/google-gax/node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "optional": true, "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/google-gax/node_modules/tr46": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT", "optional": true}, "node_modules/google-gax/node_modules/uuid": {"version": "9.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uuid/-/uuid-9.0.1.tgz", "integrity": "sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/google-gax/node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>", "optional": true}, "node_modules/google-gax/node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "optional": true, "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/google-logging-utils": {"version": "0.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/google-logging-utils/-/google-logging-utils-0.0.2.tgz", "integrity": "sha1-X9g34G+jNNpFBDO54+GHDBWURmo=", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/googleapis": {"version": "140.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/googleapis/-/googleapis-140.0.1.tgz", "integrity": "sha1-/jRzU3YfIaKVl3cYYCZHX60I7g8=", "license": "Apache-2.0", "dependencies": {"google-auth-library": "^9.0.0", "googleapis-common": "^7.0.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/googleapis-common": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/googleapis-common/-/googleapis-common-7.2.0.tgz", "integrity": "sha1-XBkQLJrx5dJ1YL5eae4sz2h1XUI=", "license": "Apache-2.0", "dependencies": {"extend": "^3.0.2", "gaxios": "^6.0.3", "google-auth-library": "^9.7.0", "qs": "^6.7.0", "url-template": "^2.0.8", "uuid": "^9.0.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/googleapis-common/node_modules/uuid": {"version": "9.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uuid/-/uuid-9.0.1.tgz", "integrity": "sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/gopd/-/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "dev": true, "license": "ISC"}, "node_modules/gtoken": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/gtoken/-/gtoken-7.1.0.tgz", "integrity": "sha1-1htOvRATIiKBf3IiseYGS9Rj/CY=", "license": "MIT", "dependencies": {"gaxios": "^6.0.0", "jws": "^4.0.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-unicode": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "license": "ISC"}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/hasown/-/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/healtether.models": {"version": "1.7.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/healtether.models/-/healtether.models-1.7.9.tgz", "integrity": "sha1-LVN9VEVSQ+s+eM2+47xd9LimTz8=", "license": "ISC", "dependencies": {"eslint": "^9.23.0"}}, "node_modules/html-entities": {"version": "2.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/html-entities/-/html-entities-2.6.0.tgz", "integrity": "sha1-fGTx6js2gYzK49P7SLaXQgjphPg=", "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "license": "MIT", "optional": true}, "node_modules/html-escaper": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/html-escaper/-/html-escaper-2.0.2.tgz", "integrity": "sha1-***************************=", "dev": true, "license": "MIT"}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-parser-js": {"version": "0.5.10", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/http-parser-js/-/http-parser-js-0.5.10.tgz", "integrity": "sha1-syd71tftVYjiDqc79yT8vkRgkHU=", "license": "MIT"}, "node_modules/http-proxy-agent": {"version": "7.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz", "integrity": "sha1-mosfJGhmwChQlIZYX2K48sGMJw4=", "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "integrity": "sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/human-signals": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ignore/-/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/ignore-by-default": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ignore-by-default/-/ignore-by-default-1.0.1.tgz", "integrity": "sha1-SMptcvbGo68Aqa1K5odr44ieKwk=", "license": "ISC"}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/import-in-the-middle": {"version": "1.13.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/import-in-the-middle/-/import-in-the-middle-1.13.1.tgz", "integrity": "sha1-eJZR+ek92QKlowb0matR63KwOhI=", "license": "Apache-2.0", "dependencies": {"acorn": "^8.14.0", "acorn-import-attributes": "^1.9.5", "cjs-module-lexer": "^1.2.2", "module-details-from-path": "^1.0.3"}}, "node_modules/import-local": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/import-local/-/import-local-3.2.0.tgz", "integrity": "sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/ip-address": {"version": "9.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ip-address/-/ip-address-9.0.5.tgz", "integrity": "sha1-EXqWCBmwh4DDvR8U7zwcwdPz6lo=", "license": "MIT", "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">= 12"}}, "node_modules/ip-address/node_modules/sprintf-js": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/sprintf-js/-/sprintf-js-1.1.3.tgz", "integrity": "sha1-SRS5A6L4toXRf994pw6RfocuREo=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true, "license": "MIT"}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-docker/-/is-docker-3.0.0.tgz", "integrity": "sha1-kAk6oxBid9inelkQ265xdH4VogA=", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-fn": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "integrity": "sha1-fRQK3DiarzARqPKipM+m+q3/sRg=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-inside-container": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-inside-container/-/is-inside-container-1.0.0.tgz", "integrity": "sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=", "license": "MIT", "dependencies": {"is-docker": "^3.0.0"}, "bin": {"is-inside-container": "cli.js"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-promise": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-promise/-/is-promise-4.0.0.tgz", "integrity": "sha1-Qv+fhCBsGZHSbev1IN1cAQQt0vM=", "license": "MIT"}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-wsl": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-wsl/-/is-wsl-3.1.0.tgz", "integrity": "sha1-4cZX45wQCQr8vt7GFyD2uSTDy9I=", "license": "MIT", "dependencies": {"is-inside-container": "^1.0.0"}, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isarray": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "license": "ISC"}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "integrity": "sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "integrity": "sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "integrity": "sha1-kIMFusmlvRdaxqdEier9D8JEWn0=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-report/node_modules/make-dir": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/make-dir/-/make-dir-4.0.0.tgz", "integrity": "sha1-w8IwencSd82WODBfkVwprnQbYU4=", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/istanbul-lib-report/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "integrity": "sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "integrity": "sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest/-/jest-29.7.0.tgz", "integrity": "sha1-mUZ2/CQXfwiPHF43N/VpcgT/JhM=", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^29.7.0", "@jest/types": "^29.6.3", "import-local": "^3.0.2", "jest-cli": "^29.7.0"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-changed-files": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-changed-files/-/jest-changed-files-29.7.0.tgz", "integrity": "sha1-HAbQfnfHjhWF0CBCTe3BDW4XrDo=", "dev": true, "license": "MIT", "dependencies": {"execa": "^5.0.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-circus": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-circus/-/jest-circus-29.7.0.tgz", "integrity": "sha1-toF6RfzINdixbVli0MAmRz7jZoo=", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^1.0.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.7.0", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0", "pretty-format": "^29.7.0", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-cli": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-cli/-/jest-cli-29.7.0.tgz", "integrity": "sha1-VZLJQHmODK5nfuwWkmTy2DmjeZU=", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "chalk": "^4.0.0", "create-jest": "^29.7.0", "exit": "^0.1.2", "import-local": "^3.0.2", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "yargs": "^17.3.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-config": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-config/-/jest-config-29.7.0.tgz", "integrity": "sha1-vL2ogG28wBseMWpGu3QIWoSwJF8=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/test-sequencer": "^29.7.0", "@jest/types": "^29.6.3", "babel-jest": "^29.7.0", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-circus": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-get-type": "^29.6.3", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-runner": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@types/node": "*", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/jest-diff": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-diff/-/jest-diff-29.7.0.tgz", "integrity": "sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-docblock": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-docblock/-/jest-docblock-29.7.0.tgz", "integrity": "sha1-j922rcPNyVXJPiqH9hz9NQ1dEZo=", "dev": true, "license": "MIT", "dependencies": {"detect-newline": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-each": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-each/-/jest-each-29.7.0.tgz", "integrity": "sha1-FiqbPyMovdmRvqq/+7dHReVld9E=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "jest-util": "^29.7.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-environment-node": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-environment-node/-/jest-environment-node-29.7.0.tgz", "integrity": "sha1-C5PhEd2o7BILyDAObR+5V24WQ3Y=", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-get-type": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-get-type/-/jest-get-type-29.6.3.tgz", "integrity": "sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E=", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-haste-map": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-haste-map/-/jest-haste-map-29.7.0.tgz", "integrity": "sha1-PCOWUkSC9aBQY3bmyFjDu8wXsQQ=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/jest-leak-detector": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz", "integrity": "sha1-W37A2t/f7Ayjg9yaoBbTa16kxyg=", "dev": true, "license": "MIT", "dependencies": {"jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz", "integrity": "sha1-ro/sef8kn9WSzoDj7kdOg6bETxI=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-message-util": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-message-util/-/jest-message-util-29.7.0.tgz", "integrity": "sha1-i8OS4gTpXf51ZKu+cqQE4o5R9/M=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-mock": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-mock/-/jest-mock-29.7.0.tgz", "integrity": "sha1-ToNs9g6Zxvz6vp+Z0Bfz/dUKY0c=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-pnp-resolver": {"version": "1.2.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "integrity": "sha1-kwsVRhZNStWTfVVA5xHU041MrS4=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}}, "node_modules/jest-regex-util": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "integrity": "sha1-SlVtnHdq9o4cX0gZT00DJ9JOilI=", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-resolve/-/jest-resolve-29.7.0.tgz", "integrity": "sha1-ZNaomS3Sb2NasMAeXu9Dmca8vDA=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve-dependencies": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz", "integrity": "sha1-GwTywJXzf8d2/0CAPckpIbHohCg=", "dev": true, "license": "MIT", "dependencies": {"jest-regex-util": "^29.6.3", "jest-snapshot": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runner": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-runner/-/jest-runner-29.7.0.tgz", "integrity": "sha1-gJrwctQIpT3P0uhJpMl20xMvcY4=", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/environment": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.13.1", "graceful-fs": "^4.2.9", "jest-docblock": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-leak-detector": "^29.7.0", "jest-message-util": "^29.7.0", "jest-resolve": "^29.7.0", "jest-runtime": "^29.7.0", "jest-util": "^29.7.0", "jest-watcher": "^29.7.0", "jest-worker": "^29.7.0", "p-limit": "^3.1.0", "source-map-support": "0.5.13"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runtime": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-runtime/-/jest-runtime-29.7.0.tgz", "integrity": "sha1-7+yzFBz303Z6OgzI98mZBYfT2Bc=", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/globals": "^29.7.0", "@jest/source-map": "^29.6.3", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0", "strip-bom": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-snapshot": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-snapshot/-/jest-snapshot-29.7.0.tgz", "integrity": "sha1-wsV0w/UYZdobsykDZ3imm/iKa+U=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-jsx": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/types": "^7.3.3", "@jest/expect-utils": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^29.7.0", "graceful-fs": "^4.2.9", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "natural-compare": "^1.4.0", "pretty-format": "^29.7.0", "semver": "^7.5.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-snapshot/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/jest-util": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-util/-/jest-util-29.7.0.tgz", "integrity": "sha1-I8K2K/sivoK0TemAVYAv83EPwLw=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-validate/-/jest-validate-29.7.0.tgz", "integrity": "sha1-e/cFURxk2lkdRrFfzkFADVIUfZw=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate/node_modules/camelcase": {"version": "6.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/camelcase/-/camelcase-6.3.0.tgz", "integrity": "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watch-typeahead": {"version": "2.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-watch-typeahead/-/jest-watch-typeahead-2.2.2.tgz", "integrity": "sha1-VRbTzQBkhcqlz8m9HeQPH4sTar8=", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^6.0.0", "chalk": "^5.2.0", "jest-regex-util": "^29.0.0", "jest-watcher": "^29.0.0", "slash": "^5.0.0", "string-length": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": "^14.17.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"jest": "^27.0.0 || ^28.0.0 || ^29.0.0"}}, "node_modules/jest-watch-typeahead/node_modules/ansi-escapes": {"version": "6.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ansi-escapes/-/ansi-escapes-6.2.1.tgz", "integrity": "sha1-dsVM6bCB2tOazsS11TN3kTgl+w8=", "dev": true, "license": "MIT", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watch-typeahead/node_modules/ansi-regex": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ansi-regex/-/ansi-regex-6.1.0.tgz", "integrity": "sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/jest-watch-typeahead/node_modules/chalk": {"version": "5.4.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/chalk/-/chalk-5.4.1.tgz", "integrity": "sha1-G0i/CWPsFY3OKqz2nAk64t0gktg=", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-watch-typeahead/node_modules/char-regex": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/char-regex/-/char-regex-2.0.2.tgz", "integrity": "sha1-gThbsHGvTfd0v/hyHQyhXvKeoLs=", "dev": true, "license": "MIT", "engines": {"node": ">=12.20"}}, "node_modules/jest-watch-typeahead/node_modules/slash": {"version": "5.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/slash/-/slash-5.1.0.tgz", "integrity": "sha1-vjrd3N8JrDjuvo3Nx7GlenWwlc4=", "dev": true, "license": "MIT", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watch-typeahead/node_modules/string-length": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/string-length/-/string-length-5.0.1.tgz", "integrity": "sha1-PWR/SXtujo1B5CL34LI7xTbIOB4=", "dev": true, "license": "MIT", "dependencies": {"char-regex": "^2.0.0", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watch-typeahead/node_modules/strip-ansi": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/strip-ansi/-/strip-ansi-7.1.0.tgz", "integrity": "sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/jest-watcher": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-watcher/-/jest-watcher-29.7.0.tgz", "integrity": "sha1-eBDTDWGcOmIJMiPOa7NZyhsoovI=", "dev": true, "license": "MIT", "dependencies": {"@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.7.0", "string-length": "^4.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jest-worker/-/jest-worker-29.7.0.tgz", "integrity": "sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/jose": {"version": "4.15.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jose/-/jose-4.15.9.tgz", "integrity": "sha1-m2jtop6aBhTAQvopOHGWx92AAQA=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jsbn/-/jsbn-1.1.0.tgz", "integrity": "sha1-sBMHyym2GKHtJux56RH4A8TaAEA=", "license": "MIT"}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-bigint": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json-bigint/-/json-bigint-1.0.0.tgz", "integrity": "sha1-rlR4I6wMrYOYZn+M2e9HMPWwH/E=", "license": "MIT", "dependencies": {"bignumber.js": "^9.0.0"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json5/-/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonwebtoken": {"version": "9.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "integrity": "sha1-Zf+R9KvvF4RpfUCVK7GZjFBMqvM=", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jsonwebtoken/node_modules/jwa": {"version": "1.4.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jwa/-/jwa-1.4.2.tgz", "integrity": "sha1-FgEaxttI3nsQJ3fleJeQFSDux7k=", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jsonwebtoken/node_modules/jws": {"version": "3.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jws/-/jws-3.2.2.tgz", "integrity": "sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=", "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/jsonwebtoken/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/jwa": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jwa/-/jwa-2.0.1.tgz", "integrity": "sha1-v4F20a0M1y4PP1gzhZWhPhELyAQ=", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jwks-rsa": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jwks-rsa/-/jwks-rsa-3.2.0.tgz", "integrity": "sha1-EyvIv6ewOSiic7vJNIb3AiZEngQ=", "license": "MIT", "dependencies": {"@types/express": "^4.17.20", "@types/jsonwebtoken": "^9.0.4", "debug": "^4.3.4", "jose": "^4.15.4", "limiter": "^1.1.5", "lru-memoizer": "^2.2.0"}, "engines": {"node": ">=14"}}, "node_modules/jws": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jws/-/jws-4.0.0.tgz", "integrity": "sha1-LU6M9qMY/6oSYV6d7H6G5slzEPQ=", "license": "MIT", "dependencies": {"jwa": "^2.0.0", "safe-buffer": "^5.0.1"}}, "node_modules/kareem": {"version": "2.5.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/kareem/-/kareem-2.5.1.tgz", "integrity": "sha1-e4ID4RgZqOd6NLNRfT6tIGdk0V0=", "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/keyv/-/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kleur": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/kleur/-/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/leven": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/leven/-/leven-3.1.0.tgz", "integrity": "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/levn/-/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/limiter": {"version": "1.1.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/limiter/-/limiter-1.1.5.tgz", "integrity": "sha1-j5KiWzsWxhMSk6DMg0tKg4oqp8I="}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "dev": true, "license": "MIT"}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/lodash.camelcase": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "integrity": "sha1-soqmKIorn8ZRA1x3EfZathkDMaY=", "license": "MIT", "optional": true}, "node_modules/lodash.clonedeep": {"version": "4.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz", "integrity": "sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=", "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168=", "dev": true, "license": "MIT"}, "node_modules/lodash.get": {"version": "4.4.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.get/-/lodash.get-4.4.2.tgz", "integrity": "sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=", "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead.", "license": "MIT"}, "node_modules/lodash.includes": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.includes/-/lodash.includes-4.3.0.tgz", "integrity": "sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=", "license": "MIT"}, "node_modules/lodash.isboolean": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=", "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha1-QVxEePK8wwEgwizhDtMib30+GOA=", "deprecated": "This package is deprecated. Use require('node:util').isDeepStrictEqual instead.", "license": "MIT"}, "node_modules/lodash.isinteger": {"version": "4.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=", "license": "MIT"}, "node_modules/lodash.isnumber": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha1-***************************=", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "license": "MIT"}, "node_modules/lodash.mergewith": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz", "integrity": "sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U=", "license": "MIT"}, "node_modules/lodash.once": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.once/-/lodash.once-4.1.1.tgz", "integrity": "sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=", "license": "MIT"}, "node_modules/long": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/long/-/long-5.3.2.tgz", "integrity": "sha1-HYRGMJWZkmLX17f4v9SozFUWf4M=", "license": "Apache-2.0", "optional": true}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/lru-memoizer": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lru-memoizer/-/lru-memoizer-2.3.0.tgz", "integrity": "sha1-7w+8AhvOtmZ5SxRe76xr5J3EfzE=", "license": "MIT", "dependencies": {"lodash.clonedeep": "^4.5.0", "lru-cache": "6.0.0"}}, "node_modules/lru-memoizer/node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/lru-memoizer/node_modules/yallist": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yallist/-/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "license": "ISC"}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/makeerror": {"version": "1.0.12", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/makeerror/-/makeerror-1.0.12.tgz", "integrity": "sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memory-pager": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/memory-pager/-/memory-pager-1.5.0.tgz", "integrity": "sha1-2HUWVdItOEaCdByXLyw9bfo+ZrU=", "license": "MIT"}, "node_modules/merge-descriptors": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "integrity": "sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "dev": true, "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mime/-/mime-3.0.0.tgz", "integrity": "sha1-s3RVDco6DBhEOwyVCmpY8ZMc96c=", "license": "MIT", "optional": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=10.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/minimist/-/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/minipass/-/minipass-5.0.0.tgz", "integrity": "sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=", "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=", "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/minipass/-/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minizlib/node_modules/yallist": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yallist/-/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "license": "ISC"}, "node_modules/mkdirp": {"version": "0.5.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/module-details-from-path": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/module-details-from-path/-/module-details-from-path-1.0.4.tgz", "integrity": "sha1-tmL9zZP2yD0/JSidoM6ByNloW5Q=", "license": "MIT"}, "node_modules/moment": {"version": "2.30.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/moment/-/moment-2.30.1.tgz", "integrity": "sha1-+MkcB7enhuMMWZJt9TC06slpdK4=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.5.48", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/moment-timezone/-/moment-timezone-0.5.48.tgz", "integrity": "sha1-ERcnuydHNKUYrhVLXKWJKD8FiWc=", "license": "MIT", "dependencies": {"moment": "^2.29.4"}, "engines": {"node": "*"}}, "node_modules/mongodb": {"version": "6.16.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongodb/-/mongodb-6.16.0.tgz", "integrity": "sha1-KnoZhuwVHZxzj8jOTPQyTD9yii8=", "license": "Apache-2.0", "dependencies": {"@mongodb-js/saslprep": "^1.1.9", "bson": "^6.10.3", "mongodb-connection-string-url": "^3.0.0"}, "engines": {"node": ">=16.20.1"}, "peerDependencies": {"@aws-sdk/credential-providers": "^3.188.0", "@mongodb-js/zstd": "^1.1.0 || ^2.0.0", "gcp-metadata": "^5.2.0", "kerberos": "^2.0.1", "mongodb-client-encryption": ">=6.0.0 <7", "snappy": "^7.2.2", "socks": "^2.7.1"}, "peerDependenciesMeta": {"@aws-sdk/credential-providers": {"optional": true}, "@mongodb-js/zstd": {"optional": true}, "gcp-metadata": {"optional": true}, "kerberos": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "snappy": {"optional": true}, "socks": {"optional": true}}}, "node_modules/mongodb-connection-string-url": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongodb-connection-string-url/-/mongodb-connection-string-url-3.0.2.tgz", "integrity": "sha1-4iMInfoKX6m/UF+K7cvGewd7M+c=", "license": "Apache-2.0", "dependencies": {"@types/whatwg-url": "^11.0.2", "whatwg-url": "^14.1.0 || ^13.0.0"}}, "node_modules/mongoose": {"version": "7.8.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongoose/-/mongoose-7.8.7.tgz", "integrity": "sha1-enzL/T2iWZDIioxBzVtavDC5E8c=", "license": "MIT", "dependencies": {"bson": "^5.5.0", "kareem": "2.5.1", "mongodb": "5.9.2", "mpath": "0.9.0", "mquery": "5.0.0", "ms": "2.1.3", "sift": "16.0.1"}, "engines": {"node": ">=14.20.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}, "node_modules/mongoose-lean-virtuals": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongoose-lean-virtuals/-/mongoose-lean-virtuals-1.1.0.tgz", "integrity": "sha1-lAcnaUoDYV530fSPpr5viVLYlm4=", "license": "Apache 2.0", "dependencies": {"mpath": "^0.8.4"}, "engines": {"node": ">=16.20.1"}, "peerDependencies": {"mongoose": ">=5.11.10"}}, "node_modules/mongoose-lean-virtuals/node_modules/mpath": {"version": "0.8.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mpath/-/mpath-0.8.4.tgz", "integrity": "sha1-a1ZtlYFiHZ6THdOxQu02GOdZkxM=", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/mongoose-paginate-v2": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongoose-paginate-v2/-/mongoose-paginate-v2-1.9.0.tgz", "integrity": "sha1-P5TxeC7OfTkKeoK3vmnC2WEj8Ww=", "license": "MIT", "dependencies": {"mongoose-lean-virtuals": "^1.1.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mongoose/node_modules/@types/whatwg-url": {"version": "8.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/whatwg-url/-/whatwg-url-8.2.2.tgz", "integrity": "sha1-dJ1bOHPoRYl62pm+REgEHUzDnmM=", "license": "MIT", "dependencies": {"@types/node": "*", "@types/webidl-conversions": "*"}}, "node_modules/mongoose/node_modules/bson": {"version": "5.5.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/bson/-/bson-5.5.1.tgz", "integrity": "sha1-9YSdQFcRp/I6zdqaRCN134WOaDM=", "license": "Apache-2.0", "engines": {"node": ">=14.20.1"}}, "node_modules/mongoose/node_modules/mongodb": {"version": "5.9.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongodb/-/mongodb-5.9.2.tgz", "integrity": "sha1-Oac7n7yHrJ2cGq+Kq1xbtp4rkT4=", "license": "Apache-2.0", "dependencies": {"bson": "^5.5.0", "mongodb-connection-string-url": "^2.6.0", "socks": "^2.7.1"}, "engines": {"node": ">=14.20.1"}, "optionalDependencies": {"@mongodb-js/saslprep": "^1.1.0"}, "peerDependencies": {"@aws-sdk/credential-providers": "^3.188.0", "@mongodb-js/zstd": "^1.0.0", "kerberos": "^1.0.0 || ^2.0.0", "mongodb-client-encryption": ">=2.3.0 <3", "snappy": "^7.2.2"}, "peerDependenciesMeta": {"@aws-sdk/credential-providers": {"optional": true}, "@mongodb-js/zstd": {"optional": true}, "kerberos": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "snappy": {"optional": true}}}, "node_modules/mongoose/node_modules/mongodb-connection-string-url": {"version": "2.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz", "integrity": "sha1-V5Ab81I3Kr3egSyBvke3XGsuxc8=", "license": "Apache-2.0", "dependencies": {"@types/whatwg-url": "^8.2.1", "whatwg-url": "^11.0.0"}}, "node_modules/mongoose/node_modules/tr46": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tr46/-/tr46-3.0.0.tgz", "integrity": "sha1-VVxOKXqVBhfo7t3vYzyH1NnWy/k=", "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=12"}}, "node_modules/mongoose/node_modules/whatwg-url": {"version": "11.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/whatwg-url/-/whatwg-url-11.0.0.tgz", "integrity": "sha1-CoSe67X68hGbkBu3b9eVwoSNQBg=", "license": "MIT", "dependencies": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/mpath": {"version": "0.9.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mpath/-/mpath-0.9.0.tgz", "integrity": "sha1-DBIv4QeEbjH8WMdbCcNVFLOHGQQ=", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/mquery": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mquery/-/mquery-5.0.0.tgz", "integrity": "sha1-qVvl38YQsjhi3zSkfT5dYOEQaV0=", "license": "MIT", "dependencies": {"debug": "4.x"}, "engines": {"node": ">=14.0.0"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/multer": {"version": "1.4.5-lts.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/multer/-/multer-1.4.5-lts.2.tgz", "integrity": "sha1-NArwZdhoXdqEbsnj12VfzVCvui0=", "license": "MIT", "dependencies": {"append-field": "^1.0.0", "busboy": "^1.0.0", "concat-stream": "^1.5.2", "mkdirp": "^0.5.4", "object-assign": "^4.1.1", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/node-addon-api": {"version": "5.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-addon-api/-/node-addon-api-5.1.0.tgz", "integrity": "sha1-SdocoFXhCaI9U36d5DwJzKIet2I=", "license": "MIT"}, "node_modules/node-cron": {"version": "4.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-cron/-/node-cron-4.0.5.tgz", "integrity": "sha1-9Og/bHnjKq/6Hg+DhqlMK1+/1jM=", "license": "ISC", "engines": {"node": ">=6.0.0"}}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha1-aIjbRqH3HAt2s/dVUBa2P+ZHZuU=", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "3.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-fetch/-/node-fetch-3.3.2.tgz", "integrity": "sha1-0eiJus33M7T/OyskPrehKGagt4s=", "license": "MIT", "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/node-forge": {"version": "1.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-forge/-/node-forge-1.3.1.tgz", "integrity": "sha1-vo2iryQ7JBfV9kancGY6krfp3tM=", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/node-int64": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-int64/-/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=", "dev": true, "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true, "license": "MIT"}, "node_modules/nodemon": {"version": "3.1.10", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/nodemon/-/nodemon-3.1.10.tgz", "integrity": "sha1-UBXF60//yyTZjPlFTfFPT+zsm8E=", "license": "MIT", "dependencies": {"chokidar": "^3.5.2", "debug": "^4", "ignore-by-default": "^1.0.1", "minimatch": "^3.1.2", "pstree.remy": "^1.1.8", "semver": "^7.5.3", "simple-update-notifier": "^2.0.0", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.5"}, "bin": {"nodemon": "bin/nodemon.js"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}}, "node_modules/nodemon/node_modules/has-flag": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/nodemon/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/nodemon/node_modules/supports-color": {"version": "5.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/nopt": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/nopt/-/nopt-5.0.0.tgz", "integrity": "sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=", "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": ">=6"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/npmlog": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/npmlog/-/npmlog-5.0.1.tgz", "integrity": "sha1-8GZ46A4pQZrWerlk4PpplZweuLA=", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0", "gauge": "^3.0.0", "set-blocking": "^2.0.0"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/object-hash/-/object-hash-3.0.0.tgz", "integrity": "sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=", "license": "MIT", "optional": true, "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "10.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/open/-/open-10.1.2.tgz", "integrity": "sha1-1d9AmEdVyanDyT34FWoSRn6IKSU=", "license": "MIT", "dependencies": {"default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^3.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/openapi-types": {"version": "12.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/openapi-types/-/openapi-types-12.1.3.tgz", "integrity": "sha1-RxmV6ybEuXt701aqz3uRtz53fdM=", "license": "MIT", "peer": true}, "node_modules/optionator": {"version": "0.9.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/optionator/-/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-locate/node_modules/p-limit": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "license": "MIT"}, "node_modules/path-to-regexp": {"version": "0.1.12", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "integrity": "sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pirates": {"version": "4.0.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/pirates/-/pirates-4.0.7.tgz", "integrity": "sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkce-challenge": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/pkce-challenge/-/pkce-challenge-5.0.0.tgz", "integrity": "sha1-w6QFy0nicglKOOiQorUdoCKMTZc=", "license": "MIT", "engines": {"node": ">=16.20.0"}}, "node_modules/pkg-dir": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/pretty-format": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/pretty-format/-/pretty-format-29.7.0.tgz", "integrity": "sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "license": "MIT"}, "node_modules/prompts": {"version": "2.4.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/prompts/-/prompts-2.4.2.tgz", "integrity": "sha1-e1fnOzpIAprRDr1E90sBcipMsGk=", "dev": true, "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/proto3-json-serializer": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/proto3-json-serializer/-/proto3-json-serializer-2.0.2.tgz", "integrity": "sha1-W3BSA7TVjziAWWyV+tZJAmF1Kd0=", "license": "Apache-2.0", "optional": true, "dependencies": {"protobufjs": "^7.2.5"}, "engines": {"node": ">=14.0.0"}}, "node_modules/protobufjs": {"version": "7.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/protobufjs/-/protobufjs-7.4.0.tgz", "integrity": "sha1-fv4yTOmzthyCquXegQ0oe8CKJIo=", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=", "license": "MIT"}, "node_modules/pstree.remy": {"version": "1.1.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/pstree.remy/-/pstree.remy-1.1.8.tgz", "integrity": "sha1-wkIiT0pnwh9oaDm720rCgrg3PTo=", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/punycode/-/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pure-rand": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/pure-rand/-/pure-rand-6.1.0.tgz", "integrity": "sha1-0XPPIyWCMZdsy9sFJHyXh5V2BPI=", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}], "license": "MIT"}, "node_modules/qs": {"version": "6.13.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/qs/-/qs-6.13.0.tgz", "integrity": "sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/random-bytes": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/random-bytes/-/random-bytes-1.0.0.tgz", "integrity": "sha1-T2ih3Arli9P7lYSMMDJNt11kNgs=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/raw-body/-/raw-body-2.5.2.tgz", "integrity": "sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/react-is": {"version": "18.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/react-is/-/react-is-18.3.1.tgz", "integrity": "sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=", "dev": true, "license": "MIT"}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/regenerate": {"version": "1.4.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/regenerate/-/regenerate-1.4.2.tgz", "integrity": "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=", "dev": true, "license": "MIT"}, "node_modules/regenerate-unicode-properties": {"version": "10.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz", "integrity": "sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/regexpu-core": {"version": "6.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/regexpu-core/-/regexpu-core-6.2.0.tgz", "integrity": "sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/regjsgen": {"version": "0.8.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/regjsgen/-/regjsgen-0.8.0.tgz", "integrity": "sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=", "dev": true, "license": "MIT"}, "node_modules/regjsparser": {"version": "0.12.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/regjsparser/-/regjsparser-0.12.0.tgz", "integrity": "sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.0.2"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/regjsparser/node_modules/jsesc": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/jsesc/-/jsesc-3.0.2.tgz", "integrity": "sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-in-the-middle": {"version": "7.5.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/require-in-the-middle/-/require-in-the-middle-7.5.2.tgz", "integrity": "sha1-3CWxSK/61C5XDPDkG6MNwA8XA+w=", "license": "MIT", "dependencies": {"debug": "^4.3.5", "module-details-from-path": "^1.0.3", "resolve": "^1.22.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/resolve/-/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "integrity": "sha1-DwB18bslRHZs9zumpuKt/ryxPy0=", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve.exports": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/resolve.exports/-/resolve.exports-2.0.3.tgz", "integrity": "sha1-QZVebxtAE7dYb4c3SaY13qB+vj8=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/retry": {"version": "0.13.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/retry/-/retry-0.13.1.tgz", "integrity": "sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=", "license": "MIT", "optional": true, "engines": {"node": ">= 4"}}, "node_modules/retry-request": {"version": "7.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/retry-request/-/retry-request-7.0.2.tgz", "integrity": "sha1-YL9Iz7Qk7AGwP8pmZd7pHQbdlfM=", "license": "MIT", "optional": true, "dependencies": {"@types/request": "^2.48.8", "extend": "^3.0.2", "teeny-request": "^9.0.0"}, "engines": {"node": ">=14"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "deprecated": "Rimraf versions prior to v4 are no longer supported", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/router": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/router/-/router-2.2.0.tgz", "integrity": "sha1-AZvmILcRyHZBFnzHm5kJDwCxRu8=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/router/node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "integrity": "sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/run-applescript": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/run-applescript/-/run-applescript-7.0.0.tgz", "integrity": "sha1-5aVTwr/9Yg4WnSdsHNjxtkd4++s=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/send": {"version": "0.19.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/send/-/send-0.19.0.tgz", "integrity": "sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/send/node_modules/mime": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/serve-static": {"version": "1.16.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/serve-static/-/serve-static-1.16.2.tgz", "integrity": "sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "license": "ISC"}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shimmer": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/shimmer/-/shimmer-1.2.1.tgz", "integrity": "sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/sift": {"version": "16.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/sift/-/sift-16.0.1.tgz", "integrity": "sha1-6cLMxyGRWFAIzz42/ER7LSYzoFM=", "license": "MIT"}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "license": "ISC"}, "node_modules/simple-update-notifier": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz", "integrity": "sha1-***************************=", "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}}, "node_modules/simple-update-notifier/node_modules/semver": {"version": "7.7.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/semver/-/semver-7.7.1.tgz", "integrity": "sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/sisteransi": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0=", "dev": true, "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/slash/-/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/smart-buffer": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/smart-buffer/-/smart-buffer-4.2.0.tgz", "integrity": "sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=", "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks": {"version": "2.8.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/socks/-/socks-2.8.4.tgz", "integrity": "sha1-BxCXVc3U2gMmm9pHJbqgYatW1cw=", "license": "MIT", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.13", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/source-map-support/-/source-map-support-0.5.13.tgz", "integrity": "sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/sparse-bitfield": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz", "integrity": "sha1-/0rm5oZWBWuks+eSqzM004JzyhE=", "license": "MIT", "dependencies": {"memory-pager": "^1.0.2"}}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/stack-chain": {"version": "1.3.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/stack-chain/-/stack-chain-1.3.7.tgz", "integrity": "sha1-0ZLJ/06moiyUxN1FkXHj8AzqEoU=", "license": "MIT"}, "node_modules/stack-utils": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/stack-utils/-/stack-utils-2.0.6.tgz", "integrity": "sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "integrity": "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/statuses/-/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/stream-events": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/stream-events/-/stream-events-1.0.5.tgz", "integrity": "sha1-u8iY7E3zOkkC2JIzPUfam/HEBtU=", "license": "MIT", "optional": true, "dependencies": {"stubs": "^3.0.0"}}, "node_modules/stream-shift": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/stream-shift/-/stream-shift-1.0.3.tgz", "integrity": "sha1-hbj6tNcQEPw7qHcugEbMSbijhks=", "license": "MIT", "optional": true}, "node_modules/streamsearch": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/streamsearch/-/streamsearch-1.1.0.tgz", "integrity": "sha1-QE3R4iR8qUr1VOhBqO8OqiONp2Q=", "engines": {"node": ">=10.0.0"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-length": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/string-length/-/string-length-4.0.2.tgz", "integrity": "sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=", "dev": true, "license": "MIT", "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/strip-bom/-/strip-bom-4.0.0.tgz", "integrity": "sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strnum": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/strnum/-/strnum-2.1.0.tgz", "integrity": "sha1-EsLG3lnTggqBKLSGycEGwb9sSSQ=", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT"}, "node_modules/stubs": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/stubs/-/stubs-3.0.0.tgz", "integrity": "sha1-6NK6H6nJBXAwPAMLaQD31fiavls=", "license": "MIT", "optional": true}, "node_modules/superagent": {"version": "9.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/superagent/-/superagent-9.0.2.tgz", "integrity": "sha1-oYeZRz/FdVconWtjlgYQ41i968E=", "license": "MIT", "dependencies": {"component-emitter": "^1.3.0", "cookiejar": "^2.1.4", "debug": "^4.3.4", "fast-safe-stringify": "^2.1.1", "form-data": "^4.0.0", "formidable": "^3.5.1", "methods": "^1.1.2", "mime": "2.6.0", "qs": "^6.11.0"}, "engines": {"node": ">=14.18.0"}}, "node_modules/superagent/node_modules/mime": {"version": "2.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mime/-/mime-2.6.0.tgz", "integrity": "sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/supertest": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/supertest/-/supertest-7.1.0.tgz", "integrity": "sha1-CbJzF0qIIOV8zbA9nKDZbAjJa1I=", "license": "MIT", "dependencies": {"methods": "^1.1.2", "superagent": "^9.0.1"}, "engines": {"node": ">=14.18.0"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/swagger-jsdoc": {"version": "6.2.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/swagger-jsdoc/-/swagger-jsdoc-6.2.8.tgz", "integrity": "sha1-bTPZ+wf/SnwVZDecUsCJiex9AlY=", "license": "MIT", "dependencies": {"commander": "6.2.0", "doctrine": "3.0.0", "glob": "7.1.6", "lodash.mergewith": "^4.6.2", "swagger-parser": "^10.0.3", "yaml": "2.0.0-1"}, "bin": {"swagger-jsdoc": "bin/swagger-jsdoc.js"}, "engines": {"node": ">=12.0.0"}}, "node_modules/swagger-jsdoc/node_modules/glob": {"version": "7.1.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/glob/-/glob-7.1.6.tgz", "integrity": "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=", "deprecated": "Glob versions prior to v9 are no longer supported", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/swagger-parser": {"version": "10.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/swagger-parser/-/swagger-parser-10.0.3.tgz", "integrity": "sha1-BMsBwYw6wZK0EWHHf4HnkwkTXQM=", "license": "MIT", "dependencies": {"@apidevtools/swagger-parser": "10.0.3"}, "engines": {"node": ">=10"}}, "node_modules/swagger-ui-dist": {"version": "5.21.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/swagger-ui-dist/-/swagger-ui-dist-5.21.0.tgz", "integrity": "sha1-rtIw/m4pTJRwIX5naX1gHju4650=", "license": "Apache-2.0", "dependencies": {"@scarf/scarf": "=1.4.0"}}, "node_modules/swagger-ui-express": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/swagger-ui-express/-/swagger-ui-express-5.0.1.tgz", "integrity": "sha1-+4wbeB0nk6a9L4ogWj9L1voCDdg=", "license": "MIT", "dependencies": {"swagger-ui-dist": ">=5.0.0"}, "engines": {"node": ">= v0.10.32"}, "peerDependencies": {"express": ">=4.0.0 || >=5.0.0-beta"}}, "node_modules/tar": {"version": "6.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tar/-/tar-6.2.1.tgz", "integrity": "sha1-cXVJxUG8PCrxV1G+qUsd0GjUsDo=", "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar/node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha1-PrXtYmInVteaXw4qIh3+utdcL34=", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/tar/node_modules/yallist": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yallist/-/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "license": "ISC"}, "node_modules/teeny-request": {"version": "9.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/teeny-request/-/teeny-request-9.0.0.tgz", "integrity": "sha1-GBQN4utllXcbGwIgMxLfrXmkcW0=", "license": "Apache-2.0", "optional": true, "dependencies": {"http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "node-fetch": "^2.6.9", "stream-events": "^1.0.5", "uuid": "^9.0.0"}, "engines": {"node": ">=14"}}, "node_modules/teeny-request/node_modules/agent-base": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=", "license": "MIT", "optional": true, "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/teeny-request/node_modules/http-proxy-agent": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz", "integrity": "sha1-USmAAgNSDUNPFCvHj/PBcIAPK0M=", "license": "MIT", "optional": true, "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/teeny-request/node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=", "license": "MIT", "optional": true, "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/teeny-request/node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "optional": true, "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/teeny-request/node_modules/tr46": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT", "optional": true}, "node_modules/teeny-request/node_modules/uuid": {"version": "9.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uuid/-/uuid-9.0.1.tgz", "integrity": "sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/teeny-request/node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>", "optional": true}, "node_modules/teeny-request/node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "optional": true, "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/test-exclude": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/test-exclude/-/test-exclude-6.0.0.tgz", "integrity": "sha1-BKhphmHYBepvopO2y55jrARO8V4=", "dev": true, "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/tmpl": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tmpl/-/tmpl-1.0.5.tgz", "integrity": "sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/touch": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/touch/-/touch-3.1.1.tgz", "integrity": "sha1-CXoj17FhR2Q15cE0SpXA91tKVpQ=", "license": "ISC", "bin": {"nodetouch": "bin/nodetouch.js"}}, "node_modules/tr46": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tr46/-/tr46-5.1.1.tgz", "integrity": "sha1-lq6GfN24/bZKScwwWajUKLzyOMo=", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "engines": {"node": ">=18"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tslib/-/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/type-check/-/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.21.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "license": "MIT"}, "node_modules/uid-safe": {"version": "2.1.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uid-safe/-/uid-safe-2.1.5.tgz", "integrity": "sha1-Kz1cckDo/C5Y+Komnl7knAhXvTo=", "license": "MIT", "dependencies": {"random-bytes": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/undefsafe": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/undefsafe/-/undefsafe-2.0.5.tgz", "integrity": "sha1-OHM7kye9zSJtuIn7cjpu/RYubiw=", "license": "MIT"}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "license": "MIT"}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz", "integrity": "sha1-yzFz/kfKdD4ighbko93EyE1ijMI=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "integrity": "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=", "dev": true, "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz", "integrity": "sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "integrity": "sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-template": {"version": "2.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/url-template/-/url-template-2.0.8.tgz", "integrity": "sha1-/FZaPMy/93MMd19WQflVV5FDnyE=", "license": "BSD"}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "10.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uuid/-/uuid-10.0.0.tgz", "integrity": "sha1-WpWqRU5uACclx5BV/UKqujDKYpQ=", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-to-istanbul": {"version": "9.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz", "integrity": "sha1-uVcqv6Yr1VbBbXX968GkEdX/MXU=", "dev": true, "license": "ISC", "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/validator": {"version": "13.12.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/validator/-/validator-13.12.0.tgz", "integrity": "sha1-fXjna6hVBNo/7k/Rkis4WRTUs18=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/walker": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/walker/-/walker-1.0.8.tgz", "integrity": "sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=", "dev": true, "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "node_modules/web-streams-polyfill": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "integrity": "sha1-IHO5Gi/bH7+9QB594KyfghTOy0s=", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/webidl-conversions": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/websocket-driver": {"version": "0.7.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/websocket-driver/-/websocket-driver-0.7.4.tgz", "integrity": "sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=", "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "integrity": "sha1-f4RzvIOd/YdgituV1+sHUhFXikI=", "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/whatwg-url": {"version": "14.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/whatwg-url/-/whatwg-url-14.2.0.tgz", "integrity": "sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=", "license": "MIT", "dependencies": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=18"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wide-align": {"version": "1.1.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/wide-align/-/wide-align-1.1.5.tgz", "integrity": "sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=", "license": "ISC", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "devOptional": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "license": "ISC"}, "node_modules/write-file-atomic": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "integrity": "sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/xtend": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/xtend/-/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/y18n/-/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "devOptional": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "2.0.0-1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yaml/-/yaml-2.0.0-1.tgz", "integrity": "sha1-jDAps+4gKDBtW885aYBiMRX/jRg=", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yargs/-/yargs-17.7.2.tgz", "integrity": "sha1-mR3zmspnWhkrgW4eA2P5110qomk=", "devOptional": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=", "devOptional": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/z-schema": {"version": "5.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/z-schema/-/z-schema-5.0.5.tgz", "integrity": "sha1-aAWkjFNmphJcrg5YdSur/VA9rzI=", "license": "MIT", "dependencies": {"lodash.get": "^4.4.2", "lodash.isequal": "^4.5.0", "validator": "^13.7.0"}, "bin": {"z-schema": "bin/z-schema"}, "engines": {"node": ">=8.0.0"}, "optionalDependencies": {"commander": "^9.4.1"}}, "node_modules/z-schema/node_modules/commander": {"version": "9.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/commander/-/commander-9.5.0.tgz", "integrity": "sha1-vAjR61zt98y3l6lhmdQce8PmDTA=", "license": "MIT", "optional": true, "engines": {"node": "^12.20.0 || >=14"}}, "node_modules/zlib": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/zlib/-/zlib-1.0.5.tgz", "integrity": "sha1-bnyXL8NxxkWmr7A6sUdp3vEU/MA=", "engines": {"node": ">=0.2.0"}}, "node_modules/zod": {"version": "3.24.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/zod/-/zod-3.24.4.tgz", "integrity": "sha1-4uLMpfqqAS125SfQ02Yi4KkMMV8=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}, "node_modules/zod-to-json-schema": {"version": "3.24.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/zod-to-json-schema/-/zod-to-json-schema-3.24.5.tgz", "integrity": "sha1-0QlUQLFH+3wgk4EqU8VN+NXfUKM=", "license": "ISC", "peerDependencies": {"zod": "^3.24.1"}}}}