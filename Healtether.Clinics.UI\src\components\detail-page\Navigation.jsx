export function NavigationButton({ prev, next }) {
    return (
        <div className="flex justify-between mt-4">
            {(prev != undefined) ?
                <button type="button" className="btn btn-secondary  btn-outline rounded-full" onClick={() => { prev(); }}> <span className="icon-[tabler--arrow-big-left-line] size-5"></span>Prev</button>
                : <></>}
            <button type="button" className="btn btn-secondary btn-outline rounded-full" onClick={() => { next(); }}>Next <span className="icon-[tabler--arrow-big-right-line] size-5"></span></button>
        </div>
    );
}