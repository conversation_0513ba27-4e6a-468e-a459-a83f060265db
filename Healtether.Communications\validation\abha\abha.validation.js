import { checkSchema, validationResult } from "express-validator";

export const validateSearchLogin = async (req, res, next) => {
  await checkSchema({
    "mobile": {
      in: ["body"],
      isString: {
        errorMessage: "Mobile number  must be a string",
      },
      notEmpty: {
        errorMessage: "Mobile number is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateIndexMobileOtp = async (req, res, next) => {
  await checkSchema({
    "index": {
      trim:true,
      escape:true,
      in: ["body"],
      errorMessage: "Index is required",
    },
    "txnId": {
      trim:true,
      escape:true,
      isString:true,
      in: ["body"],
      errorMessage: "txnId is required",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateVerifyMobileOtp = async (req, res, next) => {
  await checkSchema({
    "otp": {
      trim:true,
      escape:true,
      in: ["body"],
      errorMessage: "otp is required.",
    },
    "txnId": {
      trim:true,
      escape:true,
      isString:true,
      in: ["body"],
      errorMessage: "txnId is required.",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateUserProfile = async (req, res, next) => {
  await checkSchema({
    "x-token": {
      trim: true,
      escape: true,
      in: ["headers"],
      errorMessage: "x-token is required in the headers.",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
export const validateAbhaRequestOtp = async (req, res, next) => {
  await checkSchema({
    "abhaAddress": {
      in: ["body"],
      isString: {
        errorMessage: "ABHA address  must be a string",
      },
      notEmpty: {
        errorMessage: "ABHA address is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
export const validateAbhaVerifyOtp = async (req, res, next) => {
  await checkSchema({
    "txnId": {
      in: ["body"],
      isString: {
        errorMessage: "txnId  must be a string",
      },
      notEmpty: {
        errorMessage: "txnId is required",
      },
    },
    "otp": {
      in: ["body"],
      notEmpty: {
        errorMessage: "otp is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateMobileLogin = async (req, res, next) => {
  await checkSchema({
    "mobile": {
      in: ["body"],
      isString: {
        errorMessage: "mobile  must be a string",
      },
      notEmpty: {
        errorMessage: "mobile is required",
      },
    },
   
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateabhaNumberLogin = async (req, res, next) => {
  await checkSchema({
    "abhaNumber": {
      in: ["body"],
      isString: {
        errorMessage: "abhaNumber  must be a string",
      },
      notEmpty: {
        errorMessage: "abhaNumber is required",
      },
    },
   
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};


export const validateAadhaarLogin = async (req, res, next) => {
  await checkSchema({
    "aadhaarNumber": {
      in: ["body"],
      isString: {
        errorMessage: "aadhaarNumber  must be a string",
      },
      notEmpty: {
        errorMessage: "aadhaarNumber is required",
      },
    },
   
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateEnrollByAadhaar = async (req, res, next) => {
  await checkSchema({
    "txnId": {
      in: ["body"],
      isString: {
        errorMessage: "txnId  must be a string",
      },
      notEmpty: {
        errorMessage: "txnId is required",
      },
    },
    "mobileNumber": {
      in: ["body"],
      isString: {
        errorMessage: "mobileNumber  must be a string",
      },
      notEmpty: {
        errorMessage: "mobileNumber is required",
      },
    },
    "otp": {
      in: ["body"],
      notEmpty: {
        errorMessage: "otp is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};



export const validateEnrollByMobile = async (req, res, next) => {
  await checkSchema({
    "txnId": {
      in: ["body"],
      isString: {
        errorMessage: "txnId  must be a string",
      },
      notEmpty: {
        errorMessage: "txnId is required",
      },
    },
    "mobile": {
      in: ["body"],
      isString: {
        errorMessage: "mobile  must be a string",
      },
      notEmpty: {
        errorMessage: "mobile is required",
      },
    },
  
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
export const validateEnrollByAbhaAddress = async (req, res, next) => {
  await checkSchema({
    "txnId": {
      in: ["body"],
      isString: {
        errorMessage: "txnId  must be a string",
      },
      notEmpty: {
        errorMessage: "txnId is required",
      },
    },
    "abhaAddress": {
      in: ["body"],
      isString: {
        errorMessage: "abhaAddress  must be a string",
      },
      notEmpty: {
        errorMessage: "abhaAddress is required",
      },
    },
  
    "preferred": {
      in: ["body"],
      notEmpty: {
        errorMessage: "preferred is required",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};


export const validateEnrollByMobileOtp = async (req, res, next) => {
  await checkSchema({
    "otp": {
      trim:true,
      escape:true,
      in: ["body"],
      errorMessage: "otp is required.",
    },
    "mobileNumber": {
      trim:true,
      escape:true,
      in: ["body"],
      errorMessage: "mobileNumber is required.",
    },
    "txnId": {
      trim:true,
      escape:true,
      isString:true,
      in: ["body"],
      errorMessage: "txnId is required.",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSuggestion = async (req, res, next) => {
  await checkSchema({
    "txnId": {
      trim: true,
      escape: true,
      in: ["body"],
      errorMessage: "txnId is required in the headers.",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};


export const validateAbhaNumberAddressOtp = async (req, res, next) => {
  await checkSchema({
      "type": {
          trim:true,
          escape:true,
          isString:true,
          in: ["body"],
          errorMessage: "txnId is  required.",
        },
        "abhaNumberAddress": {
          trim:true,
          escape:true,
          isString:true,
          in: ["body"],
          errorMessage: "abhaNumber or Abha Address is  required.",
        }
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateVerifyAbhaNumberAddressOtp = async (req, res, next) => {
  await checkSchema({
      "type": {
          trim:true,
          escape:true,
          isString:true,
          in: ["body"],
          errorMessage: "txnId is  required.",
        },
        "txnId": {
          trim:true,
          escape:true,
          isString:true,
          in: ["body"],
          errorMessage: "txnId is  required.",
        },
        "otp": {
          trim:true,
          escape:true,
          in: ["body"],
          errorMessage: "otp is required.",
        },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateConsentInit = async (req, res, next) => {
  await checkSchema({
     "data.hiTypes": {
      in: ["body"],
      isArray: {
        errorMessage: "hiTypes must be an array.",
      },
      notEmpty: {
        errorMessage: "hiTypes cannot be empty.",
      },
    },
        "data.purposeCode": {
          trim:true,
          escape:true,
          isString:true,
          in: ["body"],
          errorMessage: "purposeCode is  required.",
        },
        "data.purposeText": {
          trim:true,
          escape:true,
          in: ["body"],
          errorMessage: "purposeText is required.",
        },
        "data.abhaAddress": {
          trim:true,
          escape:true,
          in: ["body"],
          errorMessage: "abhaAddress is required.",
        },
        "data.requesterName": {
          trim:true,
          escape:true,
          in: ["body"],
          errorMessage: "requesterName is required.",
        },
        "data.dateRange.endDate": {
          trim:true,
          escape:true,
          in: ["body"],
          errorMessage: "endDate is required.",
        },
        "data.dateRange.startDate": {
          trim:true,
          escape:true,
          in: ["body"],
          errorMessage: "startDate is required.",
        },
        "data.expiryDate": {
          trim:true,
          escape:true,
          in: ["body"],
          errorMessage: "expiryDate is required.",
        },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};