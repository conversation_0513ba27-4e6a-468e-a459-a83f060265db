import mongoose from "mongoose";
import { CLIENT_COLLECTION, USER_COLLECTION } from "../mongodb.collection.name.js";

const userNotificationSchema = new mongoose.Schema({
  notificationMessage: {
    type: String,
  },
  showTime: {
    type: Date,
  },
  header: {
    type: String,
  },
  seen: {
    type: <PERSON><PERSON><PERSON>,
  },
  clinic: {
    type: mongoose.Schema.Types.ObjectId,
    ref: CLIENT_COLLECTION,
    index: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
     ref: USER_COLLECTION,
    index: true,
  },
  "expireAt": {
    type: Date,
    expires: 10
  }
});

// Create the user model
// const UserNotification = new mongoose.model(
//   "UserNotification",
//   userNotificationSchema
// );
export { userNotificationSchema };
