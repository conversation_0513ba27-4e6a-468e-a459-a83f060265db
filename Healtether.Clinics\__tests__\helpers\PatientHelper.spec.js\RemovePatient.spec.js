import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { removePatient } from '../../../helpers/patient/patient.helper.js';
import { Patient } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('removePatient function', () => {
  let testPatientId;

  beforeEach(async () => {
    await Patient.deleteMany({});
    const patient = new Patient({
      firstName: 'John',
      lastName: 'Doe',
      mobile: '**********',
      patientId: 'PAT001',
      age: 30,
      prefix: "Mr.",
      birthday: new Date('1993-01-01'),
      gender: 'Male',
      email: '<EMAIL>',
      address: '123 Main St',
      height: 180,
      weight: 75,
      documentType: 'ID',
      documentNumber: 'ID123456',
      isDeleted: false, // Changed from 'deleted' to match model
    });

    const savedPatient = await patient.save();
    testPatientId = savedPatient._id; 
  });

  afterEach(async () => {
    await Patient.deleteMany({}); 
  });

  it('should mark the patient as deleted when a valid ID is provided', async () => {
    const result = await removePatient(testPatientId);
    expect(result).toBeTruthy();
    expect(result.deleted).toBe(true); 
    const dbPatient = await Patient.findById(testPatientId);
    expect(dbPatient.deleted).toBe(true);
  });

});