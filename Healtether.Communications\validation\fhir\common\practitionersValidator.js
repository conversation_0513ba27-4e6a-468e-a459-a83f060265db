
const practitionerValidator = {
  practitioners: {
    in: ["body"],
    isArray: true,
    errorMessage: "practitioners must be an array",
  },
  "practitioners.*.names": {
    in: ["body"],
    isArray: true,
    errorMessage: "names must be an array",
  },
  "practitioners.*.names.*": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each name must be a non-empty string",
  },
  "practitioners.*.licenses": {
    in: ["body"],
    isArray: true,
    errorMessage: "licenses must be an array",
  },
  "practitioners.*.licenses.*.code": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "license code is required and must be a string",
  },
  "practitioners.*.licenses.*.display": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "license display is required and must be a string",
  },
  "practitioners.*.licenses.*.licNo": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "license number is required and must be a string",
  },
  "practitioners.*.telecom": {
    in: ["body"],
    isArray: true,
    errorMessage: "telecom must be an array",
  },
  "practitioners.*.telecom.*.system": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "telecom system is required",
  },
  "practitioners.*.telecom.*.value": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "telecom value is required",
  },
  "practitioners.*.telecom.*.use": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "telecom use is required",
  },
  "practitioners.*.gender": {
    in: ["body"],
    isString: true,
    isIn: {
      options: [["male", "female", "other", "unknown"]],
    },
    errorMessage: "gender must be one of: male, female, other, unknown",
  },
  "practitioners.*.birthDate": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "birthDate must be a valid date",
  },
  "practitioners.*.patient": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "patient ID is required and must be a string",
  },
  "practitioners.*.address": {
    in: ["body"],
    isArray: true,
    errorMessage: "address must be an array",
  },
  "practitioners.*.address.*.use": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "address use is required",
  },
  "practitioners.*.address.*.type": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "address type is required",
  },
  "practitioners.*.address.*.postalCode": {
    in: ["body"],
    isPostalCode: {
      options: "IN",
    },
    errorMessage: "postalCode must be valid",
  },
  "practitioners.*.address.*.country": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "country is required",
  },
  "practitioners.*.address.*.district": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "district is required",
  },
  "practitioners.*.address.*.city": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "city is required",
  },
  "practitioners.*.address.*.state": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "state is required",
  },
  "practitioners.*.address.*.text": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "text must be a string",
  },
}

export default practitionerValidator;
