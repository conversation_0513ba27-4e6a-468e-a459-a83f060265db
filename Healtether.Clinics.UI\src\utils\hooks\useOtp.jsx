import React from "react";

export default function useOtp() {
  const handleChange = (e, index) => {
    const newOtp = [...otp];
    const inputValue = e.target.value;
    const regex = /^[0-9]*$/;
    if (regex.test(inputValue)) {
      newOtp[index] = inputValue;
    }
    // newOtp[index] = e.target.value;
    setOtp(newOtp);

    // If the value is not empty, move to the next input
    if (regex.test(inputValue) && e.target.value !== "" && e.target.nextSibling)
      e.target.nextSibling.focus();
  };

  const handleKeyDown = (e, index) => {
    // If backspace is pressed and the current input is empty, focus the previous input
    if (e.key === "Backspace" && e.currentTarget.value === "" && index > 0)
      inputs.current[index - 1].select();
    if (e.key === "ArrowLeft" && index > 0) inputs.current[index - 1].select();
    else if (e.key === "ArrowRight" && index < otp.length - 1)
      inputs.current[index + 1].select();
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim().slice(0, 6);
    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split("");
      setOtp(newOtp);
      inputs.current[newOtp.length - 1].focus();
      verify(e, newOtp);
    } else {
      // Handle the error case where pasted data is not numeric or not 6 characters long
      console.error("Invalid paste content. Only a 6-digit number is allowed.");
    }
  };

  return { handleChange, handleKeyDown, handlePaste };
}
