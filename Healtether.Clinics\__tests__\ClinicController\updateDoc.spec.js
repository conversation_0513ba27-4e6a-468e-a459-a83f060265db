import { jest } from "@jest/globals";
const { mockBlobHelper } = await import("../mocks/mock.blob.helper.js");
mockBlobHelper();
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
mockClientHelper();

const { updateDoc } = await import("../../controllers/clinic/client.controller.js");

const { BlobHelper } = await import("../../helpers/storage/blob.helper.js"); 


const mockDocData = {
    logoName:"logoname"
};

const req = {
  body: mockDocData,
  files: {
    Logo: [
      { originalname: "images.png", buffer: Buffer.from("file content") },
    ],
  },
  user: { id: "user123", name: "Test User" },
};

const res = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn(),
};

describe("updateDoc", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should update doc detail", async () => {
    const uploadBlobMock = jest.fn();
    BlobHelper.mockImplementation(() => ({
      UploadBlob: uploadBlobMock,
    }));
    await updateDoc(req, res);

    expect(uploadBlobMock).toHaveBeenCalledWith(
      req.files.Logo[0], // file object
      "clinic/", // path
      req.body.logoName // blob name
    );

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });

  
});
