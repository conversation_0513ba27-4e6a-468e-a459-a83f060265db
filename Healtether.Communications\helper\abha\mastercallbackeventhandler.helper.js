import dotenv from "dotenv";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";

import { AbhaResponse, PROFILE_SHARE_WEBHOOK, PROFILE_ON_SHARE, } from "../../utils/abha.api.js";
import { publicToken } from "./token.js";
import { callbackToClinic } from "../clinic/clinic.helper.js";

dotenv.config();

export const handleCallbackEvent = async (req) => {
    try {
        // console.log(`Received event from URL: ${req.url}`);
        // console.log("Event Body:", req.body);

        const routeKey = extractRouteKey(req.url);
        const handler = eventHandlers[routeKey] || eventHandlers.defaultHandler;
        return await handler(req);
    } catch (error) {
        console.error("Error handling event:", error.message);

        throw {
            status: error.response?.status || 500,
            data: error.response?.data || { error: { message: error.message || "Unknown error" } },
        };
    }
};

function extractRouteKey(url) {
    if (url.includes(PROFILE_SHARE_WEBHOOK)) return 'profileOnShare';
    return 'defaultHandler';
}

const eventHandlers = {
    profileOnShare: async (req) => {
        try {
            const forwardedResponse = await profileOnShare(req);

            if (forwardedResponse?.isSuccess) {
                return new AbhaResponse(true, forwardedResponse);
            } else {
                return new AbhaResponse(false, {
                    message: "Failed to process the request",
                    details: forwardedResponse,
                });
            }
        } catch (error) {
            return new AbhaResponse(false, {
                message: "An error occurred while processing the request",
                error: error.message,
            });
        }
    },

    defaultHandler: async () => {
        throw {
            status: 404,
            data: { error: { message: "Unsupported route" } },
        };
    }
};


