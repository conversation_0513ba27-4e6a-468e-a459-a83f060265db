import axios from "services/axios/axios";

  export const initiateConsent=async(data)=>{
    try {
      const response = await axios.post("/abha/m3/initiateconsentrequestcontroller",{
       data
      });
      return response.data
    } catch (err) {
      console.error("user Verification Error:", err);
    }
  }

  export const patientConsents=async(id)=>{
    try {
      const response = await axios.get(`/abha/m3/patientconsents?patientId=${id}`);
      return response.data
    } catch (err) {
      console.error("user Verification Error:", err);
    }
  }

  