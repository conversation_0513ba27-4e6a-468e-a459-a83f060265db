const procedureValidator = {
  procedures: {
    in: ["body"],
    isArray: true,
    errorMessage: "Procedures must be an array",
  },
  "procedures.*.status": {
    in: ["body"],
    isString: true,
    notEmpty: { errorMessage: "Procedure status is required" },
  },
  "procedures.*.type": {
    in: ["body"],
    isString: true,
    notEmpty: { errorMessage: "Procedure type is required" },
  },
  "procedures.*.performedDateTime": {
    in: ["body"],
    isString: true,
    errorMessage: "performedDateTime must be a string",
  },
  "procedures.*.followUp": {
    in: ["body"],
    isArray: true,
    errorMessage: "followUp must be an array of strings",
  },
  "procedures.*.followUp.*": {
    in: ["body"],
    isString: true,
    notEmpty: { errorMessage: "Each followUp item must be a non-empty string" },
  },
};

export default procedureValidator;
