import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");

mockPatientHelper();

const { getPatientLatestAppointment } = await import('../../controllers/patients/patients.controller.js'); 

const { getPatientCurrentAppointment }= await import('../../helpers/patient/patient.helper.js'); 


// Mock patient data for testing
const mockPatientData = {
    _id: 'patient123',
    firstName: 'test',
    lastName: 'test',
    age: 20,
    birthday: '2003-12-22',
    gender: 'Male',
    mobile: "**********",
    email: "<EMAIL>",
    patientId: 'SD_165',
    address: {
        house: "123",
        street: "Main St",
        city: "Test City",
        pincode: "12345"
    },
    height: 170,
    weight: 70,
    documentType: "ID",
    documentNumber: "ABC123456",
    documents: [],
    appointments: [
        {
            _id: "appointment123",
            started: "2024-08-23T09:00:00Z",
            ended: "2024-08-23T09:30:00Z",
            tokenNumber: 5,
            timeSlot: "09:00-09:30"
        }
    ]
};

// Mock request and response objects
const query = {
    query: {
        id: 'patient123',
    },
};

const res = {
    json: jest.fn().mockReturnThis(),
    status: jest.fn().mockReturnThis(),
};

describe('getPatientLatestAppointment', () => {
    beforeEach(() => {
        jest.clearAllMocks(); 
    });

    it('should return patient data with the latest appointment successfully', async () => {
        getPatientCurrentAppointment.mockResolvedValueOnce(mockPatientData);

        await getPatientLatestAppointment(query, res);

        const expectedResult = {
            firstName: mockPatientData.firstName,
            lastName: mockPatientData.lastName,
            age: mockPatientData.age,
            birthday: mockPatientData.birthday,
            gender: mockPatientData.gender,
            mobile: mockPatientData.mobile,
            email: mockPatientData.email,
            patientId: mockPatientData.patientId,
            address: mockPatientData.address,
            height: mockPatientData.height,
            weight: mockPatientData.weight,
            documentType: mockPatientData.documentType,
            documentNumber: mockPatientData.documentNumber,
            documents: mockPatientData.documents,
            appointment: {
                _id: mockPatientData.appointments[0]._id,
                started: mockPatientData.appointments[0].started,
                ended: mockPatientData.appointments[0].ended,
                tokenNumber: mockPatientData.appointments[0].tokenNumber,
                timeSlot: mockPatientData.appointments[0].timeSlot
            }
        };

        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(expectedResult);
    });

    it('should return patient data with no appointment when appointments array is empty', async () => {

        const mockPatientDataNoAppointments = { ...mockPatientData, appointments: [] };
        getPatientCurrentAppointment.mockResolvedValueOnce(mockPatientDataNoAppointments);

        await getPatientLatestAppointment(query, res);

        const expectedResult = {
            firstName: mockPatientData.firstName,
            lastName: mockPatientData.lastName,
            age: mockPatientData.age,
            birthday: mockPatientData.birthday,
            gender: mockPatientData.gender,
            mobile: mockPatientData.mobile,
            email: mockPatientData.email,
            patientId: mockPatientData.patientId,
            address: mockPatientData.address,
            height: mockPatientData.height,
            weight: mockPatientData.weight,
            documentType: mockPatientData.documentType,
            documentNumber: mockPatientData.documentNumber,
            documents: mockPatientData.documents,
            appointment: null
        };


        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(expectedResult);
    });

});
