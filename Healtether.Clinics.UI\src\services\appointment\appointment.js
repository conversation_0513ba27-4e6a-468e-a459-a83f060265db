import axios from "services/axios/axios";
import store from '../../store/store';
import {GetJSONHederWithToken} from "utils/CommonMethods";
import {AppendFileForRecords} from "../../utils/AppendFileInForm";
import { GetMultipartHederWithToken } from "../../utils/CommonMethods";

export const AppointmentSubmitApi = async(value, id) => {
    try {
        const {currentClinic} = store.getState();
        var header = GetJSONHederWithToken();
        var data = new Appointment(id, value.patientId, value.doctorId, value.mobile, value.name, value.prefix,value.gender, value.age, value.birthDate, value.appointmentDate, value.timeSlot, value.doctorName, value.reason, value.virtualConsultation, currentClinic
            ?.clinic
                ?._id,value.instantAppointment,value.clinicPatientId,value.abhaNumber , value.abhaAddress,value.speciality, value.address , value.pincode,value.district,value.state, value.isFollowUp,value.googleLink, value.tokenNumber);
        const formData = await axios.post('/appointment/upsert', {data},header);
        return formData

    } catch (error) {
        console.log(error)
    }
}
export const BundleFHIR = async (patientId, appointmentId,type) => {
    try {
        const { currentClinic } = store.getState();
        var header = GetJSONHederWithToken();
        
        // Fix query parameters
        const url = `/appointment/write-prescription/fhir-bundle?clinicId=${currentClinic?.clinic?._id}&patientId=${patientId}&appointmentId=${appointmentId}&type=${type}`;
        
        const formData = await axios.post(url,{}, header);
        return formData;
    } catch (error) {
        console.log(error);
    }
};

export const consultationCheckIn= async(id)=>{
    try {
        var header = GetJSONHederWithToken();
        const formData = await axios.get(`/booked-consultation/consultationcheckin?id=${id}`, header)
        return formData

    } catch (error) {
        console.log(error)
    }
}


export const SetStartConsultation = async(id) => {
    try {
        const {currentClinic} = store.getState();
        var data = {
            id: id,
            clientId:currentClinic?.clinic
            ?._id
        }
        var header = GetJSONHederWithToken();
        const formData = await axios.post('/appointment/startconsultation', {
            data
        }, header)
        return formData

    } catch (error) {
        console.log(error)
    }
}
export const EndConsultation = async(prescription, medical, procedure,removeFile, appointmentId) => {
    try {
        const {currentClinic} = store.getState();
        var jsonHeader = GetJSONHederWithToken();
        var multipartHeader =  GetMultipartHederWithToken();
        let isUpdateDoc = false;
        var form = new FormData();
        form.append("clientId", currentClinic?.clinic?._id);
        var prescriptionFile = AppendFileForRecords(prescription, form, "prescription", "prescriptionName");
        if (prescriptionFile.files.length > 0) {
            isUpdateDoc = true;
            form = prescriptionFile.formData;
        }
        var medicalFile = AppendFileForRecords(medical, form, "medical", "medicalName");
        if (medicalFile.files.length > 0) {
            isUpdateDoc = true;
            form = medicalFile.formData;
        }
        var procedureFile = AppendFileForRecords(procedure, form, "procedure", "procedureName");
        if (procedureFile.files.length > 0) {
            isUpdateDoc = true;
            form = procedureFile.formData;
        }

        var data = {
            id: appointmentId,
            prescriptionRecords: prescriptionFile.files,
            medicalRecords: medicalFile.files,
            procedureRecords: procedureFile.files,
            removeRecords:removeFile,
            clientId: currentClinic?.clinic?._id,

        }
        const formData = await axios.post('/appointment/endconsultation', {data}, jsonHeader).then((response) => {
            return response;
        })
        if (isUpdateDoc) {
            await axios
                .post('/appointment/updatedocument', form, multipartHeader)
                .then((response) => {
                    return response;
                });
        }
        return formData

    } catch (error) {
        console.log(error)
    }
}
// export const SaveAsDraftApi = async (prescription, medical, procedure, removeFile, appointmentId, prescriptionReport,  invoiceReport) => {
//     try {
//         const { currentClinic } = store.getState();
//         var jsonHeader = GetJSONHederWithToken();
//         var multipartHeader = GetMultipartHederWithToken();
//         let isUpdateDoc = false;
//         var form = new FormData();
//         form.append("clientId", currentClinic?.clinic?._id);

//         form.append("appointmentId", appointmentId);

//         var prescriptionFile = AppendFileForRecords(prescription, form, "prescription", "prescriptionName");
//         if (prescriptionFile.files.length > 0) {
//             isUpdateDoc = true;
//             form = prescriptionFile.formData;
//         }

//         var medicalFile = AppendFileForRecords(medical, form, "medical", "medicalName");
//         if (medicalFile.files.length > 0) {
//             isUpdateDoc = true;
//             form = medicalFile.formData;
//         }

//         var procedureFile = AppendFileForRecords(procedure, form, "procedure", "procedureName");
//         if (procedureFile.files.length > 0) {
//             isUpdateDoc = true;
//             form = procedureFile.formData;
//         }

//         // var prescriptionReport = AppendFileForRecords(prescriptionReport, form, "prescriptionReport", "prescriptionReportName");
//         // if (prescriptionReport.files.length > 0) {
//         //     isUpdateDoc = true;
//         //     form = prescriptionReport.formData;
//         // }
//         // console.log('prescriptionReport', prescriptionReport);

//         // var invoiceReport = AppendFileForRecords(invoiceReport, form, "invoiceReport", "invoiceReportName");
//         // if (invoiceReport.files.length > 0) {
//         //     isUpdateDoc = true;
//         //     form = invoiceReport.formData;
//         // }
//         // console.log('invoiceReport', invoiceReport);
     

//         const prescriptionReportResult = AppendFileForRecords(prescriptionReport, form, "prescriptionReport", "prescriptionReportName");
// if (prescriptionReportResult.files.length > 0) {
//     isUpdateDoc = true;
//     form = prescriptionReportResult.formData;
// }
// console.log('prescriptionReportResult', prescriptionReportResult);

// const invoiceReportResult = AppendFileForRecords(invoiceReport, form, "invoiceReport", "invoiceReportName");
// if (invoiceReportResult.files.length > 0) {
//     isUpdateDoc = true;
//     form = invoiceReportResult.formData;
// }
// console.log('invoiceReportResult', invoiceReportResult);

//         var data = {
//             id: appointmentId,
//             prescriptionRecords: prescriptionFile.files,
//             medicalRecords: medicalFile.files,
//             procedureRecords: procedureFile.files,
//             // prescriptionReport: prescriptionReport.files,
//             // invoiceReport: invoiceReport.files,

//             prescriptionReport: prescriptionReportResult.files,
//             invoiceReport: invoiceReportResult.files,
            
//             removeRecords: removeFile,
//             clientId: currentClinic?.clinic?._id
//         };

//         const formData = await axios.post('/appointment/draftrecords', { data }, jsonHeader).then((response) => {
//             return response;
//         });

//         if (isUpdateDoc) {
//             await axios.post('/appointment/updatedocument', form, multipartHeader).then((response) => {
//                 return response;
//             });
//         }

//         return formData;
//     } catch (error) {
//         console.log(error);
//     }
// };


export const SaveAsDraftApi = async (prescription, medical, procedure, removeFile, appointmentId, prescriptionReport, invoiceReport, vaccineCertificate) => {
    try {
        const { currentClinic } = store.getState();
        const jsonHeader = GetJSONHederWithToken();
        const multipartHeader = GetMultipartHederWithToken();
        let isUpdateDoc = false;

        let form = new FormData();
        form.append("clientId", currentClinic?.clinic?._id);
        form.append("appointmentId", appointmentId);

        const prescriptionFile = AppendFileForRecords(prescription, form, "prescription", "prescriptionName");
        if (prescriptionFile.files.length > 0) {
            isUpdateDoc = true;
            form = prescriptionFile.formData;
        }

        const medicalFile = AppendFileForRecords(medical, form, "medical", "medicalName");
        if (medicalFile?.files?.length > 0) {
            isUpdateDoc = true;
            form = medicalFile.formData;
        }

        const procedureFile = AppendFileForRecords(procedure, form, "procedure", "procedureName");
        if (procedureFile?.files?.length > 0) {
            isUpdateDoc = true;
            form = procedureFile.formData;
        }

        const prescriptionReportResult = AppendFileForRecords(prescriptionReport, form, "prescriptionReport", "prescriptionReportName");
        if (prescriptionReportResult.files.length > 0) {
            isUpdateDoc = true;
            form = prescriptionReportResult.formData;
        }

        const invoiceReportResult = AppendFileForRecords(invoiceReport, form, "invoiceReport", "invoiceReportName");
        if (invoiceReportResult.files.length > 0) {
            isUpdateDoc = true;
            form = invoiceReportResult.formData;
        }
         const vaccineCertificateResult = AppendFileForRecords(vaccineCertificate, form, "vaccineCertificate", "vaccineCertificateName");
        if (vaccineCertificateResult.files.length > 0) {
            isUpdateDoc = true;
            form = vaccineCertificateResult.formData;
        }

     
        const data = {
            id: appointmentId,
            removeRecords: removeFile,
            clientId: currentClinic?.clinic?._id,
        };

        if (prescriptionFile?.files?.length > 0) {
            data.prescriptionRecords = prescriptionFile.files;
        }
        if (medicalFile?.files?.length > 0) {
            data.medicalRecords = medicalFile.files;
        }
        if (procedureFile?.files?.length > 0) {
            data.procedureRecords = procedureFile.files;
        }
        if (prescriptionReportResult?.files?.length > 0) {
            data.prescriptionReport = prescriptionReportResult.files;
        }
        if (invoiceReportResult?.files?.length > 0) {
            data.invoiceReport = invoiceReportResult.files;
        }
         if (vaccineCertificateResult?.files?.length > 0) {
            data.vaccineCertificate = vaccineCertificateResult.files;
        }

        const formData = await axios.post('/appointment/draftrecords', { data }, jsonHeader);

        if (isUpdateDoc) {
            await axios.post('/appointment/updatedocument', form, multipartHeader);
        }

        return formData;
    } catch (error) {
        console.log(error);
    }
};


export const GetAppointmentOverview = async(page, size, param) => {
    try {
        const {currentClinic} = store.getState();
        var header = GetJSONHederWithToken();
        const formData = await axios.get('/appointment/getappointments?clinicId=' + currentClinic
            ?.clinic
                ?._id + '&page=' + page + '&size=' + size + '' + param, header);
        return formData

    } catch (error) {
        console.log(error)
    }
}

export const GetAppointmentCountToday = async() => {
    try {
        const {currentClinic} = store.getState();
        var header = GetJSONHederWithToken();
        const formData = await axios.get('/appointment/getappointmentcount?clinicId=' + currentClinic
            ?.clinic
                ?._id, header);
        return formData

    } catch (error) {
        console.log(error)
    }
}
export const GetAppointmentConsultation = async(id) => {
    try {
        var header = GetJSONHederWithToken();
        const formData = await axios.get('/appointment/getappointmentconsultation?id=' + id, header);

        if (formData.status == 200) 
            return formData.data;
        
        return null;

    } catch (error) {
        console.log(error)
    }
}
export const GetAppointmentById = async(id) => {
    try {
        var header = GetJSONHederWithToken();
        const formData = await axios.get('/appointment/getappointmentbyid?id=' + id, header);

        if (formData.status == 200) 
            return formData.data;
        
        return null;

    } catch (error) {
        console.log(error)
    }
}
export const GetPatientAppointmentDetailsForFollowUpAndCancel = async(id) => {
    try {
        var header = GetJSONHederWithToken();
        const formData = await axios.get('/patient/getpatientandappointmentdetails?id=' + id, header);

        if (formData.status == 200) 
            return formData.data;
        
        return null;

    } catch (error) {
        console.log(error)
    }
}
export const GetCurrentRecords = async(id) => {
    try {
        var header = GetJSONHederWithToken();
        const formData = await axios.get('/appointment/getcurrentrecords?id=' + id, header);

        if (formData.status == 200) 
            return formData.data;
        
        return null;

    } catch (error) {
        console.log(error)
    }
}
export const SetFollowUpAppointment = async(appointmentId,followUpDate,followUpTimeSlot) => {
    try {
        var header = GetJSONHederWithToken();
        var data = {
            id: appointmentId,
            appointmentDate:followUpDate,
            timeSlot:followUpTimeSlot
        }
        const formData = await axios.post('/appointment/followup',{data}, header);
            return formData;
        

    } catch (error) {
        console.log(error)
    }
}

export const RescheduleAppointment = async(appointmentId,rescheduleDate,rescheduleTimeSlot) => {
    try {
        var header = GetJSONHederWithToken();
        var data = {
            id: appointmentId,
            appointmentDate:rescheduleDate,
            timeSlot:rescheduleTimeSlot
        }
        const formData = await axios.post('/appointment/reschedule',{data}, header);
            return formData;
        

    } catch (error) {
        console.log(error)
    }
}
export const Cancel = async(appointmentId) => {
    try {
        var header = GetJSONHederWithToken();
        var data = {
            id: appointmentId,
        }
        const formData = await axios.post('/appointment/cancelled',{data}, header);
            return formData;
        

    } catch (error) {
        console.log(error)
    }
}
export class Appointment
{
    constructor(id, patientId, doctorId, mobile, name, prefix,gender, age, birthDate, appointmentDate,
                timeSlot, doctorName, reason, virtualConsultation, clientId,instantAppointment,clinicPatientId , abhaNumber,abhaAddress, speciality,address,pincode,district,state,isFollowUp,googleLink, tokenNumber)
    {
        this.id = id,
        this.mobile = mobile;
        this.name = name;
        this.prefix=prefix;
        this.gender = gender;
        this.age = age;
        this.birthDate = birthDate&&birthDate!=""?birthDate:undefined;
        this.appointmentDate = appointmentDate;
        this.timeSlot = timeSlot;
        this.reason = reason;
        this.virtualConsultation = virtualConsultation
        this.patientId = patientId;
        this.doctorId = doctorId;
        this.doctorName = doctorName;
        this.clientId = clientId;
        this.instantAppointment=instantAppointment;
        this.clinicPatientId=clinicPatientId;
        this.abhaNumber=abhaNumber;
        this.abhaAddress=abhaAddress;
        this.speciality=speciality;
        this.address=address,
        this.pincode=pincode,
        this.district=district,
        this.state=state,
        this.isFollowUp=isFollowUp;
        this.googleLink=googleLink;
        this.tokenNumber=tokenNumber;
    }
}