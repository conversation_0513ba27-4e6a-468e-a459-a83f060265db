import { Router } from "express";
import { addRecentUsers, getRecentUsers } from "../controllers/user.controller.js";

const userRouter = Router();

userRouter.route("/addchatuser").post(async (req, res, next) => {
    try {
        return await addRecentUsers(req, res);
    }
    catch (e) {
        next(e)
    }
});

userRouter.route("/getchatuser").get(async (req, res, next) => {
    try {
        return await getRecentUsers(req, res);
    }
    catch (e) {
        next(e)
    }
});

export default userRouter;
