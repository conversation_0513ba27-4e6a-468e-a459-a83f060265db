import { jest } from "@jest/globals";
const { mockStaffHelper } = await import("../mocks/mock.staff.helper");
mockStaffHelper();
const { getStaff } = await import('../../controllers/staffs/staffs.controller.js'); 
const { staffById }= await import('../../helpers/staff/staff.helper.js');


describe('getStaff', () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: {}
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
        staffById.mockReset();
    });

    it('should return staff data successfully', async () => {
        const mockData = {
            "1": {
                "7": 3
            },
            "address": {
                "house": "",
                "street": "",
                "landmarks": "",
                "city": "",
                "pincode": ""
            },
            "modified": {
                "by": {
                    "id": "662ca0ab1a2431e16c41ebae",
                    "name": "<PERSON><PERSON><PERSON><PERSON>"
                },
                "on": "2024-08-15T12:26:01.871Z"
            },
            "_id": "662ca0ad1a2431e16c41ebb1",
            "staffId": "CHENNAI01",
            "firstName": "Venkatesh",
            "lastName": "Raja",
            "isDoctor": true,
            "mobile": "**********",
            "countryCode": "+91",
            "email": "<EMAIL>",
            "isAdmin": false,
            "deleted": false,
            "userId": "662ca0ab1a2431e16c41ebae",
            "documents": [],
            "accountName": "",
            "age": 25,
            "bankName": "",
            "birthday": null,
            "documentNumber": "",
            "documentType": "",
            "gender": "",
            "ifsc": "",
            "profilePic": "",
            "specialization": "",
            "clinic": "662ca0a41a2431e16c41ebaa",
            "availableTimeSlot": [
                {
                    "weekDay": ["Sun", "Thu", "Fri", "Sat"],
                    "timeSlot": [
                        {
                            "start": "6:30 PM",
                            "end": "10:30 PM",
                            "_id": "66bdf3d9c790ba7e0fa0deab"
                        }
                    ],
                    "slotDuration": 20,
                    "_id": "66bdf3d9c790ba7e0fa0deaa"
                }
            ]
        };
        
        staffById.mockResolvedValue(mockData);
        req.query = { id: '12345' };

        await getStaff(req, res);

        expect(staffById).toHaveBeenCalledWith('12345');
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockData);
    });

});
