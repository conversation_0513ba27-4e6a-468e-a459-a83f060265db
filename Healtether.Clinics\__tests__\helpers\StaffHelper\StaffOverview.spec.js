
import { jest } from "@jest/globals";
import { Staff } from '../../../model/clinics.model.js';
import { staffOverview } from '../../../helpers/staff/staff.helper.js'; 
import { setup, teardown } from "../../../setup.js"; 
import mongoose from 'mongoose';

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); // Set up MongoDB connection
});

afterAll(async () => {
  await teardown(); // Tear down MongoDB connection
});

describe('staffOverview function', () => {
  const clientId = new mongoose.Types.ObjectId(); 
  beforeEach(async () => {
    await Staff.deleteMany({}); 
    const staff1 = new Staff({
      firstName: 'John',
      lastName: 'Doe',
      staffId: 'STF001',
      mobile: '1234567890',
      isAdmin: false,
      prefix:"Mr.",
      isDoctor: true,
      email: '<EMAIL>',
      createdOn: new Date(),
      modifiedOn: new Date(),
      clinic: clientId, 
    });

    const staff2 = new Staff({
      firstName: 'Jane',
      lastName: 'Doe',
      staffId: 'STF002',
      mobile: '0987654321',
      isAdmin: true,
      isDoctor: false,
      prefix:"Mr.",
      email: '<EMAIL>',
      createdOn: new Date(),
      modifiedOn: new Date(),
      clinic: clientId, 
    });

    await staff1.save();
    await staff2.save();
  });

  afterEach(async () => {
    await Staff.deleteMany({});
  });

  it('should return staff collection and total count with valid client ID and no keyword', async () => {
    const result = await staffOverview(clientId, 0, 10, '', null, null, null);
    
    expect(result.data).toHaveLength(2); 
    expect(result.totalCount).toBe(2); 
    expect(result.data[0].firstName).toBe('John'); 
  });

  it('should apply keyword filtering when keyword is provided', async () => {
    const keyword = 'John';

    const result = await staffOverview(clientId, 0, 10, keyword, null, null, null);
    
    expect(result.data).toHaveLength(1); 
    expect(result.data[0].firstName).toBe('John'); 
  });

  it('should return an empty array if no staff match the keyword', async () => {
    const result = await staffOverview(clientId, 0, 10, 'Nonexistent', null, null, null);
    
    expect(result.data).toHaveLength(0); 
    expect(result.totalCount).toBe(0); 
  });

  it('should filter by status when provided', async () => {
    const result = await staffOverview(clientId, 0, 10, '', null, null, 'Doctor');
    
    expect(result.data).toHaveLength(1); 
    expect(result.data[0].isDoctor).toBe(true); 
  });
});
