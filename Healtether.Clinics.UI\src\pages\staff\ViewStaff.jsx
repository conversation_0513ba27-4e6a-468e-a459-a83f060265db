import { useState } from "react";
import Sidebar from "components/detail-page/Sidebar";
import profile from "assets/images/upload_image.jpg"
import ViewBankDetails from "components/view-page/ViewBankDetails";
import ViewPersonalDetails from "components/view-page/ViewPersonalDetails";
import ViewDocument from "components/view-page/ViewDocument";
import defaultUploadImg from "assets/images/upload_image.jpg"
import { Link, useLoaderData, useNavigate } from "react-router-dom";
import { DEFAULT_UPLOAD_IMAGE } from "utils/Classes";
import { useSelector } from "react-redux";


function ViewStaff() {
    const { staffData } = useLoaderData();
    const { clinic } = useSelector((state) => state.currentClinic);
    const settingsArray = ["Personal-details", "Bank-details", "Documents"];
    const blob_URL = `${import.meta.env.VITE_BLOB_URL}${import.meta.env.VITE_CLINICBLOB_CONTAINER_PREFIX}${clinic._id
        }/staff/`;
    const [activeTab,
        setActiveTab] = useState(0);

    const handleTabClick = (tabNumber) => {
        setActiveTab(tabNumber);
    };
    const navigation = useNavigate();
    return (<div className="flex flex-row h-full space-x-3 font-primary">
        <nav className="w-[24%] tabs flex-col border items-start p-3 text-base font-medium" aria-label="Tabs" role="tablist" data-tabs-vertical="true" aria-orientation="horizontal" >
            <div className='flex flex-row w-full justify-start p-3'>
                <img
                    src={(staffData?.profilePic != null && staffData?.profilePic != ""
                        ? blob_URL + staffData.profilePic
                        : defaultUploadImg)}
                    alt="setimage"
                    onError={"this.src='" + DEFAULT_UPLOAD_IMAGE + "';"}
                    className="h-[60px] w-[60px] rounded-[50%] object-cover" />
                <div className='flex flex-col ml-3'>
                    <span
                        className="uppercase !p-0 h-fit text-warning text-start text-xs font-medium">
                        {(staffData.isAdmin ? "Admin" : (staffData.isDoctor ? "Doctor" : "Staff"))}
                    </span>
                    <span className="capitalize text-secondary text-2xl text-start font-medium">
                        {staffData.firstName + " " + staffData.lastName}
                    </span>
                    <span className="uppercase text-primary !p-0 h-fit text-start text-sm font-medium ">
                        {staffData.specialization}
                    </span>
                </div>
            </div>

            <div class="flex justify-between  border-b w-full py-2">
                <Link to={"edit"} className="btn btn-primary btn-soft btn-md ">
                    Edit Profile
                </Link>
                <button
                    type="button"
                    className="btn btn-secondary btn-soft btn-md" onClick={() => { navigation(-1); }}>Cancel</button>
            </div>

            {settingsArray.map((data, index) => {
                return (
                    <button type="button" key={`tab_button-${index}`} className={`mt-3 btn btn-text btn-secondary active-tab:bg-secondary/10 active-tab:text-black hover:text-secondary hover:bg-secondary/10 ${index == 0 ? "active" : ""} w-full px-10 py-7 justify-start`}
                        id={`tab-${data}`}
                        data-tab={`#tab-${data}-data`}
                        aria-controls={`tab-${data}-data`}
                        role="tab"
                        aria-selected="true" >
                        {data.toString().replace("-", " ")}
                    </button>)
            })}
        </nav>

        
        <div className='h-full w-[76%]'>
            <div id="tab-Personal-details-data" className=" flex flex-col h-full px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Personal-details">
                <ViewPersonalDetails obj={staffData} />
            </div>
            <div id="tab-Bank-details-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Bank-details">
                <ViewBankDetails obj={staffData} />
            </div>
            <div id="tab-Documents-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Documents">
                <ViewDocument obj={staffData} />
            </div>
        </div>
    </div>)
}

export default ViewStaff