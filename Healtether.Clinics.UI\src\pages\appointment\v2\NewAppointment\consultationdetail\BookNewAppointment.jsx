import { useEffect, useState } from "react";
import { useLocation, useNavigate, useNavigation } from "react-router-dom";
import Autocomplete from "../../../../../components/autocomplete/Autocomplete";
import dropdown from "../../../../../components/detail-page/dropdown";
import DefaultTextboxClass, {
  DefaultSelectboxClass,
} from "../../../../../utils/Classes";
import { SearchPatientMobileApi } from "../../../../../services/patient/patient";
import AutocompleteModel from "utils/AutocompleteModel";
import { Icons } from "../../../../../components/detail-page/icons";
import { Input } from "../../../../../components/detail-page/input";
import CustomSelect from "../../../../../components/detail-page/select";
import {
  CalculateAge,
  FindIndexInArrayById,
  generateTimeSlots,
  isNumberTextBox,
  SortAndRemoveDuplicateTimeSlots,
} from "../../../../../utils/CommonMethods";
import { GetDoctorsWithAvailableTimeSlotApi } from "../../../../../services/staff/staff";
import { format, getDay } from "date-fns";
import AddAbhaUsingAddress from "../../../../../components/detail-page/add-abha/AddAbhaUsingAddress";
import AddAbhaUsingMobile from "../../../../../components/detail-page/add-abha/AddAbhaUsingMobile";
import AbhaCreation from "../../../../../components/detail-page/add-abha/AbhaCreation";
import AddAbhaUsingAadharNum from "../../../../../components/detail-page/add-abha/AddAbhaUsingAadhaar";
import {
  ValidateAppointment,
  ValidateBookedConsultation,
} from "../../../../../validation/Appointment";
import {
  AppointmentSubmitApi,
  consultationCheckIn,
} from "../../../../../services/appointment/appointment";
import { BookConsultation } from "../../../../../services/booked-consultation/bookedConsultation";
import moment from "moment/moment";
import Spinner from "../../../../../components/loader/Spinner";
import store from "../../../../../store/store";
import { useSelector } from "react-redux";
import { es } from "date-fns/locale";
import { SPECIALISATION_LIST } from "../../../../../utils/OptionList";

export function BookNewAppointment({ fetchQueueData, setTab, doctors }) {
  const { user } = useSelector((state) => state.user);
  const { state } = useLocation();
  let bookedConsultData = {};
  const navigate = useNavigate();
  const currentDate = new Date().toISOString().split("T")[0];
  const [selectedPatient, setselectedPatient] = useState(() =>
    appointmentData?.patientId != null
      ? {
          _id: appointmentData.patientId,
          firstName: appointmentData.name.split(" ")[0],
          lastName: appointmentData.name.split(" ")[1],
          clinicPatientId: appointmentData.clinicPatientId,
        }
      : {}
  );
  const [patientMobile, setpatientMobile] = useState(() =>
    appointmentData?.mobile != null ? appointmentData.mobile : ""
  );
  const [patientName, setpatientName] = useState({
    firstName: appointmentData?.firstName || "",
    lastName: appointmentData?.lastName || "",
  });
  const [patientPrefix, setpatientPrefix] = useState(() =>
    appointmentData?.prefix != null ? appointmentData.prefix : ""
  );
  const [patientSpeciality, setPatientSpeciality] = useState(() =>
    appointmentData?.speciality != null ? appointmentData.speciality : ""
  );
  const [patientGender, setpatientGender] = useState(() =>
    appointmentData?.gender != null ? appointmentData.gender : ""
  );
  const [patientBirthday, setPatientBirthday] = useState(() =>
    appointmentData?.birthDate != null
      ? format(new Date(appointmentData.birthDate), "yyyy-MM-dd")
      : ""
  );
  const [patientAge, setpatientAge] = useState(() =>
    appointmentData?.age != null ? appointmentData.age : ""
  );
  const [patientAddress, setpatientAddress] = useState(() =>
    appointmentData?.address ? appointmentData.address : ""
  );
  const [patientPincode, setpatientPincode] = useState(() =>
    appointmentData?.pincode ? appointmentData.pincode : ""
  );
  const [patientDistrict, setpatientDistrict] = useState(() =>
    appointmentData?.district ? appointmentData.district : ""
  );
  const [patientState, setpatientState] = useState(() =>
    appointmentData?.state ? appointmentData.state : ""
  );
  const [tokenNumber, setTokenNumber] = useState(0);
  const [googleLink, setGoogleLink] = useState({});

  let loadingDoctor = false;
  const navigation = useNavigation();
  const busy = navigation.state === "submitting";

  var appointmentData = null;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [appoitmentDetails, setAppoitmentDetails] = useState({});
  const [timeSlots, setTimeSlots] = useState(() => []);
  const [address, setAddress] = useState("");
  const [abhaNumber, setAbhaNumber] = useState("");
  const [selectedDoctor, SetSelectedDoctor] = useState(() =>
    appointmentData?.doctorId != null ? appointmentData.doctorId : "0"
  );
  const [selectedDoctorName, SetSelectedDoctorNam] = useState(() =>
    appointmentData?.doctorName != null ? appointmentData.doctorName : ""
  );
  const [selectedTimeSlot, SetSelectedTimeSlot] = useState(() =>
    appointmentData?.timeSlot != null ? appointmentData.timeSlot : ""
  );
  const [virtual, setVirtual] = useState(() =>
    appointmentData?.virtualConsultation != null
      ? appointmentData.virtualConsultation
      : false
  );

  const [disableProperty, setDisableProperty] = useState(false);

  const [appointmentDate, setAppointmentDate] = useState(() =>
    appointmentData?.appointmentDate != null
      ? format(new Date(appointmentData.appointmentDate), "yyyy-MM-dd")
      : ""
  );
  const [reason, setReason] = useState(() =>
    appointmentData?.reason != null ? appointmentData.reason : ""
  );
  const [patientAbhaNumber, setpatientAbhaNumber] = useState(() =>
    appointmentData?.abhaNumber != null ? appointmentData.abhaNumber : ""
  );
  const [patientAbhaAddress, setpatientAbhaAddress] = useState(() =>
    appointmentData?.abhaAddress != null ? appointmentData.abhaAddress : ""
  );
  const [selectedText, setSelectedText] = useState();
  const [addAbhaUsing, setAddAbhaUsing] = useState("");
  const [isCreatingAddress, setIsCreatingAddress] = useState(false);
  const [isFollowUp, setIsFollowUp] = useState(false);

  const {
    isOpen,
    setIsOpen,
    DropdownMenu,
    DropdownMenuItem,
    DropdownMenuContent,
  } = dropdown();

  const ResetPatientDetail = (onNewPatientClick = false) => {
    if (!onNewPatientClick) {
      setpatientMobile("");
    }
    setselectedPatient({});
    setPatientBirthday("");
    setpatientAge("");
    setpatientGender("");
    setpatientPrefix("");
    setpatientName({ firstName: "", lastName: "" });
    setSelectedText(undefined);
    setPatientSpeciality("");
    setpatientAddress("");
    setpatientAbhaNumber("");
    setpatientAbhaAddress("");
    setpatientPincode("");
    setpatientDistrict("");
    setpatientState("");
  };
  const ResetAppointmentDetail = () => {
    setAppointmentDate("");
    SetSelectedDoctor("0");
    setReason("");
    SetSelectedTimeSlot("0");
    setGoogleLink({});
    setVirtual(false);
    setIsFollowUp(false);
    setPatientSpeciality("");
    setTokenNumber(0);
  };
  const searchPatientMobile = (text, size) => {
    return SearchPatientMobileApi(text, size);
  };

  const OnSelectPatient = (data) => {
    if (data != undefined) {
      setselectedPatient(data);
      if (data.mobile != undefined) {
        setpatientMobile(data.mobile);
      }
      if (data.firstName != undefined)
        setpatientName({ firstName: data.firstName, lastName: data.lastName });
      if (data.gender != undefined) setpatientGender(data.gender);
      if (data.prefix != undefined) setpatientPrefix(data.prefix);
      if (data.abhaNumber) setpatientAbhaNumber(data.abhaNumber);
      if (data.abhaAddress) setpatientAbhaAddress(data.abhaAddress);
      if (data.address) setpatientPincode(data.address.pincode);
      if (data.address) setpatientDistrict(data.address.district);
      if (data.address) setpatientState(data.address.state);
      if (data.address) setpatientAddress(data.address.house);
      setPatientBirthday("");
      if (data.birthday != undefined) {
        var birthday = data.birthday.split("T")[0];
        setPatientBirthday(birthday);
      }
      if (data.age != undefined) {
        setpatientAge(data.age);
      } else {
        setpatientAge(CalculateAge(data.birthday));
      }

      var selected =
        patientMobile + `${patientName.firstName} ${patientName.lastName}`;
      setSelectedText(selected);
      setDisableProperty(true);
    } else {
      setTokenNumber(0);
      setDisableProperty(false);
      ResetPatientDetail(true);
    }
  };
  const ChangeSelectedDoctor = (value) => {
    var doctor = [...doctors];
    SetSelectedDoctor(value);
    var i = FindIndexInArrayById(doctor, value);
    SetSelectedDoctorNam(doctor[i].firstName + " " + doctor[i].lastName);
    AssignTimeSlots(value, undefined);
  };
  let dayOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const AssignTimeSlots = (id, appDate) => {
    let availableTimeSlots = [];
    let doctorId = id ? id : selectedDoctor;
    let dateSelected = appDate ? appDate : appointmentDate;

    if (doctorId && dateSelected) {
      let filteredDoctor = doctors.filter((item) => item._id == doctorId);
      let dayId = getDay(new Date(dateSelected));

      if (filteredDoctor.length > 0) {
        let timeSlotsBasedOnDoctor = filteredDoctor[0].availableTimeSlot.filter(
          (item) => item.weekDay.indexOf(dayOfWeek[dayId]) > -1
        );
        for (const doctorTimeSlots of timeSlotsBasedOnDoctor) {
          for (const doctorTimeSlot of doctorTimeSlots.timeSlot) {
            let generateSlots = generateTimeSlots(
              doctorTimeSlot.start,
              doctorTimeSlot.end,
              doctorTimeSlots.slotDuration
            );
            availableTimeSlots = availableTimeSlots.concat(generateSlots);
          }
        }
        if (availableTimeSlots.length > 0) {
          availableTimeSlots = availableTimeSlots.map(
            (item) => item.start + " - " + item.end
          );
        }
        availableTimeSlots =
          SortAndRemoveDuplicateTimeSlots(availableTimeSlots);
        setTimeSlots(availableTimeSlots);
      }
    }
  };

  const onSubmit = async (type) => {
    let appointmentData = {
      prefix: patientPrefix,
      name: `${patientName.firstName + " " + patientName.lastName}`,
      gender: patientGender,
      age: patientAge,
      birthDate: patientBirthday,
      mobile: patientMobile,
      address: patientAddress,
      pincode: patientPincode,
      state: patientState,
      district: patientDistrict,
      abhaNumber: patientAbhaNumber,
      patientId: selectedPatient._id,
      clinicPatientId: selectedPatient.patientId,
      virtualConsultation: virtual,
      abhaAddress: patientAbhaAddress,
      doctorId: selectedDoctor,
      doctorName: selectedDoctorName,
      speciality: patientSpeciality,
      tokenNumber: tokenNumber,
      reason: reason,
      isFollowUp,
    };

    console.log("appointmentData", appointmentData);

    if (type == "queue") {
      appointmentData.appointmentDate = appointmentDate || new Date();
      if (virtual) {
        appointmentData.timeSlot = selectedTimeSlot;
        appointmentData.googleLink = googleLink;
      }
      const validation = ValidateAppointment(appointmentData);
      if (!validation) {
        return false;
      }
    } else {
      (appointmentData.appointmentDate = new Date(appointmentDate)),
        (appointmentData.timeSlot = selectedTimeSlot);
      const validation = ValidateBookedConsultation(appointmentData);
      if (!validation) {
        return false;
      }
    }
    setIsSubmitting(true);
    var result =
      type === "booked"
        ? await BookConsultation(appointmentData, "")
        : await AppointmentSubmitApi(appointmentData, "");

    if (result) {
      if (type == "queue") {
        fetchQueueData(true);
        state?.appointmentDetails &&
          handleCheckIn(state?.appointmentDetails?._id);
      }
      setIsSubmitting(false);
      bookedConsultData = null;
      ResetPatientDetail();
      ResetAppointmentDetail();
      navigate(".", { replace: true, state: {} });
    }
  };

  const handleCheckIn = async (id) => {
    await consultationCheckIn(id);
    bookedConsultData = null;
    ResetPatientDetail();
    ResetAppointmentDetail();
    navigate(".", { replace: true, state: {} });
  };

  useEffect(() => {
    if (state?.appointmentDetails) {
      bookedConsultData = state?.appointmentDetails;
      const nameParts = bookedConsultData?.name?.trim().split(" ");
      let patientId = bookedConsultData?.clinicPatientId;
      let birthDate = bookedConsultData?.birthDate;
      let _id = bookedConsultData?.patientId;
      let address = {
        house: bookedConsultData?.address,
        state: bookedConsultData?.state,
        district: bookedConsultData?.district,
        pincode: bookedConsultData?.pincode,
      };
      let appointmentData = {
        firstName: nameParts[0],
        lastName: nameParts[1],
        ...bookedConsultData,
        birthday: birthDate,
      };
      bookedConsultData?.tokenNumber &&
        setTokenNumber(bookedConsultData?.tokenNumber);
      OnSelectPatient({ ...appointmentData, patientId, _id, address });
      setIsFollowUp(bookedConsultData.isFollowUp);
      if (state.appointmentDetails.virtualConsultation) {
        setVirtual(state?.appointmentDetails.virtualConsultation);
        setGoogleLink(state?.appointmentDetails.googleLink);
        setPatientSpeciality(state?.appointmentDetails.speciality);
        SetSelectedDoctor(state?.appointmentDetails.doctorId);
        SetSelectedDoctorNam(state?.appointmentDetails.doctorName);
        setAppointmentDate(
          format(
            new Date(state?.appointmentDetails.appointmentDate),
            "yyyy-MM-dd"
          )
        );
        SetSelectedTimeSlot(state?.appointmentDetails.timeSlot);
      }
      setTab(0);
    }
  }, [state]);

  let [filteredDoctor, setFilteredDoctor] = useState([]);
  useEffect(() => {
    let doctor = doctors?.filter(
      (doctor) => doctor.specialization === patientSpeciality
    );
    setFilteredDoctor(doctor);
  }, [patientSpeciality]);

  

  return (
    <>
      <AddAbhaUsingAddress
        addAbhaUsing={addAbhaUsing}
        setAddAbhaUsing={setAddAbhaUsing}
        setpatientMobile={setpatientMobile}
        OnSelectPatient={OnSelectPatient}
      />
      <AddAbhaUsingMobile
        addAbhaUsing={addAbhaUsing}
        setAddAbhaUsing={setAddAbhaUsing}
        patientMobile={patientMobile}
        setpatientMobile={setpatientMobile}
        OnSelectPatient={OnSelectPatient}
        setAddress={setAddress}
        address={address}
      />
      <AddAbhaUsingAadharNum
        addAbhaUsing={addAbhaUsing}
        setAddAbhaUsing={setAddAbhaUsing}
        setpatientMobile={setpatientMobile}
        OnSelectPatient={OnSelectPatient}
        user={user}
      />
      <AbhaCreation
        addAbhaUsing={addAbhaUsing}
        setAddAbhaUsing={setAddAbhaUsing}
        setIsCreatingAddress={setIsCreatingAddress}
        isCreatingAddress={isCreatingAddress}
        setAddress={setAddress}
        address={address}
        OnSelectPatient={OnSelectPatient}
        setpatientMobile={setpatientMobile}
        patientMobile={patientMobile}
        user={user}
      />
      <div>
        <section className=" flex items-center justify-between text-color_dark font-semibold xl:text-lg">
          Personal details of the Patient
          <div className="px-5 py-1 bg-backcolor_grey rounded-lg text-xs font-normal text-color_muted ">
            Patient ID
            <div className=" text-color_dark text-base truncate">
              {selectedPatient.patientId}
            </div>
          </div>
        </section>

        <section className="grid lg:grid-cols-1 xl:grid-cols-2 gap-4 gap-y-5 mt-3 font-secondary">
          <Autocomplete
            options={[]}
            value={patientMobile}
            placeHolder="Mobile no."
            name="mobile"
            onChange={setpatientMobile}
            customClass={`${DefaultSelectboxClass} w-full `}
            search={searchPatientMobile}
            searchMapper={searchPatientMobileMapper}
            onSelect={(data) => {
              OnSelectPatient(data);
            }}
            maxLength={10}
          />
          <DropdownMenu
            className={`${
              patientMobile.length === 10
                ? ""
                : "pointer-events-none opacity-50"
            }`}
          >
            <div
              className={`h-full w-max border border-color_muted/20 rounded-lg flex items-center gap-3 pl-4 cursor-pointer bg-white text-color_muted font-medium text-md relative `}
            >
              Add ABHA
              <div className="h-full px-4 flex items-center justify-center border-l border-color_muted/20">
                <Icons.arrow />
              </div>
            </div>
            <DropdownMenuContent>
              <DropdownMenuItem
                handleClick={() => setAddAbhaUsing("mobile number")}
              >
                Add using mobile number
              </DropdownMenuItem>
              <DropdownMenuItem
                handleClick={() => setAddAbhaUsing("Aadhar number")}
              >
                Add using Aadhar number
              </DropdownMenuItem>
              <DropdownMenuItem handleClick={() => setAddAbhaUsing("address")}>
                Add using ABHA 
              </DropdownMenuItem>
              <DropdownMenuItem
                handleClick={() => setAddAbhaUsing("abha creation")}
              >
                ABHA Creation
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>{" "}
          <CustomSelect
            medium={true}
            value={patientPrefix}
            // isDisabled={disableProperty}
            data={[
              { value: "Mr." },
              { value: "Miss." },
              { value: "Mrs." },
              { value: "Dr." },
              { value: "Prof." },
            ]}
            handleClick={(prop) => {
              setpatientPrefix(prop.value);
            }}
            placeholder={"Prefix"}
          />
          <input
            name="Firstname"
            placeholder="First name"
            autoComplete="off"
            type="text"
            value={patientName?.firstName}
            readOnly={disableProperty}
            className={`w-full ${DefaultTextboxClass} `}
            onChange={(e) => {
              setpatientName({ ...patientName, firstName: e.target.value });
            }}
          />
          <input
            name="Lastname"
            placeholder="Last name"
            autoComplete="off"
            type="text"
            value={patientName?.lastName}
            readOnly={disableProperty}
            className={`w-full ${DefaultTextboxClass} `}
            onChange={(e) => {
              setpatientName({ ...patientName, lastName: e.target.value });
            }}
          />
          <input
            name="abhaNumber"
            placeholder="ABHA Number"
            autoComplete="off"
            type="text"
            value={patientAbhaNumber}
            readOnly={disableProperty}
            className={`w-full ${DefaultTextboxClass} `}
            onChange={(e) => {
              setpatientAbhaNumber({
                ...patientAbhaNumber,
                lastName: e.target.value,
              });
            }}
          />
          <input
            name="abhaAddress"
            placeholder="ABHA Address"
            type="text"
            value={patientAbhaAddress}
            readOnly={disableProperty}
            className={`w-full ${DefaultTextboxClass} `}
            onChange={(e) => {
              setpatientAbhaAddress(e.target.value);
            }}
          />
          <div className="grid grid-cols-12 gap-4">
            <input
              name="Age"
              placeholder="Age"
              autoComplete="off"
              type="text"
              value={patientAge}
              readOnly={disableProperty}
              className={`w-full col-span-4 ${DefaultTextboxClass} `}
              onChange={(e) => {
                if (isNumberTextBox(e)) {
                  setpatientAge(e.target.value);
                }
              }}
            />
            <input
              type="date"
              name="birthDate"
              placeholder="Birthdate"
              autoComplete="off"
              min="1900-01-01"
              max={currentDate}
              value={patientBirthday}
              readOnly={disableProperty}
              onChange={(e) => {
                setPatientBirthday(e.target.value);
                setpatientAge(CalculateAge(e.target.value));
              }}
              className={`w-full col-span-8 cursor-pointer ${DefaultTextboxClass} `}
            />
          </div>
          <CustomSelect
            medium={true}
            value={patientGender}
            isDisabled={disableProperty}
            data={[{ value: "Male" }, { value: "Female" }]}
            handleClick={(prop) => {
              setpatientGender(prop.value);
            }}
            placeholder={"Gender"}
          />
          <input
            name="address"
            placeholder="Patient Address"
            type="text"
            value={patientAddress}
            readOnly={disableProperty}
            className={`w-full ${DefaultTextboxClass} `}
            onChange={(e) => {
              setpatientAddress(e.target.value);
            }}
          />
          <input
            name="pincode"
            placeholder="Patient pincode"
            type="text"
            value={patientPincode}
            readOnly={disableProperty}
            className={`w-full ${DefaultTextboxClass} `}
            onChange={(e) => {
              setpatientPincode(e.target.value);
            }}
          />
          <input
            name="district"
            placeholder="Patient district"
            type="text"
            value={patientDistrict}
            readOnly={disableProperty}
            className={`w-full ${DefaultTextboxClass} `}
            onChange={(e) => {
              setpatientDistrict(e.target.value);
            }}
          />
          <input
            name="state"
            placeholder="Patient state"
            type="text"
            value={patientState}
            readOnly={disableProperty}
            className={`w-full ${DefaultTextboxClass} `}
            onChange={(e) => {
              setpatientState(e.target.value);
            }}
          />
          <div className="flex items-center gap-2 text-lg text-dark font-medium font-primary">
            <input
              type="checkbox"
              id="virutalBox"
              value={virtual}
              onChange={() => {
                let isOnline = virtual;
                setVirtual(!isOnline);
              }}
            />
            <label htmlFor="virutalBox" className=" cursor-pointer">
              Virtual Consultation
            </label>
          </div>
        </section>
        <div className=" font-semibold mt-7 xl:text-lg">
          Appointment details of the patient
        </div>
        <section className="grid lg:grid-cols-1 xl:grid-cols-2 gap-4 gap-y-5 mt-3 mb-4 font-secondary">
          <CustomSelect
            isSearch={true}
            medium={true}
            value={patientSpeciality}
            data={[...SPECIALISATION_LIST]}
            handleClick={(prop) => {
              setPatientSpeciality(prop.value);
            }}
            placeholder={"Speciality"}
          />
          <CustomSelect
            isSearch={false}
            medium={false}
            data={filteredDoctor}
            handleClick={(prop) => {
              ChangeSelectedDoctor(prop._id);
            }}
            value={selectedDoctorName}
            placeholder={"Attending Doctor"}
          />
          <input
            name="Reason"
            placeholder="Appointment Brief"
            autoComplete="off"
            type="text"
            className={`w-full lg:grid-cols-1  xl:col-span-2 ${DefaultTextboxClass} `}
            onChange={(e) => {
              setReason(e.target.value);
            }}
          />

          <div className="lg:grid-cols-1  xl:col-span-2 flex justify-center mb-2">
            <button
              className={`bg-white text-color_dark  h-10 w-fit flex items-center rounded-lg border px-4 cursor-pointer text-base font-primary font-medium`}
              onClick={() => onSubmit("queue")}
            >
              Add to Queue {isSubmitting && <Spinner show={true} />}
            </button>
          </div>
          <input
            type="date"
            name="appointmentDate"
            placeholder="Appointment Date"
            autoComplete="off"
            min={currentDate}
            value={appointmentDate}
            onChange={(e) => {
              setAppointmentDate(e.target.value);
              AssignTimeSlots(undefined, e.target.value);
            }}
            className={`w-full cursor-pointer ${DefaultTextboxClass} `}
          />
          <CustomSelect
            bottom={true}
            medium={true}
            normal={true}
            value={selectedTimeSlot}
            data={timeSlots?.map((item) => ({ value: item })) || []}
            handleClick={(e) => SetSelectedTimeSlot(e.value)}
            placeholder={"Slot time"}
          />
        </section>

        <section className="mt-6 flex justify-center gap-2 mb-5">
          <button
            disable={isSubmitting}
            onClick={() => onSubmit("booked")}
            type="submit"
            className={`bg-Primary text-white  w-fit flex items-center gap-2 rounded-lg border-2 border-backcolor_grey px-4 py-1.5 cursor-pointer text-base font-primary font-medium`}
          >
            Book appointment {isSubmitting && <Spinner show={true} />}
          </button>
          <div
            onClick={() => {
              ResetPatientDetail();
              ResetAppointmentDetail();
              navigate(".", { replace: true, state: {} });
            }}
            className={`bg-white text-color_dark  w-fit flex items-center rounded-lg border border-Primary px-4 cursor-pointer text-base font-primary font-medium`}
          >
            Clear All
          </div>
        </section>
      </div>
    </>
  );
}

export const searchPatientMobileMapper = (data) => {
  var options = [
    new AutocompleteModel("Add New Patient", undefined, undefined, false),
  ];
  for (let index = 0; index < data.data.length; index++) {
    var option = new AutocompleteModel(
      data.data[index].firstName + " " + data.data[index].lastName,
      "(" + data.data[index].patientId + ") " + data.data[index].mobile,
      data.data[index],
      true
    );
    options.push(option);
  }

  return options;
};
