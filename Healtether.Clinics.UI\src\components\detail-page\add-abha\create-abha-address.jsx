import React, { useEffect, useState } from "react";
import { Input } from "../input";
import { Buttons } from "../appointment/button";
import Spinner from "../../loader/Spinner";
import { getSuggestion } from "../../../services/appointment/abha-m1";

export default function CreateAbhaAddress({
  handleCancel,
  handleSubmit,
  address,
  error,
  setAddress,
  onComplete,
  busy,
  profileData,
  setIsCreatingAddress,
  isCreatingAddress,
}) {
  const [suggestions, setSuggestions] = useState([]);
  const [abhaAddressError, setABHAAddressError] = useState("");
  const handleSelect = (name) => {
    
    setAddress(name);
  };

  useEffect(() => {
    const fetchSuggestions = async () => {
      const suggestion = await getSuggestion();
      console.log("Response :", suggestion);
      setSuggestions(suggestion.response.abhaAddressList || []);
    };

    fetchSuggestions();
  }, []);

 const validateABHAAddress = (value) => {
  if (value.length < 8) {
    return "ABHA address must be at least 8 characters long.";
  }
  if (value.length > 18) {
    return "ABHA address must not exceed 18 characters.";
  }
  if (!/^[a-zA-Z0-9._]+$/.test(value)) {
    return "Only alphanumeric characters, one dot (.) or one underscore (_) are allowed.";
  }
  if (/^[._]|[._]$/.test(value)) {
    return "Dot (.) or underscore (_) cannot be at the beginning or end.";
  }
  if ((value.match(/\./g) || []).length > 1 || (value.match(/_/g) || []).length > 1) {
    return "Only one dot (.) and/or one underscore (_) is allowed.";
  }
  
  // Check if the address contains only letters or only numbers
  if (/^[a-zA-Z._]+$/.test(value)) {
    return "ABHA address must contain at least one number.";
  }
  if (/^[0-9._]+$/.test(value)) {
    return "ABHA address must contain at least one letter.";
  }
  
  return ""; // No error
};
  useEffect(() => {
    if (address) {
      const validationError = validateABHAAddress(address);
      setABHAAddressError(validationError);
      console.log(validationError)
    } else {
      setABHAAddressError("");
    }
  }, [address]);
  return (
    <article className="w-96">
      <div className="text-lg font-semibold text-dark">
      Please choose an ABHA Address from below or create new  ABHA Address:
      </div>
      <section>
        {profileData?.phrAddress?.length > 0 &&
          profileData.phrAddress.map((item, index) => (
            <div
              key={`${item}-${index}`}
              className="flex items-start gap-3 p-2 border-b border-color_muted/20"
            >
              <input
                type="radio"
                name="abha-address"
                checked={item === address}
                onChange={() => {
                  setIsCreatingAddress("no");
                  setAddress(item);
                }}
                className="mt-1.5"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium mr-2">{item}</span>
                </div>
              </div>
            </div>
          ))}

        <div className="flex items-start gap-3 p-2 border-b border-color_muted/20">
          <input
            type="radio"
            name="abha-address"
            onChange={() => {
              setIsCreatingAddress("yes");
              setAddress("");
            }}
            checked={isCreatingAddress == "yes"}
            className="mt-1.5"
          />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium mr-2">Create ABHA Address</span>
            </div>
          </div>
        </div>
      </section>
      {/* <div className=" text-lg font-roboto font-normal text-dark">
        Create ABHA Address
      </div> */}
      {isCreatingAddress == "yes" && (
        <section className="mt-3 flex flex-col gap-2">
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="relative">
                <Input.text
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  placeholder="Enter ABHA address"
                  className={`pr-16 ${error ? "border-red-600 " : ""}`}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-color_muted-foreground z-20">
                  @sbx
                </div>
              </div>
            </div>
            {error && (
              <div className="text-red-500 text-xs font-semibold">{error}</div>
            )}
            {
              <div className="text-red-500 text-sm font-semibold">{abhaAddressError}</div>
            }
          </div>
          <div className="">
            <div className="text-lg font-semibold text-dark">Suggestions</div>
            {/* {error && <p className="text-red-600 mb-2">{error}</p>} */}
            <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
              {suggestions.length > 0 ? (
                suggestions.map((name, index) => (
                  <div
                    key={index}
                    className="h-9 py-2 px-3 rounded-md border-gray duration-300 hover:border-primary border bg-white flex items-center justify-center text-sm font-roboto text-dark cursor-pointer whitespace-nowrap"
                    onClick={() => handleSelect(name)}
                  >
                    {name}
                  </div>
                ))
              ) : (
                <p className="text-gray-500">No suggestions available</p>
              )}
            </div>
          </div>
          <div className=" text-lg font-semibold text-dark">
            Set of rules to create ABHA Address
          </div>
          <div>
            <ul className="list-decimal ml-4">
              <li>Minimum length - 8 characters</li>
              <li>Maximum length - 18 characters</li>
              <li>
                Special characters allowed - 1 dot (.) and/or 1 underscore (_)
              </li>
              <li>
                Special character dot and underscore should be in between.
                Special characters cannot be in the beginning or at the end
              </li>
              <li>
                Alphanumeric - only numbers, only letters, letters_letters,
                letters.letters, number.number, number_number not allowed,Any
                combination of numbers and letters is allowed
              </li>
            </ul>
          </div>
        </section>
      )}

      <footer className=" flex justify-end gap-3 mt-3">
        <Buttons.secondary
          title={"Cancel"}
          classname=""
          onClick={handleCancel}
        />
        <Buttons.secondary
          title={busy ? <Spinner show={true} /> : "Create ABHA"}
          classname=""
          onClick={() => onComplete()}
        />
      </footer>
    </article>
  );
}
