import mongoose from "mongoose";
const tokenSchema = new mongoose.Schema({
  value: {
    type: Number,
    default: 0
  },
  date: {
    type: Date,
    default: Date.now
  },
  clinic: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client', // Assuming this references your CLIENT_COLLECTION
    required: true
  }
});
//const tokenModel = mongoose.model("tokenSchema", tokenSchema);

// Export the model for use in other files
export { tokenSchema };
