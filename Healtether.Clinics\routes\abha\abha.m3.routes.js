import { Router } from "express";
import {
  initiateConsentRequestController,
  checkConsentStatusController,
  notifyConsentRequestController,
  fetchConsentDetailsController,
  requestHealthInformationController,
  notify<PERSON><PERSON><PERSON><PERSON>n<PERSON><PERSON>ontroller,
  patientConsentList
} from "../../controllers/abha/abha.m3.controller.js";
import {
  validateInitiateConsentRequest,
  validateCheckConsentStatus,
  validateNotifyConsentRequest,
  validateFetchConsentDetails,
  validateRequestHealthInformation,
  validateNotifyHealthInformation
} from "../../validation/abha/m3.validation.js";

const abhaM3 = Router();

/**
 * @swagger
 * /abha/m3/initiateconsentrequest:
 *   post:
 *     summary: Initiate Consent Request
 *     description: Initiates a consent request for health information.
 *     tags:
 *       - ABHA M3
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               requestdata:
 *                 type: object
 *                 example: { "patientId": "12345", "consentPurpose": "treatment" }
 *     responses:
 *       200:
 *         description: Successfully initiated consent request
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */
abhaM3.post(
  "/initiateconsentrequestcontroller",
  validateInitiateConsentRequest,
  (req, res, next) => initiateConsentRequestController(req, res, next)
);

/**
 * @swagger
 * /abha/m3/checkconsentstatus:
 *   post:
 *     summary: Check Consent Status
 *     description: Checks the status of a consent request.
 *     tags:
 *       - ABHA M3
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               requestdata:
 *                 type: object
 *                 example: { "consentId": "12345" }
 *     responses:
 *       200:
 *         description: Successfully checked consent status
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */
abhaM3.post(
  "/checkconsentstatuscontroller",
  validateCheckConsentStatus,
  (req, res, next) => checkConsentStatusController(req, res, next)
);

/**
 * @swagger
 * /abha/m3/notifyconsentrequest:
 *   post:
 *     summary: Notify Consent Request
 *     description: Notifies about a consent request.
 *     tags:
 *       - ABHA M3
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               consentId:
 *                 type: string
 *                 example: "12345"
 *               type:
 *                 type: string
 *                 example: "consent"
 *               data:
 *                 type: object
 *                 example: { "status": "approved" }
 *     responses:
 *       200:
 *         description: Successfully notified consent request
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */
abhaM3.post(
  "/notifyconsentrequestcontroller",
  validateNotifyConsentRequest,
  (req, res, next) => notifyConsentRequestController(req, res, next)
);

/**
 * @swagger
 * /abha/m3/fetchconsentdetails:
 *   post:
 *     summary: Fetch Consent Details
 *     description: Fetches details of a consent request.
 *     tags:
 *       - ABHA M3
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               consentArtefactId:
 *                 type: string
 *                 example: "12345"
 *               hiuId:
 *                 type: string
 *                 example: "hiu_12345"
 *     responses:
 *       200:
 *         description: Successfully fetched consent details
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */
abhaM3.post(
  "/fetchconsentdetailscontroller",
  validateFetchConsentDetails,
  (req, res, next) => fetchConsentDetailsController(req, res, next)
);

/**
 * @swagger
 * /abha/m3/requesthealthinformation:
 *   post:
 *     summary: Request Health Information
 *     description: Requests health information based on consent.
 *     tags:
 *       - ABHA M3
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 example: { "consentId": "12345", "hiuId": "hiu_12345" }
 *     responses:
 *       200:
 *         description: Successfully requested health information
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */
abhaM3.post(
  "/requesthealthinformationcontroller",
  validateRequestHealthInformation,
  (req, res, next) => requestHealthInformationController(req, res, next)
);

/**
 * @swagger
 * /abha/m3/notifyhealthinformation:
 *   post:
 *     summary: Notify Health Information
 *     description: Notifies about health information.
 *     tags:
 *       - ABHA M3
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               transaction_id:
 *                 type: string
 *                 example: "12345"
 *     responses:
 *       200:
 *         description: Successfully notified health information
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */
abhaM3.post(
  "/notifyhealthinformationcontroller",
  validateNotifyHealthInformation,
  (req, res, next) => notifyHealthInformationController(req, res, next)
);


abhaM3.get(
  "/patientconsents",
  (req, res, next)=> patientConsentList(req, res, next) 
)

export default abhaM3;