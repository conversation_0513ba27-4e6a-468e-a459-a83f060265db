import { jest } from "@jest/globals";
 await jest.unstable_mockModule("../../../helpers/clinic/group.helper.js", async () => ({
        createGroup: jest.fn(),
        getAllGroup: jest.fn(),
    }));

const { getAllClinicGroup } = await import("../../../controllers/clinic/group.controller.js");
const { getAllGroup } = await import("../../../helpers/clinic/group.helper.js");



describe('getAllClinicGroup', () => {
  let req, res;

  beforeEach(() => {
    req = {}; 
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  });

  it('should return overview data with status 200', async () => {
    const mockOverviewData = [{ groupName: 'Group 1' }, { groupName: 'Group 2' }];
    getAllGroup.mockResolvedValue(mockOverviewData);

    await getAllClinicGroup(req, res);

    expect(getAllGroup).toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(mockOverviewData);
  });

});
