import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { searchByMobile } from '../../../helpers/patient/patient.helper.js'; 
import { Patient } from '../../../model/clinics.model.js'; 
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('searchByMobile function', () => {
  const clinicId = new mongoose.Types.ObjectId(); 
  
  beforeEach(async () => {
    await Patient.deleteMany({}); 
    
    const patient1 = await Patient.create({
      firstName: 'John',
      lastName: 'Doe',
      mobile: '**********',
      patientId: 'PAT001',
      age: 30,
      prefix: "Mr.",
      birthday: new Date('1993-01-01'),
      gender: 'Male',
      email: '<EMAIL>',
      address: '123 Main St',
      height: 180,
      weight: 75,
      documentType: 'ID',
      documentNumber: 'ID123456',
      isDeleted: false,
      clinic: clinicId // Added clinic reference
    });

    const patient2 = await Patient.create({
      firstName: 'John1',
      lastName: 'Doe1',
      mobile: '**********',
      patientId: 'PAT002',
      age: 30,
      prefix: "Mr.",
      birthday: new Date('1993-01-02'),
      gender: 'Male',
      email: '<EMAIL>',
      address: '123 Main St',
      height: 180,
      weight: 75,
      documentType: 'ID',
      documentNumber: 'ID123456',
      deleted: false,
      clinic: clinicId // Added clinic reference
    });
  });

  afterEach(async () => {
    await Patient.deleteMany({}); 
  });

  it('should return patients matching the mobile number search', async () => {
    const search = '123'; 
    const size = 10; 
    const result = await searchByMobile(search, clinicId, size);
    
    expect(result.patientCollection.length).toBeGreaterThan(0);
    expect(result.patientCollection.some(p => p.mobile === '**********')).toBe(true);
  });

  it('should return an empty array when no patients match the search', async () => {
    const search = '000'; 
    const size = 10; 
    const result = await searchByMobile(search, clinicId, size);
    
    expect(result.patientCollection).toHaveLength(0);
  });
});