import { profileOnShare } from "../helper/abha/abha.webhook.helper.js";
import { AbhaResponse } from "../utils/abha.api.js";

export const profileShareHook = async (req, res) => {

    const responseFromAbdm = await profileOnShare(req);

    if (responseFromAbdm?.isSuccess) {
        return new AbhaResponse(true, responseFromAbdm);
    } else {
        res.status(400).json(responseFromAbdm.data);

    }


};