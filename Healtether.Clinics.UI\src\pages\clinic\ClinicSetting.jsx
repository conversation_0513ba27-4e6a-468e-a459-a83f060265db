import ContainerHeading from "components/detail-page/ContainerHeading";
import { useState } from "react";
import DefaultTextboxClass from "utils/Classes";
import PropTypes from "prop-types";

function ClinicSetting({ patientId, staffId, times, googleMeetEmail }) {
  patientId = patientId != null ? patientId : {};
  staffId = staffId != null ? staffId : {};

  return (
    <div className=" space-y-3">
      <ContainerHeading heading={"Patient ID"} />
      <div className="text-text_secondary text-sm font-normal">
        Set default text to patient id prefix or suffix
      </div>
      <div>
        <div className="w-full flex flex-nowrap space-x-4 items-center">
          <input
            type="text"
            name="PatientId_Prefix"
            placeholder="Prefix"
            maxLength={10}
            defaultValue={patientId.prefix}
            className={DefaultTextboxClass + " w-1/2 text-md"}
          />
          <span className="fond-bold text-[20px]">-</span>
          <input
            type="text"
            name="PatientId_Suffix"
            placeholder="Suffix"
            defaultValue={patientId.suffix}
            maxLength={10}
            className={DefaultTextboxClass + " w-1/2 text-md"}
          />
        </div>
      </div>

      <ContainerHeading heading={"Staff ID"} />
      <div className="text-text_secondary text-sm font-normal">
        Set default text to staff id prefix or suffix
      </div>
      <div>
        <div className="w-full flex flex-nowrap space-x-4 items-center">
          <input
            type="text"
            name="StaffId_Prefix"
            placeholder="Prefix"
            defaultValue={staffId.prefix}
            maxLength={10}
            className={DefaultTextboxClass + " w-1/2 text-md"}
          />
          <span className="fond-bold text-[20px]">-</span>
          <input
            type="text"
            name="StaffId_Suffix"
            defaultValue={staffId.suffix}
            placeholder="Suffix"
            maxLength={10}
            className={DefaultTextboxClass + " w-1/2 text-md"}
          />
        </div>
      </div>
      <div>
        <ContainerHeading heading={"Email for Virtual Consultation"} />
        <div className="w-full flex flex-nowrap space-x-4 items-center">
          <input
            type="email"
            name="GoogleMeetEmail"
            placeholder="Email Address"
            defaultValue={googleMeetEmail}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
      </div>
 
    </div>
  );
}

ClinicSetting.propTypes = {
  patientId: PropTypes.object,
  staffId: PropTypes.object,
  times: PropTypes.arrayOf(PropTypes.object),
};

export default ClinicSetting;