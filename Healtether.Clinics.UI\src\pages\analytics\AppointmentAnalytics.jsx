import Chart from "react-apexcharts";
export default function AppointmentAnalytics() {
    const stdLegend = {
        show: true,
        position: 'top',
        horizontalAlign: 'center',
        fontSize: '14px',
        fontFamily: "Urbanist",
        fontWeight: 400,
        labels: {
            useSeriesColors: true,
        }
    };
    const stdTooltip = {
        style: {
            fontSize: '12px',
            fontFamily: "Urbanist",
        },
    };
    const stdFill = {
        type: 'gradient',
        gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            gradientToColors: ['var(--color-base-100)'],
            opacityTo: 0.3,
            stops: [0, 90, 100]
        }
    };

    const stdResponsive = [{
        breakpoint: 568,
        options: {
            chart: {
                height: 300
            },
            labels: {
                style: {
                    fontSize: '10px',
                    colors: 'var(--color-base-content)',
                    fontFamily: 'Urbanist'
                },
                offsetX: -2,
                formatter: title => title.slice(0, 3)
            },
            yaxis: {
                labels: {
                    align: 'left',
                    minWidth: 0,
                    maxWidth: 140,
                    style: {
                        fontSize: '10px',
                        colors: 'var(--color-base-content)',
                        fontFamily: 'Urbanist'
                    },
                    formatter: value => (value >= 1000 ? `${value / 1000}k` : value)
                }
            }
        }
    }];



    const modeOption = {
        chart: {
            height: 350,
            type: 'bar',
            toolbar: {
                show: false
            },
            zoom: {
                enabled: false
            }
        },
        legend: stdLegend,
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '60px',
                borderRadius: 4,
                borderRadiusApplication: 'end'
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val, opts) {
                return val + "%"
            },
        },
        tooltip: {
            enabled: false,
        },
        markers: {
            size: [5, 5]
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        grid: {
            strokeDashArray: 2,
            borderColor: 'color-mix(in oklab, var(--color-base-content) 40%, transparent)'
        },
        colors: ['var(--color-accent)'],
        xaxis: {
            categories: ["Completed", "Cancelled", "Reschedule"],
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: true
            },
            tooltip: {
                enabled: false
            },
            labels: {
                style: {
                    colors: 'var(--color-base-content)',
                    fontSize: '12px',
                    fontFamily: "Urbanist",
                    fontWeight: 400
                },
            }
        },
        yaxis: {
            labels: {
                align: 'left',
                minWidth: 0,
                maxWidth: 140,
                style: {
                    colors: 'var(--color-base-content)',
                    fontSize: '12px',
                    fontFamily: "Urbanist",
                    fontWeight: 400
                },
                formatter: value => (value + "%")
            }
        },
        responsive: stdResponsive
    }
    const areaOption = {
        chart: {
          height: 350,
          type: 'area',
          toolbar: {
            show: false
          },
          zoom: {
            enabled: false
          }
        },
        legend: stdLegend,
        dataLabels: {
          enabled: false,
        },
        tooltip: stdTooltip,
        markers: {
          size: [5, 5]
        },
        stroke: {
          curve: 'smooth',
          width: 2
        },
        grid: {
          strokeDashArray: 2,
          borderColor: 'color-mix(in oklab, var(--color-base-content) 40%, transparent)'
        },
        colors: ['var(--color-warning)', 'var(--color-accent)'],
        fill: stdFill,
        xaxis: {
          type: 'category',
          tickPlacement: 'on',
          categories: [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
          ],
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: true
          },
          tooltip: {
            enabled: false
          },
          labels: {
            style: {
              colors: 'var(--color-base-content)',
              fontSize: '12px',
              fontFamily: "Urbanist",
              fontWeight: 400
            },
           
          }
        },
        yaxis: {
          labels: {
            align: 'left',
            minWidth: 0,
            maxWidth: 140,
            style: {
              colors: 'var(--color-base-content)',
              fontSize: '12px',
              fontFamily: "Urbanist",
              fontWeight: 400
            },
            formatter: value => (value >= 1000 ? `${value / 1000}k` : value)
          }
        },
        responsive: stdResponsive
      }
    return (<>
    <div className="stats w-full p-3 mx-3">
          <div className="stat overflow-hidden">
            <div className="stat-title font-primary text-lg font-semibold">Appointment Booking Analysis</div>
            <div className="stat-value">
              <div className="app">
                <div className="row">
                  <div className="mixed-chart">
                    <Chart
                      options={areaOption}
                      series={
                        [
                            {
                              name: "By Whatsapp Assistance",
                              data: [25, 27, 35, 37, 40, 45, 50, 73, 48, 45, 43, 44],
                            },
                            {
                              name: "In The Hospitals",
                              data: [30, 34, 37, 52, 55, 63, 67, 70, 73, 76, 80, 85],
                            },
                          ]
                        }
                      type="area"
                      height="350"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="stats sm:w-full md:w-1/2 mt-3 mx-3">
            <div className="stat overflow-hidden">
                <div className="stat-title font-primary text-lg font-semibold">Appointment Status</div>
                <div className="stat-value">
                    <div className="app">
                        <div className="row">
                            <div className="mixed-chart">
                                <Chart
                                    options={modeOption}
                                    series={[
                                        {
                                            name: 'Appointment',
                                            data: [25, 67, 10]
                                        },
                                    ]}
                                    type="bar"
                                    height="350"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </>)
}