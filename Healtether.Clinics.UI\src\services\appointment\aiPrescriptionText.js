
import axios from "axios";
const AI_API_URL = import.meta.env.VITE_AI_API_URL;
export const GetAssociatedSymptomsDiagnosis = async (symptoms, diagnosis) => {
    var aiAxios = axios.create({
        withCredentials: false,
        headers: {  
            'Content-Type': 'application/json; application/javascript',
        }
    });

    return await aiAxios
        .post(
            `${AI_API_URL}/ddx/predict`, // This will be proxied by Vite
            {
                input_symptoms: symptoms.map((symptom) => symptom.name),
                input_diagnoses: diagnosis.map((diag) => diag.name),
                n_diseases: 6,
                n_symptoms: 6,
                min_symptoms: 2,
            },
            {
                timeout: 50000,
            }
        )
        .then((response) => {
            if (response.status === 200) {
                return response.data;
            }
            return;
        })
        .catch((err) => {
            console.log('axios ai error', err);
        });
};
