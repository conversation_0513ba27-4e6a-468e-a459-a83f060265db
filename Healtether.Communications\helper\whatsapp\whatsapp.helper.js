import { sendMessageToWhatsAppAPI } from "../../config/send.whatsapp.config.js";
import { getDay } from "date-fns";
import { generateTimeSlots, sortAndRemoveDuplicateTimeSlots } from "../../utils/common.js";
import { getClinicCacheForWhatsapp } from "../clinic/clinic.helper.js";

export async function sendTemplate(templateData) {
  const response = await sendMessageToWhatsAppAPI(templateData);
  const res = getMessageIdFromResponse(response);
  return res;
}

export const sendReply = async (data) => {
  const response = await sendMessageToWhatsAppAPI(data);
  return response;
};

function getMessageIdFromResponse(response) {
  if (response != null && response.messaging_product == "whatsapp") {
    return response?.data?.messages[0].id;
  }
}



export const getCacheDetailForAppointmentTemplate = async (clinicId) => {
  var clinicDetail = await getClinicCacheForWhatsapp(clinicId);
  if (clinicDetail != null) {

      var doctors = clinicDetail
          .doctors
          .map((item) => {
              return {
                  id: item.id,
                  title: item.firstName + " " + item.lastName,
                  description: item.specialization
              };
          });
      return { doctors: doctors };
  }

}


export const getDoctorDetailDataExchange = async (clinicId, doctorId, scheduleDate) => {
  var clinicDetail = await getClinicCacheForWhatsapp(clinicId);
  let dayOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  if (clinicDetail != null) {
      
      if (doctorId != null) {
          let availableWeekDays = new Set();
          let availableTimeSlots = [];
          let doctor = clinicDetail?.doctors?.filter((value) => value._id == doctorId);

          if (doctor.length > 0) {
              for (const availableTimeSlot of doctor[0].availableTimeSlot) {
                  for (const weekDay of availableTimeSlot.weekDay) {
                      availableWeekDays.add(weekDay);
                  }
              }

              let availableText = `Available On ${(Array.from(availableWeekDays.values())?.join(", "))}`


              if (scheduleDate > 1000) {
                  let dayId = getDay(new Date(parseInt(scheduleDate)));
                  let timeSlotsBasedOnDoctor = doctor[0].availableTimeSlot.filter((item) => (item.weekDay.indexOf(dayOfWeek[dayId]) > -1));
                  for (const doctorTimeSlots of timeSlotsBasedOnDoctor) {
                      for (const doctorTimeSlot of doctorTimeSlots.timeSlot) {
                          let generateSlots = generateTimeSlots(doctorTimeSlot.start, doctorTimeSlot.end, doctorTimeSlots.slotDuration);
                          availableTimeSlots = availableTimeSlots.concat(generateSlots);
                      }
                  }
                  if (availableTimeSlots.length > 0) {
                      availableTimeSlots = availableTimeSlots.map((item) =>
                          item.start + " - " + item.end
                      );
                      availableTimeSlots = sortAndRemoveDuplicateTimeSlots(availableTimeSlots);
                      availableTimeSlots = availableTimeSlots.map((value) => {
                          return {
                              "id": value,
                              "title": value
                          }
                      });
                  }

              }

              return {
                  availableText: availableText,
                  timeSlots: availableTimeSlots
              };
          }
      }
      return {
          availableText: "",
          timeSlots: []
      };

  }

}

