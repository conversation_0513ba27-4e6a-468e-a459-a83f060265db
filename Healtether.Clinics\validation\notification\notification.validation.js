import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateNotificationUpsert = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    header: {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Header is required",
    },
    message: {
      in: ["body"],
      isString: true,
      errorMessage: "Message is required",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetNotificationOverview = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic Id must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
