import { jest } from "@jest/globals";
const { mockStaffHelper } = await import("../mocks/mock.staff.helper");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
mockStaffHelper();
mockCommonUtils();
mockFirebaseMethod();
const { deleteStaff } = await import('../../controllers/staffs/staffs.controller.js'); 
const { removeStaff }= await import('../../helpers/staff/staff.helper.js');

const { buildNotificationText, resultObject } = await import("../../utils/common.utils.js");
const { sendNotificationViaToken } = await import("../../config/firebase.admin.js");

describe('deleteStaff', () => {
    let res;
    let query;

    beforeEach(() => {
        // Set up mock response object
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
        };

        // Example query object
        query = {
            query: { id: 'staff-id' },
            Notificationkey: 'some-notification-key',
            user: { id: 'user-id', name: 'Admin' }
        };
    });

    it('should delete staff and send notification ', async () => {
        const staff = { firstName: 'John', lastName: 'Doe', clinic: 'clinic-id' };
        removeStaff.mockResolvedValue(staff);
        buildNotificationText.mockReturnValue('John Doe has been deleted');
        sendNotificationViaToken.mockResolvedValue();
        resultObject.mockReturnValue({ status: 200, success: true });

        await deleteStaff(query, res);

        expect(removeStaff).toHaveBeenCalledWith('staff-id');
        expect(buildNotificationText).toHaveBeenCalledWith('John', 'Doe', ' has been deleted', query.user);
        expect(sendNotificationViaToken).toHaveBeenCalledWith('some-notification-key', 'John Doe has been deleted', 'Staff Detail', true, 'clinic-id', 'user-id');
        expect(resultObject).toHaveBeenCalledWith(200, null, true, {});
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ status: 200, success: true });
    });

});
