import React, { useEffect, useState } from "react";
import Table from "./table";
import Request<PERSON>bha from "./request-abha";
import { Buttons } from "../appointment/button";
import { patientConsents } from "../../../services/abha/abha-m3";

export default function AbhaConsents({ tab,setActiveTab, patientData,setGrantedConsentAbhaRecords}) {
  const [consent, setConsent] = useState({
    request: false,
    successfull: false,
    failed: false,
    data:  Array(5).fill(""),
  });
  const [consentList, setConsentList]= useState([])

  const props = { consent, setConsent,tab,setActiveTab };

  const getConsentList= async()=>{
    if(patientData&&patientData._id){
      let result = await patientConsents(patientData._id)
      setConsentList(result);
    }
  }
  useEffect(()=>{
    getConsentList();
  },[])

  return (
    <section
      className={
        // tab.current == tab.list[3]
          "p-5 w-full rounded-lg  flex flex-col gap-5"
          // : "hidden"
      }
    >
      {/*-------------------- Modals & more  --------------------*/}
      <RequestAbha {...props} />
      {/*-------------------- End of modals & more  --------------------*/}

      {consent.data.length < 1 ? (
         <section
         className="m-auto text-center font-medium text-lg text-color_muted flex flex-col items-center justify-center gap-3 h-80"
       >
         <div className="text-center">Nothing to show</div>
         <Buttons.primary
           onClick={() => setConsent({ ...consent, request: true })}
           title="Request ABHA"
         />
       </section>
      ) : (
        <article>
          <header className=" flex justify-between items-center mb-4">
            <div className=" text-base font-semibold text-dark">
              ABHA Consents
              <div className="h-px w-12 rounded-md bg-primary" />
            </div>
            <div
              onClick={() => setConsent({ ...consent, request: true })}
              className="text-lg text-blue font-medium cursor-pointer leading-none"
            >
              Request ABHA
              <div className="h-px w-full rounded-md bg-blue" />
            </div>
          </header>
          <Table {...props} consentList={consentList} setGrantedConsentAbhaRecords={setGrantedConsentAbhaRecords} />
        </article>
      )}
    </section>
  );
}
