import { jest } from "@jest/globals";
const { mockStaffHelper } = await import("../mocks/mock.staff.helper");
mockStaffHelper();
const { getDoctorsByClinic } = await import('../../controllers/staffs/staffs.controller.js'); 
const { getDoctor }= await import('../../helpers/staff/staff.helper.js');

describe('getDoctorsByClinic', () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: {}
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
        getDoctor.mockReset();
    });

    it('should return a list of doctors when provided a valid clinicId', async () => {
        const mockClinicId = 'clinic123';
        const mockDoctors = [
            { name: 'Dr. <PERSON>', specialty: 'Cardiology' },
            { name: 'Dr. <PERSON><PERSON>', specialty: 'Dermatology' }
        ];

        getDoctor.mockResolvedValue(mockDoctors);

        req.query = { clinicId: mockClinicId };

        await getDoctorsByClinic(req, res);

        expect(getDoctor).toHaveBeenCalledWith(mockClinicId);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockDoctors);
    });

});
