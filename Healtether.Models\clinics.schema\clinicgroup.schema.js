import mongoose from "mongoose";

const clinicGroupSchema = new mongoose.Schema({
  groupName: {
    type: String,
    required: true,
  },
  created: {
    on: {
      type: Date,
      default: Date.Now,
    },
    by: {
      id: String,
      name: {
        type: String,
        maxLength: 255,
      },
    },
  },
  deleted: {
    type: Boolean,
    default: false,
  },
});

export { clinicGroupSchema };
