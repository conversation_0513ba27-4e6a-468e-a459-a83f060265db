import { jest } from "@jest/globals";
import mongoose from 'mongoose';
import { upsertAppointment } from '../../../helpers/appointment/appointment.helper.js';
import { Appointment } from '../../../model/clinics.model.js';

import { setup, teardown } from '../../../setup.js';
import {cancelMeeting} from "../../../helpers/googleMeet/googleMeet.js"

jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe('upsertAppointment Function Test', () => {
  let user = { id: "test_user", name: 'Test User' };

  it('should update an existing appointment and cancel the old meeting if necessary', async () => {
    const testAppointmentId = new mongoose.Types.ObjectId();
    const testClinicId = new mongoose.Types.ObjectId();
    const testDoctorId = new mongoose.Types.ObjectId();

    // Create a mock appointment to update
    const appointment = new Appointment({
      _id: testAppointmentId,
      name: "Test Patient",
      mobile: "**********",
      age: 25,
      gender: "Male",
      appointmentDate: new Date("2024-09-15"),
      timeSlot: "10:00 am",
      virtualConsultation: true,
      googleLink: {
        link: "https://meeting.link",
        id: "event123",
      },
      doctorId: testDoctorId,
      clinic: testClinicId,
    });

    await appointment.save();

    const updateData = {
      reason: "Follow-up checkup",
      timeSlot: "11:00 am",
      appointmentDate: "2024-09-16",
      virtualConsultation: false,
      doctorId: testDoctorId,
      doctorName: "Dr. Smith",
    };

    const [result, updatedAppointment] = await upsertAppointment(updateData, testAppointmentId, user);

    expect(result).toBe(true);
    expect(updatedAppointment.virtualConsultation).toBe(false);

    await cancelMeeting("event123");
  });

  it('should create a new appointment if no id is passed', async () => {
    const testClinicId = new mongoose.Types.ObjectId();
    const testDoctorId = new mongoose.Types.ObjectId();
    const testPatientId = new mongoose.Types.ObjectId();

    const newAppointmentData = {
      name: "New Patient",
      mobile: "**********",
      age: 30,
      birthDate: "1994-05-12",
      gender: "Female",
      appointmentDate: "2024-09-20",
      timeSlot: "2:00 pm",
      doctorId: testDoctorId,
      doctorName: "Dr. Wilson",
      reason: "Consultation",
      clinic: testClinicId,
      patientId: testPatientId,
      instantAppointment: "false",
      virtualConsultation: "true",
      clinicPatientId: "clinic123",
    };

    const [result, newAppointment] = await upsertAppointment(newAppointmentData, null, user);

    expect(result).toBe(true);
    expect(newAppointment.name).toBe("New Patient");
    expect(newAppointment.timeSlot).toBe("2:00 pm");
    expect(newAppointment.virtualConsultation).toBe(true);
    expect(newAppointment.appointmentDate.toISOString()).toBe(new Date("2024-09-20T00:00:00.000Z").toISOString());
  });

  it('should not cancel meeting if appointment time or virtual consultation was not changed', async () => {
    const testAppointmentId = new mongoose.Types.ObjectId();
    const testDoctorId = new mongoose.Types.ObjectId();
    const testClinicId = new mongoose.Types.ObjectId();

    const appointment = new Appointment({
      _id: testAppointmentId,
      name: "Stable Patient",
      mobile: "**********",
      age: 40,
      gender: "Female",
      appointmentDate: new Date("2024-09-22"),
      timeSlot: "3:00 pm",
      virtualConsultation: true,
      googleLink: {
        link: "https://meeting.link",
        id: "event456",
      },
      doctorId: testDoctorId,
      clinic: testClinicId,
    });

    await appointment.save();

    const unchangedData = {
      reason: "Regular checkup",
      timeSlot: "3:00 pm",
      appointmentDate: "2024-09-22",
      virtualConsultation: "true",
      doctorId: testDoctorId,
      doctorName: "Dr. Adams",
    };

    const [result, updatedAppointment] = await upsertAppointment(unchangedData, testAppointmentId, user);

    expect(result).toBe(true);


  });
});
