import { jest } from "@jest/globals";
import mongoose from 'mongoose';
import { updateRecords } from '../../../helpers/appointment/appointment.helper.js';
import { Appointment } from '../../../model/clinics.model.js';
import { setup, teardown } from '../../../setup.js';

jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe('Appointment Model Test', () => {
  it('should insert and retrieve an appointment successfully', async () => {
    let user = { id: "user", name: 'Test User' }; 
    const testAppointmentId = new mongoose.Types.ObjectId();
    const testClinicId = new mongoose.Types.ObjectId();
    const testAppointment = {
      _id:testAppointmentId,
      name: 'test',
      appointmentDate: new Date(),
      clinic: testClinicId,
      paymentStatus: false,
      isDeleted: false,
      gender: "Male",
      age: 33,
      mobile: "**********",
      medicalRecords: [
        {
          fileName: "diabetes_report1.pdf",
          blobName: "45678_diabetes_report1"
        }
      ],
      procedureRecords: [
        {
          fileName: "procedurefile2.pdf",
          blobName: "45678_diabetes_report2"
        }
      ],
      prescriptionRecords: [
        {
          fileName: "prescriptionRecords3.pdf",
          blobName: "45678_diabetes_report3"
        }
      ],

    };
    const data = {
        medicalRecords: [
            {
              fileName: "update1.pdf",
              blobName: "update12"
            }
          ],
          procedureRecords: [
            {
              fileName: "update2.pdf",
              blobName: "update123"
            }
          ],
          prescriptionRecords: [
            {
              fileName: "update3.pdf",
              blobName: "update1234"
            }
          ],
    };

    await Appointment.create(testAppointment);
    const result = await updateRecords(data,testAppointmentId, user); 
    expect(result.prescriptionRecords[0].fileName).toStrictEqual(data.prescriptionRecords[0].fileName);
    expect(result.prescriptionRecords[0].blobName).toStrictEqual(data.prescriptionRecords[0].blobName);
    expect(result.procedureRecords[0].fileName).toStrictEqual(data.procedureRecords[0].fileName);
    expect(result.procedureRecords[0].blobName).toStrictEqual(data.procedureRecords[0].blobName);
    expect(result.medicalRecords[0].fileName).toStrictEqual(data.medicalRecords[0].fileName);
    expect(result.medicalRecords[0].blobName).toStrictEqual(data.medicalRecords[0].blobName);
    expect(result.clinic).toEqual(testClinicId);
    expect(result.name).toEqual("test");

    expect(result.paymentStatus).toEqual(false);
    expect(result.gender).toEqual("Male");
    expect(result.age).toEqual(33);
    expect(result.mobile).toEqual("**********");

  });
});
