import mongoose, { Schem<PERSON> } from "mongoose";

export const WhatsappSessionSchema = new mongoose.Schema(
  {
    mobile: {
      type: String,
      required: true,
      unique: true,
      index: true,
      maxLength: 15,
    },
    language: {
      type: String,
      maxLength: 10,
    },
    isLive: {
      type: Boolean,
      default: false,
    },
    currentClinic: {
      id: String,
      clinicName: String,
    },
    cache: {
      clinic: [
        {
          id: String,
          clinicName: String,
        },
      ],
    },
    timeStamp: {
      type: Date,
      default: Date.now,
    },
    lastWhatsappMessageId: {
      type: String,
    },
    expireAt: {
      type: Date,
      expires: 10,
    },
  },
  { versionKey: "1.1" }
);
