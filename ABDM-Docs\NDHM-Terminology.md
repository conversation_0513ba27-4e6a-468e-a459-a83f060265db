# NDHM FHIR R4 Terminology and Code Systems Guide

## Table of Contents
1. [Terminology Overview](#terminology-overview)
2. [Administrative Code Systems](#administrative-code-systems)
3. [Clinical Value Sets](#clinical-value-sets)
4. [Indian-Specific Terminologies](#indian-specific-terminologies)
5. [International Standards Integration](#international-standards-integration)
6. [Implementation Guidelines](#implementation-guidelines)

## Terminology Overview

The NDHM FHIR R4 implementation uses a combination of international standards (SNOMED CT, LOINC, ICD-10) and Indian-specific code systems to ensure both global interoperability and local relevance.

### Terminology Hierarchy
```
International Standards (SNOMED CT, LOINC, ICD-10)
    ↓
NDHM Code Systems (Indian Context)
    ↓
Local/Regional Extensions
    ↓
Free Text (when codes unavailable)
```

### Base URL Pattern
All NDHM code systems follow the pattern:
`https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-{domain}-{type}`

## Administrative Code Systems

### 1. Billing Codes
**System**: `https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes`

| Code | Display | Use Case |
|------|---------|----------|
| 00 | Consultation | Doctor visit fees |
| 01 | Pharmacy | Medication purchases |
| 02 | Diagnostic | Lab tests, imaging |
| 03 | Procedure | Medical procedures |
| 04 | Hospitalization | Inpatient charges |

### 2. Price Components
**System**: `https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components`

| Code | Display | Type | Description |
|------|---------|------|-------------|
| 00 | MRP | informational | Maximum Retail Price |
| 01 | Rate | base | Actual charged price |
| 02 | Discount | discount | Applied discount |
| 03 | CGST | tax | Central GST |
| 04 | SGST | tax | State GST |
| 05 | IGST | tax | Integrated GST |
| 06 | CESS | tax | Additional cess |

### 3. Identifier Types
**System**: `https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-identifier-type-code`

| Code | Display | Use Case |
|------|---------|----------|
| ABHA | ABHA ID | Patient identification |
| MCI | MCI Registration | Doctor registration |
| ROHINI | ROHINI ID | Facility identification |
| PAN | PAN Number | Tax identification |
| GSTIN | GST Number | Business identification |

### 4. Payment Types
**System**: `https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-payment-type`

| Code | Display | Description |
|------|---------|-------------|
| CASH | Cash Payment | Direct cash payment |
| CARD | Card Payment | Credit/Debit card |
| UPI | UPI Payment | Unified Payments Interface |
| NEFT | NEFT Transfer | Bank transfer |
| INSURANCE | Insurance | Insurance claim |

### 5. Task Codes
**System**: `https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-task-codes`

| Code | Display | Use Case |
|------|---------|----------|
| CONSENT_REQUEST | Consent Request | Patient consent workflow |
| DATA_REQUEST | Data Request | Health information request |
| CLAIM_PROCESS | Claim Processing | Insurance claim workflow |
| APPOINTMENT | Appointment | Appointment scheduling |

## Clinical Value Sets

### 1. Medicine Codes
**System**: Multiple (SNOMED CT + Indian Extensions)
- **SNOMED CT International**: Global drug codes
- **Common Drug Codes for India**: National extension for Indian medicines
- **Local Formularies**: Hospital/region specific codes

**Example Usage**:
```json
{
  "coding": [
    {
      "system": "http://snomed.info/sct",
      "code": "*********",
      "display": "Paracetamol"
    },
    {
      "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/indian-drug-codes",
      "code": "IND-PAR-500",
      "display": "Paracetamol 500mg (Indian)"
    }
  ],
  "text": "Paracetamol 500mg tablet"
}
```

### 2. Diagnostic Codes
**Primary System**: ICD-10
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-diagnostic-code`

**Common Codes**:
| ICD-10 Code | Display |
|-------------|---------|
| E11 | Type 2 diabetes mellitus |
| I10 | Essential hypertension |
| J44 | Chronic obstructive pulmonary disease |
| N18 | Chronic kidney disease |

### 3. Vital Signs
**Primary System**: LOINC
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-vital-signs`

**Common Codes**:
| LOINC Code | Display | Unit |
|------------|---------|------|
| 8480-6 | Systolic blood pressure | mmHg |
| 8462-4 | Diastolic blood pressure | mmHg |
| 8867-4 | Heart rate | /min |
| 8310-5 | Body temperature | °C |
| 2708-6 | Oxygen saturation | % |

### 4. Body Measurements
**Primary System**: LOINC
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-body-measurement`

**Common Codes**:
| LOINC Code | Display | Unit |
|------------|---------|------|
| 8302-2 | Body height | cm |
| 29463-7 | Body weight | kg |
| 39156-5 | Body mass index | kg/m² |
| 8280-0 | Waist circumference | cm |

## Indian-Specific Terminologies

### 1. Lifestyle Assessments

#### Tobacco Smoking Status
**System**: SNOMED CT
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-tobacco-smoking-status`

| SNOMED Code | Display |
|-------------|---------|
| 266919005 | Never smoked |
| 77176002 | Smoker |
| 8517006 | Former smoker |
| 428041000124106 | Occasional smoker |

#### Tobacco Chewing Status (Indian Context)
**System**: SNOMED CT
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-tobacco-chewing-status`

| SNOMED Code | Display |
|-------------|---------|
| 228494002 | Chews tobacco |
| 228495001 | Does not chew tobacco |
| 228496000 | Former tobacco chewer |

#### Alcohol Drinking Status
**System**: SNOMED CT
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-alcohol-drinking-status`

| SNOMED Code | Display |
|-------------|---------|
| 219006 | Current drinker |
| 228273003 | Finding relating to alcohol drinking behavior |
| 228274009 | Never drinks alcohol |
| 228275005 | Drinks alcohol |

### 2. Women's Health
**Primary System**: LOINC
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-women-health`

**Common Codes**:
| LOINC Code | Display |
|------------|---------|
| 21840-4 | Age at menarche |
| 8665-2 | Last menstrual period start date |
| 49051-6 | Age at menopause |
| 8678-5 | Ovulation date |

### 3. Diet Types (Indian Context)
**System**: SNOMED CT
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-diet-type`

| SNOMED Code | Display |
|-------------|---------|
| 226529007 | Vegetarian diet |
| 226528004 | Non-vegetarian diet |
| 226530002 | Vegan diet |
| 182922004 | Dietary finding |

### 4. Vaccine Codes
**System**: SNOMED CT
**ValueSet**: `https://nrces.in/ndhm/fhir/r4/ValueSet/ndhm-vaccine-codes`

**Common Indian Vaccines**:
| SNOMED Code | Display |
|-------------|---------|
| 396429000 | Hepatitis B vaccine |
| 396424004 | BCG vaccine |
| 396425003 | DPT vaccine |
| 396439005 | Polio vaccine |
| 396441006 | MMR vaccine |

## International Standards Integration

### SNOMED CT Integration
- **International Edition**: Core medical concepts
- **Indian Extension**: India-specific medical terms
- **Local Extensions**: Regional/institutional terms

### LOINC Integration
- **Core LOINC**: Laboratory and clinical observations
- **Indian Translations**: Hindi/regional language mappings
- **Local Lab Codes**: Institution-specific mappings

### ICD-10 Integration
- **WHO ICD-10**: International disease classification
- **ICD-10-CM**: Clinical modification
- **Indian Adaptations**: Local disease patterns

## Implementation Guidelines

### 1. Coding Best Practices

#### Primary Coding Pattern
```json
{
  "coding": [
    {
      "system": "http://snomed.info/sct",
      "code": "*********",
      "display": "Paracetamol"
    }
  ],
  "text": "Paracetamol 500mg tablet"
}
```

#### Multiple Coding Pattern
```json
{
  "coding": [
    {
      "system": "http://snomed.info/sct",
      "code": "*********",
      "display": "Paracetamol"
    },
    {
      "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/indian-drug-codes",
      "code": "IND-PAR-500",
      "display": "Paracetamol 500mg (Indian)"
    }
  ],
  "text": "Paracetamol 500mg tablet"
}
```

### 2. Fallback Strategy
1. **Primary Code**: Use preferred international standard
2. **Secondary Code**: Add Indian/local code if available
3. **Text Description**: Always provide human-readable text
4. **Translation**: Include local language if needed

### 3. Validation Rules
- **System URLs**: Must be valid and accessible
- **Code Values**: Must exist in the specified system
- **Display Text**: Should match the code system
- **Binding Strength**: Follow profile requirements (required/extensible/preferred)

### 4. Extension Strategy
```json
{
  "coding": [
    {
      "system": "http://snomed.info/sct",
      "code": "182922004",
      "display": "Dietary finding"
    }
  ],
  "text": "Jain vegetarian diet",
  "extension": [
    {
      "url": "https://nrces.in/ndhm/fhir/r4/StructureDefinition/diet-restriction",
      "valueString": "No root vegetables"
    }
  ]
}
```

### 5. Multilingual Support
```json
{
  "coding": [
    {
      "system": "http://snomed.info/sct",
      "code": "*********",
      "display": "Paracetamol"
    }
  ],
  "text": "Paracetamol",
  "extension": [
    {
      "url": "https://nrces.in/ndhm/fhir/r4/StructureDefinition/translation",
      "extension": [
        {
          "url": "lang",
          "valueCode": "hi"
        },
        {
          "url": "content",
          "valueString": "पैरासिटामोल"
        }
      ]
    }
  ]
}
```

---

**Related Documents:**
- [NDHM FHIR R4 Overview](./NDHM-FHIR-R4-Overview.md)
- [Clinical Profiles Guide](./NDHM-Clinical-Profiles.md)
- [Billing Implementation](./NDHM-Billing-Implementation.md)
