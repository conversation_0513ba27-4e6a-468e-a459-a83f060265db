import ContainerHeading from "components/detail-page/ContainerHeading";
import Required<PERSON>abel from "components/detail-page/RequiredLabel";
import DefaultTextboxClass from "utils/Classes";
function ContactDetails({ mobile, email, address }) {
  address = address != null ? address : {};
  return (
    <div className="flex flex-col space-y-3">
      <ContainerHeading heading={"Contact details"} />

      <div className="flex gap-5">
        <div className="w-1/2">
          <label className="label-text" htmlFor="mobile">
            Mobile no.
            <RequiredLabel />
          </label>
          <input
            type="number"
            pattern="[7-9]{1}[0-9]{9}"
            id="mobile"
            name="mobile"
            placeholder="Mobile no."
            autoComplete="off"
            maxLength={10}
            onInput={(e) => {
              e.target.value = e.target.value
                .replace(/[^0-9]/g, "")
                .slice(0, 10);
            }}
            onKeyDown={(e) => {
              if (e.key === "e" || e.key === "-" || e.key === "+") {
                e.preventDefault();
              }
            }}
            defaultValue={mobile}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        <div className="w-1/2">
          <label className="label-text" htmlFor="email">Email</label>
          <input
            type="text"
            id="email"
            name="email"
            placeholder="Email"
            autoComplete="off"
            defaultValue={email}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
      </div>

      <ContainerHeading heading={"Address"} />
      <div className="flex flex-col gap-2">
        <div className="w-full">
          <label className="label-text" htmlFor="address_house">House / Building / Room no.</label>
          <input
            type="text"
            id="address_house"
            name="address_house"
            placeholder="House/Building/Room no."
            autoComplete="off"
            defaultValue={address.house || ""}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>

        <div className="w-full">
          <label className="label-text" htmlFor="address_street">Street / Area</label>
          <input
            type="text"
            id="address_street"
            name="address_street"
            placeholder="Street/Area"
            autoComplete="off"
            defaultValue={address.house}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        <div className="w-full space-y-1">
          <label className="text-sm ">District</label>
          <input
            type="text"
            name="address_district"
            placeholder="District"
            autoComplete="off"
            defaultValue={address.district}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        <div className="w-full space-y-1">
          <label className="text-sm ">State</label>
          <input
            type="text"
            name="address_state"
            placeholder="State"
            autoComplete="off"
            defaultValue={address.state}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        <div className="w-full">
          <label className="label-text" htmlFor="address_pincode">Pincode</label>
          <input
            type="number"
            name="address_pincode"
            id="address_pincode"
            placeholder="Pincode"
            autoComplete="off"
            onInput={(e) => {
              e.target.value = e.target.value
                .replace(/[^0-9]/g, "")
                .slice(0, 6);
            }}
            defaultValue={address.pincode}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
      </div>
    </div>
  );
}

export default ContactDetails;
