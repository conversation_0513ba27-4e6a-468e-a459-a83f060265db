@import 'tailwindcss';
@import "flyonui/variants.css";

@config '../../../tailwind.config.js';

@plugin "flyonui" {
  themes: light --default;
 };
@source "/node_modules/flyonui/flyonui.js";

@plugin "flyonui/theme" {
  name: "light";
  default: true;
  --color-primary: "#32856E";
  --text-base: 14px;
  --text-lg: 16px;
  --default-font-family:"Roboto"
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
  html {
    color: #202741;
    font-size: 13.5px;
    height: 100%;
  }
  
}
  #detail {
    font-size: 13.5px;
  }

  .assoc-symptom-blur {
    filter: blur(5px);
  }

  label.label-text{
    font-family: 'Urbanist';
    font-size: 12.5px;
    font-weight: 500;
  }


@layer utilities {
  body {
    width: 100%;
    overflow-x: hidden;
  }
}

@layer utilities {
  body::-webkit-scrollbar,
  div::-webkit-scrollbar {
    width: 3px;
    height: 8px;
  }

  body::-webkit-scrollbar-track,
  div::-webkit-scrollbar-track {
    border-radius: 100vh;
    background: transparent;
  }

  body::-webkit-scrollbar-thumb,
  div::-webkit-scrollbar-thumb {
    background: #6d6d6d;
    border-radius: 50vh;
    border: 0.5px solid #6d6d6d;
    max-height: 80%;
  }

  body::-webkit-scrollbar-thumb:hover,
  div::-webkit-scrollbar-thumb:hover {
    background: #f5f5f5;
  }

  *::placeholder {
    /* Most modern browsers support this now. */
    color: #6d6d6d;
  }
}

:root {
  --toastify-icon-color-success: #32856e !important;
}

#error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.Toastify__progress-bar--success {
  background-color: #32856e !important;
  color: #32856e !important;
}

.Toastify__progress-bar--info {
  background-color: #5351c7 !important;
  color: #5351c7 !important;
}

.Toastify__toast-theme--light {
  background: #f7f4fa !important;
  color: #202741;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.giveMeEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* number of lines to show */
}

* Skeleton btn-loader animation */ .skeleton {
  min-height: 20px;
  border-radius: 4px;
  /* background-color: #ffea91; */
}

.skeleton {
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.5),
    rgba(255, 255, 255, 0)
  );
  background-size: 40px 100%;
  background-repeat: no-repeat;
  background-position: left -40px top 0;
  animation: shine 1s ease infinite;
}

@keyframes shine {
  to {
    background-position: right -40px top 0;
  }
}
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 3px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* For IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
@media print {
  .no-print {
    display: none !important;
  }

  input,
  button,
  select,
  textarea {
    display: none !important;
    /* Hide all form controls */
  }

  .print-text {
    display: inline-block !important;
  }
}