{"resourceType": "Bundle", "id": "93e29223-2df1-4638-8b7a-4c8c3c2a2873", "meta": {"versionId": "1", "lastUpdated": "2025-04-11T14:13:32.903+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"], "security": [{"system": "http://terminology.hl7.org/CodeSystem/v3-Confidentiality", "code": "V", "display": "very restricted"}]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "type": "document", "timestamp": "2025-04-11T14:13:32.903+05:30", "entry": [{"fullUrl": "urn:uuid:6f87a94a-15eb-4bff-91ac-30cc69b448fa", "resource": {"resourceType": "Composition", "id": "6f87a94a-15eb-4bff-91ac-30cc69b448fa", "meta": {"versionId": "1", "lastUpdated": "2025-04-11T14:13:32.903+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord"]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "language": "en", "status": "final", "type": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}], "text": "Clinical consultation report"}, "date": "2025-04-11T14:13:32.903+05:30", "title": "Consultation Report", "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "encounter": {"reference": "urn:uuid:8261c39e-d0fa-4727-80bb-7373ea2a320d", "display": "Encounter"}, "author": [{"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "<PERSON><PERSON>"}], "attester": [{"mode": "legal", "party": {"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "<PERSON><PERSON>"}}, {"mode": "legal", "party": {"reference": "urn:uuid:c427fc4f-e916-48c5-8d2e-1d8809540b67", "display": "Health Organization"}}], "custodian": {"reference": "urn:uuid:c427fc4f-e916-48c5-8d2e-1d8809540b67", "display": "Health Organization"}, "section": [{"title": "Chief complaints", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422843007", "display": "Chief complaint section"}], "text": "Chief complaint section"}, "entry": [{"reference": "urn:uuid:6096a9aa-1843-4e03-8f2d-7db5c265cf3e", "display": "Condition"}, {"reference": "urn:uuid:77c5a7a3-4c2b-436e-a469-048521379416", "display": "Condition"}, {"reference": "urn:uuid:def088f8-08a4-4c23-bbaf-5d32c9295664", "display": "Condition"}]}, {"title": "Allergies", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "722446000", "display": "Allergy record"}]}, "entry": [{"reference": "urn:uuid:09086b93-351e-444e-8561-3e87d69d5cb6", "display": "AllergyIntolerance"}, {"reference": "urn:uuid:356cfa02-99b2-433a-a47f-db42537c265e", "display": "AllergyIntolerance"}]}, {"title": "Medical History", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371529009", "display": "History and physical report"}]}, "entry": [{"reference": "urn:uuid:6096a9aa-1843-4e03-8f2d-7db5c265cf3e", "display": "Condition"}, {"reference": "urn:uuid:77c5a7a3-4c2b-436e-a469-048521379416", "display": "Condition"}, {"reference": "urn:uuid:def088f8-08a4-4c23-bbaf-5d32c9295664", "display": "Condition"}]}, {"title": "Investigation Advice", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721963009", "display": "Order document"}]}, "entry": [{"reference": "urn:uuid:db5c37f5-905f-46bc-bd0c-6e852e5c6eec", "display": "ServiceRequest"}, {"reference": "urn:uuid:4214d19a-d1d8-4235-88bb-0d347fce38d8", "display": "ServiceRequest"}]}, {"title": "Medications", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721912009", "display": "Medication summary document"}]}, "entry": [{"reference": "urn:uuid:f6608688-59c3-4bd6-a4e7-916b5901940f", "display": "MedicationStatement"}, {"reference": "urn:uuid:af953420-62ad-4d1d-a0de-4b01bb1c66a6", "display": "MedicationStatement"}, {"reference": "urn:uuid:ed5db232-ae0d-4489-8c6e-3eafe5a7580a", "display": "MedicationRequest"}, {"reference": "urn:uuid:5b3685eb-4fef-44b6-93b1-25e9192c1c25", "display": "MedicationRequest"}]}, {"title": "Procedure", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371525003", "display": "Clinical procedure report"}]}, "entry": [{"reference": "urn:uuid:42c10de8-cfba-47fe-aa62-f2da7ca86a2a", "display": "Procedure"}, {"reference": "urn:uuid:cb165e6e-1e51-41c8-a367-c0780e211fe0", "display": "Procedure"}]}, {"title": "Follow Up", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "736271009", "display": "Outpatient care plan"}]}, "entry": [{"reference": "urn:uuid:916d5bf8-a47d-4105-a5bd-26a6e6870796", "display": "Appointment"}]}, {"title": "Document Reference", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}]}, "entry": []}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Composition Record</p></div>"}}}, {"fullUrl": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "resource": {"resourceType": "Practitioner", "id": "8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "meta": {"versionId": "1", "lastUpdated": "2025-04-11T14:13:33.024+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MD", "display": "Medical License number"}]}, "system": "https://doctor.ndhm.gov.in", "value": "23532"}], "name": [{"use": "official", "text": "<PERSON><PERSON>", "prefix": ["Dr."]}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "female", "address": [{"use": "home", "type": "physical", "postalCode": "6998", "country": "india", "district": "begger", "city": "Lagos", "state": "begger", "text": "NUM 5y"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Practitioner Record</p></div>"}}}, {"fullUrl": "urn:uuid:c427fc4f-e916-48c5-8d2e-1d8809540b67", "resource": {"resourceType": "Organization", "id": "c427fc4f-e916-48c5-8d2e-1d8809540b67", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "PRN", "display": "Provider number"}]}, "system": "https://facility.ndhm.gov.in", "value": "1234567"}], "name": "Health Organization", "telecom": [{"system": "phone", "value": "**********", "use": "work"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Organization Record</p></div>"}}}, {"fullUrl": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "resource": {"resourceType": "Patient", "id": "4de11a6a-322e-4ee3-b8de-48022616ef8f", "meta": {"versionId": "1", "lastUpdated": "2025-04-11T14:13:33.024+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAID"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "91-1880-2233-6856"}, {"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAADDRESS"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "singh_singh090909@sbx"}], "name": [{"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["Mr."]}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "male", "birthDate": "2000-09-09", "address": [{"use": "home", "type": "physical", "postalCode": "", "country": "india", "district": "", "city": "", "state": "", "text": ""}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Patient Record</p></div>"}}}, {"fullUrl": "urn:uuid:8261c39e-d0fa-4727-80bb-7373ea2a320d", "resource": {"resourceType": "Encounter", "id": "8261c39e-d0fa-4727-80bb-7373ea2a320d", "meta": {"lastUpdated": "2025-04-11T14:13:33.024+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]}, "identifier": [{"system": "https://ndhm.in", "value": "hip1"}, {"system": "https://ndhm.in", "value": "hip2"}], "status": "finished", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "AMB", "display": "ambulatory"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "period": {"start": "2023-10-01T10:00:00+05:30", "end": "2023-11-01T10:00:00+05:30"}, "diagnosis": [{"condition": {"reference": "urn:uuid:6096a9aa-1843-4e03-8f2d-7db5c265cf3e", "display": "Condition"}, "use": {"coding": [{"system": "http://snomed.info/sct", "code": "38341003", "display": "Hypertension"}], "text": "Hypertension"}}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang=en'><p>Encounter Record</p></div>"}}}, {"fullUrl": "urn:uuid:09086b93-351e-444e-8561-3e87d69d5cb6", "resource": {"resourceType": "AllergyIntolerance", "id": "09086b93-351e-444e-8561-3e87d69d5cb6", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/AllergyIntolerance"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "75413007", "display": "Peanut"}], "text": "Peanut"}, "recordedDate": "2025-04-11T14:13:32.903+05:30", "patient": {"reference": "urn:uuid:d7ddb236-83c6-489d-a205-ef5bcc890c03", "display": "Patient"}, "recorder": {"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "Practitioner"}, "note": [{"text": "test"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>AllergyIntolerance Record</p></div>"}}}, {"fullUrl": "urn:uuid:356cfa02-99b2-433a-a47f-db42537c265e", "resource": {"resourceType": "AllergyIntolerance", "id": "356cfa02-99b2-433a-a47f-db42537c265e", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/AllergyIntolerance"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "75413007", "display": "Peanut"}], "text": "Peanut"}, "recordedDate": "2025-04-11T14:13:32.856+05:30", "patient": {"reference": "urn:uuid:be32a41e-c351-42f5-a90a-ca8a966ca3b4", "display": "Patient"}, "recorder": {"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "Practitioner"}, "note": [{"text": "test"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>AllergyIntolerance Record</p></div>"}}}, {"fullUrl": "urn:uuid:916d5bf8-a47d-4105-a5bd-26a6e6870796", "resource": {"resourceType": "Appointment", "id": "916d5bf8-a47d-4105-a5bd-26a6e6870796", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment"]}, "status": "booked", "description": "hello world", "start": "2023-10-01T10:00:00+05:30", "end": "2023-10-01T11:00:00+05:30", "created": "2025-04-09T07:12:54.912Z", "serviceCategory": [{"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}], "serviceType": [{"coding": [{"system": "http://snomed.info/sct", "code": "60132005", "display": "General"}], "text": "General"}], "appointmentType": {"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}, "reasonReference": [{"reference": "urn:uuid:6096a9aa-1843-4e03-8f2d-7db5c265cf3e", "display": "Condition"}], "basedOn": [{"reference": "urn:uuid:db5c37f5-905f-46bc-bd0c-6e852e5c6eec", "display": "ServiceRequest"}], "specialty": [{"coding": [{"system": "http://snomed.info/sct", "code": "394579002", "display": "Cardiology"}], "text": "Cardiology"}], "participant": [{"actor": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "status": "accepted"}, {"actor": {"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "Practitioner"}, "status": "accepted"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Appointment Record</p></div>"}}}, {"fullUrl": "urn:uuid:6096a9aa-1843-4e03-8f2d-7db5c265cf3e", "resource": {"resourceType": "Condition", "id": "6096a9aa-1843-4e03-8f2d-7db5c265cf3e", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "38341003", "display": "Hypertension"}], "text": "Hypertension"}, "recordedDate": "2025-04-11T08:43:32.716Z", "onsetPeriod": {"start": "2025-04-11T08:43:32.716Z", "end": "2025-04-11T08:43:32.716Z"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Condition Record</p></div>"}}}, {"fullUrl": "urn:uuid:77c5a7a3-4c2b-436e-a469-048521379416", "resource": {"resourceType": "Condition", "id": "77c5a7a3-4c2b-436e-a469-048521379416", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "38341003", "display": "Hypertension"}], "text": "Hypertension"}, "recordedDate": "2025-04-11T08:43:31.535Z", "onsetPeriod": {"start": "2025-04-11T08:43:31.535Z", "end": "2025-04-11T08:43:31.535Z"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Condition Record</p></div>"}}}, {"fullUrl": "urn:uuid:def088f8-08a4-4c23-bbaf-5d32c9295664", "resource": {"resourceType": "Condition", "id": "def088f8-08a4-4c23-bbaf-5d32c9295664", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "38341003", "display": "Hypertension"}], "text": "Hypertension"}, "recordedDate": "2025-04-11T08:43:31.531Z", "onsetPeriod": {"start": "2025-04-11T08:43:31.531Z", "end": "2025-04-11T08:43:31.531Z"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Condition Record</p></div>"}}}, {"fullUrl": "urn:uuid:42c10de8-cfba-47fe-aa62-f2da7ca86a2a", "resource": {"resourceType": "Procedure", "id": "42c10de8-cfba-47fe-aa62-f2da7ca86a2a", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"]}, "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "29303009", "display": "Electrocardiogram"}], "text": "Electrocardiogram"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "performedDateTime": "2023-10-01T10:30:00+05:30", "followUp": [{"coding": [{"system": "http://snomed.info/sct", "code": "281036007", "display": "Follow-up consultation"}], "text": "Follow-up consultation"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Procedure Record</p></div>"}}}, {"fullUrl": "urn:uuid:cb165e6e-1e51-41c8-a367-c0780e211fe0", "resource": {"resourceType": "Procedure", "id": "cb165e6e-1e51-41c8-a367-c0780e211fe0", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"]}, "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "71388002", "display": "Procedure"}], "text": "Procedure"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "performedDateTime": "Electrocardiogram", "followUp": [{"coding": [{"system": "http://snomed.info/sct", "code": "281036007", "display": "Follow-up consultation"}], "text": "Follow-up consultation"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Procedure Record</p></div>"}}}, {"fullUrl": "urn:uuid:db5c37f5-905f-46bc-bd0c-6e852e5c6eec", "resource": {"resourceType": "ServiceRequest", "id": "db5c37f5-905f-46bc-bd0c-6e852e5c6eec", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ServiceRequest"]}, "status": "completed", "intent": "order", "authoredOn": "2025-04-11T14:13:32.903+05:30", "category": [{"coding": [{"system": "http://snomed.info/sct", "code": "396550006", "display": "blood test"}], "text": "blood test"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "15220000", "display": "Laboratory test"}], "text": "Laboratory test"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "requester": {"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "Practitioner"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>ServiceRequest Record</p></div>"}}}, {"fullUrl": "urn:uuid:4214d19a-d1d8-4235-88bb-0d347fce38d8", "resource": {"resourceType": "ServiceRequest", "id": "4214d19a-d1d8-4235-88bb-0d347fce38d8", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ServiceRequest"]}, "status": "completed", "intent": "order", "authoredOn": "2025-04-11T14:13:32.856+05:30", "category": [{"coding": [{"system": "http://snomed.info/sct", "code": "396550006", "display": "blood test"}], "text": "blood test"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "15220000", "display": "Laboratory test"}], "text": "Laboratory test"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "requester": {"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "Practitioner"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>ServiceRequest Record</p></div>"}}}, {"fullUrl": "urn:uuid:f6608688-59c3-4bd6-a4e7-916b5901940f", "resource": {"resourceType": "MedicationStatement", "id": "f6608688-59c3-4bd6-a4e7-916b5901940f", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationStatement"]}, "status": "completed", "dateAsserted": "2025-04-11T14:13:32.903+05:30", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "134463001", "display": "Telmisartan 20 mg oral tablet"}], "text": "Telmisartan 20 mg oral tablet"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>MedicationStatement Record</p></div>"}}}, {"fullUrl": "urn:uuid:af953420-62ad-4d1d-a0de-4b01bb1c66a6", "resource": {"resourceType": "MedicationStatement", "id": "af953420-62ad-4d1d-a0de-4b01bb1c66a6", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationStatement"]}, "status": "completed", "dateAsserted": "2025-04-11T14:13:32.856+05:30", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "134463001", "display": "Telmisartan 20 mg oral tablet"}], "text": "Telmisartan 20 mg oral tablet"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>MedicationStatement Record</p></div>"}}}, {"fullUrl": "urn:uuid:ed5db232-ae0d-4489-8c6e-3eafe5a7580a", "resource": {"resourceType": "MedicationRequest", "id": "ed5db232-ae0d-4489-8c6e-3eafe5a7580a", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]}, "status": "active", "intent": "order", "authoredOn": "2025-04-11T08:43:32.716Z", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "387517004", "display": "Paracetamol"}], "text": "Paracetamol"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "requester": {"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "Practitioner"}, "reasonReference": [{"reference": "urn:uuid:6096a9aa-1843-4e03-8f2d-7db5c265cf3e", "display": "Condition"}], "dosageInstruction": [{"text": "500mg every 6 hours", "timing": {"repeat": {"frequency": "", "period": null, "periodUnit": "Hours"}}, "route": {"coding": [{"system": "http://snomed.info/sct", "code": "738956005", "display": "Oral"}], "text": "Oral"}, "doseQuantity": {"value": "", "unit": "tablet"}, "site": {"coding": [{"system": "http://snomed.info/sct", "code": "123851003", "display": "Mouth"}], "text": "Mouth"}, "additionalInstruction": [{"coding": [{"system": "http://snomed.info/sct", "code": "272393004", "display": "Tests"}], "text": "Tests"}]}], "reasonCode": [{"coding": [{"system": "http://snomed.info/sct", "code": "182970005", "display": "Pain Relief"}], "text": "Pain Relief"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>MedicationRequest Record</p></div>"}}}, {"fullUrl": "urn:uuid:5b3685eb-4fef-44b6-93b1-25e9192c1c25", "resource": {"resourceType": "MedicationRequest", "id": "5b3685eb-4fef-44b6-93b1-25e9192c1c25", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]}, "status": "active", "intent": "order", "authoredOn": "2025-04-11T08:43:31.531Z", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "387517004", "display": "Paracetamol"}], "text": "Paracetamol"}, "subject": {"reference": "urn:uuid:4de11a6a-322e-4ee3-b8de-48022616ef8f", "display": "Patient"}, "requester": {"reference": "urn:uuid:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "Practitioner"}, "reasonReference": [{"reference": "urn:uuid:6096a9aa-1843-4e03-8f2d-7db5c265cf3e", "display": "Condition"}], "dosageInstruction": [{"text": "500mg every 6 hours", "timing": {"repeat": {"frequency": "", "period": null, "periodUnit": "Hours"}}, "route": {"coding": [{"system": "http://snomed.info/sct", "code": "738956005", "display": "Oral"}], "text": "Oral"}, "doseQuantity": {"value": "", "unit": "tablet"}, "site": {"coding": [{"system": "http://snomed.info/sct", "code": "123851003", "display": "Mouth"}], "text": "Mouth"}, "additionalInstruction": [{"coding": [{"system": "http://snomed.info/sct", "code": "272393004", "display": "Tests"}], "text": "Tests"}]}], "reasonCode": [{"coding": [{"system": "http://snomed.info/sct", "code": "182970005", "display": "Pain Relief"}], "text": "Pain Relief"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>MedicationRequest Record</p></div>"}}}], "signature": {"type": [{"system": "urn:iso-astm:E1762-95:2013", "code": "1.2.840.10065.1.12.1.1", "display": "Author's Signature"}], "when": "2025-04-11T14:13:32.903+05:30", "who": {"reference": "urn.:8cf1aa08-0c51-4c19-9d0f-7b02e7238e48", "display": "Practitioner"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ=="}}