{"name": "healtether.models", "version": "1.7.8", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "healtether.models", "version": "1.7.8", "license": "ISC", "dependencies": {"eslint": "^9.23.0", "mongoose": "^8.15.1"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.6.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.6.1.tgz", "integrity": "sha1-5MWP3PBpbnpfGcMCAe1DEjqxWrw=", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.20.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/config-array/-/config-array-0.20.0.tgz", "integrity": "sha1-ehIy6CN2cS0zQAEqL1YaJ2TRmI8=", "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-helpers": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/config-helpers/-/config-helpers-0.2.1.tgz", "integrity": "sha1-JgQsAo0b7uXOIjWnkpuRxSZRZG0=", "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.12.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/core/-/core-0.12.0.tgz", "integrity": "sha1-X5YMPVdyi+n2xlvYSqaqYTB4eY4=", "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "integrity": "sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=", "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/js": {"version": "9.24.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/js/-/js-9.24.0.tgz", "integrity": "sha1-aFJ3mAu3v4TsyOThM8zdp1RaaR4=", "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/object-schema/-/object-schema-2.1.6.tgz", "integrity": "sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=", "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.2.8", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz", "integrity": "sha1-R0iNj4FxtdRhPoMzE/POcI41Jfg=", "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.13.0", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit/node_modules/@eslint/core": {"version": "0.13.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@eslint/core/-/core-0.13.0.tgz", "integrity": "sha1-vwLyCYRtO/mW+egAnbYt8nObRYw=", "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanfs/core/-/core-0.19.1.tgz", "integrity": "sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=", "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanfs/node/-/node-0.16.6.tgz", "integrity": "sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=", "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanwhocodes/retry/-/retry-0.3.1.tgz", "integrity": "sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=", "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@humanwhocodes/retry/-/retry-0.4.2.tgz", "integrity": "sha1-GGBHPeffoVRnZ0SPMz24DLD/IWE=", "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@mongodb-js/saslprep": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@mongodb-js/saslprep/-/saslprep-1.2.2.tgz", "integrity": "sha1-CVBvKcwqmdnXuVHKp//8h+UiptM=", "license": "MIT", "dependencies": {"sparse-bitfield": "^3.0.3"}}, "node_modules/@types/estree": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/estree/-/estree-1.0.7.tgz", "integrity": "sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=", "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "license": "MIT"}, "node_modules/@types/webidl-conversions": {"version": "7.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz", "integrity": "sha1-Ewbb+lN2i8vPyVocjN42eXVYGFk=", "license": "MIT"}, "node_modules/@types/whatwg-url": {"version": "11.0.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/@types/whatwg-url/-/whatwg-url-11.0.5.tgz", "integrity": "sha1-qqJUbmDwyZIJyhM2DDLHjK8sQJ8=", "license": "MIT", "dependencies": {"@types/webidl-conversions": "*"}}, "node_modules/acorn": {"version": "8.14.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/acorn/-/acorn-8.14.1.tgz", "integrity": "sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/argparse/-/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "license": "Python-2.0"}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "license": "MIT"}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/bson": {"version": "6.10.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/bson/-/bson-6.10.3.tgz", "integrity": "sha1-X5pGOva4PiZL7dCLI20TVqMO2kc=", "license": "Apache-2.0", "engines": {"node": ">=16.20.1"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/debug": {"version": "4.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/debug/-/debug-4.4.0.tgz", "integrity": "sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.24.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eslint/-/eslint-9.24.0.tgz", "integrity": "sha1-mn8ubLLegcQFqyRLAvRYTHnca+4=", "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.0", "@eslint/core": "^0.12.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.24.0", "@eslint/plugin-kit": "^0.2.7", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-scope": {"version": "8.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eslint-scope/-/eslint-scope-8.3.0.tgz", "integrity": "sha1-EM06kY/91yL18/e1uD25sjyHNA0=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz", "integrity": "sha1-aHussq+IT83aim59ZcYG9GoUzUU=", "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree": {"version": "10.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/espree/-/espree-10.3.0.tgz", "integrity": "sha1-KSZ89bDLmHNbZeZLoH4O1J0e7Yo=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.14.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/esquery/-/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "license": "MIT"}, "node_modules/file-entry-cache": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/file-entry-cache/-/file-entry-cache-8.0.0.tgz", "integrity": "sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=", "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/find-up/-/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/flat-cache/-/flat-cache-4.0.1.tgz", "integrity": "sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=", "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/flatted/-/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "license": "ISC"}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/globals": {"version": "14.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/globals/-/globals-14.0.0.tgz", "integrity": "sha1-iY10E8Kbq89rr+Vvyt3thYrack4=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ignore/-/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "license": "ISC"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "license": "MIT"}, "node_modules/kareem": {"version": "2.6.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/kareem/-/kareem-2.6.3.tgz", "integrity": "sha1-IxaOyP+2wav9MbcWmm+x3ShZkqw=", "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/keyv/-/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/levn/-/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "license": "MIT"}, "node_modules/memory-pager": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/memory-pager/-/memory-pager-1.5.0.tgz", "integrity": "sha1-2HUWVdItOEaCdByXLyw9bfo+ZrU=", "license": "MIT"}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/mongodb": {"version": "6.16.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongodb/-/mongodb-6.16.0.tgz", "integrity": "sha1-KnoZhuwVHZxzj8jOTPQyTD9yii8=", "license": "Apache-2.0", "dependencies": {"@mongodb-js/saslprep": "^1.1.9", "bson": "^6.10.3", "mongodb-connection-string-url": "^3.0.0"}, "engines": {"node": ">=16.20.1"}, "peerDependencies": {"@aws-sdk/credential-providers": "^3.188.0", "@mongodb-js/zstd": "^1.1.0 || ^2.0.0", "gcp-metadata": "^5.2.0", "kerberos": "^2.0.1", "mongodb-client-encryption": ">=6.0.0 <7", "snappy": "^7.2.2", "socks": "^2.7.1"}, "peerDependenciesMeta": {"@aws-sdk/credential-providers": {"optional": true}, "@mongodb-js/zstd": {"optional": true}, "gcp-metadata": {"optional": true}, "kerberos": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "snappy": {"optional": true}, "socks": {"optional": true}}}, "node_modules/mongodb-connection-string-url": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongodb-connection-string-url/-/mongodb-connection-string-url-3.0.2.tgz", "integrity": "sha1-4iMInfoKX6m/UF+K7cvGewd7M+c=", "license": "Apache-2.0", "dependencies": {"@types/whatwg-url": "^11.0.2", "whatwg-url": "^14.1.0 || ^13.0.0"}}, "node_modules/mongoose": {"version": "8.15.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mongoose/-/mongoose-8.15.1.tgz", "integrity": "sha1-0MUiSgHqio+W9f1fhVdnWgVNCm8=", "license": "MIT", "dependencies": {"bson": "^6.10.3", "kareem": "2.6.3", "mongodb": "~6.16.0", "mpath": "0.9.0", "mquery": "5.0.0", "ms": "2.1.3", "sift": "17.1.3"}, "engines": {"node": ">=16.20.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}, "node_modules/mpath": {"version": "0.9.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mpath/-/mpath-0.9.0.tgz", "integrity": "sha1-DBIv4QeEbjH8WMdbCcNVFLOHGQQ=", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/mquery": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/mquery/-/mquery-5.0.0.tgz", "integrity": "sha1-qVvl38YQsjhi3zSkfT5dYOEQaV0=", "license": "MIT", "dependencies": {"debug": "4.x"}, "engines": {"node": ">=14.0.0"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "license": "MIT"}, "node_modules/optionator": {"version": "0.9.4", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/optionator/-/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/punycode/-/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/sift": {"version": "17.1.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/sift/-/sift-17.1.3.tgz", "integrity": "sha1-nSAA1NQVhogLAHm1GD2DnHoUK/c=", "license": "MIT"}, "node_modules/sparse-bitfield": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz", "integrity": "sha1-/0rm5oZWBWuks+eSqzM004JzyhE=", "license": "MIT", "dependencies": {"memory-pager": "^1.0.2"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/tr46": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/tr46/-/tr46-5.1.1.tgz", "integrity": "sha1-lq6GfN24/bZKScwwWajUKLzyOMo=", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "engines": {"node": ">=18"}}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/type-check/-/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/webidl-conversions": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/whatwg-url": {"version": "14.2.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/whatwg-url/-/whatwg-url-14.2.0.tgz", "integrity": "sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=", "license": "MIT", "dependencies": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=18"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}