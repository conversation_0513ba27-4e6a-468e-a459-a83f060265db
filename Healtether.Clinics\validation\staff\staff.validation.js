import { check, checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateUpsertStaff = async (req, res, next) => {
  await checkSchema({
    "data.staffId": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Staff ID must be a string",
      isLength: {
        options: { max: 255 },
        errorMessage: "Staff ID must be at most 255 characters long",
      },
      notEmpty: {
        errorMessage: "Staff ID is required",
      },
    },
    "data.firstName": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "First name must be a string",
      isLength: {
        options: { max: 255 },
        errorMessage: "First name must be at most 255 characters long",
      },
      notEmpty: {
        errorMessage: "First name is required",
      },
    },
    "data.lastName": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Last name must be a string",
      isLength: {
        options: { max: 255 },
        errorMessage: "Last name must be at most 255 characters long",
      },
      notEmpty: {
        errorMessage: "Last name is required",
      },
    },
    "data.specialisation": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Specialisation must be a string",
      isLength: {
        options: { max: 50 },
        errorMessage: "Specialisation must be at most 50 characters long",
      },
    },
    "data.isDoctor": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isBoolean: true,
      errorMessage: "isDoctor must be a boolean",
      toBoolean: true, // Convert string "false" to boolean false
    },
    "data.age": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isInt: {
        options: { min: 1, max: 100 },
        errorMessage: "Age must be a number between 1 and 100",
      },
      toInt: true, // Convert string "12" to integer 12
    },
    "data.birthday": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isISO8601: true,
      errorMessage: "Birthday must be a valid date",
    },
    "data.gender": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Gender must be a string",
      isLength: {
        options: { max: 10 },
        errorMessage: "Gender must be at most 10 characters long",
      },
    },
    "data.mobile": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Mobile must be a string",
      isLength: {
        options: { max: 15 },
        errorMessage: "Mobile must be at most 15 characters long",
      },
      notEmpty: {
        errorMessage: "Mobile is required",
      },
    },
    "data.email": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: {
        options: { checkFalsy: true }, // Skip validation if the field is an empty string
      },
      isEmail: {
        errorMessage: "Must be a valid email address",
      },
      isLength: {
        options: { max: 100 },
        errorMessage: "Email must be at most 100 characters long",
      },
    },
    "data.address.house": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "House must be a string",
      isLength: {
        options: { max: 255 },
        errorMessage: "House must be at most 255 characters long",
      },
    },
    "data.address.street": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Street must be a string",
      isLength: {
        options: { max: 1000 },
        errorMessage: "Street must be at most 1000 characters long",
      },
    },
    "data.address.landmarks": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Landmarks must be a string",
      isLength: {
        options: { max: 1000 },
        errorMessage: "Landmarks must be at most 1000 characters long",
      },
    },
    "data.address.city": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "City must be a string",
      isLength: {
        options: { max: 500 },
        errorMessage: "City must be at most 500 characters long",
      },
    },
    "data.address.pincode": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Pincode must be a string",
      isLength: {
        options: { max: 50 },
        errorMessage: "Pincode must be at most 50 characters long",
      },
    },
    "data.documentType": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Document type must be a string",
      isLength: {
        options: { max: 100 },
        errorMessage: "Document type must be at most 100 characters long",
      },
    },
    "data.documentNumber": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Document number must be a string",
      isLength: {
        options: { max: 100 },
        errorMessage: "Document number must be at most 100 characters long",
      },
    },
    "data.bankName": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Bank name must be a string",
      isLength: {
        options: { max: 100 },
        errorMessage: "Bank name must be at most 100 characters long",
      },
    },
    "data.accountName": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Account name must be a string",
      isLength: {
        options: { max: 255 },
        errorMessage: "Account name must be at most 255 characters long",
      },
    },
    "data.accountNo": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Account number must be a string",
      isLength: {
        options: { max: 100 },
        errorMessage: "Account number must be at most 100 characters long",
      },
    },
    "data.ifsc": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "IFSC code must be a string",
      isLength: {
        options: { max: 50 },
        errorMessage: "IFSC code must be at most 50 characters long",
      },
    },
    "data.created.on": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isISO8601: true,
      errorMessage: "Created date must be a valid date",
      toDate: true, // Convert string to Date object
    },
    "data.modified.on": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isISO8601: true,
      errorMessage: "Modified date must be a valid date",
      toDate: true, // Convert string to Date object
    },
    "data.documents.*.fileName": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Document file name must be a string",
      isLength: {
        options: { max: 255 },
        errorMessage: "Document file name must be at most 255 characters long",
      },
    },
    "data.documents.*.blobName": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Document blob name must be a string",
      isLength: {
        options: { max: 255 },
        errorMessage: "Document blob name must be at most 255 characters long",
      },
    },
    "data.documents.*.uploadedOn": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isISO8601: true,
      errorMessage: "Document uploaded date must be a valid date",
      toDate: true, // Convert string to Date object
    },
    "data.deleted": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isBoolean: true,
      errorMessage: "Deleted must be a boolean",
      toBoolean: true, // Convert string "false" to boolean false
    },
    "data.availableTimeSlot": {
      in: ["body"],
      optional: true,
      isJSON: true, // Validate if it's a valid JSON
      errorMessage: "Available time slot must be a valid JSON string",
      custom: {
        options: (value) => {
          try {
            const parsed = JSON.parse(value);
            return (
              Array.isArray(parsed) &&
              parsed.every((item) => typeof item === "object")
            );
          } catch {
            return false;
          }
        },
        errorMessage:
          "Available time slot must be a valid JSON array of objects",
      },
    },
    "data.clientId": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateCheckMobileNumber = async (req, res, next) => {
  await checkSchema({
    mobile: {
      trim: true,
      escape: true,
      in: ["body"], // Assuming mobile number is coming in the body
      isString: true,
      errorMessage: "Mobile number must be a string",
      isLength: {
        options: { max: 10 },
        errorMessage: "Mobile number must be at most 15 characters long",
      },
      notEmpty: {
        errorMessage: "Mobile number is required",
      },
    },
  }).run(req);
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetDoctorsWithTime = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"], // Assuming these are query parameters
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    date: {
      trim: true,
      escape: true,
      in: ["query"],
      optional: true,
      isISO8601: true,
      errorMessage: "Date must be a valid ISO 8601 date",
    },
  }).run(req);
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const ValidateGetDoctorTimeSlot = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"], // Assuming these are query parameters
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    id: {
      in: ["query"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Id must be a valid ObjectId",
      },
    },
  }).run(req);
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSearchStaffName = async (req, res, next) => {
  await checkSchema({
    name: {
      trim: true,
      escape: true,
      in: ["query"], // Assuming this is a query parameter
      optional: true,
      isString: true,
      errorMessage: "Name must be a string",
      isLength: {
        options: { max: 255 },
        errorMessage: "Name must be at most 255 characters long",
      },
    },
  }).run(req);
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateAllDoctors = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"], // Assuming this is a query parameter
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
  }).run(req);
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateStaffOverview = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"], // Assuming these are query parameters
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
    page: {
      trim: true,
      escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 0 },
        errorMessage: "Page number must be a non-negative integer",
      },
    },
    size: {
      trim: true,
      escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
    },
    keyword: {
      trim: true,
      escape: true,
      in: ["query"],
      optional: true,
      isString: true,
      errorMessage: "Keyword must be a string",
    },
    sortby: {
      trim: true,
      escape: true,
      in: ["query"],
      optional: true,
      isString: true,
      errorMessage: "Sort by field must be a string",
    },
    direction: {
      in: ["query"],
      optional: true,
      isIn: {
        options: [["asc", "desc"]],
        errorMessage: 'Sort direction must be either "asc" or "desc"',
      },
    },
  }).run(req);
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateStaffById = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "ID must be a valid ObjectId",
      },
    },
  }).run(req);
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateRemoveStaff = validateStaffById;
