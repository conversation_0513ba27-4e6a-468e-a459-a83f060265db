import { jest } from "@jest/globals";
 await jest.unstable_mockModule("../../../helpers/clinic/group.helper.js", async () => ({
        createGroup: jest.fn(),
        getAllGroup: jest.fn(),
    }));

const { createClinicGroup } = await import("../../../controllers/clinic/group.controller.js");
const { createGroup } = await import("../../../helpers/clinic/group.helper.js");

describe('createClinicGroup', () => {
  let req, res;

  beforeEach(() => {
    req = {
        body: {
          data: {
            groupName: 'Test Group',
          },
        },
        user: { id: 'user123', name: 'Test User' }, // Example user object
      };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  });

  it('should call createGroup with the correct data and user', async () => {
    await createClinicGroup(req, res);

    expect(createGroup).toHaveBeenCalledWith(req.body.data, req.user);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });

});
