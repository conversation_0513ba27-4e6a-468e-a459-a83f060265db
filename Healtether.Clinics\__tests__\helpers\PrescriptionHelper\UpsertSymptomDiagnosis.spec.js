import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { upsertSymptomDiagnosis } from "../../../helpers/appointment/write-prescription/prescription.helper.js";
import { Patient } from "../../../model/clinics.model.js";
import { Appointment } from "../../../model/clinics.model.js";
import { Client } from "../../../model/clinics.model.js";
import { PatientPrescriptions } from "../../../model/clinics.model.js"; // Add this import for the prescription model
import { setup, teardown } from "../../../setup.js";

jest.setTimeout(30000);
beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe("UpsertSymptomDiagnosis function", () => {
  let patientId;
  let appointmentId;
  let clinicId;

  beforeEach(async () => {
    // Clean up any previous data to ensure a fresh test environment
    await PatientPrescriptions.deleteMany({});
  });

  afterEach(async () => {
    await PatientPrescriptions.deleteMany({});
  });

  it("should add a new symptom and diagnosis when no existing record is found", async () => {
    const patient = new Patient({
      firstName: "John",
      lastName: "Doe",
      mobile: "**********",
      patientId: "PAT001",
      prefix:"Mr.",
      deleted: false,
    });
    patientId = (await patient.save())._id;

    const clinic = await Client.create({
      clinicName: "TEST",
      address: "18 Veera Alagamman Koil Street",
      created: { on: new Date("2024-04-27T06:52:20.110Z") },
      isDeleted: false,
      logo: "",
      modified: { on: new Date("2024-09-17T09:15:18.594Z") },
      patientId: { prefix: "SD", suffix: "" },
    });
    clinicId = clinic._id;

    const appointment = new Appointment({
      mobile: "**********",
      name: "First Patient",
      gender: "Male",
      age: 33,
      patientId: new mongoose.Types.ObjectId(patientId),
      clinic: new mongoose.Types.ObjectId(clinicId),
      doctorName: "Dr. Smith",
      appointmentDate: new Date(),
      timeSlot: "10:00 AM",
      reason: "Checkup",
      paymentStatus: true,
      virtualConsultation: false,
      isCanceled: false,
    });
    appointmentId = (await appointment.save())._id;

    const data = {
      symptoms: [
        {
          name: "cough",
          duration: {
            value: 7,
            unit: "days",
          },
          notes: "Take after meals",
        },
      ],
      diagnosis: [
        {
          name: "cold",
          notes: "Take after meals",
        },
      ],
    };
    const user = { id: "user1", name: "User One" };
    const result = await upsertSymptomDiagnosis(data, user, patientId, clinicId, appointmentId);

    expect(result.symptoms[0].name).toBe("cough");
    expect(result.diagnosis[0].name).toBe("cold");
  });

  it("should update existing symptoms and diagnosis when a record is found", async () => {
    // Create an initial prescription record
    const existingPrescription = new PatientPrescriptions({
      clinic: clinicId,
      patient: patientId,
      appointment: appointmentId,
      symptoms: [
        { name: "fever", duration: { value: 3, unit: "days" }, notes: "Monitor temperature" },
      ],
      diagnosis: [{ name: "flu", notes: "Drink plenty of fluids" }],
      created: { on: new Date().toISOString(), by: { id: "user1", name: "User One" } },
      modified: { on: new Date().toISOString(), by: { id: "user1", name: "User One" } },
    });

    await existingPrescription.save();

    const data = {
      symptoms: [
        {
          name: "cough",
          duration: {
            value: 7,
            unit: "days",
          },
          notes: "Take after meals",
        },
      ],
      diagnosis: [
        {
          name: "cold",
          notes: "Take after meals",
        },
      ],
    };
    const user = { id: "user2", name: "User Two" };

    const result = await upsertSymptomDiagnosis(data, user, patientId, clinicId, appointmentId);

    expect(result.data.symptoms[0].name).toBe("cough");
    expect(result.data.diagnosis[0].name).toBe("cold");
    expect(result.message).toBe("Symptoms updated successfully");
  });
});
