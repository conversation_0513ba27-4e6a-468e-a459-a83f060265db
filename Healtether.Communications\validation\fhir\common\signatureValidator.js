
const signatureValidator = {
  signature: {
    in: ["body"],
    exists: { errorMessage: "signature is required" },
    isObject: true,
  },
  "signature.who.type": {
    in: ["body"],
    isString: true,
    errorMessage: "signature.who.type must be a string",
  },
  "signature.who.name": {
    in: ["body"],
    isString: true,
    errorMessage: "signature.who.name must be a string",
  },
  "signature.sigFormat": {
    in: ["body"],
    isString: true,
    isIn: { options: [["image/jpeg", "image/png"]] },
    errorMessage: "sigFormat must be a valid image MIME type",
  },
  "signature.data": {
    in: ["body"],
    isBase64: true,
    errorMessage: "signature data must be base64 encoded",
  },
}

export default signatureValidator;
