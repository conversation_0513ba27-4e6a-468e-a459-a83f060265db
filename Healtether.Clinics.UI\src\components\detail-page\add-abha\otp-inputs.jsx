import { useRef } from "react";

export const OtpInputs = ({ 
  verifyCode, 
  otp, 
  setOtp, 
  verify, 
  timer, 
  isResendDisabled, 
  onResendOtp, 
  error,
  resendAttemptsCount = 0 // Default to 0 if not provided
}) => {
  const inputs = useRef([]);
  // Calculate remaining attempts (max 3 attempts allowed)
  const maxAttempts = 2; 
  const remainingAttempts = maxAttempts - resendAttemptsCount;

  const handleChange = (e, index) => {
    const newOtp = [...otp];
    const inputValue = e.target.value;
    const regex = /^[0-9]*$/;
    if (regex.test(inputValue)) {
      newOtp[index] = inputValue;
    }
    // newOtp[index] = e.target.value;
    setOtp(newOtp);

    // If the value is not empty, move to the next input
    if (regex.test(inputValue) && e.target.value !== "" && e.target.nextSibling)
      e.target.nextSibling.focus();
  };

  const handleKeyDown = (e, index) => {
    // If backspace is pressed and the current input is empty, focus the previous input
    if (e.key === "Backspace" && e.currentTarget.value === "" && index > 0)
      inputs.current[index - 1].select();
    if (e.key === "ArrowLeft" && index > 0) inputs.current[index - 1].select();
    else if (e.key === "ArrowRight" && index < otp.length - 1)
      inputs.current[index + 1].select();
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim().slice(0, 6);
    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split("");
      setOtp(newOtp);
      inputs.current[newOtp.length - 1].focus();
      verify(e, newOtp);
    } else {
      // Handle the error case where pasted data is not numeric or not 6 characters long
      console.error("Invalid paste content. Only a 6-digit number is allowed.");
    }
  };

  return (
    <>
      <div className="mt-2 text-sm font-semibold text-dark">
        Enter the OTP received on your mobile number
      </div>
      <section className="grid grid-cols-6 h-12 gap-1.5 overflow-hidden rounded-sm">
        {otp.map((value, index) => (
          <input
            key={index}
            type="text"
            maxLength={1}
            autoFocus={index === 0}
            required
            value={value}
            onChange={(e) => handleChange(e, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            onPaste={handlePaste}
            ref={(input) => (inputs.current[index] = input)}
            className={`flex h-full w-8 items-center justify-center ${
              error?"border-b-2 border-red-600":
              value
                ? isNaN(value) || value.length === 0
                  ? "border-b-2 border-destructive"
                  : "border-b-4 border-[#5351C7]"
                : "border-color_muted/20"
            } bg-light text-center w-full font-body text-base font-semibold text-color_muted-foreground focus:outline-hidden`}
          />
        ))}
      </section>
      {
        error && (
          <div className="mt-2 text-sm text-red-600 font-semibold text-destructive">{error}</div>
        )
      }
      <div className="flex justify-between items-center mt-2">
        <div
          className={`text-base cursor-pointer font-semibold text-blue hover:underline ${isResendDisabled ? "opacity-50 cursor-not-allowed" : ""}`}
          onClick={isResendDisabled ? null : onResendOtp}
        >
          {isResendDisabled ? `Resend OTP (${timer}s) ` : "Resend OTP"}
        </div>
        {remainingAttempts >= 0 && (
          <div className="text-sm font-medium text-gray-600">
            {remainingAttempts > 0 
              ? `${remainingAttempts} ${remainingAttempts === 1 ? 'attempt' : 'attempts'} remaining` 
              : "No attempts remaining"}
          </div>
        )}
      </div>
    </>
  );
};