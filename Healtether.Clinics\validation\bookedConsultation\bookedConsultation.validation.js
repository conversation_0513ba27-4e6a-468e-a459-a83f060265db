import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateScheduleAppointment = async (req, res, next) => {
  await checkSchema({
    "data.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: true,
      errorMessage: "Name is required and must be a valid string",
    },
    "data.mobile": {
      trim:true,
      escape:true,
      in: ["body"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
    },
    "data.age": {
      trim:true,
      escape:true,
      in: ["body"],
      isInt: {
        options: { min: 0 },
        errorMessage: "Age must be a positive integer",
      },
      optional: true,
    },
    "data.birthDate": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isISO8601: true,
      toDate: true,
      errorMessage: "Birth date must be a valid date",
    },
    "data.gender": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Gender must be a string",
      isLength: {
        options: { max: 10 },
        errorMessage: "Gender must be at most 10 characters long",
      },
    },
    "data.appointmentDate": {
      in: ["body"],
      isISO8601: true,
      toDate: true,
      errorMessage: "Appointment date must be a valid date",
    },
    "data.doctorId": {
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Doctor ID must be a valid ObjectId",
      },
    },
    "data.isFollowUp": {
      in: ["body"],
      isBoolean: true,
      optional: true,
      errorMessage: "Is follow up must be a boolean",
    },
    "data.doctorName": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
      notEmpty: true,
      errorMessage: "Doctor name is required and must be a valid string",
    },
    "data.reason": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Reason must be a valid string",
    },
    "data.timeSlot": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
      notEmpty: true,
      errorMessage: "Time slot must be a valid string",
    },
    "data.patientId": {
      in: ["body"],
      optional:true,
      optional: true,
      custom: {
        options: (value) =>{
          if(value?.trim()!=="")
        { return mongoose.Types.ObjectId.isValid(value)}
          return true;
          },
        errorMessage: "Patient ID must be a valid ObjectId",
      },
    },
    "data.virtualConsultation": {
      trim:true,
      escape:true,
      in: ["body"],
      isBoolean: true,
      toBoolean: true,
      errorMessage: "Virtual consultation flag must be a boolean",
    },
    "data.abhaAddress": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
      notEmpty: true,
      errorMessage: "abhaAddress must be a valid string",
    },
    "data.abhaNumber": {
      in: ["body"],
      isString: true,
      optional: true,
      notEmpty: true,
      errorMessage: "abhaNumber must be a valid string",
    },

  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateScheduleAppointmentOverview = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    page: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 0 },
        errorMessage: "Page number must be a non-negative integer",
      },
    },
    size: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
    },
    keyword: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isString: true,
      errorMessage: "Keyword must be a string",
    },
    sortby: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isString: true,
      errorMessage: "Sort by field must be a string",
    },
    direction: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isIn: {
        options: [["asc", "desc"]],
        errorMessage: 'Sort direction must be either "asc" or "desc"',
      },
    },
    status: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isIn: {
        options: [["Booked", "Cancelled","All","FollowUp"]],
        errorMessage:
          "Status must be one of 'Upcoming', 'Cancelled'",
      },
    }, 
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};