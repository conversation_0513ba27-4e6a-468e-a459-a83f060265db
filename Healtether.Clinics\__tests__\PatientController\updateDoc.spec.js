import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");
const { mockBlobHelper } = await import("../mocks/mock.blob.helper.js");
mockBlobHelper()
mockPatientHelper();

const { updateDoc } = await import('../../controllers/patients/patients.controller.js'); 
const { BlobHelper } = await import("../../helpers/storage/blob.helper.js"); 

const mockDocData = {
  clientId: "662ca0a41a2431e16c41ebaa",
  documentName: JSON.stringify([
    { fileName: "images.png", blobName: "9DY8I12GMGSkEmi_zrjdd.png" },
  ]),
  profileName: null // Add this if needed
};

const req = {
  body: mockDocData,
  files: {
    documents: [
      { 
        originalname: "images.png", 
        buffer: Buffer.from("file content"),
        fieldname: 'documents',
        encoding: '7bit',
        mimetype: 'image/png'
      }
    ],
    profile: undefined // Add this to match the original implementation
  },
  user: { id: "user123", name: "Test User" },
};

const res = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn(),
};

describe("updateDoc", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should update doc detail", async () => {
    const uploadBlobMock = jest.fn();
    BlobHelper.mockImplementation(() => ({
      UploadBlob: uploadBlobMock,
    }));

    await updateDoc(req, res);

    // Check that UploadBlob was called with the correct parameters
    expect(uploadBlobMock).toHaveBeenCalledWith(
      req.files.documents[0], // file object
      process.env.PATIENT_BLOB_FOLDER, // path from environment variable
      "9DY8I12GMGSkEmi_zrjdd.png" // blob name
    );

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });
});