import axios from "axios";
import { sendReply } from "../helper/whatsapp/whatsapp.helper.js";
import { forgotPasswordOTP } from "../template/whatsapp/interactive.template.js";
import { sendMessageToWhatsAppAPI } from "../config/send.whatsapp.config.js";
import { templateSendOTP } from "../template/email/send.otp.js";
import { sendEmail } from "../config/send.email.config.js";


export const sendOTP = async (req, res) => {
   
        const data = req.body.data;
        var whatsappNumber = data.mobile;
        var email =data.email;

        var template = forgotPasswordOTP(whatsappNumber, data.otp);
        const response = await sendMessageToWhatsAppAPI(template);

        if(email!=null && email!="")
        {
           var emailHtml=templateSendOTP(data.otp);
          var result= await sendEmail([email],"Forgot Password OTP",emailHtml);
        }

        if (response.status == 200)
            res
                .json({ success: true })
                .status(200);
        else
            res
                .json({ success: false })
                .status(500);
   
}

export const send = async (req, res) => {
  

  const data = req.body.data;
  if (
    data.mobile != undefined &&
    data.message != undefined &&
    data.clientId != undefined
  ) {
    const messageData = {
      messaging_product: "whatsapp",
      recipient_type: "individual",
      to: data.mobile,
      type: "text",
      text: {
        preview_url: false,
        body: data.message,
      },
    };

    const response = await sendMessageToWhatsAppAPI(messageData);
    await saveMessage(data.message, data.clientId, data.tableName);
    res.json({ success: true }).status(200);
  } else {
    //   let id = req.Token;   const userId = new mongoose.Types.ObjectId(id);
    res.json({ success: false, error: "Invalid data" }).status(500);
  }
};


