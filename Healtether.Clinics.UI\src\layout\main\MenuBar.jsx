import defaultLogo from "assets/images/healtether-logo.png";
import { useSelector } from "react-redux";
import { Link, NavLink, useLocation } from "react-router-dom";
export default function MenuBar() {
    const location = useLocation();
    const { user } = useSelector((state) => state.user);
    let SuperAdmin = user.isSuperAdmin;
    let admin = user.isSuperAdmin;
    for (let index = 0; index < user.linkedClinics.length && !admin; index++) {
        const linkedClinic = user.linkedClinics[index];

        var isCurrent = linkedClinic?.clinic?._id == clinic._id;

        if (isCurrent) {
            admin = linkedClinic.isAdmin;
            break;
        }
    }
    const menuHtml = [];
    const menu = [
        {
            title: "Home",
            icon: "icon-[system-uicons--home] text-xl",
            cssclass: "block  py-3  transition duration-200 ",
            route: "dashboard",
            path: "dashboard",
            role: "All",
        },
        {
            title: "Appointments",
            icon: "icon-[solar--calendar-linear] text-xl",
            cssclass: "block py-3   transition duration-200 ",
            route: "appointment",
            path: "appointment",
            role: "All",
        },
        {
            title: "ABHA Queue",
            icon: "icon-[solar--calendar-linear] text-xl",
            cssclass: "block py-3   transition duration-200 ",
            route: "appointmentoverview",
            path: "appointmentoverview",
            role: "All",
        },
        {
            title: "WhatsApp Chat",
            icon: "icon-[ion--logo-whatsapp] text-xl",
            cssclass: "block py-3   transition duration-200 ",
            route: "chats",
            path: "chats",
            role: "All",
        },
        {
            title: "Patients Record",
            icon: "icon-[ooui--user-contributions-ltr] text-xl",
            cssclass: "block  py-3   transition duration-200 ",
            route: "patient/managepatient",
            path: "patient",
            role: "All",
        },
        {
            title: "Manage Staff",
            icon: "icon-[medical-icon--i-care-staff-area] text-xl ",
            cssclass: "block  py-3  transition duration-200  ",
            route: "staff/managestaffs",
            path: "staff",
            role: "Admin",
        },
        {
            title: "Payments",
            icon: "icon-[streamline--money-wallet-money-payment-finance-wallet] text-xl",
            cssclass: "block py-3  transition duration-200 ",
            route: "payments",
            path: "payments",
            role: "All",
        },
        {
            title: "Analytics",
            icon: "icon-[ion--trending-up-outline] text-2xl ",
            cssclass: "block py-3  transition duration-200 ",
            role: "Admin",
            route: "analytics",
            path: "analytics",
        },
        /*  {
                title: "Logout",
                icon: "icon-[ant-design--logout-outlined] text-2xl",
                cssclass: "block py-3  transition duration-200 ",
                route: "",
                role:"All"
            }*/
    ];


    const selectedMenuCss = "bg-backcolor_primary border-r-2 border-r-[#110C2C]";
    menu.forEach((item, i) => {
        const isActive = location.pathname.includes(item.path);

        if (
            (item.role == "Admin" && admin) ||
            item.role == "All" ||
            (item.role == "SuperAdmin" && SuperAdmin)
        )
            menuHtml.push(
                <li key={i} className="!rounded-none" >
                    <Link
                        to={item.route}
                        className={
                            item.cssclass +
                            ` hover:bg-backcolor_primary hover:border-r-2 hover:border-r-[#110C2C] !rounded-none   ${isActive ? selectedMenuCss : ""
                            } `
                        }
                        data-overlay="#healtether-menubar"

                    >
                        <div className="flex justify-start pl-5 content-center">
                            <span className={item.icon}></span>
                            <span
                                className={
                                    " ml-3  transition-all ease-in-out duration-300"
                                }
                            >
                                {item.title}
                            </span>
                        </div>
                    </Link>
                </li>
            );
    });
    return (

        <div id="healtether-menubar" className="overlay overlay-open:translate-x-0 drawer w-2xs drawer-start hidden overlay-backdrop-open:bg-color_dark/40" role="dialog" tabIndex="-1">
            <div className="drawer-header">
                <img
                    src={defaultLogo}
                    className="h-full max-h-[73px] p-3"
                    alt="main_logo"
                />
                <button type="button" className="btn btn-text btn-circle btn-sm absolute end-3 top-3" aria-label="Close" data-overlay="#healtether-menubar">
                    <span className="icon-[tabler--x] size-5"></span>
                </button>
            </div>
            <div className="!p-[0] drawer-body">
                <ul className="menu  p-0 font-primary gap-4">
                    {menuHtml}
                </ul>
            </div>
            <div className="drawer-footer">
                <button type="button" className="btn btn-soft btn-secondary" data-overlay="#healtether-menubar">Close</button>
            </div>
        </div>)

}