import { Router } from "express";
import {
  addChatMessageForClinic, addRecent,
  appointmentDataExchange,
  getRecentChatMessageForClinic, getRecentChatsForClinic, setReaded
} from "../controllers/whatsapp.chat.controller.js";
import { validateAddChatMessageForClinic, validateAddRecent, validateGetRecentChatMessageForClinic, validateGetRecentChatsForClinic, validateSetReaded } from "../validation/whatsappChat/chat.validation.js";

const whatsappChatRouter = Router();

/**
 * @swagger
 * /api/whatsappchat/addmessage:
 *   post:
 *     summary: Adds a chat message for a clinic
 *     description: This endpoint allows you to add a chat message for a specific clinic and processes the delivery of the message.
 *     tags:
 *       - WhatsApp Chat
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   message:
 *                     type: string
 *                     description: The message content to be sent.
 *                   clinicId:
 *                     type: string
 *                     description: The ID of the clinic associated with the message.
 *                   mobile:
 *                     type: string
 *                     description: The mobile number of the recipient.
 *             required:
 *               - data
 *     responses:
 *       200:
 *         description: Message successfully added and processed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: object
 *                       properties:
 *                         message:
 *                           type: string
 *                           description: The message that was saved.
 *                         isDelivered:
 *                           type: boolean
 *                           description: Indicates if the message was delivered.
 *                         isReceived:
 *                           type: boolean
 *                           description: Indicates if the message was received.
 *                         type:
 *                           type: string
 *                           description: The type of message (e.g., text).
 *                         createdOn:
 *                           type: string
 *                           format: date-time
 *                           description: The timestamp of when the message was created.
 *                     success:
 *                       type: boolean
 *                       description: Indicates if the operation was successful.
 *       400:
 *         description: Bad Request. Invalid input parameters.
 *       500:
 *         description: Internal Server Error. An error occurred while processing the request.
 */

whatsappChatRouter.route("/addmessage").post(validateAddChatMessageForClinic, async (req, res, next) => {
  try {
    return await addChatMessageForClinic(req, res);
  }
  catch (e) {
    next(e)
  }
});

/**
 * @swagger
 * /api/whatsappchat/getmessage:
 *   get:
 *     summary: Retrieve recent chat messages for a clinic
 *     description: This endpoint retrieves recent chat messages for a specified clinic based on mobile number and pagination.
 *     tags:
 *       - WhatsApp Chat
 *     parameters:
 *       - in: query
 *         name: mobile
 *         required: true
 *         schema:
 *           type: string
 *           description: The mobile number of the recipient.
 *       - in: query
 *         name: clinicId
 *         required: true
 *         schema:
 *           type: string
 *           description: The ID of the clinic.
 *       - in: query
 *         name: pg
 *         required: true
 *         schema:
 *           type: integer
 *           description: The page number for pagination.
 *       - in: query
 *         name: pgSize
 *         required: true
 *         schema:
 *           type: integer
 *           description: The number of messages to retrieve per page.
 *     responses:
 *       200:
 *         description: Successfully retrieved recent chat messages.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   message:
 *                     type: string
 *                     description: The content of the chat message.
 *                   createdOn:
 *                     type: string
 *                     format: date-time
 *                     description: The timestamp of when the message was created.
 *                   type:
 *                     type: string
 *                     description: The type of message (e.g., text).
 *                   isReceived:
 *                     type: boolean
 *                     description: Indicates if the message was received.
 *                   isDelivered:
 *                     type: boolean
 *                     description: Indicates if the message was delivered.
 *       400:
 *         description: Bad Request. Missing or invalid query parameters.
 *       404:
 *         description: Not Found. No messages found for the specified mobile and clinic ID.
 *       500:
 *         description: Internal Server Error. An error occurred while processing the request.
 */

whatsappChatRouter.route("/getmessage").get(validateGetRecentChatMessageForClinic, async (req, res, next) => {
  try {
    return await getRecentChatMessageForClinic(req, res);
  }
  catch (e) {
    next(e)
  }
});


/**
 * @swagger
 * /api/whatsappchat/addrecentchat:
 *   post:
 *     summary: Add a recent chat entry
 *     description: This endpoint adds a recent chat entry for a specified mobile number and clinic ID.
 *     tags:
 *       - WhatsApp Chat
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   mobile:
 *                     type: string
 *                     description: The mobile number of the patient.
 *                   clinicId:
 *                     type: string
 *                     description: The ID of the clinic.
 *     responses:
 *       200:
 *         description: Successfully added recent chat entry.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: string
 *                   example: success
 *       400:
 *         description: Bad Request. Missing or invalid request body parameters.
 *       404:
 *         description: Not Found. No patient found for the given mobile and clinic ID.
 *       500:
 *         description: Internal Server Error. An error occurred while processing the request.
 */
whatsappChatRouter.route("/addrecentchat").post(validateAddRecent, async (req, res, next) => {
  try {
    return await addRecent(req, res);
  }
  catch (e) {
    next(e)
  }
});

/**
 * @swagger
 * /api/whatsappchat/getrecentchat:
 *   get:
 *     summary: Get recent chats for a clinic
 *     description: This endpoint retrieves recent chat entries for a specified clinic ID.
 *     tags:
 *       - WhatsApp Chat
 *     parameters:
 *       - in: query
 *         name: clinicId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the clinic to fetch recent chats for.
 *     responses:
 *       200:
 *         description: Successfully retrieved recent chat entries.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   timeStamp:
 *                     type: string
 *                     format: date-time
 *                     description: The timestamp of the chat entry.
 *                   mobile:
 *                     type: string
 *                     description: The mobile number of the patient.
 *                   patientName:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         firstName:
 *                           type: string
 *                           description: The first name of the patient.
 *                         lastName:
 *                           type: string
 *                           description: The last name of the patient.
 *                   unReaded:
 *                     type: boolean
 *                     description: Indicates whether the chat is unread.
 *       400:
 *         description: Bad Request. Missing or invalid query parameters.
 *       404:
 *         description: Not Found. No recent chats found for the given clinic ID.
 *       500:
 *         description: Internal Server Error. An error occurred while processing the request.
 */

whatsappChatRouter.route("/getrecentchat").get(validateGetRecentChatsForClinic, async (req, res, next) => {
  try {
    return await getRecentChatsForClinic(req, res);
  }
  catch (e) {
    next(e)
  }
});

/**
 * @swagger
 * /api/whatsappchat/markasread:
 *   post:
 *     summary: Set chat as read for a clinic
 *     description: This endpoint marks the recent chat for a specified mobile number and clinic ID as read.
 *     tags:
 *       - WhatsApp Chat
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   mobile:
 *                     type: string
 *                     description: The mobile number of the patient.
 *                   clinicId:
 *                     type: string
 *                     description: The ID of the clinic associated with the chat.
 *     responses:
 *       200:
 *         description: Successfully marked the chat as read.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: string
 *                   example: success
 *       400:
 *         description: Bad Request. Missing or invalid request body.
 *       404:
 *         description: Not Found. No recent chat found for the given mobile and clinic ID.
 *       500:
 *         description: Internal Server Error. An error occurred while processing the request.
 */

whatsappChatRouter.route("/markasread").post(validateSetReaded, async (req, res, next) => {
  try {
    return await setReaded(req, res);
  }
  catch (e) {
    next(e)
  }
});

//add swagger
// dont validate 
whatsappChatRouter.route("/appointmentdataexchange").post(async (req, res, next) => {
  try {
    return await appointmentDataExchange(req, res);
  }
  catch (e) {
    next(e)
  }
});




export default whatsappChatRouter;
