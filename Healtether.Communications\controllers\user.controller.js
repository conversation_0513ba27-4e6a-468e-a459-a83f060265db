//import { getAllUserChat, saveUser } from "../helper/user/userHelper.js";

//get all users
export const addRecentUsers = async (req, res) => {
  const { mobile, name, email, whatsApp, profilePic, clientId, userId } =
    req.body;

  // await saveUser(userId,clientId,name,mobile,email,whatsApp,profilePic);

  res.status(200).json({
    success: true,
  });
};

//get all users
export const getRecentUsers = async (query, res) => {
  const data = query;
  const user = {}; // await getAllUserChat(data.query.clientId,data.query.size,data.query.pg);
  res.json(user).status(200);
};
