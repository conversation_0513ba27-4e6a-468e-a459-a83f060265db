import PrescriptionSidebar from "components/detail-page/write-prescription/SiderBar/PrescriptionSidebar.jsx";
import MedicalHistory from "pages/writePrescription/MedicalHistory.jsx";
import Symptoms from "./SymptomsDiagonsis";
import VitalsGeneralExamination from "./VitalsGeneralExamination";
import { useEffect, useState } from "react";
import {
  Form,
  redirect,
  useLoaderData,
  useLocation,
  useNavigation,
  useParams,
} from "react-router-dom";
import {
  GetPrescriptionByAppointmentId,
  UpdatePrescriptionApi,
  GetPrescriptionForReportByAppointmentId,
} from "services/appointment/writePrescription";
import {
  GetPreviousMedicalReportsByPatientId,
  GetFrequenttextForPrescription,
} from "../../services/appointment/writePrescription";
import Spinner from "../../components/loader/Spinner";
import AppointmentCard from "../../components/AppointmentCard";
import { GetRedirectUrlFromFormRequest } from "../../utils/CommonMethods";
import { BundleFHIR } from "../../services/appointment/appointment";
import { useReactToPrint } from "react-to-print";
import domtoimage from "dom-to-image-more";
import jsPDF from "jspdf";
import { SaveAsDraftApi } from "../../services/appointment/appointment";
import { alertBox } from "../../components/dialog/prompt";
import { PrescriptionModal } from "../../components/PrescriptionTemplate";
import { VaccinationCertificateModal } from "../../components/VaccinationTemplate";

export async function PrescriptionAction({ request, params }) {
  const formData = await request.formData();
  const updates = Object.fromEntries(
    Array.from(formData.keys()).map((key) => [
      key,
      formData.getAll(key).length > 1
        ? formData.getAll(key)
        : formData.get(key),
    ])
  );
  // validation
  await UpdatePrescriptionApi(
    params.id,
    params.patientId,
    params.appointmentId,
    { data: updates }
  );

  const redirectUrl = GetRedirectUrlFromFormRequest(request, "/appointment");
  return redirect(redirectUrl);
}

export async function PrescriptionLoader({ params }) {
  var prescriptionData = undefined;
  if (params?.appointmentId != undefined && params?.patientId != undefined) {
    prescriptionData = await GetPrescriptionByAppointmentId(
      params?.appointmentId
    );
    return { prescriptionData };
  }
  return { prescriptionData };
}

export default function WritePrescription() {
  const [frequentSearches, setFrequentSearches] = useState({});
  const navigation = useNavigation();
  const location = useLocation();
  const busy = navigation.state === "submitting";
  const { prescriptionData } = useLoaderData();
  const { appointmentId, patientId } = useParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCertificateModalOpen, setIsCertificateModalOpen] = useState(false);
  const [prescriptionDetails, setPrescriptionDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  let loadingHistory = false;
  const [medicalHistories, setMedicalHistories] = useState();
  const settingsArray = [
    "Sx, Dx & Prescription",
    "Vitals and General Examination",
    "Medical History",
  ];

  let tabId = 0;
  if (location.pathname.includes("vitals")) {
    tabId = 1;
  } else if (location.pathname.includes("medicalhistory")) {
    tabId = 2;
  }
  const [activeTab, setActiveTab] = useState(tabId);
  const handleTabClick = (tabNumber) => {
    setActiveTab(tabNumber);
  };

  useEffect(() => {
    if (!loadingHistory) {
      loadingHistory = true;
      const loadHistoryDetail = async () => {
        loadingHistory = true;
        const result = await GetPreviousMedicalReportsByPatientId(patientId);
        setMedicalHistories(result);
      };
      // frequent details
      const getFrequencyforPrescription = async () => {
        loadingHistory = true;
        const result = await GetFrequenttextForPrescription();
        setFrequentSearches(result);
      };
      getFrequencyforPrescription();
      loadHistoryDetail();
    }
    const checkPrescriptionStatus = async () => {
      if (prescriptionData?.prescriptionFinished) {
        setIsLoading(true);
        try {
          const prescriptionResult =
            await GetPrescriptionForReportByAppointmentId(appointmentId);
          setPrescriptionDetails(prescriptionResult);
          setIsModalOpen(true);
        } catch (error) {
          console.error("Error loading prescription details:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    checkPrescriptionStatus();
  }, [patientId]);

  const handlePrint = useReactToPrint({
    documentTitle: "Print This Document",
    onBeforePrint: () => console.log("before printing..."),
    onAfterPrint: () => console.log("after printing..."),
    removeAfterPrint: true,
  });

  const generatePDFFromElement = async (contentElement, filename) => {
    try {
      const timestamp = Date.now();
      const dataUrl = await domtoimage.toPng(contentElement);
      const img = new Image();
      img.src = dataUrl;

      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "px",
        format: [contentElement.offsetWidth, contentElement.offsetHeight],
      });

      pdf.addImage(
        dataUrl,
        "PNG",
        0,
        0,
        contentElement.offsetWidth,
        contentElement.offsetHeight
      );

      const pdfBlob = pdf.output("blob");
      const pdfFile = new File([pdfBlob], `${filename}_${timestamp}.pdf`, {
        type: "application/pdf",
      });

      return pdfFile;
    } catch (err) {
      console.error(`Error generating ${filename} PDF:`, err);
      throw err;
    }
  };

const handleFinishPrescription = async () => {
  try {
    setIsLoading(true);

    // Save the current form data with prescriptionFinished set to true
    const formElement = document.getElementById("prescriptionForm");
    if (formElement) {
      const formData = new FormData(formElement);

      // Update the prescription with the finished status
      await UpdatePrescriptionApi(
        patientId, // Assuming this is the id param
        patientId,
        appointmentId,
        { data: Object.fromEntries(formData) },
        false
      );
    }

    // Fetch the updated prescription details for the report
    const prescriptionResult = await GetPrescriptionForReportByAppointmentId(
      appointmentId
    );
    setPrescriptionDetails(prescriptionResult);
    
    if (prescriptionData) {
      setIsCertificateModalOpen(true);
    }

    // First show the modal with the prescription
    setIsModalOpen(true);
    
    // Wait for modal to render before generating PDFs
    setTimeout(async () => {
      try {
        // Get the content elements from the modals
        const prescriptionContentElement = document.querySelector(
          '[data-prescription-content="true"]'
        );

        const certificateContentElement = document.querySelector(
          '[data-certificate-content="true"]'
        );

        // Initialize arrays properly
        let prescriptionPDFs = [];
        let certificatePDFs = [];

        // Generate prescription PDF if element exists
        if (prescriptionContentElement) {
          try {
            const prescriptionPDFFile = await generatePDFFromElement(
              prescriptionContentElement,
              "prescription"
            );
            prescriptionPDFs.push(prescriptionPDFFile);
          } catch (error) {
            console.error("Error generating prescription PDF:", error);
          }
        }

        // Generate vaccination certificate PDF if element exists
        if (certificateContentElement) {
          try {
            const certificatePDFFile = await generatePDFFromElement(
              certificateContentElement,
              "vaccination_certificate"
            );
            certificatePDFs.push(certificatePDFFile);
          } catch (error) {
            console.error("Error generating certificate PDF:", error);
          }
        }

        // Save PDFs if any were generated successfully
        if (prescriptionPDFs.length > 0 || certificatePDFs.length > 0) {
          try {
            const response = await SaveAsDraftApi(
              [], // images
              [], // videos  
              [], // audios
              [], // documents
              appointmentId,
              prescriptionPDFs,
              [], // reports
              certificatePDFs
            );

            if (response.status !== 200) {
              throw new Error("Failed to save PDFs");
            }

            console.log("PDFs saved successfully:", response);
          } catch (saveError) {
            console.error("Error saving PDFs to server:", saveError);
            // Continue with FHIR bundling even if PDF save fails
          }
        }

        // Call FHIR bundling
        try {
          await bundleFHIR("op");
        } catch (fhirError) {
          console.error("Error with FHIR bundling:", fhirError);
        }

        // Show success message
        alertBox({
          show: true,
          title: "Success",
          confirmation: "Prescription has been finalized and saved successfully!",
        });

      } catch (error) {
        console.error("Error in PDF generation process:", error);
        
        // Still try to complete the FHIR bundling
        try {
          await bundleFHIR("op");
        } catch (fhirError) {
          console.error("Error with FHIR bundling:", fhirError);
        }
        
        // Show error message
        alertBox({
          show: true,
          title: "Warning",
          confirmation: "Prescription finalized but there was an issue saving PDFs.",
        });
      }
    }, 2000); // Increased timeout to give DOM more time to render

  } catch (error) {
    console.error("Error finishing prescription:", error);
    alertBox({
      show: true,
      title: "Error",
      confirmation: "Failed to finalize prescription. Please try again.",
    });
  } finally {
    setIsLoading(false);
  }
};

  // Add a new function to handle saving the PDF before FHIR bundling
  const handleSavePDFBeforeFHIR = async (contentElement) => {
    try {
      const timestamp = Date.now();
      const dataUrl = await domtoimage.toPng(contentElement);
      const img = new Image();
      img.src = dataUrl;

      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "px",
        format: [contentElement.offsetWidth, contentElement.offsetHeight],
      });

      pdf.addImage(
        dataUrl,
        "PNG",
        0,
        0,
        contentElement.offsetWidth,
        contentElement.offsetHeight
      );

      const pdfBlob = pdf.output("blob");
      const pdfFile = new File([pdfBlob], `prescription_${timestamp}.pdf`, {
        type: "application/pdf",
      });

      const response = await SaveAsDraftApi(
        [],
        [],
        [],
        [],
        appointmentId,
        [pdfFile],
        []
      );

      if (response.status !== 200) {
        throw new Error("Failed to save PDF");
      }

      return true;
    } catch (err) {
      console.error("Error saving PDF:", err);
      throw err;
    }
  };

  let bundleFHIR = async (type) => {
    const result = await BundleFHIR(patientId, appointmentId, type);
    console.log(result);
    return result;
  };

  return (
    <>
      {/* ///// appointment card /////// */}
      <AppointmentCard />
      <div className="flex flex-row items-center gap-3 border-b ">
        {settingsArray.map((data, index) => {
          return (
            <span
              className={
                (activeTab === index ? "bg-linear-to-t from-green-100 " : "") +
                " px-9  font-normal border rounded-lg py-1.5 bg-text_bg_primary cursor-pointer truncate"
              }
              key={index}
              placeholder={data}
              onClick={() => {
                handleTabClick(index);
                return index;
              }}
            >
              {data}
            </span>
          );
        })}
      </div>
      <div className="flex w-full mt-2">
        <div className="w-[80%]">
          <Form
            id="prescriptionForm"
            method="post"
            encType="multipart/form-data"
            noValidate
          >
            <div className={`${activeTab === 0 ? "" : "hidden"}`}>
              <Symptoms
                frequentSearches={frequentSearches}
                prescriptionData={prescriptionData}
              />
            </div>
            <div className={`${activeTab === 1 ? "" : "hidden"}`}>
              <VitalsGeneralExamination
                vitalsObj={prescriptionData?.vitals}
                medicalHistoryObj={medicalHistories}
              />
            </div>
            <div className={`${activeTab === 2 ? "" : "hidden"}`}>
              <MedicalHistory medicalHistoryObj={medicalHistories} />
            </div>
            <div className="flex justify-end gap-5 mb-2">
              <button
                type="submit"
                disabled={
                  busy || isLoading || prescriptionData.prescriptionFinished
                }
                className={`bg-Primary rounded-lg py-3 px-14 text-white font-semibold text-md ${
                  prescriptionData.prescriptionFinished
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              >
                Draft &nbsp; {busy ? <Spinner show={true} /> : <></>}
              </button>

              <button
                type="button"
                disabled={
                  busy || isLoading || prescriptionData.prescriptionFinished
                }
                className={`bg-Primary rounded-lg py-3 px-14 text-white font-semibold text-md relative ${
                  prescriptionData.prescriptionFinished
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
                onClick={handleFinishPrescription}
              >
                Finish Prescription &nbsp;
                {isLoading && <Spinner show={true} />}
              </button>
            </div>
          </Form>
        </div>
        <div className="w-[20%] px-4">
          <PrescriptionSidebar
            isLoading={false}
            vitals={prescriptionData?.vitals}
            medicalHistory={medicalHistories}
            medicalRecords={[
              ...(prescriptionData?.prescriptionRecords || []),
              ...(prescriptionData?.procedureRecords || []),
              ...(prescriptionData?.medicalRecords || []),
            ]}
          />
        </div>
      </div>

      {/* Prescription Modal */}
      <PrescriptionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        prescriptionDetails={prescriptionDetails}
        medicalHistoryDetails={medicalHistories}
        appointmentId={appointmentId}
        onPrint={(contentRef) => handlePrint(null, () => contentRef)}
        onSave={handleSavePDFBeforeFHIR}
      />

      <VaccinationCertificateModal
        isOpen={isCertificateModalOpen}
        onClose={() => setIsCertificateModalOpen(false)}
        certificateData={prescriptionDetails}
        onPrint={(contentRef) => handlePrint(null, () => contentRef)}
        onSave={handleSavePDFBeforeFHIR}
      />
    </>
  );
}
