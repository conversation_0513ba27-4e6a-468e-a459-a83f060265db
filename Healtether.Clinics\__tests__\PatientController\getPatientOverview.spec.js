import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");

mockPatientHelper();

const { getPatientOverview } = await import('../../controllers/patients/patients.controller.js'); 

const { overview }= await import('../../helpers/patient/patient.helper.js'); 

const mockPatientData = {
    _id: 'patient123',
    firstName: 'test',
    lastName: 'test',
    age: 20,
    birthday: '2003-12-22',
    gender: 'Male',
    mobile: "**********",
    email: "<EMAIL>",
    patientId: 'SD_165',
    address: {
        house: "123",
        street: "Main St",
        city: "Test City",
        pincode: "12345"
    },
    height: 170,
    weight: 70,
    documentType: "Passport",
    documentNumber: "A1234567",
    documents: []
};

const req = {
    query: {
        clientId: 'testClientId',
        page: 1,
        size: 10,
        keyword: 'test',
        sortby: 'firstName',
        direction: 'asc',
    },
};

const res = {
    json: jest.fn().mockReturnThis(),
    status: jest.fn().mockReturnThis(),
};

describe('getPatientOverview', () => {
    beforeEach(() => {
        jest.clearAllMocks(); 
    });

    it('should return patient data successfully', async () => {
        overview.mockResolvedValueOnce({ data: mockPatientData, totalCount: 1 });
        await getPatientOverview(req, res);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ data: mockPatientData, totalCount: 1 });
    });


});
