import { exec, spawn } from "child_process";
import fs from "fs/promises";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import portalDb from "../config/clinics.collections.config.js";
const AbhaConsentsModel = portalDb.model("abhaConsents");
const executeCommand = (command, cwd) => {
  return new Promise((resolve, reject) => {
    const process = spawn(command, { cwd, shell: true });

    let stdout = "";
    let stderr = "";

    process.stdout.on("data", (data) => {
      stdout += data.toString();
    });

    process.stderr.on("data", (data) => {
      stderr += data.toString();
    });

    process.on("close", (code) => {
      if (code !== 0) {
        // Return the error details if the command fails
        reject(new Error(`Command failed with code ${code}: ${stderr}`));
      } else {
        resolve(stdout.trim());
      }
    });
  });
};
// Function to generate key material (core logic)
export const generateKeyMaterialCore = async () => {
  const command = `"${process.cwd()}/fidelius-cli/bin/fidelius-cli.bat" gkm`;
  // console.log("key material: "+command);
  const output = await executeCommand(
    command,
    path.join(process.cwd(), "fidelius-cli", "bin")
  );
  console.log("output k m: " + output);
  return JSON.parse(output);
};

const deriveKeyToShare = (publicKey) => {
  const keyToShare = Buffer.from(publicKey, "utf-8").toString("base64");
  return keyToShare;
};

export const generateEncryptedkeyCore = async (
  data,
  requesterNonce,
  requesterPublicKey,
  privateKey,
  publicKey,
  nonce
) => {
  const command = `"${process.cwd()}/fidelius-cli/bin/fidelius-cli.bat" e ${data} ${nonce} ${requesterNonce} ${privateKey} ${requesterPublicKey}`;
  const output = await executeCommand(
    command,
    path.join(process.cwd(), "fidelius-cli", "bin")
  );

  const encryptionResult = JSON.parse(output);
  encryptionResult.keyToShare = deriveKeyToShare(publicKey);
  return encryptionResult;
};
export const generateEncryptedkeyCore2 = async (
  id,
  data,
  requesterNonce,
  requesterPublicKey,
  privateKey,
  publicKey,
  nonce
) => {
  const fhirString = JSON.stringify(data);

  const content = `encrypt\n${fhirString}\n${nonce}\n${requesterNonce}\n${privateKey}\n${requesterPublicKey}`;
  const rawId = id.includes('/')
  ? id.split('/').pop()
  : id;
  const filePath = path.join(
    process.cwd(),
    "encryptedFhir",
    `output-${rawId}.txt`
  );

  console.log(filePath);
  try {
    await fs.writeFile(filePath, content);
    console.log("Data successfully written to file!");
  } catch (error) {
    console.error("Error writing to file:", error);
  }
  const command = `"${process.cwd()}/fidelius-cli/bin/fidelius-cli.bat" -f "${process.cwd()}/encryptedFhir/output-${id}.txt" > "${process.cwd()}/encryptedFhir/encrypted_output-${id}.txt"`;
  console.log("Executing Command:", command);

  try {
    await executeCommand(
      command,
      path.join(process.cwd(), "fidelius-cli", "bin")
    );
  } catch (error) {
    console.error("Encryption failed:", error);
    throw new Error("Encryption failed");
  }

  const encryptedData = await fs.readFile(
    `${process.cwd()}/encryptedFhir/encrypted_output-${id}.txt`,
    "utf-8"
  );
  const jsonData = JSON.parse(encryptedData); // Convert string to JSON object

  const encrptedValue = jsonData.encryptedData;
  return encrptedValue;
};

export const cleanEncryptedFhir = async () => {
  const folderPath = path.join(process.cwd(), "encryptedFhir");

  try {
    const files = await fs.readdir(folderPath);
    await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(folderPath, file);
        const stat = await fs.stat(filePath);
        if (stat.isFile()) {
          await fs.unlink(filePath);
        }
      })
    );
    console.log("All files inside encryptedFhir folder deleted.");
  } catch (err) {
    console.error("Error cleaning encryptedFhir folder:", err);
  }
};

// API Handler to generate key material
export const generateKeyMaterial = async (req, res) => {
  try {
    const keyMaterial = await generateKeyMaterialCore();
    res.status(200).json({ success: true, data: keyMaterial });
  } catch (error) {
    res.status(500).json({ success: false, error });
  }
};

export const encryptData = async (req, res) => {
  try {
    const { privateKey, publicKey, nonce, x509PublicKey } =
      await generateKeyMaterialCore();

    console.log("keys", privateKey, publicKey, nonce, x509PublicKey);
    const { data, requesterNonce, requesterPublicKey } = req.body;

    const keyMaterial = await generateEncryptedkeyCore(
      data,
      requesterNonce,
      requesterPublicKey,
      privateKey,
      publicKey,
      nonce
    );
    res.status(200).json({ success: true, data: keyMaterial });
  } catch (error) {
    res.status(500).json({ success: false, error });
  }
};

// API Handler to decrypt data
export const decryptData = async (data, consentId) => {
  const {
    encryptedData,
    senderPublicKey,
    senderNonce,
    receiverNonce,
    receiverPrivateKey,
  } = data;

  try {
    const uniqueId = uuidv4(); // Generate a unique ID for the operation
    // Create unique filenames for each operation
    const filePath = path.join(
      process.cwd(),
      `decrypted_output_${uniqueId}.txt`
    );
    const outputPath = path.join(process.cwd(), `decpt_${uniqueId}.txt`);

    // Write the content to file
    const content = `decrypt\n${encryptedData}\n${senderNonce}\n${receiverNonce}\n${receiverPrivateKey}\n${senderPublicKey}`;
    await fs.writeFile(filePath, content);

    // Execute the decryption command with unique files
    const command = `"${process.cwd()}/fidelius-cli/bin/fidelius-cli.bat" -f "${filePath}" > "${outputPath}"`;
    const output = await executeCommand(
      command,
      path.join(process.cwd(), "fidelius-cli", "bin")
    );

    console.log("Decryption output:", output);

    // Read the decrypted data
    const decryptedData = await fs.readFile(outputPath, "utf-8");
    let dataObject;
    dataObject = typeof decryptedData === 'string' ? JSON.parse(decryptedData) : decryptedData;    
    const updatedConsent = await AbhaConsentsModel.findByIdAndUpdate(
      consentId,
      { $push: { grantedConsentData: dataObject.decryptedData || decryptedData } },
      { new: true }
    );
    // Clean up temporary files
    await fs
      .unlink(filePath)
      .catch((err) => console.warn(`Couldn't delete ${filePath}:`, err));
    await fs
      .unlink(outputPath)
      .catch((err) => console.warn(`Couldn't delete ${outputPath}:`, err));

    return decryptedData;
  } catch (error) {
    console.error("Decryption failed:", error);
    throw new Error("Decryption failed: " + error.message);
  }
};
