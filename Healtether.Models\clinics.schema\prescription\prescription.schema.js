import mongoose from "mongoose";
import { CLIENT_COLLECTION, PATIENT_COLLECTION, QUEUED_APPOINTMENT_COLLECTION } from "../../mongodb.collection.name.js";
const prescriptionSchema = new mongoose.Schema(
    {
        symptoms: [
            {
                name: String,
                duration: {
                    value: Number,
                    unit: String
                },
                notes: {
                    type: String,
                    maxlength: 1500
                },
            },
        ],
        diagnosis: [
            {
                name: String,
                notes: {
                    type: String,
                    maxlength: 1500
                },
            },
        ],
        labTests: [
            {
                name: String,
                repeat: Boolean,
                notes: {
                    type: String,
                    maxlength: 1500
                },
            },
        ],
        drugPrescriptions: [
            {
                isBeforeMeal: Boolean,
                drugName: String,
                dosage: String,
                frequency: String,
                duration: {
                    value: Number,
                    unit: String,
                },
                content: String,
                notes: String,
                occurrenceDateTime: Date,
                lotNumber: Number,
                conceptId: String,
            },
        ],
        patientAdvice: String,
        privateNotes: String,
        appointment: {
            type: mongoose.Schema.Types.ObjectId,
            ref: QUEUED_APPOINTMENT_COLLECTION
        },
        patient: {
            type: mongoose.Schema.Types.ObjectId,
            ref: PATIENT_COLLECTION
        },
        clinic: {
            type: mongoose.Schema.Types.ObjectId,
            ref: CLIENT_COLLECTION
        },
        deleted: {
            type: Boolean,
            default: false,
        },
        followUpDate:{
            type:Date
        },
        followUpTimeSlot:{
            type: String,
            maxLength: 50
        },
        created: {
            on: {
                type: Date,
                default: Date.now,
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255,
                },
            },
        },
        modified: {
            on: {
                type: Date,
                default: Date.now,
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255,
                },
            },
        },
    },
    {
        versionKey: "1.0",
    }
);

prescriptionSchema.index({ clinic: 1, patient: 1, appointment: 1 });



//const PatientPrescriptions = mongoose.model("prescriptions", prescriptionSchema);



const frequentSymptomsSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    count: {
        type: Number,
        required: true
    },
    clinic: {
        type: mongoose.Schema.Types.ObjectId,
        ref: CLIENT_COLLECTION
    },
},
    { versionKey: '1.2' });
frequentSymptomsSchema.index({ name: 1, clinic: 1 }, { unique: true });
//const FrequentSymptoms = mongoose.model("frequentSymptom", frequentSymptomsSchema);

const frequentDiagnosisSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    count: {
        type: Number,
        required: true
    },
    clinic: {
        type: mongoose.Schema.Types.ObjectId,
        ref: CLIENT_COLLECTION
    },
},
    { versionKey: '1.2' });
frequentDiagnosisSchema.index({ name: 1, clinic: 1 }, { unique: true });
//const FrequentDiagnosis = mongoose.model("frequentDiagnosis", frequentDiagnosisSchema);


const frequentLabtestSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    count: {
        type: Number,
        required: true
    },
    clinic: {
        type: mongoose.Schema.Types.ObjectId,
        ref: CLIENT_COLLECTION
    },
},
    { versionKey: '1.2' });
frequentLabtestSchema.index({ name: 1, clinic: 1 }, { unique: true });
//const FrequentLabtest = mongoose.model("frequentLabtest", frequentLabtestSchema);

const frequentDrugsSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    count: {
        type: Number,
        required: true
    },
    clinic: {
        type: mongoose.Schema.Types.ObjectId,
        ref: CLIENT_COLLECTION
    },
},
    { versionKey: '2.0' });
frequentDrugsSchema.index({ name: 1, clinic: 1 }, { unique: true });
//const FrequentDrugs = mongoose.model("frequentDrugs", frequentDrugsSchema);

export { prescriptionSchema, frequentSymptomsSchema , frequentDiagnosisSchema , frequentLabtestSchema , frequentDrugsSchema  };
