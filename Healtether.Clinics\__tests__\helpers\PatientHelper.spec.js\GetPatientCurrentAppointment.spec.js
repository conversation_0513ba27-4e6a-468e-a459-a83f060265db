import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { getPatientCurrentAppointment } from '../../../helpers/patient/patient.helper.js'; 
import { Patient } from '../../../model/clinics.model.js'; 
import { setup, teardown } from "../../../setup.js"; 
import { Appointment } from '../../../model/clinics.model.js';

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('getPatientCurrentAppointment function', () => {
  let patientId;
  let appointmentId;

  beforeEach(async () => {
    await Patient.deleteMany({}); 
    await Appointment.deleteMany({});
    
    const patient = new Patient({
      firstName: 'John',
      lastName: 'Doe',
      prefix: 'Mr.', // Added required prefix field
      mobile: '**********',
      patientId: 'PAT001',
      age: 30,
      birthday: new Date('1993-01-01'),
      gender: 'Male',
      email: '<EMAIL>',
      address: {
        house: "",
        street: "",
        city: "Test City",
        pincode: "123456"
      },
      height: 180,
      weight: 75,
      documentType: 'ID',
      documentNumber: 'ID123456',
      isDeleted: false, // Changed from 'deleted' to match your model
    });
    
    patientId = (await patient.save())._id;

    const appointment = new Appointment({
      mobile: '**********',
      name: 'John Doe',
      appointmentDate: new Date(),
      timeSlot: '10:00 AM',
      reason: 'Checkup',
      patientId: patientId,
    });
    
    appointmentId = (await appointment.save())._id;

    patient.appointments = [appointmentId];
    await patient.save();
  });

  afterEach(async () => {
    await Patient.deleteMany({}); 
    await Appointment.deleteMany({}); 
  });

  it('should return patient details along with current appointment', async () => {
    const result = await getPatientCurrentAppointment(patientId);
    
    expect(result).toBeDefined();
    expect(result).toHaveProperty('firstName', 'John');
    expect(result).toHaveProperty('lastName', 'Doe');
    expect(result).toHaveProperty('prefix', 'Mr.'); // Verify prefix
    expect(result).toHaveProperty('appointments');
    expect(result.appointments).toHaveLength(1); 
    expect(result.appointments[0]).toHaveProperty('timeSlot', '10:00 AM'); 
    expect(result.appointments[0]).toHaveProperty('reason', 'Checkup'); 
  });

  it('should return null when no patient is found', async () => {
    const nonExistentId = new mongoose.Types.ObjectId();
    const result = await getPatientCurrentAppointment(nonExistentId);
    
    expect(result).toBeNull(); 
  });
});