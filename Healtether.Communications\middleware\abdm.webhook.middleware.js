import jwt from "jsonwebtoken";
import dotenv from "dotenv";

dotenv.config();

export const abdmWebHook = async (req, res, next) => {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return res.status(401).json({ message: "Unauthorized: No token provided" });
    }

    const token = authHeader.split(" ")[1];
    const decoded = jwt.decode(token, { complete: true });

    // need to validated token properly..
    if (!decoded || !decoded.payload || !decoded.payload.iss || decoded.payload.iss !== process.env.ABDM_PAYLOAD_HEADER) {
        console.error("Invalid token issuer", decoded);
        return res.status(401).json({ message: "Invalid token issuer" });
    }

    next();
};
