export const Icons = {
  arrow: (props) => (
    <svg
      {...props}
      width="14"
      height="8"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.7072 0.636214C13.5197 0.448743 13.2654 0.343427 13.0002 0.343427C12.735 0.343427 12.4807 0.448743 12.2932 0.636214L7.34321 5.58621L2.39321 0.636214C2.20461 0.454056 1.95201 0.353261 1.68981 0.35554C1.42762 0.357818 1.1768 0.462987 0.991394 0.648395C0.805986 0.833803 0.700818 1.08462 0.69854 1.34681C0.696261 1.60901 0.797055 1.86161 0.979214 2.05021L6.63621 7.70721C6.82374 7.89468 7.07805 8 7.34321 8C7.60838 8 7.86269 7.89468 8.05021 7.70721L13.7072 2.05021C13.8947 1.86269 14 1.60838 14 1.34321C14 1.07805 13.8947 0.823741 13.7072 0.636214Z"
        fill="black"
      />
    </svg>
  ),
  profile: (props) => (
    <svg
      {...props}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.33398 12.9993C1.33398 12.1153 1.68517 11.2674 2.31029 10.6423C2.93542 10.0172 3.78326 9.66602 4.66732 9.66602H11.334C12.218 9.66602 13.0659 10.0172 13.691 10.6423C14.3161 11.2674 14.6673 12.1153 14.6673 12.9993C14.6673 13.4414 14.4917 13.8653 14.1792 14.1779C13.8666 14.4904 13.4427 14.666 13.0007 14.666H3.00065C2.55862 14.666 2.1347 14.4904 1.82214 14.1779C1.50958 13.8653 1.33398 13.4414 1.33398 12.9993Z"
        stroke="#D0F5F2"
        stroke-width="1.8"
        stroke-linejoin="round"
      />
      <path
        d="M8 6.33301C9.38071 6.33301 10.5 5.21372 10.5 3.83301C10.5 2.4523 9.38071 1.33301 8 1.33301C6.61929 1.33301 5.5 2.4523 5.5 3.83301C5.5 5.21372 6.61929 6.33301 8 6.33301Z"
        stroke="#D0F5F2"
        stroke-width="1.8"
      />
    </svg>
  ),
  delete: (props) => (
    <svg
      {...props}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.64014 6.34961H7.77396V14.965H6.64014V6.34961Z"
        fill="#0C091F"
      />
      <path
        d="M8.90771 6.34961H10.0415V14.965H8.90771V6.34961Z"
        fill="#0C091F"
      />
      <path
        d="M11.1748 6.34961H12.3086V14.965H11.1748V6.34961Z"
        fill="#0C091F"
      />
      <path
        d="M2.10547 3.11719H16.8452V4.19411H2.10547V3.11719Z"
        fill="#0C091F"
      />
      <path
        d="M12.2732 3.65641H11.215V2.57949C11.215 2.25641 10.9504 2.00513 10.6103 2.00513H8.34263C8.00248 2.00513 7.73792 2.25641 7.73792 2.57949V3.65641H6.67969V2.57949C6.67969 1.71795 7.43557 1 8.34263 1H10.6103C11.5173 1 12.2732 1.71795 12.2732 2.57949V3.65641Z"
        fill="#0C091F"
      />
      <path
        d="M12.8787 18.1954H6.07577C5.16871 18.1954 4.37503 17.4775 4.29945 16.616L3.24121 3.69289L4.37503 3.62109L5.43327 16.5442C5.47106 16.8672 5.77341 17.1185 6.07577 17.1185H12.8787C13.2189 17.1185 13.5212 16.8314 13.5212 16.5442L14.5794 3.62109L15.7133 3.69289L14.655 16.616C14.5794 17.5134 13.7858 18.1954 12.8787 18.1954Z"
        fill="#0C091F"
      />
    </svg>
  ),
  file: (props) => (
    <svg
      {...props}
      width="24"
      height="22"
      viewBox="0 0 24 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.22417 12.977C8.22417 13.6296 8.4834 14.2554 8.94484 14.7169C9.40628 15.1783 10.0321 15.4375 10.6847 15.4375C11.3373 15.4375 11.9631 15.1783 12.4246 14.7169C12.886 14.2554 13.1452 13.6296 13.1452 12.977C13.1452 12.3244 12.886 11.6986 12.4246 11.2372C11.9631 10.7757 11.3373 10.5165 10.6847 10.5165C10.0321 10.5165 9.40628 10.7757 8.94484 11.2372C8.4834 11.6986 8.22417 12.3244 8.22417 12.977ZM17.2461 18.382V8.87613L12.325 3.95508H5.76364C5.32859 3.95508 4.91136 4.1279 4.60374 4.43553C4.29611 4.74315 4.12329 5.16038 4.12329 5.59543V18.7182C4.12329 19.1533 4.29611 19.5705 4.60374 19.8781C4.91136 20.1858 5.32859 20.3586 5.76364 20.3586H15.6057C15.9748 20.3586 16.3029 20.2356 16.5818 20.0305L12.9484 16.3971C12.2922 16.8236 11.5049 17.0779 10.6847 17.0779C9.59707 17.0779 8.554 16.6458 7.78494 15.8768C7.01587 15.1077 6.58382 14.0646 6.58382 12.977C6.58382 11.8894 7.01587 10.8463 7.78494 10.0772C8.554 9.30819 9.59707 8.87613 10.6847 8.87613C11.7723 8.87613 12.8154 9.30819 13.5845 10.0772C14.3535 10.8463 14.7856 11.8894 14.7856 12.977C14.7856 13.7972 14.5313 14.5846 14.1048 15.2325L17.2461 18.382Z"
        fill="#3E3E3E"
      />
    </svg>
  ),
  address: (props) => (
    <svg
      {...props}
      width="20"
      height="13"
      viewBox="0 0 20 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.13829 8.51322C7.45223 4.68533 8.85173 2.02259 9.97032 0.897955C11.2833 2.72893 11.6011 4.33838 11.4863 5.6043C11.3622 6.97316 10.729 7.98375 10.2102 8.48423L9.55336 9.03863L9.13829 8.51322Z"
        fill="white"
        stroke="#353535"
      />
      <path
        d="M3.47756 3.56508C5.68999 3.99115 7.0276 4.94092 7.81498 5.9388C8.67216 7.02512 8.90497 8.20278 8.86874 8.92166L8.86267 9.04221L8.91226 9.15225L9.16395 9.71073L8.08955 9.65621C4.2515 8.03543 3.43914 5.14784 3.47756 3.56508Z"
        fill="white"
        stroke="#353535"
      />
      <path
        d="M16.287 3.71066C15.9808 5.94285 15.1045 7.3297 14.1505 8.16971C13.112 9.08417 11.9486 9.3801 11.2288 9.38266L11.201 9.38275L11.1733 9.38595L10.3143 9.4851L10.4399 8.68176C11.8468 4.73057 14.6995 3.75775 16.287 3.71066Z"
        fill="white"
        stroke="#353535"
      />
      <path
        d="M0.733623 10.3391C2.50397 8.94543 4.09757 8.55594 5.36736 8.61389C6.7497 8.67699 7.79238 9.27188 8.3121 9.76983L8.34395 9.80035L8.38066 9.82482L9.19748 10.3694L8.42282 10.8072C4.65143 12.6951 1.91184 11.4104 0.733623 10.3391Z"
        fill="white"
        stroke="#353535"
      />
      <path
        d="M19.0874 10.0698C17.3298 11.4796 15.7399 11.8836 14.4696 11.8373C13.0867 11.7868 12.0387 11.2014 11.5144 10.7082L11.5039 10.6984L11.4929 10.6891L10.7702 10.0838L11.407 9.66542C15.1545 7.75251 17.9008 9.01053 19.0874 10.0698Z"
        fill="white"
        stroke="#353535"
      />
      <path
        d="M9.78193 11.6381C10.559 11.6381 11.1889 11.0082 11.1889 10.2311C11.1889 9.45412 10.559 8.82422 9.78193 8.82422C9.0049 8.82422 8.375 9.45412 8.375 10.2311C8.375 11.0082 9.0049 11.6381 9.78193 11.6381Z"
        fill="white"
        stroke="#353535"
      />
    </svg>
  ),
  success_check: (props) => (
    <svg
      {...props}
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.3124 9.93731C16.6874 13.0623 14.3312 16.0048 11.0249 16.6623C9.4124 16.9834 7.73968 16.7876 6.24492 16.1028C4.75016 15.418 3.50956 14.279 2.69978 12.848C1.89 11.4171 1.55231 9.76717 1.7348 8.13316C1.91729 6.49914 2.61065 4.96434 3.71616 3.74731C5.98366 1.24981 9.81241 0.562313 12.9374 1.81231"
        stroke="#52CFAC"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.6875 8.6875L9.8125 11.8125L17.3125 3.6875"
        stroke="#52CFAC"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  ),
  phone: (props) => (
    <svg
      {...props}
      width="10"
      height="16"
      viewBox="0 0 10 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.33203 0.708984H1.66536C0.865365 0.708984 0.207031 1.36732 0.207031 2.16732V13.834C0.207031 14.634 0.865365 15.2923 1.66536 15.2923H8.33203C9.13203 15.2923 9.79036 14.634 9.79036 13.834V2.16732C9.79036 1.36732 9.13203 0.708984 8.33203 0.708984ZM8.54036 13.834C8.54036 13.9507 8.4487 14.0423 8.33203 14.0423H1.66536C1.5487 14.0423 1.45703 13.9507 1.45703 13.834V2.16732C1.45703 2.05065 1.5487 1.95898 1.66536 1.95898H8.33203C8.4487 1.95898 8.54036 2.05065 8.54036 2.16732V13.834ZM5.83203 11.334C5.83203 11.7923 5.45703 12.1673 4.9987 12.1673C4.54036 12.1673 4.16536 11.7923 4.16536 11.334C4.16536 10.8757 4.54036 10.5007 4.9987 10.5007C5.45703 10.5007 5.83203 10.8757 5.83203 11.334Z"
        fill="black"
      />
    </svg>
  ),
  abha_num: (props) => (
    <svg
      {...props}
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.6"
        y="0.6"
        width="12.8"
        height="12.8"
        rx="6.4"
        stroke="black"
        stroke-width="1.2"
      />
      <rect
        x="4.1"
        y="4.1"
        width="5.8"
        height="5.8"
        rx="2.9"
        stroke="black"
        stroke-width="1.2"
      />
    </svg>
  ),
  check_out: (props) => (
    <svg
      {...props}
      width="17"
      height="18"
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.55209 6.41633C1.74991 6.51458 1.97865 6.53026 2.18804 6.45995C2.39742 6.38963 2.57032 6.23907 2.66876 6.04133C3.1962 4.97616 3.99884 4.07137 4.99352 3.42068C5.98821 2.77 7.13877 2.39708 8.32603 2.34055C9.5133 2.28402 10.6941 2.54594 11.7461 3.09918C12.7981 3.65241 13.6831 4.47685 14.3093 5.48709C14.9356 6.49734 15.2804 7.65664 15.308 8.84493C15.3356 10.0332 15.0449 11.2073 14.4662 12.2455C13.8875 13.2837 13.0418 14.1483 12.0166 14.7498C10.9914 15.3513 9.82403 15.6677 8.63542 15.6663C7.39282 15.6717 6.17388 15.3266 5.1185 14.6707C4.06311 14.0148 3.21405 13.0746 2.66876 11.958C2.5693 11.7591 2.3949 11.6078 2.18392 11.5375C1.97294 11.4672 1.74267 11.4835 1.54376 11.583C1.34484 11.6825 1.19359 11.8569 1.12326 12.0678C1.05293 12.2788 1.0693 12.5091 1.16876 12.708C2.00492 14.3907 3.38523 15.7415 5.08565 16.541C6.78608 17.3405 8.70681 17.5419 10.5361 17.1124C12.3654 16.683 13.9958 15.6479 15.1628 14.1752C16.3298 12.7025 16.9648 10.8787 16.9648 8.99967C16.9648 7.12065 16.3298 5.29679 15.1628 3.8241C13.9958 2.35141 12.3654 1.31634 10.5361 0.886896C8.70681 0.457449 6.78608 0.658833 5.08565 1.45836C3.38523 2.25788 2.00492 3.60862 1.16876 5.29133C1.1191 5.39019 1.08962 5.49795 1.08204 5.60832C1.07446 5.71869 1.08893 5.82946 1.12461 5.93418C1.16029 6.0389 1.21647 6.13547 1.28986 6.21825C1.36326 6.30103 1.4524 6.36837 1.55209 6.41633ZM1.13542 9.833L9.12709 9.833L7.21042 11.7413C7.13272 11.819 7.07109 11.9113 7.02904 12.0128C6.98699 12.1143 6.96535 12.2231 6.96535 12.333C6.96535 12.4429 6.98699 12.5517 7.02904 12.6532C7.07109 12.7547 7.13272 12.847 7.21042 12.9247C7.28812 13.0024 7.38036 13.064 7.48188 13.1061C7.5834 13.1481 7.69221 13.1697 7.80209 13.1697C7.91197 13.1697 8.02078 13.1481 8.1223 13.1061C8.22381 13.064 8.31606 13.0024 8.39376 12.9247L11.7271 9.59133C11.803 9.51208 11.8624 9.41863 11.9021 9.31633C11.9854 9.11345 11.9854 8.88588 11.9021 8.683C11.8624 8.58071 11.803 8.48725 11.7271 8.408L8.39376 5.07467C8.31629 4.99656 8.22412 4.93456 8.12257 4.89226C8.02102 4.84995 7.9121 4.82817 7.80209 4.82817C7.69208 4.82817 7.58316 4.84995 7.48161 4.89226C7.38006 4.93456 7.28789 4.99656 7.21042 5.07467C7.13232 5.15214 7.07032 5.2443 7.02801 5.34585C6.98571 5.4474 6.96392 5.55632 6.96392 5.66633C6.96392 5.77634 6.98571 5.88526 7.02801 5.98681C7.07032 6.08836 7.13232 6.18053 7.21042 6.258L9.12709 8.16633H1.13542C0.91441 8.16633 0.702446 8.25413 0.546165 8.41041C0.389885 8.56669 0.30209 8.77865 0.30209 8.99967C0.30209 9.22068 0.389885 9.43264 0.546165 9.58892C0.702446 9.7452 0.91441 9.833 1.13542 9.833Z"
        fill={props.color || "black"}
      />
    </svg>
  ),
  no_show: (props) => (
    <svg
      {...props}
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.2845 17.1589C13.8845 17.1589 17.6178 13.4255 17.6178 8.82552C17.6178 4.22552 13.8845 0.492188 9.2845 0.492188C4.68451 0.492188 0.951172 4.22552 0.951172 8.82552C0.951172 13.4255 4.68451 17.1589 9.2845 17.1589ZM9.2845 2.15885C12.9678 2.15885 15.9512 5.14219 15.9512 8.82552C15.9512 10.3672 15.4262 11.7839 14.5428 12.9089L5.20117 3.56719C6.36516 2.65188 7.80374 2.15571 9.2845 2.15885ZM4.02617 4.74219L13.3678 14.0839C12.2038 14.9992 10.7653 15.4953 9.2845 15.4922C5.60117 15.4922 2.61784 12.5089 2.61784 8.82552C2.61784 7.28385 3.14284 5.86719 4.02617 4.74219Z"
        fill="black"
      />
    </svg>
  ),
  cancel: (props) => (
    <svg
      {...props}
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.80599 10.2402L11.2227 12.6569C11.3754 12.8097 11.5699 12.8861 11.806 12.8861C12.0421 12.8861 12.2365 12.8097 12.3893 12.6569C12.5421 12.5041 12.6185 12.3097 12.6185 12.0736C12.6185 11.8375 12.5421 11.643 12.3893 11.4902L9.97266 9.07357L12.3893 6.6569C12.5421 6.50412 12.6185 6.30968 12.6185 6.07357C12.6185 5.83746 12.5421 5.64301 12.3893 5.49023C12.2365 5.33746 12.0421 5.26107 11.806 5.26107C11.5699 5.26107 11.3754 5.33746 11.2227 5.49023L8.80599 7.9069L6.38932 5.49023C6.23655 5.33746 6.0421 5.26107 5.80599 5.26107C5.56988 5.26107 5.37543 5.33746 5.22266 5.49023C5.06988 5.64301 4.99349 5.83746 4.99349 6.07357C4.99349 6.30968 5.06988 6.50412 5.22266 6.6569L7.63932 9.07357L5.22266 11.4902C5.06988 11.643 4.99349 11.8375 4.99349 12.0736C4.99349 12.3097 5.06988 12.5041 5.22266 12.6569C5.37543 12.8097 5.56988 12.8861 5.80599 12.8861C6.0421 12.8861 6.23655 12.8097 6.38932 12.6569L8.80599 10.2402ZM8.80599 17.4069C7.65321 17.4069 6.56988 17.188 5.55599 16.7502C4.5421 16.3125 3.66016 15.7188 2.91016 14.9694C2.16016 14.22 1.56655 13.338 1.12932 12.3236C0.692102 11.3091 0.473213 10.2258 0.472657 9.07357C0.472102 7.92135 0.690991 6.83801 1.12932 5.82357C1.56766 4.80912 2.16127 3.92718 2.91016 3.17773C3.65905 2.42829 4.54099 1.83468 5.55599 1.3969C6.57099 0.959123 7.65432 0.740234 8.80599 0.740234C9.95766 0.740234 11.041 0.959123 12.056 1.3969C13.071 1.83468 13.9529 2.42829 14.7018 3.17773C15.4507 3.92718 16.0446 4.80912 16.4835 5.82357C16.9224 6.83801 17.141 7.92135 17.1393 9.07357C17.1377 10.2258 16.9188 11.3091 16.4827 12.3236C16.0465 13.338 15.4529 14.22 14.7018 14.9694C13.9507 15.7188 13.0688 16.3127 12.056 16.7511C11.0432 17.1894 9.95988 17.408 8.80599 17.4069ZM8.80599 15.7402C10.6671 15.7402 12.2435 15.0944 13.5352 13.8027C14.8268 12.5111 15.4727 10.9347 15.4727 9.07357C15.4727 7.21246 14.8268 5.63607 13.5352 4.3444C12.2435 3.05273 10.6671 2.4069 8.80599 2.4069C6.94488 2.4069 5.36849 3.05273 4.07682 4.3444C2.78516 5.63607 2.13932 7.21246 2.13932 9.07357C2.13932 10.9347 2.78516 12.5111 4.07682 13.8027C5.36849 15.0944 6.94488 15.7402 8.80599 15.7402Z"
        fill={props.color || "black"}
      />
    </svg>
  ),
  cancel_open: (props) => (
    <svg
      {...props}
      width="8"
      height="8"
      viewBox="0 0 8 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 7L4 4L7 7M7 1L3.99943 4L1 1"
        stroke={props.color?props.color:`#413D56`}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  ),
  reschedule: (props) => (
    <svg
      {...props}
      width="16"
      height="18"
      viewBox="0 0 16 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.1615 2.57292H13.3281V0.90625H11.6615V2.57292H4.99479V0.90625H3.32812V2.57292H2.49479C1.56979 2.57292 0.828125 3.31458 0.828125 4.23958V15.9062C0.828125 16.3483 1.00372 16.7722 1.31628 17.0848C1.62884 17.3973 2.05276 17.5729 2.49479 17.5729H14.1615C15.0781 17.5729 15.8281 16.8229 15.8281 15.9062V4.23958C15.8281 3.79756 15.6525 3.37363 15.34 3.06107C15.0274 2.74851 14.6035 2.57292 14.1615 2.57292ZM14.1615 15.9062H2.49479V6.73958H14.1615V15.9062ZM8.32812 8.40625V10.0729H11.6615V12.5729H8.32812V14.2396L4.99479 11.3229L8.32812 8.40625Z"
        fill={props.color?props.color:`#413D56`}
      />
    </svg>
  ),
  check_in: (props) => (
    <svg
      {...props}
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.8463 11.4089C15.6485 11.3106 15.4198 11.2949 15.2104 11.3652C15.001 11.4356 14.8281 11.5861 14.7297 11.7839C14.2022 12.849 13.3996 13.7538 12.4049 14.4045C11.4102 15.0552 10.2597 15.4281 9.07241 15.4846C7.88514 15.5412 6.70435 15.2793 5.65235 14.726C4.60034 14.1728 3.71537 13.3483 3.08911 12.3381C2.46285 11.3279 2.11807 10.1686 2.09048 8.98027C2.06289 7.79198 2.35349 6.61792 2.9322 5.5797C3.5109 4.54148 4.35665 3.67686 5.38185 3.0754C6.40705 2.47393 7.57441 2.15749 8.76301 2.15886C10.0056 2.15348 11.2246 2.49856 12.2799 3.15449C13.3353 3.81042 14.1844 4.75062 14.7297 5.86719C14.8291 6.06611 15.0035 6.21736 15.2145 6.28769C15.4255 6.35802 15.6558 6.34165 15.8547 6.24219C16.0536 6.14274 16.2049 5.96834 16.2752 5.75736C16.3455 5.54638 16.3291 5.31611 16.2297 5.1172C15.3935 3.43448 14.0132 2.08374 12.3128 1.28422C10.6124 0.484694 8.69163 0.283312 6.86235 0.712758C5.03307 1.14221 3.40262 2.17727 2.23562 3.64996C1.06863 5.12265 0.433594 6.94652 0.433594 8.82553C0.433594 10.7045 1.06863 12.5284 2.23562 14.0011C3.40262 15.4738 5.03307 16.5089 6.86235 16.9383C8.69163 17.3677 10.6124 17.1664 12.3128 16.3668C14.0132 15.5673 15.3935 14.2166 16.2297 12.5339C16.2793 12.435 16.3088 12.3272 16.3164 12.2169C16.324 12.1065 16.3095 11.9957 16.2738 11.891C16.2381 11.7863 16.182 11.6897 16.1086 11.6069C16.0352 11.5242 15.946 11.4568 15.8463 11.4089ZM16.263 7.9922H8.27135L10.188 6.08386C10.2657 6.00616 10.3273 5.91392 10.3694 5.8124C10.4114 5.71088 10.4331 5.60208 10.4331 5.4922C10.4331 5.38231 10.4114 5.27351 10.3694 5.17199C10.3273 5.07047 10.2657 4.97823 10.188 4.90053C10.1103 4.82283 10.0181 4.7612 9.91656 4.71915C9.81504 4.67709 9.70623 4.65545 9.59635 4.65545C9.48647 4.65545 9.37766 4.67709 9.27614 4.71915C9.17462 4.7612 9.08238 4.82283 9.00468 4.90053L5.67135 8.23386C5.59548 8.31311 5.53601 8.40657 5.49635 8.50886C5.413 8.71175 5.413 8.93931 5.49635 9.14219C5.53601 9.24449 5.59548 9.33794 5.67135 9.4172L9.00468 12.7505C9.08215 12.8286 9.17432 12.8906 9.27587 12.9329C9.37742 12.9752 9.48634 12.997 9.59635 12.997C9.70636 12.997 9.81528 12.9752 9.91683 12.9329C10.0184 12.8906 10.1105 12.8286 10.188 12.7505C10.2661 12.6731 10.3281 12.5809 10.3704 12.4793C10.4127 12.3778 10.4345 12.2689 10.4345 12.1589C10.4345 12.0489 10.4127 11.9399 10.3704 11.8384C10.3281 11.7368 10.2661 11.6447 10.188 11.5672L8.27135 9.65886H16.263C16.484 9.65886 16.696 9.57106 16.8523 9.41478C17.0086 9.2585 17.0963 9.04654 17.0963 8.82553C17.0963 8.60452 17.0086 8.39255 16.8523 8.23627C16.696 8.07999 16.484 7.9922 16.263 7.9922Z"
        fill={props.color?props.color:`#413D56`}
      />
    </svg>
  ),
  write_x: (props) => (
    <svg
      {...props}
      width="13"
      height="16"
      viewBox="0 0 13 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.918 12.6875L12.443 11.1633C12.6191 10.9872 12.718 10.7483 12.718 10.4992C12.718 10.2502 12.6191 10.0113 12.443 9.83516C12.2668 9.65904 12.028 9.5601 11.7789 9.5601C11.5298 9.5601 11.291 9.65904 11.1148 9.83516L9.58984 11.3594L7.00625 8.78126C8.02812 8.50058 8.91338 7.85855 9.49759 6.97442C10.0818 6.0903 10.3253 5.02417 10.1827 3.97409C10.0402 2.924 9.5214 1.96134 8.72266 1.26492C7.92392 0.568496 6.89955 0.185654 5.83984 0.187507H1.77734C1.5287 0.187507 1.29025 0.286279 1.11443 0.462094C0.938616 0.637909 0.839844 0.876366 0.839844 1.12501V13C0.839844 13.2486 0.938616 13.4871 1.11443 13.6629C1.29025 13.8387 1.5287 13.9375 1.77734 13.9375C2.02598 13.9375 2.26444 13.8387 2.44026 13.6629C2.61607 13.4871 2.71484 13.2486 2.71484 13V8.93751H4.51172L8.26172 12.6875L6.73672 14.2117C6.64951 14.2989 6.58034 14.4025 6.53314 14.5164C6.48595 14.6303 6.46165 14.7525 6.46165 14.8758C6.46165 14.9991 6.48595 15.1212 6.53314 15.2352C6.58034 15.3491 6.64951 15.4526 6.73672 15.5399C6.91284 15.716 7.15171 15.8149 7.40078 15.8149C7.52411 15.8149 7.64623 15.7906 7.76017 15.7434C7.87411 15.6962 7.97764 15.6271 8.06484 15.5399L9.58984 14.0156L11.1141 15.5406C11.2902 15.7168 11.5291 15.8157 11.7781 15.8157C12.0272 15.8157 12.2661 15.7168 12.4422 15.5406C12.6183 15.3645 12.7173 15.1256 12.7173 14.8766C12.7173 14.6275 12.6183 14.3886 12.4422 14.2125L10.918 12.6875ZM2.71484 2.06251H5.83984C6.50288 2.06251 7.13877 2.3259 7.60761 2.79474C8.07645 3.26358 8.33984 3.89947 8.33984 4.56251C8.33984 5.22555 8.07645 5.86143 7.60761 6.33027C7.13877 6.79911 6.50288 7.06251 5.83984 7.06251H2.71484V2.06251Z"
        fill="#413D56"
      />
    </svg>
  ),
  vitals: (props) => (
    <svg
      {...props}
      width="17"
      height="18"
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.9723 1.8H11.2103C10.8323 0.756 9.84227 0 8.67227 0C7.50227 0 6.51227 0.756 6.13427 1.8H2.37227C1.89488 1.8 1.43704 1.98964 1.09947 2.32721C0.761908 2.66477 0.572266 3.12261 0.572266 3.6V16.2C0.572266 16.6774 0.761908 17.1352 1.09947 17.4728C1.43704 17.8104 1.89488 18 2.37227 18H14.9723C15.4497 18 15.9075 17.8104 16.2451 17.4728C16.5826 17.1352 16.7723 16.6774 16.7723 16.2V3.6C16.7723 3.12261 16.5826 2.66477 16.2451 2.32721C15.9075 1.98964 15.4497 1.8 14.9723 1.8ZM8.67227 1.8C8.91096 1.8 9.13988 1.89482 9.30866 2.0636C9.47744 2.23239 9.57227 2.46131 9.57227 2.7C9.57227 2.93869 9.47744 3.16761 9.30866 3.3364C9.13988 3.50518 8.91096 3.6 8.67227 3.6C8.43357 3.6 8.20465 3.50518 8.03587 3.3364C7.86709 3.16761 7.77227 2.93869 7.77227 2.7C7.77227 2.46131 7.86709 2.23239 8.03587 2.0636C8.20465 1.89482 8.43357 1.8 8.67227 1.8ZM2.37227 12.6H5.17127L6.53027 10.035L7.21427 15.228L10.5353 10.989L12.1733 12.6H14.9723V16.2H2.37227V12.6ZM14.9723 11.214H12.7493L10.4093 8.874L8.16827 11.745L7.32227 5.472L4.32527 11.214H2.37227V3.6H4.17227V4.5H13.1723V3.6H14.9723V11.214Z"
        fill="black"
      />
    </svg>
  ),
  date: (props) => (
    <svg
      {...props}
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.05556 4.51476V2.07031M9.94444 4.51476V2.07031M4.44444 6.9592H10.5556M3.22222 13.0703H11.7778C12.1019 13.0703 12.4128 12.9415 12.642 12.7123C12.8712 12.4831 13 12.1722 13 11.8481V4.51476C13 4.1906 12.8712 3.87973 12.642 3.65052C12.4128 3.4213 12.1019 3.29253 11.7778 3.29253H3.22222C2.89807 3.29253 2.58719 3.4213 2.35798 3.65052C2.12877 3.87973 2 4.1906 2 4.51476V11.8481C2 12.1722 2.12877 12.4831 2.35798 12.7123C2.58719 12.9415 2.89807 13.0703 3.22222 13.0703Z"
        stroke="#888888"
        stroke-width="1.3"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  ),
  receipt: (props) => (
    <svg
      {...props}
      width="14"
      height="18"
      viewBox="0 0 14 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.69857 4.83333H9.69857M4.69857 8.16667H9.69857M8.0319 11.5H9.69857M1.36523 16.5V3.16667C1.36523 2.72464 1.54083 2.30072 1.85339 1.98816C2.16595 1.67559 2.58987 1.5 3.0319 1.5H11.3652C11.8073 1.5 12.2312 1.67559 12.5437 1.98816C12.8563 2.30072 13.0319 2.72464 13.0319 3.16667V16.5L10.5319 14.8333L8.86523 16.5L7.19857 14.8333L5.5319 16.5L3.86523 14.8333L1.36523 16.5Z"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  ),
  whatsapp: (props) => (
    <svg
      {...props}
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M15.5617 2.79301C14.7528 1.98366 13.7917 1.34231 12.7339 0.905881C11.6761 0.469451 10.5424 0.24656 9.39805 0.25004C4.5957 0.25004 0.686328 4.13988 0.684375 8.92192C0.682251 10.4446 1.08363 11.9407 1.84766 13.2579L0.611328 17.75L5.23047 16.5442C6.50907 17.2369 7.94036 17.5994 9.39453 17.5989H9.39805C14.2 17.5989 18.109 13.7086 18.1113 8.92699C18.1142 7.78659 17.8903 6.65699 17.4526 5.60392C17.0149 4.55086 16.3721 3.59535 15.5617 2.79301ZM9.39805 16.1352H9.39492C8.09859 16.1356 6.82582 15.7887 5.70898 15.1305L5.44453 14.9743L2.70352 15.6899L3.43516 13.0301L3.26289 12.7567C2.53804 11.6091 2.154 10.2793 2.15547 8.92192C2.15547 4.94809 5.40586 1.71488 9.40078 1.71488C11.3172 1.71146 13.1565 2.46939 14.5141 3.82196C15.8717 5.17454 16.6365 7.01098 16.6402 8.92738C16.6387 12.9016 13.3898 16.1352 9.39805 16.1352ZM13.3703 10.7372C13.1527 10.6286 12.0812 10.1043 11.8828 10.0321C11.6844 9.95981 11.5379 9.92348 11.393 10.1407C11.248 10.3579 10.8305 10.8438 10.7035 10.9903C10.5766 11.1368 10.4496 11.1528 10.232 11.0442C10.0145 10.9356 9.3125 10.7071 8.48086 9.96879C7.83359 9.39418 7.39688 8.68481 7.26992 8.46801C7.14297 8.25121 7.25625 8.13363 7.36523 8.02582C7.46328 7.92856 7.58281 7.7727 7.6918 7.64613C7.80078 7.51957 7.83711 7.42895 7.90938 7.28442C7.98164 7.13988 7.9457 7.01332 7.89141 6.90512C7.83711 6.79692 7.40156 5.73012 7.22031 5.29613C7.04336 4.87348 6.86406 4.9309 6.73047 4.92426C6.60352 4.91801 6.45703 4.91645 6.31289 4.91645C6.2027 4.91932 6.09428 4.94487 5.99441 4.99151C5.89454 5.03816 5.80535 5.10489 5.73242 5.18754C5.53281 5.40473 4.97031 5.92973 4.97031 6.99535C4.97031 8.06098 5.75156 9.09223 5.85938 9.23676C5.96719 9.38129 7.39453 11.57 9.57852 12.5086C9.98406 12.6823 10.3984 12.8346 10.8199 12.9649C11.3414 13.1297 11.816 13.1067 12.191 13.0508C12.6094 12.9887 13.4801 12.5266 13.6609 12.0204C13.8418 11.5141 13.8422 11.0805 13.7879 10.9903C13.7336 10.9 13.5883 10.8454 13.3703 10.7372Z"
        fill="#0C091F"
      />
    </svg>
  ),
  doctor_profile: (props) => (
    <svg
      {...props}
      width="14"
      height="16"
      viewBox="0 0 14 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.00065 8C8.011 8 8.97997 7.57857 9.69439 6.82843C10.4088 6.07828 10.8102 5.06087 10.8102 4C10.8102 2.93913 10.4088 1.92172 9.69439 1.17157C8.97997 0.421427 8.011 0 7.00065 0C5.9903 0 5.02134 0.421427 4.30691 1.17157C3.59249 1.92172 3.19113 2.93913 3.19113 4C3.19113 5.06087 3.59249 6.07828 4.30691 6.82843C5.02134 7.57857 5.9903 8 7.00065 8ZM4.14351 9.725C1.94113 10.4031 0.333984 12.5406 0.333984 15.0719C0.333984 15.5844 0.729818 16 1.21791 16H12.7834C13.2715 16 13.6673 15.5844 13.6673 15.0719C13.6673 12.5406 12.0602 10.4031 9.85779 9.725V11.3125C10.6792 11.5344 11.2864 12.3188 11.2864 13.25V14.5C11.2864 14.775 11.0721 15 10.8102 15H10.334C10.0721 15 9.85779 14.775 9.85779 14.5C9.85779 14.225 10.0721 14 10.334 14V13.25C10.334 12.6969 9.90839 12.25 9.3816 12.25C8.85482 12.25 8.42922 12.6969 8.42922 13.25V14C8.69113 14 8.90541 14.225 8.90541 14.5C8.90541 14.775 8.69113 15 8.42922 15H7.95303C7.69113 15 7.47684 14.775 7.47684 14.5V13.25C7.47684 12.3188 8.08398 11.5344 8.90541 11.3125V9.52812C8.72684 9.50937 8.54529 9.5 8.36077 9.5H5.64053C5.45601 9.5 5.27446 9.50937 5.09589 9.52812V11.5719C5.78339 11.7875 6.28637 12.4563 6.28637 13.25C6.28637 14.2156 5.53934 15 4.6197 15C3.70006 15 2.95303 14.2156 2.95303 13.25C2.95303 12.4563 3.45601 11.7875 4.14351 11.5719V9.725ZM4.6197 14C4.80914 14 4.99082 13.921 5.12477 13.7803C5.25873 13.6397 5.33398 13.4489 5.33398 13.25C5.33398 13.0511 5.25873 12.8603 5.12477 12.7197C4.99082 12.579 4.80914 12.5 4.6197 12.5C4.43026 12.5 4.24858 12.579 4.11462 12.7197C3.98067 12.8603 3.90541 13.0511 3.90541 13.25C3.90541 13.4489 3.98067 13.6397 4.11462 13.7803C4.24858 13.921 4.43026 14 4.6197 14Z"
        fill="#D2D2D2"
      />
    </svg>
  ),
  search: (props) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.5 14.5H14.71L14.43 14.23C15.4439 13.054 16.0011 11.5527 16 10C16 8.71442 15.6188 7.45772 14.9046 6.3888C14.1903 5.31988 13.1752 4.48676 11.9874 3.99479C10.7997 3.50282 9.49279 3.37409 8.23192 3.6249C6.97104 3.8757 5.81285 4.49477 4.90381 5.40381C3.99477 6.31285 3.3757 7.47104 3.1249 8.73192C2.87409 9.99279 3.00282 11.2997 3.49479 12.4874C3.98676 13.6752 4.81988 14.6903 5.8888 15.4046C6.95772 16.1188 8.21442 16.5 9.5 16.5C11.11 16.5 12.59 15.91 13.73 14.93L14 15.21V16L19 20.99L20.49 19.5L15.5 14.5ZM9.5 14.5C7.01 14.5 5 12.49 5 10C5 7.51 7.01 5.5 9.5 5.5C11.99 5.5 14 7.51 14 10C14 12.49 11.99 14.5 9.5 14.5Z"
        fill="#110C2C"
      />
    </svg>
  ),
  filter: (props) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.1451 6.85603H8.57366M6.28795 6.85603H2.85938M21.1451 18.2846H8.57366M6.28795 18.2846H2.85938M15.4308 12.5703H2.85938M21.1451 12.5703H17.7165M7.4308 4.57031C7.73391 4.57031 8.0246 4.69072 8.23893 4.90505C8.45325 5.11938 8.57366 5.41007 8.57366 5.71317V7.99888C8.57366 8.30199 8.45325 8.59268 8.23893 8.80701C8.0246 9.02133 7.73391 9.14174 7.4308 9.14174C7.1277 9.14174 6.83701 9.02133 6.62268 8.80701C6.40835 8.59268 6.28795 8.30199 6.28795 7.99888V5.71317C6.28795 5.41007 6.40835 5.11938 6.62268 4.90505C6.83701 4.69072 7.1277 4.57031 7.4308 4.57031ZM7.4308 15.9989C7.73391 15.9989 8.0246 16.1193 8.23893 16.3336C8.45325 16.5479 8.57366 16.8386 8.57366 17.1417V19.4275C8.57366 19.7306 8.45325 20.0213 8.23893 20.2356C8.0246 20.4499 7.73391 20.5703 7.4308 20.5703C7.1277 20.5703 6.83701 20.4499 6.62268 20.2356C6.40835 20.0213 6.28795 19.7306 6.28795 19.4275V17.1417C6.28795 16.8386 6.40835 16.5479 6.62268 16.3336C6.83701 16.1193 7.1277 15.9989 7.4308 15.9989ZM16.5737 10.2846C16.8768 10.2846 17.1675 10.405 17.3818 10.6193C17.5961 10.8337 17.7165 11.1244 17.7165 11.4275V13.7132C17.7165 14.0163 17.5961 14.307 17.3818 14.5213C17.1675 14.7356 16.8768 14.856 16.5737 14.856C16.2706 14.856 15.9799 14.7356 15.7655 14.5213C15.5512 14.307 15.4308 14.0163 15.4308 13.7132V11.4275C15.4308 11.1244 15.5512 10.8337 15.7655 10.6193C15.9799 10.405 16.2706 10.2846 16.5737 10.2846Z"
        stroke="#110C2C"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  ),
  upload: (props) => (
    <svg
      {...props}
      aria-hidden="true"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 20 16"
    >
      <path
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
      />
    </svg>
  ),
  settings: (props) => (
    <svg
      {...props}
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.1486 13.3083L18.0986 12.475L17.2652 11.0333L16.0569 11.4417C15.7902 11.2167 15.4902 11.0417 15.1569 10.9167L14.9069 9.66667H13.2402L12.9902 10.9083C12.6569 11.0333 12.3569 11.2083 12.0902 11.4333L10.8819 11.025L10.0486 12.4667L10.9986 13.3C10.9319 13.7167 10.9319 13.9333 10.9986 14.35L10.0486 15.1833L10.8819 16.625L12.0902 16.2167C12.3569 16.4417 12.6569 16.6167 12.9902 16.7417L13.2402 18H14.9069L15.1569 16.7583C15.4902 16.6333 15.7902 16.4583 16.0569 16.2333L17.2652 16.6417L18.0986 15.2L17.1486 14.3667C17.2152 13.9417 17.2152 13.725 17.1486 13.3083ZM14.0736 15.5C13.1569 15.5 12.4069 14.75 12.4069 13.8333C12.4069 12.9167 13.1569 12.1667 14.0736 12.1667C14.9902 12.1667 15.7402 12.9167 15.7402 13.8333C15.7402 14.75 14.9902 15.5 14.0736 15.5ZM7.4069 3.83333V8.34167L9.37357 10.3083L10.2402 8.81667L9.07357 7.65833V3.83333H7.4069ZM15.7402 8C15.7402 6.01088 14.9501 4.10322 13.5435 2.6967C12.137 1.29018 10.2294 0.5 8.24023 0.5C5.8819 0.5 3.7819 1.6 2.4069 3.3V1.33333H0.740234V6.33333H5.74023V4.66667H3.4569C3.99438 3.89599 4.70983 3.26634 5.54254 2.83113C6.37525 2.39591 7.30065 2.16798 8.24023 2.16667C11.4569 2.16667 14.0736 4.78333 14.0736 8H15.7402ZM7.29023 13.7583C4.79857 13.35 2.8319 11.3417 2.47357 8.83333H0.790234C1.2069 12.5833 4.3819 15.5 8.24023 15.5H8.29857L7.29023 13.7583Z"
        fill="#413D56"
      />
    </svg>
  ),
};
