import appInsights from 'applicationinsights';
import dotenv from 'dotenv';

let env = dotenv.config();

export const initializeAppInsights = async () => {
    if (process.env.ENV && process.env.APPLICATIONINSIGHTS_CONNECTION_STRING && process.env.ENV !== "dev") {
        console.log("into telemetry")
        appInsights.setup(process.env.APPLICATIONINSIGHTS_CONNECTION_STRING)
            .setAutoDependencyCorrelation(true)
            .setAutoCollectRequests(true)
            .setAutoCollectPerformance(true, true)
            .setAutoCollectExceptions(true)
            .setAutoCollectDependencies(true)
            .setAutoCollectConsole(true)
            .setUseDiskRetryCaching(true)
            .setSendLiveMetrics(true)
            .setDistributedTracingMode(appInsights.DistributedTracingModes.AI);


        appInsights.defaultClient.config.samplingPercentage = 99;
        appInsights.defaultClient.context.tags[appInsights.defaultClient.context.keys.cloudRole] = "Healtether.Communications";

        appInsights.start();
    }

};

export const LogTrace = (message) => {
    if (process.env.ENV === "dev") {
        console.log(message);
    }
    appInsights.defaultClient?.trackTrace({ message: message });
}

export const LogException = (exception) => {
    console.log("log exception")
    if (process.env.ENV === "dev") {
        console.error(`Error in Application ${exception}\nStack: ${exception.stack}`);
    }
    appInsights.defaultClient?.trackException({ exception: exception });
}

export const LogEvent = (name, customProperty) => {
    appInsights.defaultClient?.trackEvent({ name: name, properties: { customProperty: customProperty } });
}

export const ConsoleLog = (message) => {
    if (process.env.ENV === "dev")
        console.log(message);
}
export const ConsoleLogError = (error) => {
    if (process.env.ENV === "dev")
        console.error(`Error in Application ${error}`);
}