import axios from "axios";
import dotenv from "dotenv";
import { toTitleCase } from "../../../utils/titlecase.generator.js";

dotenv.config();

export const generateSnomedCtCode = async (term) => {
    try {

        if (term) {
        const snomedBaseUrl = process.env.SNOMED_SERVER_BASE_URL;
        const response = await axios.get(`${snomedBaseUrl}/csnoserv/api/search/search`, {
            params: {
                term: toTitleCase(term),
                state: 'active'
            }
        });

        const results = response.data;
        const activeItem = results.find(item => item.activeStatus === 1);

        if (activeItem) {
            const { conceptId, term } = activeItem;
            return { conceptId, term };
        } else {
            throw { message: `No active snomed item found with the term: ${term}` };
        }
    }else{
      return "";
    }
    } catch (error) {
        console.error(`Error fetching Snomed data: ${term}`, error);
        throw error
    }
};