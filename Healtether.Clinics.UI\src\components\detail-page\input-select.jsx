import React, { useState, useEffect } from "react";
import { useClickOut } from "../../utils/hooks/useClickout";
import { Input } from "./input";

export default function InputSelect(props) {
  const { placeholder, data, handleClick, bottom } = props;

  const [title, setTitle] = useState(placeholder);
  const { targetRef, isOpen, setIsOpen } = useClickOut.auto();

  const select = (prop) => {
    handleClick(prop);
    setTitle(prop?.name);
    setIsOpen(false);
  };

  return (
    <article className=" relative">
      <Input.text {...props} value={title} onClick={() => setIsOpen(true)} />
      <section
        ref={targetRef}
        className={`${
          isOpen
            ? " visible opacity-100 scale-100 skew-y-0"
            : " invisible opacity-0 scale-y-[0.006] skew-y-"
        } bg-gray mt-2 border border-color_muted/20 ${
          bottom
            ? "bottom-full mb-2 origin-bottom-right"
            : "top-11 origin-bottom"
        } duration-300 w-full min-w-max h-fit absolute right-0 z-30 overflow-hidden rounded-sm shadow-xl p-1`}
      >
        <div className="overflow-y-auto custom-scrollbar max-h-60">
          {data.map((prop, idx) => (
            <div
              key={idx}
              onClick={() => select(prop)}
              className="text-xs sm:text-base  font-semibold justify-between h-11 px-4 pt-2.5 cursor-pointer hover:bg-dark/10 duration-200 capitalize"
            >
              {prop?.name}
            </div>
          ))}
        </div>
      </section>
    </article>
  );
}
