import {
  informHUI,
  linkCareContext,
  linkingToken,
  transferData,
} from "../helper/abha/milestone.two.helper.js";
import { consentResponses } from "./abha.milestone.three.controller.js";
import {
  cleanEncryptedFhir,
  generateEncryptedkeyCore,
  generateEncryptedkeyCore2,
  generateKeyMaterialCore,
} from "./fidelius.controller.js";
import fs from "fs";
import path from "path";

import portalDb from "../config/clinics.collections.config.js";
import { handleApisComingFromAbhaAppForFhir } from "../helper/fhir/main.bundle.fhir.helper.js";
const OPConsultRecordSchema = portalDb.model("OPConsultFHIRRecord");
const DischargeSummaryRecordSchema = portalDb.model(
  "DischargeSummaryFHIRRecord"
);
const PrescriptionRecordSchema = portalDb.model("PrescriptionFHIRRecord");
const HealthDocumentRecordSchema = portalDb.model("HealthDocumentFHIRRecord");
const DiagnosticReportSchema = portalDb.model("DiagnosticReportFHIRRecord");
const InvoiceRecordSchema = portalDb.model("InvoiceReportFHIRRecord");
const WellnessRecordSchema = portalDb.model("WellnessReportFHIRRecord");
const ImmunizationRecordSchema = portalDb.model("ImmunizationReportFHIRRecord");

export const GenerateLinkingTokenM22 = async (req, res) => {
  const hipId = req.headers["x-hip-id"];
  const cmId = req.headers["x-cm-id"];
  const patientData = req.body;

  const abhaNumber = patientData.abhaNumber.replace(/-/g, "");
  // const requestBody = {
  //   abhaNumber: abhaNumber || null,
  //   abhaAddress: patientData.abhaAddress || null,
  //   name: patientData.name,
  //   gender: patientData.gender[0],
  //   yearOfBirth:patientData.yearOfBirth
  // };
  const result = await linkingToken(patientData, hipId, cmId);
  res.json(result);
};

export const GenerateLinkingTokenM2 = async (req, res) => {
  const hipId = req.headers["x-hip-id"];
  const cmId = req.headers["x-cm-id"];
  const requestBody = {
    abhaNumber: req.body.abhaNumber || null,
    abhaAddress: req.body.abhaAddress || null,
    name: req.body.name,
    gender: req.body.gender,
    yearOfBirth: req.body.yearOfBirth,
  };

  let result = await linkingToken(requestBody, hipId, cmId);
  res.json(result);
};

export const LinkCareContextM2 = async (req, res) => {
  const { abhaNumber, abhaAddress, patient } = req.body;

  // Ensure all required fields are present
  if (!abhaNumber || !abhaAddress || !patient) {
    return res.status(400).json({
      error: "Missing required fields: abhaNumber, abhaAddress, patient",
    });
  }

  let linkingToken = req.headers["x-link-token"];
  let hipId = req.headers["x-hip-id"];
  let cmId = req.headers["x-cm-id"];
  const filePath = path.join("./", "careContextData.json");
  fs.writeFileSync(filePath, JSON.stringify(req.body, null, 2), "utf-8");
  let result = await linkCareContext(req.body, hipId, cmId, linkingToken);
  res.json(result);
};

export const HealthInfoTransfer = async (data, res) => {
  const notifyResponse = consentResponses[data.hiRequest.consent.id].hip;
  const healthInfoResponse = data;
  const transactionId = healthInfoResponse.transactionId;
  const expiry = healthInfoResponse.hiRequest.keyMaterial.dhPublicKey.expiry;
  const keyValue =
    healthInfoResponse.hiRequest.keyMaterial.dhPublicKey.keyValue;
  const nonceValue = healthInfoResponse.hiRequest.keyMaterial.nonce;
  const dataPushURL = healthInfoResponse.hiRequest.dataPushUrl;
  const carecontextArray =
    notifyResponse.data.notification.consentDetail.careContexts;

console.log(
    "carecontextArray",carecontextArray);

  const { privateKey, publicKey, nonce, x509PublicKey } =
    await generateKeyMaterialCore();
  const fhirData = await Promise.all(
    carecontextArray.map(async (carecontext) => {
      const data = [];

      const op = await OPConsultRecordSchema.findOne({
        fhirId: carecontext.careContextReference,
      }).lean();
      if (op) data.push({ body: op });

      const discharge = await DischargeSummaryRecordSchema.findOne({
        fhirId: carecontext.careContextReference,
      }).lean();
      if (discharge) data.push({ body: discharge });

      const prescription = await PrescriptionRecordSchema.findOne({
        fhirId: carecontext.careContextReference,
      }).lean();
      if (prescription) data.push({ body: prescription });

      const healthDoc = await HealthDocumentRecordSchema.findOne({
        fhirId: carecontext.careContextReference,
      }).lean();
      if (healthDoc) data.push({ body: healthDoc });

      const diagnostic = await DiagnosticReportSchema.findOne({
        fhirId: carecontext.careContextReference,
      }).lean();
      if (diagnostic) data.push({ body: diagnostic });

      const invoice = await InvoiceRecordSchema.findOne({
        fhirId: carecontext.careContextReference,
      }).lean();
      if (invoice) data.push({ body: invoice });

      const wellness = await WellnessRecordSchema.findOne({
        fhirId: carecontext.careContextReference,
      }).lean();
      if (wellness) data.push({ body: wellness });

      const immunization = await ImmunizationRecordSchema.findOne({
        fhirId: carecontext.careContextReference,
      }).lean();
      if (immunization) data.push({ body: immunization });

      return data;
    })
  );

  const filteredResults = fhirData.flat().filter(Boolean);
  const fhirObject = await Promise.all(
    filteredResults.map(async (data) => {
        const fhirbundle = await handleApisComingFromAbhaAppForFhir(data);
        if (fhirbundle?.isSuccess === false && fhirbundle?.response) {
          return null;
        }
          return { fhirId: data.body.fhirId, fhirbundle };
      
    })
  );

  const fhirStructureArray = fhirObject.filter(Boolean);
  await cleanEncryptedFhir();
  const EntriesArray = async (privateKey, publicKey, nonce) => {
    const entries = await Promise.all(
      fhirStructureArray.map(async (fire) => {
        const encrptedValues = await generateEncryptedkeyCore2(
          fire.fhirId,
          fire.fhirbundle,
          nonceValue,
          keyValue,
          privateKey,
          publicKey,
          nonce
        );
        return {
          content: encrptedValues,
          media: "application/fhir+json",
          checksum: "fhirbundleing", // Replace with actual checksum
          careContextReference: fire.fhirId, // Replace with actual reference
        };
      })
    );
    return entries;
  };
  const entiresStructure = await EntriesArray(privateKey, publicKey, nonce);
  const requestBody = {
    pageNumber: 0,
    pageCount: 1,
    transactionId: transactionId,
    entries: entiresStructure,
    keyMaterial: {
      cryptoAlg: "ECDH",
      curve: "Curve25519",
      dhPublicKey: {
        expiry: `${expiry}`,
        parameters: "Curve25519/32byte random key",
        keyValue: x509PublicKey,
      },
      nonce: nonce,
    },
  };
  await transferData(requestBody, dataPushURL);
  await cleanEncryptedFhir();
  PushHealthInfoM2(data, consentResponses[data.hiRequest.consent.id].hip);
};

export const PushHealthInfoM2 = async (request, hipdata) => {
  const hipId = hipdata.data.notification.consentDetail.hip.id;

  const CareContextRefArray =
    hipdata.data.notification.consentDetail.careContexts.map(
      (careContext) => careContext.careContextReference
    );
  const statusResponsesArray = CareContextRefArray.map((careContext) => {
    return {
      careContextReference: careContext,
      hiStatus: "OK",
      description: "test care context",
    };
  });
  const transactionId = request.transactionId;
  const consentId = request.hiRequest.consent.id;
  const datedoneAt = request.hiRequest.dateRange.to;
  const requestBody = {
    notification: {
      // ...req.body.notification,
      consentId: consentId,
      transactionId: transactionId,
      doneAt: datedoneAt,
      notifier: {
        type: "HIP",
        id: hipId,
      },
      statusNotification: {
        // ...req.body.statusNotification,
        sessionStatus: "TRANSFERRED",
        hipId: hipId,
        statusResponses: statusResponsesArray,
      },
    },
  };
  await informHUI(requestBody);
};
