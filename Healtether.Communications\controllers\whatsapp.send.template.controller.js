
import { sendTemplate } from "../helper/whatsapp/whatsapp.helper.js";
import { getAppointmentLinkTemplate, getAppointmentSummaryTemplate } from "../template/whatsapp/appointment.template.js";
import { getPaymentLinkTemplate } from "../template/whatsapp/payment.template.js";

const code="";

export const sendAppointmentSummary = async (req, res) => {
  const data = req.body.data;
  const templateData = getAppointmentSummaryTemplate(
    data.to,
    code,
    data.clinicName,
    data.patientName,
    data.doctorName,
    data.scheduleDate,
    data.timeSlots
  );
  var result = await sendTemplate(templateData);
  res.json({ success: true }).status(200);
};

export const sendPaymentLink = async (req, res) => {
  const data = req.body.data;
  const templateData = getPaymentLinkTemplate(
    data.to,
    code,
    data.clinicName,
    data.patientName,
    data.doctorName,
    data.paymentLink
  );
  var result = await sendTemplate(templateData);
  res.json({ success: true }).status(200);
};

export const sendAppointmentLink = async (req, res) => {
  const data = req.body.data;
  const templateData = getAppointmentLinkTemplate(
    data.to,
    code,
    data.clinicName,
    data.patientName,
    data.doctorName,
    data.scheduleDate,
    data.timeSlots,
    data.googleMeetLink
  );
  var result = await sendTemplate(templateData);
  res.json({ success: true }).status(200);
};
