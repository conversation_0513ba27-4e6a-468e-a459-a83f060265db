import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { getDoctor } from '../../../helpers/staff/staff.helper.js'; 
import { Staff } from '../../../model/clinics.model.js'; 
import { ClientUser } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js"; 
jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown(); 
});

describe('getDoctor function', () => {
  let testClinicId, doctorId, linkedClinicId;

  beforeEach(async () => {
    await Staff.deleteMany({});
    await ClientUser.deleteMany({});

    const doctor = new Staff({
      firstName: 'Test',
      staffId:"testId1",
      lastName: 'Doctor',
      mobile: '1234567890',
      prefix:"Mr.",
      specialization: 'General',
      userId: new mongoose.Types.ObjectId(),
      isDoctor: true,
      deleted: false,
    });
    doctorId = doctor._id;
    await doctor.save();

  
    const linkedClinic = new ClientUser({
      userId: doctor.userId, 
      clinic: new mongoose.Types.ObjectId(), 
      isAdmin: false,
      isdeleted: false
    });
    linkedClinicId = linkedClinic.clinic;
    await linkedClinic.save();

    testClinicId = linkedClinic.clinic;
  });

  afterEach(async () => {
    await Staff.deleteMany({});
    await ClientUser.deleteMany({}); 
  });

  it('should return doctors linked to the specified clinic', async () => {
    const result = await getDoctor(testClinicId);

    expect(result.length).toBe(1);
    expect(result[0].firstName).toBe('Test');
    expect(result[0].lastName).toBe('Doctor');
    expect(result[0].specialization).toBe('General');
  });

  it('should return an empty array if no doctors are linked to the clinic', async () => {
    const newClinicId = new mongoose.Types.ObjectId();
    const result = await getDoctor(newClinicId); 

    expect(result.length).toBe(0); 
  });


});
