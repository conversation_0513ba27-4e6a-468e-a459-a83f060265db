import { Schema } from "mongoose";

const telecomSchema = new Schema({
  system: String,
  value: String,
  use: String,
});

const licenseSchema = new Schema({
  code: String,
  display: String,
  licNo: String,
});

const allergyIntoleranceSchema = new Schema({
  type: String,
  clinicalStatus: String,
  verificationStatus: String,
  doctor: String,
  notes: [String],
});

const attachmentSchema = new Schema({
  contentType: String,
  language: String,
  data: String,
  title: String,
  creation: String,
});

const dischargeSummarySchema = new Schema({
  status: String,
  docStatus: String,
  type: String,
  content: [{ attachment: attachmentSchema }],
});

const signatureSchema = new Schema({
  who: {
    type: { type: String },
    name: String,
  },
  sigFormat: String,
  data: String,
});
const addressSchema = new Schema({
  use: String,
  type: String,
  text: String,
  line: [String],
  city: String,
  state: String,
  district: String,
  postalCode: String,
  country: String,
});
const patientSchema = new Schema({
  id: String,
  abhaNumber: String,
  abhaAddress: String,
  name: {
    text: String,
    prefix: [String],
  },
  gender: String,
  dob: String,
  doctors: [String],
  allergyIntolerances: [allergyIntoleranceSchema],
  telecom: [telecomSchema],
  address: [addressSchema],
});

const generalSchema = new Schema({
  artifact: String,
  hipUrl: String,
  hipIds: [String],
  status: String,
  clientId: String,
});

const practitionerSchema = new Schema({
  names: [String],
  licenses: [licenseSchema],
  patient: String,
});

const organizationSchema = new Schema({
  name: String,
  telecom: [telecomSchema],
  licenses: [licenseSchema],
});
const serviceRequestSchema = new Schema({
  status: String,
  intent: String,
  categories: [String],
  type: String,
});
const medicationStatementSchema = new Schema({
  status: String,
  type: String,
});

const dosageInstructionSchema = new Schema({
  text: String,
  additionalInstruction: String,
  route: String,
  repeat: {
    frequency: Number,
    period: Number,
    periodUnit: String,
  },
  method: String,
});

const medicationRequestSchema = new Schema({
  status: String,
  intent: String,
  authoredOn: String,
  medication: String,
  forCondition: [String],
  dosageInstruction: [dosageInstructionSchema],
});
const procedureSchema = new Schema({
  status: String,
  type: String,
  performedDateTime: String,
  followUp: [String],
});

const appointmentSchema = new Schema({
  status: String,
  serviceCategories: [String],
  serviceTypes: [String],
  specialty: [String],
  appointmentType: String,
  description: String,
  start: String,
  end: String,
  created: String,
  reasonReference: [String],
  basedOnServices: [String],
});

const conditionSchema = new Schema({
  type: String,
  status: String,
  recordedDate: String,
  startDate: String,
  endDate: String,
});
const encounterSchema = new Schema({
  status: String,
  startTime: String,
});

const DischargeSummaryRecordSchema = new Schema({
  fhirId: {
    type: String,
    required: true,
    index: true,
  },
  general: generalSchema,
  patient: patientSchema,
  practitioners: [practitionerSchema],
  encounter: encounterSchema,
  organization: organizationSchema,
  conditions: [conditionSchema],
  serviceRequests: [serviceRequestSchema],
  medicationStatements: [medicationStatementSchema],
  medicationRequests: [medicationRequestSchema],
  procedures: [procedureSchema],
  appointment: appointmentSchema,
  signature: signatureSchema,
  dischargeSummary: [dischargeSummarySchema], // Added field for Discharge Summary data
  abhaCareContextLinked:{
    type: Boolean,
    default: false
}
});

export { DischargeSummaryRecordSchema };
