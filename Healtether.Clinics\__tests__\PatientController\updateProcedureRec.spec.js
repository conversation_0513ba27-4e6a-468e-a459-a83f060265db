import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");
const { mockBlobHelper } = await import("../mocks/mock.blob.helper.js");
mockBlobHelper()
mockPatientHelper();

const { updateProcedureRec } = await import('../../controllers/patients/patients.controller.js'); 
const { Blob<PERSON>elper } = await import("../../helpers/storage/blob.helper.js"); 

describe("updateProcedureRec", () => {
  let req;
  let res;
  
  beforeEach(() => {
    // Reset mocks and create fresh request and response objects
    jest.clearAllMocks();

    // Mock environment variables
    process.env.AZURE_CONNECTIONSTRING = 'test-connection-string';
    process.env.CLINICBLOB_CONTAINER_PREFIX = 'test-prefix-';
    process.env.PATIENT_BLOB_FOLDER = 'patient/';

    // Setup request object
    req = {
      body: {
        clientId: 'testClientId', // Add clientId for BlobHelper constructor
        procedureRecordName: 'record1,record2',
        profileName: 'profilePic.jpg',
      },
      files: {
        profile: [{ 
          originalname: 'profilePic.jpg', 
          buffer: Buffer.from('test'),
          fieldname: 'profile',
          encoding: '7bit',
          mimetype: 'image/jpeg'
        }],
        procedureRecords: [
          { 
            originalname: 'record1.jpg', 
            buffer: Buffer.from('test1'),
            fieldname: 'procedureRecords',
            encoding: '7bit',
            mimetype: 'image/jpeg'
          },
          { 
            originalname: 'record2.jpg', 
            buffer: Buffer.from('test2'),
            fieldname: 'procedureRecords',
            encoding: '7bit',
            mimetype: 'image/jpeg'
          },
        ],
      },
    };

    // Setup response object
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  });

  it('should upload profile and procedure records and return success', async () => {
    // Create a mock for UploadBlob method
    const uploadBlobMock = jest.fn();
    
    // Mock BlobHelper constructor to return an object with the mocked UploadBlob method
    BlobHelper.mockImplementation(() => ({
      UploadBlob: uploadBlobMock
    }));

    await updateProcedureRec(req, res);

    // Verify profile upload
    expect(uploadBlobMock).toHaveBeenCalledWith(
      req.files.profile[0],
      process.env.PATIENT_BLOB_FOLDER,
      req.body.profileName
    );

    // Verify procedure records uploads
    expect(uploadBlobMock).toHaveBeenCalledWith(
      req.files.procedureRecords[0],
      process.env.PATIENT_BLOB_FOLDER,
      'record1'
    );
    expect(uploadBlobMock).toHaveBeenCalledWith(
      req.files.procedureRecords[1],
      process.env.PATIENT_BLOB_FOLDER,
      'record2'
    );

    // Expect a successful response
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });
});