import { useEffect, useRef, useState } from "react";
import { format } from "date-fns";
import domtoimage from 'dom-to-image-more';
import jsPDF from 'jspdf';
import { SaveAsDraftApi } from "../services/appointment/appointment";
import { alertBox } from "./dialog/prompt";
import Spinner from "../components/loader/Spinner";

export const PrescriptionModal = ({ 
  isOpen, 
  onClose, 
  prescriptionDetails, 
  medicalHistoryDetails, 
  appointmentId, 
  onPrint, 
  onSave 
}) => {
  if (!isOpen) return null;
  
  const contentToPrint = useRef(null);
  
  if (!prescriptionDetails || !medicalHistoryDetails) return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-8 rounded-lg shadow-xl">
        <p className="text-xl font-semibold">Loading prescription data...</p>
        <Spinner show={true} />
      </div>
    </div>
  );

  const {
    vitals,
    prescriptions,
    doctorName,
    patientName,
    patientAge,
    patientGender,
    patientMobile,
    doctorId,
    clinicPatientId,
    clinic,
    appointmentDate,
    timeSlot,
  } = prescriptionDetails;

  const {
    allergies,
    medication,
    familyHistory,
    pastHistory,
    pastProcedureHistory,
  } = medicalHistoryDetails;
  
  return (
    <div className="fixed inset-0 bg-color_dark/10 backdrop-blur-xs flex items-center justify-center z-50 overflow-auto">
      <div className="bg-white rounded-lg shadow-xl w-11/12 max-w-4xl max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-bold">Prescription Preview</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="p-4">
          <div
            ref={contentToPrint}
            data-prescription-content="true"  // Add this data attribute
            className="bg-white p-6 border border-gray-300"
          >
            {/* Header Section */}
            <div className="flex justify-between items-center border-b pb-4 mb-4">
              <div className="flex items-center">
                {clinic?.logo?.toString() !== "" ? (
                  <img
                    src={clinic?.logo}
                    alt="Clinic Logo"
                    className="h-16 w-16 object-contain"
                  />
                ) : (
                  <div className="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                    <span className="text-gray-500">No Logo</span>
                  </div>
                )}
                <div className="ml-4">
                  <h1 className="text-lg font-bold">{clinic?.clinicName}</h1>
                  <p>Dr. {doctorName},</p>
                  <p className="text-sm">{doctorId?.specialization}</p>
                </div>
              </div>
            </div>

            {/* Patient Details */}
            <div className="flex justify-between items-center border-b pb-4 mb-4 text-md">
              <div className="font-semibold">
                <p className="font-normal">Patient Details:</p>
                <p>
                  {patientName}, {patientGender}, {patientAge} yrs,{" "}
                </p>
                <p> {patientMobile}</p>
              </div>
              <div className="text-right">
                <p className="text-sm">
                  Date:{" "}
                  {appointmentDate
                    ? format(new Date(appointmentDate), "dd MMM yyyy")
                    : ""}{" "}
                  {timeSlot}
                </p>
                <p className="text-sm">Patient ID: {clinicPatientId}</p>
              </div>
            </div>

            {/* Medical History */}
            <div className="grid grid-cols-2 gap-2 border-b pb-2 mb-2">
              <div>
                <h2 className="font-semibold">Patient Medical History</h2>

                <p>
                  <span className="font-medium"> Allergies: </span>{" "}
                  {allergies?.length > 0
                    ? allergies.map((allergy, index) => (
                        <span key={index}>{allergy.name}, </span>
                      ))
                    : "No allergies recorded."}
                </p>

                <p>
                  <span className="font-medium"> Medication:</span>{" "}
                  {medication?.length > 0
                    ? medication.map((med) => (
                        <span key={med._id}>
                          {med.name} ({med.duration.value} {med.duration.unit}),{" "}
                        </span>
                      ))
                    : "No medications recorded."}
                </p>

                <p>
                  <span className="font-medium"> Family History:</span>{" "}
                  {familyHistory?.length > 0
                    ? familyHistory.map((history) => (
                        <span key={history._id}>{history.name}, </span>
                      ))
                    : "No family history recorded."}
                </p>

                <p>
                  <span className="font-medium"> Past Medical History:</span>{" "}
                  {pastHistory?.length > 0
                    ? pastHistory.map((history) => (
                        <span key={history._id}>{history.name}, </span>
                      ))
                    : "No past medical history recorded."}
                </p>

                <p>
                  <span className="font-medium"> Past Procedures:</span>{" "}
                  {pastProcedureHistory?.length > 0
                    ? pastProcedureHistory.map((procedure) => (
                        <span key={procedure._id}>{procedure.name}, </span>
                      ))
                    : "No past procedures recorded."}
                </p>
              </div>
            </div>

            {/* Vitals */}
            <div className="grid grid-cols-2 gap-2 border-b pb-2 mb-2">
              <div>
                <h2 className="font-semibold">Vitals</h2>
                <p>
                  <span className="font-medium"> Blood Pressure: </span>
                  {vitals?.bloodPressure?.systolic}/
                  {vitals?.bloodPressure?.diastolic} mmHg
                </p>
                <p>
                  <span className="font-medium"> Pulse Rate: </span>
                  {vitals?.pulseRate} bpm
                </p>
                <p>
                  <span className="font-medium"> SpO2: </span>
                  {vitals?.spo2} %
                </p>
                <p>
                  <span className="font-medium">Temperature: </span>
                  {vitals?.temperature} °C
                </p>
                <p>
                  <span className="font-medium">Height: </span>
                  {vitals?.height} cm
                </p>
                <p>
                  <span className="font-medium">Weight: </span>
                  {vitals?.weight} kg
                </p>
                <p>
                  <span className="font-medium">RBS: </span>
                  {vitals?.rbs} mg/dL
                </p>
              </div>
            </div>

            {/* Symptoms */}
            <div className="mb-2 border-b pb-2">
              <h2 className="font-semibold">Symptoms</h2>
              {prescriptions?.symptoms?.length > 0 ? (
                prescriptions?.symptoms.map((symptom) => {
                  const durationText =
                    symptom?.duration?.value &&
                    `for ${symptom.duration.value} ${
                      parseInt(symptom.duration.value) > 1
                        ? symptom.duration.unit
                        : symptom.duration.unit.replace(/s$/, "")
                    }`;
                  const notesText =
                    symptom?.notes?.length > 0 ? `: ${symptom.notes}` : "";

                  return (
                    <p key={symptom._id}>{`${symptom.name} ${
                      durationText || ""
                    } ${notesText}`}</p>
                  );
                })
              ) : (
                <p>No symptoms recorded.</p>
              )}
            </div>

            {/* Diagnosis */}
            <div className="mb-2 border-b pb-2">
              <h2 className="font-semibold">Diagnosis</h2>
              {prescriptions?.diagnosis?.length > 0 ? (
                prescriptions?.diagnosis.map((diag) => (
                  <p key={diag._id}>{diag.name} {diag.notes ? ": "+diag.notes : ""}</p>
                ))
              ) : (
                <p>No diagnosis recorded.</p>
              )}
            </div>

            {/* Medication Section */}
            <div className="mb-2">
              <h2 className="font-semibold mb-2">Medication</h2>
              <table className="min-w-full table-auto text-left border">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="px-4 py-2">Drug</th>
                    <th className="px-4 py-2">Dosage</th>
                    <th className="px-4 py-2">Time</th>
                    <th className="px-4 py-2">Frequency</th>
                    <th className="px-4 py-2">Duration</th>
                    <th className="px-4 py-2">Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {prescriptions?.drugPrescriptions?.length > 0 ? (
                    prescriptions?.drugPrescriptions.map((drug) => (
                      <tr key={drug._id}>
                        <td className="border px-4 py-2">{drug.drugName}</td>
                        <td className="border px-4 py-2">{drug.dosage}</td>
                        <td className="border px-4 py-2">
                          {drug.isBeforeMeal ? "Before Meal" : "After Meal"}
                        </td>
                        <td className="border px-4 py-2">{drug.frequency}</td>
                        <td className="border px-4 py-2">
                          {drug.duration.value || "N/A"} {drug.duration.unit}
                        </td>
                        <td className="border px-4 py-2">
                          {drug.notes || "No notes"}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td className="border px-4 py-2" colSpan="6">
                        No medications prescribed.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Advice/Instructions */}
            <div className="mb-2">
              <h2 className="font-semibold text">Advice/Instructions</h2>
              <p>
                {prescriptions?.patientAdvice || "No specific advice provided."}
              </p>
            </div>

            {/* Follow Up */}
            <div className="mb-2">
              <h2 className="font-semibold">Follow Up</h2>
              <p>
                {prescriptions?.followUpDate
                  ? `Next follow-up on ${new Date(
                      prescriptions?.followUpDate
                    ).toLocaleDateString()}`
                  : "No follow-up scheduled."}
              </p>
            </div>

            {/* Footer Section */}
            <div className="flex justify-between items-center pt-4 border-t font-medium text-sm">
              <div>
                <p>Ph: {clinic?.adminUserId?.mobile || "N/A"}</p>
                <p>Email: {clinic?.adminUserId?.email || "N/A"}</p>
              </div>
              <div className="text-right">
                <p>{clinic?.address}</p>
                <p>Timings: 8:30 am - 10:50 pm Mon-Fri</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end p-4 gap-3 border-t">
          <button
            onClick={onClose}
            className="border border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-100"
          >
            Close
          </button>
          <button
            onClick={() => onPrint(contentToPrint.current)}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Print
          </button>
          <button
            onClick={() => onSave(contentToPrint.current)}
            className="bg-Primary text-white px-4 py-2 rounded hover:bg-Primary/80"
          >
            Save PDF
          </button>
        </div>
      </div>
    </div>
  );
};
