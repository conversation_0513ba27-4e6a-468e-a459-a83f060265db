import mongoose from "mongoose";
import {Vitals} from "../../../model/clinics.model.js"
import { checkStringEmptyAndReturnNull } from "../../../utils/common.utils.js";
import { upsertPersonalHistory } from "./medical-histories.helper.js";

export const addUpdateVitals = async (data, user, patientId, clientId, appointmentId) => {
    if (!data?.vitals) {
        throw new Error("Vitals data is required");
    }

    const vitalObj = data.vitals;
    
    try {
        // Convert IDs to ObjectId if they're strings
        const clinicId = new mongoose.Types.ObjectId(clientId);
        const patId = new mongoose.Types.ObjectId(patientId);
        const apptId = new mongoose.Types.ObjectId(appointmentId);

        // Try to find existing vitals
        let vitals = await Vitals.findOne({
            clinic: clinicId,
            patient: patId,
            appointment: apptId
        });

        if (vitals) {
            // Update existing vitals
            vitals.bloodPressure = checkBpObjEmptyAndReturnNull(vitalObj?.bloodPressure);
            vitals.interpretation = checkStringEmptyAndReturnNull(vitalObj?.interpretation);
            vitals.spo2 = checkStringEmptyAndReturnNull(vitalObj?.spo2);
            vitals.respiratoryRate = checkStringEmptyAndReturnNull(vitalObj?.respiratoryRate);
            vitals.temperature = checkStringEmptyAndReturnNull(vitalObj?.temperature);
            vitals.height = checkStringEmptyAndReturnNull(vitalObj?.height);
            vitals.weight = checkStringEmptyAndReturnNull(vitalObj?.weight);
            vitals.pulseRate = checkStringEmptyAndReturnNull(vitalObj?.pulseRate);
            vitals.rbs = checkStringEmptyAndReturnNull(vitalObj?.rbs);
            vitals.modified = {
                on: new Date().toISOString(),
                by: user,
            };
            await vitals.save();
            return vitals;
        } else {
            // Create new vitals
            const vitalsCollection = new Vitals({
                bloodPressure: checkBpObjEmptyAndReturnNull(vitalObj?.bloodPressure),
                interpretation: checkStringEmptyAndReturnNull(vitalObj?.interpretation),
                spo2: checkStringEmptyAndReturnNull(vitalObj?.spo2),
                temperature: checkStringEmptyAndReturnNull(vitalObj?.temperature),
                height: checkStringEmptyAndReturnNull(vitalObj?.height),
                weight: checkStringEmptyAndReturnNull(vitalObj?.weight),
                pulseRate: checkStringEmptyAndReturnNull(vitalObj?.pulseRate),
                rbs: checkStringEmptyAndReturnNull(vitalObj?.rbs),
                respiratoryRate: checkStringEmptyAndReturnNull(vitalObj?.respiratoryRate),
                created: {
                    on: new Date().toISOString(),
                    by: user,
                },
                clinic: clinicId,
                patient: patId,
                appointment: apptId
            });
            await vitalsCollection.save();
            return vitalsCollection;
        }
    } catch (error) {
        console.error("Error in addUpdateVitals:", error);
        throw error;
    }
};
export const addUpdateVitalsNdPersonalHistory=async(data, user, patientId, clientId, appointmentId)=>{
    console.log("addUpdateVitalsNdPersonalHistory",data);
      await addUpdateVitals(data, user, patientId, clientId, appointmentId);
      await upsertPersonalHistory(data.personalHistories, user, patientId, clientId)
}


export function checkBpObjEmptyAndReturnNull(data)
{
    if(data!=null && (data?.systolic?.trim()!="" || data?.diastolic?.trim()!=""))
    {
        return data;
    }
    else
    {
        return undefined;
    }
}