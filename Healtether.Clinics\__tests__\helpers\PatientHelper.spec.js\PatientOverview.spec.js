import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { overview } from '../../../helpers/patient/patient.helper.js';
import { Patient } from '../../../model/clinics.model.js'; 
import { Appointment } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe('overview function', () => {
  const clientId = new mongoose.Types.ObjectId();
  let patientId;
  let appointmentId;

  beforeEach(async () => {
    await Patient.deleteMany({});
    await Appointment.deleteMany({});
    
    // Create patient with clinic reference
    const patient = await Patient.create({
      firstName: 'John',
      lastName: 'Doe',
      mobile: '**********',
      patientId: 'PAT001',
      age: 30,
      prefix: "Mr.",
      birthday: new Date('1993-01-01'),
      gender: 'Male',
      email: '<EMAIL>',
      address: '123 Main St',
      height: 180,
      weight: 75,
      documentType: 'ID',
      documentNumber: 'ID123456',
      isDeleted: false, // Changed from 'deleted' to match model
      clinic: clientId, // Added clinic reference
    });
    patientId = patient._id;

    // Create appointment linked to patient and clinic
    const appointment = await Appointment.create({
      mobile: '**********',
      name: 'John Doe',
      appointmentDate: new Date(),
      timeSlot: '10:00 AM',
      reason: 'Checkup',
      paymentStatus: true,
      clinic: clientId,
      patientId: patientId,
    });
    appointmentId = appointment._id;

    // Update patient with appointment reference
    await Patient.findByIdAndUpdate(patientId, {
      $push: { appointments: appointmentId }
    });
  });

  afterEach(async () => {
    await Patient.deleteMany({});
    await Appointment.deleteMany({});
  });

  it('should return filtered patients based on keyword', async () => {
    const result = await overview(clientId, 0, 10, 'John', 'firstName', 'asc');
    expect(result.data.length).toBeGreaterThan(0);
    expect(result.totalCount).toBeGreaterThan(0);
    expect(result.data[0].firstName).toBe('John');
  });

  it('should return all patients when no keyword is provided', async () => {
    const result = await overview(clientId, 0, 10, null, null, null);
    expect(result.data.length).toBeGreaterThan(0);
    expect(result.totalCount).toBeGreaterThan(0);
  });

  it('should return empty array when no patients match the keyword', async () => {
    const result = await overview(clientId, 0, 10, 'Nonexistent', null, null);
    expect(result.data.length).toBe(0);
    expect(result.totalCount).toBe(0);
  });
});