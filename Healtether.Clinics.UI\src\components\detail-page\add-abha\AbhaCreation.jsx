import React, { useEffect, useState } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "./create-abha";
import <PERSON><PERSON><PERSON><PERSON>Address from "./create-abha-address";

import PatientUpdateAbort from "./PatientUpdateAbort";
import {
  create<PERSON><PERSON>,
  enroll<PERSON>bhaAddress,
  enroll<PERSON>y<PERSON>ad<PERSON>,
  enrolledByMobile,
  fetchAbhaCard,
  verifyEnrolledMobileOtp,
} from "../../../services/appointment/abha-m1";
import { PatientProfileWithAbhaDetails } from "../../../services/patient/patient";
import {
  CalculateAge,
  formatDateToUTC,
  startTimer,
  updateResendAttempts,
} from "../../../utils/CommonMethods";
import Spinner from "../../loader/Spinner";
import { AadhaarInput } from "../appointment/aadhar-input";
import { Input } from "../input";
import Modal from "../modal";
import ABHACard from "./ABHACard";
import { OtpInputs } from "./otp-inputs";
import { Buttons } from "../appointment/button";

// Define constants for flow steps
const Steps = {
  AADHAAR_INPUT: "aadhaarInput",
  VERIFY_NUMBER: "verifyNumber",
  ABHA_PRESENT: "abhaPresnt",
  ABHA_NOT_PRESENT: "abhaNotPresent",
  AADHAR_AUTHENTICATION: "aadharAuthentication",
  AADHAR_MOBILE_VERIFY: "aadharMobileVerify",
  NUMBER_REVIEW: "numberReview",
  NUMBER_NOT_SAME: "numberNotSAME",
  AADHAR_MOBILE_AUTHENTICATION: "aadharMobileAuthentication",
  CREATE_ABHA: "createAbha",
  VIEW_ABHA_CARD: "viewAbhaCard",
  NEW_ADDRESS: "newAddress",
  PATIENT_EXIST: "patientexist",
};

export default function AbhaCreation({
  addAbhaUsing,
  setAddAbhaUsing,
  patientMobile,
  setpatientMobile,
  OnSelectPatient,
  setIsCreatingAddress,
  isCreatingAddress,
  address,
  setAddress,
  user,
}) {
  const [busy, setBusy] = useState(false);
  const [currentStep, setCurrentStep] = useState(Steps.AADHAR_AUTHENTICATION);
  const [otp, setOtp] = useState(Array(6).fill(""));
  const [error, setError] = useState(false);
  const [isAadhaarvalid, setIsAadhaarValid] = useState(false);
  const [patientName, setPatientName] = useState("");
  const [aadhaar, setAadhaar] = useState(Array(3).fill(""));
  // const [address, setAddress] = useState("");
  const [abhaCard, setAbhaCard] = useState(null);
  const [mobileEnding, setMobileEnding] = useState("");
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [patientData, setPatientData] = useState(null);

  const [timer, setTimer] = useState(60);
  const [resendAttemptsMap, setResendAttemptsMap] = useState(new Map());
  const [txnId, setTxnId] = useState(null);
  const currentAttempts = resendAttemptsMap.get(aadhaar.join("")) || 0;

  const [checkboxes, setCheckboxes] = useState({
    consent1: false,
    consent2: false,
    consent3: false,
    consent4: false,
    consent5: false,
    consent6: false,
  });

  const areAllChecked = Object.values(checkboxes).every(Boolean);

  const handleCheckboxChange = (key) => {
    setCheckboxes((prev) => ({ ...prev, [key]: !prev[key] }));
    // handleError(message);
  };

  const resetState = () => {
    setCurrentStep(Steps.AADHAAR_INPUT);
    setOtp(Array(6).fill(""));
    setAadhaar(Array(3).fill(""));
    setAddress("");
    setAddAbhaUsing("");
  };

  const props = {
    otp,
    setOtp,
    aadhaar,
    setAadhaar,
    address,
    error,
    busy,
    setAddress,
    setIsCreatingAddress,
    isCreatingAddress,
    nextStep: setCurrentStep,
  };
  const handleAbhaEnrollment = async (isResend = false) => {
    if (!areAllChecked) {
      handleError("Please accept all terms to proceed.");
      return;
    }

    const { canResend, updatedMap, message } = updateResendAttempts(
      aadhaar.join(""),
      isResend,
      resendAttemptsMap
    );
    try {
      if (!canResend) {
        handleError(message);
        return;
      }
      const aadhaarNumber = aadhaar.join("");
      setBusy(true);
      let result = await createAbha(aadhaarNumber);
      if (result?.isSuccess) {
        localStorage.setItem("txnId", result.response.txnId);
        setResendAttemptsMap(updatedMap);
        setTxnId(result.response.txnId);
        const message = result.response.message;
        const match = message.match(/ending (.+)$/);
        if (match) {
          setMobileEnding(match[1]);
        }
        setCurrentStep(Steps.AADHAR_MOBILE_VERIFY);

        startTimer(60, setTimer, setIsResendDisabled);
        setOtp(Array(6).fill(""));
        setBusy(false);
      } else {
        setBusy(false);
        handleError(result?.response?.message);
      }
    } catch (error) {
      setBusy(false);
      if (
        error?.response?.data?.response?.code == "101504" ||
        error.response?.data?.response?.error?.code == "ABDM-1100"
      ) {
        handleError(
          error?.response?.data?.response?.message ||
            error?.response?.data?.response?.error?.message
        );
      }
      handleError(error.message);
    }
  };

  const enrollAbha = () => handleAbhaEnrollment(false); // Initial enrollment

  const resendAbhaEnrollment = () => handleAbhaEnrollment(true);

  const enrollByAdhar = async () => {
    try {
      setBusy(true);
      let result = await enrollByAadhar(patientMobile, otp);
      if (result.isSuccess) {
        const token = result?.response?.tokens?.token;
        localStorage.setItem("verifyUserToken", token);
        localStorage.setItem("txnId", result?.response?.txnId);

        console.log(
          "condition for mobile check",
          patientMobile,
          result.response.ABHAProfile.mobile,
          patientMobile != result.response.ABHAProfile.mobile
        );

        if (result.response.ABHAProfile.mobile === null) {
          setProfileData(result.response.ABHAProfile);
          setCurrentStep(Steps.NUMBER_NOT_SAME);
          const message = result?.response?.message;
          const match = message.match(/ending (.+)$/);
          if (match) {
            setMobileEnding(match[1]);
          }
        } else if (patientMobile != result.response.ABHAProfile.mobile) {
          setProfileData(result.response.ABHAProfile);
          setCurrentStep(Steps.NUMBER_NOT_SAME);
        } else if (result.response.ABHAProfile.mobile !== null) {
          setProfileData(result.response.ABHAProfile);
          setCurrentStep(Steps.NEW_ADDRESS);
        } else {
          handleError(
            result.response.message || "Unable to verify OTP. Please try again."
          );
        }

        setBusy(false);
        setOtp(Array(6).fill(""));
      } else {
        setBusy(false);
        handleError(result.response.message);
      }
    } catch (error) {
      console.log(error);
      setBusy(false);
      if (error?.response?.data?.response?.code == "101504") {
        handleError(error.response.data.response.message);
      } else if (
        error?.response?.data?.response?.error.code == "ABDM-1204" ||
        error.response?.data?.response?.error.code == "ABDM-1100"
      ) {
        handleError(
          "OTP Validation Failed" || error.response.data.response.error.message
        );
      } else {
        handleError(error.message);
      }
    }
  };

  const handleMobileEnrollment = async (isResend = false) => {
    const { canResend, updatedMap, message } = updateResendAttempts(
      patientMobile,
      isResend,
      resendAttemptsMap
    );
    try {
      if (!canResend) {
        handleError(message);
        return;
      }
      setBusy(true);
      let result = await enrolledByMobile(patientMobile);
      if (result.isSuccess) {
        localStorage.setItem("txnId", result.response.txnId);
        setTxnId(result.response.txnId);
        setResendAttemptsMap(updatedMap);
        startTimer(60, setTimer, setIsResendDisabled);
        setBusy(false);
      } else {
        setBusy(false);
        handleError(
          result.response.message || "unable to verify OTP. Please try again."
        );
      }
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };

  // Example usage
  const enrollByMobile = () => handleMobileEnrollment(false); // Initial enrollment
  const resendMobileEnrollment = () => handleMobileEnrollment(true);

  const EnrollAbhaAddress = async () => {
    try {
      setBusy(true);
      let result = await enrollAbhaAddress(address);
      if (result.isSuccess) {
        setAddress(result.response.preferredAbhaAddress);
        setCurrentStep(Steps.CREATE_ABHA);
        setBusy(false);
      } else {
        setBusy(false);
        handleError(result.response.message);
      }
    } catch (error) {
      console.log(error);
      console.error(error.response.data.response.error.message);
      console.error(error.response.data.response.error.code);

      if (error.response.data.response.error.code == "ABDM-1101") {
        handleError(error.response.data.response.error.message);
      } else if (!error.response?.data?.response?.isSuccess) {
        handleError(error.response.data.response.abhaAddress);
      } else {
        handleError(error.message);
      }
      setBusy(false);
    }
  };

  const verifyEnrolledMobile = async () => {
    setBusy(true);
    try {
      let result = await verifyEnrolledMobileOtp(otp);

      if (result.isSuccess) {
        localStorage.setItem("txnId", result.response.txnId);
        setTxnId(result.response.txnId);
        setBusy(false);
      }
      if (result.response.authResult === "success") {
        setPatientData({ ...profileData, mobile: patientMobile });
        localStorage.setItem("authToken", result.response.token);
        setCurrentStep(Steps.NEW_ADDRESS);
      } else {
        handleError(result.response.message);
      }
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };

  const AbhaCard = async () => {
    try {
      setBusy(true);
      let result = await fetchAbhaCard();
      setAbhaCard(result);
      setCurrentStep(Steps.VIEW_ABHA_CARD);
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };

  const handleAddPatientProfile = async (update) => {
    let data;
    try {
      let birthDay;
      if (profileData.dob) {
        birthDay = formatDateToUTC(profileData.dob);
      } else if (profileData.dateOfBirth) {
        birthDay = new Date(Date.UTC(profileData.dateOfBirth));
      } else if (
        profileData.yearOfBirth &&
        profileData.monthOfBirth &&
        profileData.dayOfBirth
      ) {
        birthDay = new Date(
          Date.UTC(
            parseInt(profileData.yearOfBirth),
            parseInt(profileData.monthOfBirth) - 1, // Month is zero-based
            parseInt(profileData.dayOfBirth)
          )
        );
      } else {
        throw new Error("Insufficient data to calculate birth date.");
      }
      let addresshouse = {
        house: profileData.address,
        city: profileData.districtName,
        district: profileData.districtName,
        state: profileData.stateName,
        pincode: profileData.pinCode || profileData.pincode,
        landmarks: profileData.stateName,
      };

      data = {
        firstName:
          `${profileData.firstName} ${profileData.middleName || ""}`.trim() ||
          profileData.name?.split(" ")[0],

        lastName: profileData.lastName || profileData.name?.split(" ")[1],
        abhaAddress: address || profileData?.phrAddress[0],
        gender: profileData.gender?.toUpperCase() === "M" ? "Male" : "Female",

        birthday: birthDay,
        address: addresshouse,
        age: CalculateAge(birthDay),
        profilePic: `data:image/jpeg;base64,${profileData?.photo}`,
        abhaNumber: profileData.ABHANumber,
        mobile: profileData.mobile || patientMobile,
      };
      setBusy(true);
      let result = await PatientProfileWithAbhaDetails(data, update);

      if (result.success) {
        if (result.isExist && !update) {
          setCurrentStep(Steps.PATIENT_EXIST);

          console.log("patient data", result.data);
          setPatientData(result.data);
        } else {
          OnSelectPatient(result.data);
          resetState();
        }
      }
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };

  const handleError = (message) => {
    setError(message);
    setTimeout(() => setError(""), 10000);
  };

  useEffect(() => {
    if (currentStep === Steps.NUMBER_NOT_SAME) {
      enrollByMobile();
    }
  }, [currentStep]);

  useEffect(() => {
    setCurrentStep(Steps.AADHAR_AUTHENTICATION);
  }, [addAbhaUsing == "abha creation"]);

  const renderStep = () => {
    switch (currentStep) {
      case Steps.AADHAR_AUTHENTICATION:
        return (
          <div>
            <p className="py-2 text-sm font-semibold text-dark">
              Aadhaar Number
            </p>
            <AadhaarInput
              aadhaar={aadhaar}
              setAadhaar={setAadhaar}
              setError={setIsAadhaarValid}
              error={isAadhaarvalid}
            />
            {isAadhaarvalid && (
              <p className="text-red-600 font-semibold">
                Aadhaar Number is not valid
              </p>
            )}

            {[
              "I authorize NHA to use my Aadhaar number for performing Aadhaar based authentication with UIDAI for the sole purpose of creation of ABHA number.",
              "I consent to usage of my ABHA address and ABHA number for linking of my legacy (past) health records and those which will be generated during this encounter.",
              "I authorize the sharing of all my health records with healthcare provider(s) for the purpose of providing healthcare services to me during this encounter.",
              "I consent to the anonymization and subsequent use of my health records for public health purposes.",
            ].map((text, index) => (
              <label
                key={index}
                className="text-sm text-dark flex items-start mb-2"
              >
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={checkboxes[`consent${index + 1}`]}
                  onChange={() => handleCheckboxChange(`consent${index + 1}`)}
                />
                {text}
              </label>
            ))}

            {/* Sub-points under the 4th point with left margin */}
            <div className="ml-8">
              <label className="text-sm text-dark flex items-start mb-2">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={checkboxes[`consent5`]}
                  onChange={() => handleCheckboxChange(`consent5`)}
                />
                I, {user?.firstName} {user?.lastName} confirm that I have duly
                informed and explained the beneficiary of the contents of
                consent for aforementioned purposes.
              </label>

              <label className="text-sm text-dark flex items-start">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={checkboxes[`consent6`]}
                  onChange={() => handleCheckboxChange(`consent6`)}
                />
                <span>
                  I,
                  <input
                    type="text"
                    className="border border-gray-300 rounded-sm p-2 mx-1 text-sm"
                    placeholder="Enter Patient Name"
                    value={patientName}
                    onChange={(e) => setPatientName(e.target.value)}
                  />
                  have been explained about the consent as stated above and
                  hereby provide my consent for the aforementioned purposes.
                </span>
              </label>
            </div>

            {error && <p className="text-red-600 font-semibold">{error}</p>}
            <button
              onClick={enrollAbha}
              className="mt-7 mx-auto h-10 w-20 flex items-center justify-center text-white bg-Primary rounded-lg"
            >
              {busy ? <Spinner show={true} /> : "Submit"}
            </button>
          </div>
        );
      case Steps.AADHAR_MOBILE_VERIFY:
        return (
          <div>
            <p className="text-sm text-dark font-semibold">
              We just sent an OTP on the Mobile Number {mobileEnding} linked
              with Aadhaar
            </p>
            <OtpInputs
              error={error}
              otp={otp}
              setOtp={setOtp}
              verify={() => setCurrentStep(Steps.NEW_ADDRESS)}
              onResendOtp={resendAbhaEnrollment}
              timer={timer}
              isResendDisabled={isResendDisabled}
              resendAttemptsCount={resendAttemptsMap.get(aadhaar.join(""))}
            />
            <div className=" text-sm font-semibold text-dark">
              Enter your communication Mobile Number*{" "}
            </div>
            <Input.text
              placeholder={"**********"}
              name="patientMobile"
              value={patientMobile}
              readOnly={false}
              maxLength="10"
              onChange={(e) => {
                setpatientMobile(e.target.value);
              }}
            />
            <footer className="flex justify-end gap-3 mt-3">
              {/* <button
                onClick={() =>{ setCurrentStep(Steps.AADHAR_AUTHENTICATION); setAadhaar("")}}
                className="mt-7 mx-auto h-10 w-20 flex items-center justify-center text-dark rounded-lg shadow-sm"
              >
                Back
              </button> */}
              <button
                onClick={enrollByAdhar}
                className="mt-7 mx-auto h-10 w-20 flex items-center justify-center text-white bg-Primary rounded-lg"
              >
                {busy ? <Spinner show={true} /> : "Submit"}
              </button>
            </footer>
          </div>
        );
      case Steps.NUMBER_NOT_SAME:
        return (
          <div>
            <p className="text-sm text-dark font-semibold">
              Please enter the OTP sent to your communication mobile number{" "}
              {patientMobile && `******${patientMobile.slice(-4)}`}
            </p>
            <OtpInputs
              error={error}
              otp={otp}
              setOtp={setOtp}
              timer={timer}
              onResendOtp={resendMobileEnrollment}
              isResendDisabled={isResendDisabled}
              resendAttemptsCount={resendAttemptsMap.get(patientMobile)}
            />
            <footer className="flex justify-end gap-3 mt-3">
              <button
                onClick={() => {
                  setCurrentStep(Steps.AADHAR_MOBILE_VERIFY);
                  setOtp("");
                }}
                className="mt-7 mx-auto h-10 w-20 flex items-center justify-center text-dark rounded-lg shadow-sm"
              >
                Back
              </button>
              <button
                onClick={verifyEnrolledMobile}
                className="mt-7 mx-auto h-10 w-20 flex items-center justify-center text-white bg-Primary rounded-lg"
              >
                {busy ? <Spinner show={true} /> : "Submit"}
              </button>
            </footer>
          </div>
        );
      case Steps.NEW_ADDRESS:
        return (
          <CreateAbhaAddress
            {...props}
            profileData={profileData}
            onComplete={() => {
              isCreatingAddress == "yes"
                ? EnrollAbhaAddress()
                : setCurrentStep(Steps.CREATE_ABHA);
            }}
          />
        );
      case Steps.CREATE_ABHA:
        return (
          <CreateAbha
            {...props}
            handleChancel={resetState}
            onComplete={() => handleAddPatientProfile(false)}
            profileData={profileData}
            onViewAbhaCard={AbhaCard}
            patientMobile={patientMobile}
          />
        );
      case Steps.VIEW_ABHA_CARD:
        return (
          <ABHACard
            {...props}
            abhaCard={abhaCard}
            onCancel={() => setCurrentStep(Steps.CREATE_ABHA)}
          />
        );
      case Steps.PATIENT_EXIST:
        return (
          <PatientUpdateAbort
            handlePatientAutoFill={(value) => {
              OnSelectPatient(value);
              resetState();
            }}
            address={address}
            handleBack={() => setCurrentStep(Steps.CREATE_ABHA)}
            onComplete={() => handleAddPatientProfile(true)}
            patientData={patientData}
            profileData={profileData}
            patientMobile={patientMobile}
          />
        );
    }
  };

  return (
    <Modal
      isOpen={addAbhaUsing === "abha creation"}
      setIsOpen={resetState}
      isCard={currentStep == Steps.VIEW_ABHA_CARD}
      classname={`font-primary ${
        currentStep == Steps.VIEW_ABHA_CARD
          ? "w-[400px] "
          : "min-w-[350px]! min-h-96!"
      }`}
    >
      {renderStep()}
    </Modal>
  );
}
