import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'HealTether.Clinics.API',
    version: '1.0.0',
    description: 'API documentation for the project',
  },
  servers: [
    {
      url:"https://api-tst-clinic.healtether.com/api"
    },
    {
      url: 'http://localhost:2222/api',  
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',  
      },
    },
  },
  security: [
    {
      bearerAuth: [],  
    },
  ],
};

const options = {
  swaggerDefinition,
  apis: ['./routes/**/*.js'], 
};

const swaggerSpec = swaggerJSDoc(options);

export { swaggerUi, swaggerSpec };
