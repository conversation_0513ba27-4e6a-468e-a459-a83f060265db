import axios from "axios";
import https from 'https'
import { LogException } from "./appinsight.config.js";

const apiEndpoint = process.env.FACEBOOK_MSG_URL;
const accessToken = process.env.FACEBOOK_ACCESS_TOKEN;
const config = {
    headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json"
    },
    httpsAgent: new https.Agent({ keepAlive: true }),
    timeout: 60000,
};


export async function sendMessageToWhatsAppAPI(data) {
    try{
        return  await axios.post(apiEndpoint, data, config);
    }
    catch(e)
    {
        LogException(e);
    }

}