import React, { useEffect, useState } from "react";
import { months } from "../../../components/detail-page/appointment/data";
import InputSelect from "../../../components/detail-page/input-select";
import { Input } from "../../../components/detail-page/input";
import Modal from "../modal";
import { Buttons } from "../appointment/button";

export default function FilterModal({ filters, timerange, isOpen, onConfirm }) {
  const [selectedFilter, setselectedFilter] = useState([]);
  const [selectedTimeRange, setselectedTimeRange] = useState("");

  useEffect(() => {
    setselectedFilter(filters);
    setselectedTimeRange(timerange);
  }, [filters, timerange])

  const record_types = [
    "Diagnostic Report",
    "Prescription",
    "Discharge Summary",
    "OP Consultation",
    "Immunization record",
    "Wellness record",
    "Health document record",
  ];
  const time_ranges = [
    "Last 3 months",
    "Last 6 months",
    "Last 12 months",
    "Custom",
  ];

  const UpdateSelectedFilter = (type, time_range) => {
    var previousFilter = [...selectedFilter];
    var alreadyPresent = previousFilter.find(o => (o.type != '' && o.type == type))
    let index = previousFilter.indexOf(alreadyPresent);
    if (index > -1) {
      previousFilter.splice(index, 1);
      setselectedFilter(previousFilter);
    }
    else {
      previousFilter.push({
        type: type,
      });
      setselectedFilter(previousFilter);
    }
  }
  const apply = () => {
    onConfirm(selectedFilter, selectedTimeRange);
  }
  const reset = () => {
    onConfirm([], "");
  }

  return (
    <Modal
      isOpen={isOpen}
      setIsOpen={() => !isOpen}
      plain
      classname={`font-primary h-fit w-full! max-w-md`}
    >
      <>
        <div className=" text-lg font-semibold text-dark">Record Type</div>
        <section className="flex flex-wrap gap-1">
          {record_types.map((name) => (
            <div
              key={name}
              onClick={() => UpdateSelectedFilter(name, '')}
              className={`${selectedFilter.find(o => o.type == name)?.type == name ? " bg-Primary text-white" : "text-color_muted"
                } h-7 px-1.5 flex items-center border border-gray rounded-md text-sm font-bold cursor-pointer duration-300`}
            >
              {name}
            </div>
          ))}
        </section>
        <div className="mt-4 text-lg font-semibold text-dark">Time range</div>
        <section className="flex flex-wrap gap-1 mb-4">
          {time_ranges.map((name) => (
            <div
              key={name}
              onClick={() => setselectedTimeRange(name)}
              className={`${selectedTimeRange == name
                ? " bg-Primary text-white"
                : "text-color_muted"
                } h-7 px-1.5 flex items-center border border-gray rounded-md text-sm font-bold cursor-pointer duration-300`}
            >
              {name}
            </div>
          ))}
        </section>
        {selectedTimeRange == "Custom" && (
          <section className=" grid grid-cols-2 gap-2">
            <div className="mt-2 text-lg font-semibold text-dark">
              Month and year
            </div>
            <div />
            <InputSelect
              bottom={true}
              placeholder={"Month"}
              data={months}
              handleClick={(prop) => console.log(prop)}
            />
            <Input.number placeholder="2024" />
          </section>
        )}
        <footer className="grid grid-cols-2 gap-2 mt-10">
          <Buttons.secondary title="Reset" onClick={reset} />
          <Buttons.primary
            disabled={selectedFilter.length > 0 ? false : selectedTimeRange != "" ? false : true}
            title="Apply"
            onClick={apply}
          />
        </footer>
      </>
    </Modal>
  );
}
