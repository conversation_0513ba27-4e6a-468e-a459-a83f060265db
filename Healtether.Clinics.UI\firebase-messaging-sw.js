importScripts(
  "https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"
);

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAa3gwGsD-RfwYrGQYbsjNWf3bWU0B4YoM",
  authDomain: "healtether-apps-tst-716e0.firebaseapp.com",
  projectId: "healtether-apps-tst-716e0",
  storageBucket: "healtether-apps-tst-716e0.firebasestorage.app",
  messagingSenderId: "878707632565",
  appId: "1:878707632565:web:8d624261b9dfd1bc2991fc"
};

// Initialize Firebase
const app = firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage(function (payload) {
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
  };

  self.registration.showNotification(notificationTitle,
    notificationOptions);
});
