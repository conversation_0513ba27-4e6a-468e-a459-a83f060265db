import { Router } from "express";
import {
  validateAppointmentUpsert,
  validateDraftR<PERSON>ord,
  validateGetAppointmentById,
  validateGetAppointmentOverview,
  validateScheduleGoogleMeet,
  validateSetAppointmentEnded,
  validateSetAppointmentStarted,
  validateWithdrawGoogleMeet,
  validateUpdateDoc,
  validateCancelAppointment,
  validateEditAppointmentAdviceNotes,
  validateGetCurrentMedicalRecord,
  validateGetPatientAppointmentById,
  validateGetTodayAppointment,
  validateReScheduleAppointment,
  validateSetFollowUpAppointment,
} from "../../validation/appointment/appointment.validation.js";
import {
  getAppointmentOverview,
  appointmentUpsert,
  getTodayAppointment,
  getAppointmentById,
  setAppointmentStarted,
  draftRecords,
  setAppointmentEnded,
  updateDoc,
  getCurrentMedicalRecord,
  reScheduleAppointment,
  cancelAppointment,
  setFollowUpAppointment,
  getPatientAppointmentById,
  editAppointmentAviceNotes,
  scheduleGoogleMeet,
  withDrawGoogleMeet,
} from "../../controllers/appointments/appointment.controller.js";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";
import multer from "multer";

const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

const appointment = Router();

/**
 * @swagger
 * /appointment/upsert:
 *   post:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Create or update an appointment
 *     description: Upserts (inserts or updates) an appointment. If an appointment ID is provided, it updates the existing appointment; otherwise, it creates a new one. Notifications are sent if the appointment is successfully created or updated. For virtual appointments, a Google Meet link is generated and sent via WhatsApp.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: ID of the appointment to update. Omit for new appointments.
 *                   name:
 *                     type: string
 *                     description: Name of the patient.
 *                   mobile:
 *                     type: string
 *                     description: Mobile number of the patient.
 *                   age:
 *                     type: integer
 *                     description: Age of the patient.
 *                   birthDate:
 *                     type: string
 *                     format: date
 *                     description: Birth date of the patient.
 *                   gender:
 *                     type: string
 *                     enum: [Male, Female, Other]
 *                     description: Gender of the patient.
 *                   appointmentDate:
 *                     type: string
 *                     format: date-time
 *                     description: Date and time of the appointment.
 *                   doctorId:
 *                     type: string
 *                     description: ID of the doctor for the appointment.
 *                   doctorName:
 *                     type: string
 *                     description: Name of the doctor.
 *                   reason:
 *                     type: string
 *                     description: Reason for the appointment.
 *                   timeSlot:
 *                     type: string
 *                     description: Time slot for the appointment.
 *                   clientId:
 *                     type: string
 *                     description: Clinic ID where the appointment is scheduled.
 *                   patientId:
 *                     type: string
 *                     description: ID of the patient. If not provided, a new patient will be created.
 *                   virtualConsultation:
 *                     type: boolean
 *                     description: Whether the appointment is virtual. If true, a Google Meet link will be generated and sent via WhatsApp.
 *                   address:
 *                     type: string
 *                     description: Address of the patient.
 *                   pincode:
 *                     type: string
 *                     description: Pincode of the patient's address.
 *                   abhaNumber:
 *                     type: string
 *                     description: ABHA number of the patient.
 *                   abhaAddress:
 *                     type: string
 *                     description: ABHA address of the patient.
 *                   speciality:
 *                     type: string
 *                     description: Speciality of the doctor.
 *                   isFollowUp:
 *                     type: boolean
 *                     description: Whether the appointment is a follow-up.
 *     responses:
 *       200:
 *         description: Appointment created or updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   description: The ID of the appointment.
 *                 patientId:
 *                   type: string
 *                   description: The ID of the patient associated with the appointment.
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Invalid request or appointment could not be processed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   description: Error message describing the issue.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   description: Error message from the server.
 */
appointment.post(
  "/upsert",
  authorizationCheck,
  validateAppointmentUpsert,
  async (req, res, next) => {
    try {
      return await appointmentUpsert(req, res);
    } catch (e) {
      next(e);
    }
  }
);
/**
 * @swagger
 * /appointment/getappointments:
 *   get:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Get appointment overview
 *     description: Retrieves an overview of appointments based on various query parameters such as clinic ID, pagination, keyword search, date, sorting, status, and doctor filter.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     parameters:
 *       - in: query
 *         name: clinicId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the clinic to retrieve appointments from
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *         example: 1
 *       - in: query
 *         name: size
 *         required: false
 *         schema:
 *           type: integer
 *         description: Number of appointments per page
 *         example: 10
 *       - in: query
 *         name: keyword
 *         required: false
 *         schema:
 *           type: string
 *         description: Keyword to search for appointments
 *         example: "John"
 *       - in: query
 *         name: date
 *         required: false
 *         schema:
 *           type: string
 *           format: date
 *         description: Date to filter appointments
 *         example: "2024-12-01"
 *       - in: query
 *         name: sortby
 *         required: false
 *         schema:
 *           type: string
 *         description: Field to sort by (e.g., appointment date)
 *         example: "appointmentDate"
 *       - in: query
 *         name: direction
 *         required: false
 *         schema:
 *           type: string
 *         description: Sorting direction (asc or desc)
 *         example: "asc"
 *       - in: query
 *         name: status
 *         required: false
 *         schema:
 *           type: string
 *         description: Status of appointments to filter by (e.g., completed, pending)
 *         example: "completed"
 *       - in: query
 *         name: doctor
 *         required: false
 *         schema:
 *           type: string
 *         description: Doctor ID to filter appointments
 *         example: "doctor123"
 *     responses:
 *       200:
 *         description: Successfully retrieved the appointment overview
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: Appointment ID
 *                         example: "60d5ec49c67e4d8f889a4d65"
 *                       patientName:
 *                         type: string
 *                         description: Name of the patient
 *                         example: "John Doe"
 *                       appointmentDate:
 *                         type: string
 *                         format: date-time
 *                         description: Date and time of the appointment
 *                         example: "2024-12-01T10:00:00Z"
 *                       status:
 *                         type: string
 *                         description: Status of the appointment
 *                         example: "completed"
 *                 totalPages:
 *                   type: integer
 *                   description: Total number of pages available
 *                   example: 5
 *                 currentPage:
 *                   type: integer
 *                   description: Current page number
 *                   example: 1
 *       400:
 *         description: Bad request, invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid query parameters"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while fetching the overview"
 */

appointment.get(
  "/getappointments",
  authorizationCheck,
  validateGetAppointmentOverview,
  async (req, res, next) => {
    try {
      return await getAppointmentOverview(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/getappointmentbyid:
 *   get:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Get appointment by ID
 *     description: Retrieves appointment details along with patient information based on the provided appointment ID.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the appointment to retrieve
 *         example: "60d5ec49c67e4d8f889a4d65"
 *     responses:
 *       200:
 *         description: Successfully retrieved the appointment details with patient information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   description: The ID of the appointment
 *                   example: "60d5ec49c67e4d8f889a4d65"
 *                 mobile:
 *                   type: string
 *                   description: Mobile number of the patient
 *                   example: "+**********"
 *                 name:
 *                   type: string
 *                   description: Name of the patient
 *                   example: "John Doe"
 *                 gender:
 *                   type: string
 *                   description: Gender of the patient
 *                   example: "Male"
 *                 age:
 *                   type: number
 *                   description: Age of the patient
 *                   example: 30
 *                 birthDate:
 *                   type: string
 *                   format: date-time
 *                   description: Birth date of the patient
 *                   example: "1990-01-01T00:00:00Z"
 *                 appointmentDate:
 *                   type: string
 *                   format: date-time
 *                   description: Date and time of the appointment
 *                   example: "2024-12-01T10:00:00Z"
 *                 timeSlot:
 *                   type: string
 *                   description: Time slot of the appointment
 *                   example: "10:00 AM - 11:00 AM"
 *                 reason:
 *                   type: string
 *                   description: Reason for the appointment
 *                   example: "General Checkup"
 *                 virtualConsultation:
 *                   type: boolean
 *                   description: Indicates if the consultation is virtual
 *                   example: true
 *                 doctorName:
 *                   type: string
 *                   description: Name of the doctor
 *                   example: "Dr. Smith"
 *                 doctorId:
 *                   type: string
 *                   description: ID of the doctor
 *                   example: "60d5ec49c67e4d8f889a4d66"
 *                 patientId:
 *                   type: string
 *                   description: ID of the patient
 *                   example: "60d5ec49c67e4d8f889a4d67"
 *                 abhaAddress:
 *                   type: string
 *                   description: ABHA address of the patient
 *                   example: "john.doe@abdm"
 *                 abhaNumber:
 *                   type: string
 *                   description: ABHA number of the patient
 *                   example: "**********12"
 *                 address:
 *                   type: string
 *                   description: Address of the patient
 *                   example: "123 Main St, City, Country"
 *                 pincode:
 *                   type: string
 *                   description: Pincode of the patient's address
 *                   example: "123456"
 *                 speciality:
 *                   type: string
 *                   description: Speciality of the doctor
 *                   example: "Cardiology"
 *                 clinic:
 *                   type: string
 *                   description: Name of the clinic
 *                   example: "City Clinic"
 *                 medicalRecords:
 *                   type: array
 *                   description: List of medical records
 *                   items:
 *                     type: string
 *                   example: ["record1", "record2"]
 *                 procedureRecords:
 *                   type: array
 *                   description: List of procedure records
 *                   items:
 *                     type: string
 *                   example: ["procedure1", "procedure2"]
 *                 prescriptionRecords:
 *                   type: array
 *                   description: List of prescription records
 *                   items:
 *                     type: string
 *                   example: ["prescription1", "prescription2"]
 *                 started:
 *                   type: boolean
 *                   description: Indicates if the appointment has started
 *                   example: true
 *                 ended:
 *                   type: boolean
 *                   description: Indicates if the appointment has ended
 *                   example: false
 *                 patientDetails:
 *                   type: object
 *                   description: Detailed information about the patient
 *                   properties:
 *                     firstName:
 *                       type: string
 *                       description: First name of the patient
 *                       example: "John"
 *                     lastName:
 *                       type: string
 *                       description: Last name of the patient
 *                       example: "Doe"
 *                     age:
 *                       type: number
 *                       description: Age of the patient
 *                       example: 30
 *                     birthday:
 *                       type: string
 *                       format: date-time
 *                       description: Birth date of the patient
 *                       example: "1990-01-01T00:00:00Z"
 *                     gender:
 *                       type: string
 *                       description: Gender of the patient
 *                       example: "Male"
 *                     mobile:
 *                       type: string
 *                       description: Mobile number of the patient
 *                       example: "+**********"
 *                     email:
 *                       type: string
 *                       description: Email address of the patient
 *                       example: "<EMAIL>"
 *                     patientId:
 *                       type: string
 *                       description: ID of the patient
 *                       example: "60d5ec49c67e4d8f889a4d67"
 *                     abhaNumber:
 *                       type: string
 *                       description: ABHA number of the patient
 *                       example: "**********12"
 *                     abhaAddress:
 *                       type: string
 *                       description: ABHA address of the patient
 *                       example: "john.doe@abdm"
 *                     address:
 *                       type: string
 *                       description: Address of the patient
 *                       example: "123 Main St, City, Country"
 *                     height:
 *                       type: number
 *                       description: Height of the patient
 *                       example: 175
 *                     weight:
 *                       type: number
 *                       description: Weight of the patient
 *                       example: 70
 *                     documentType:
 *                       type: string
 *                       description: Type of document
 *                       example: "Passport"
 *                     documentNumber:
 *                       type: string
 *                       description: Document number
 *                       example: "A12345678"
 *                     documents:
 *                       type: array
 *                       description: List of documents
 *                       items:
 *                         type: string
 *                       example: ["doc1", "doc2"]
 *                     appointments:
 *                       type: array
 *                       description: List of appointments
 *                       items:
 *                         type: string
 *                       example: ["appointment1", "appointment2"]
 *       400:
 *         description: Bad request, invalid or missing appointment ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Appointment ID is required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while fetching the appointment details"
 */

appointment.get(
  "/getappointmentbyid",
  authorizationCheck,
  validateGetAppointmentById,
  async (req, res, next) => {
    try {
      return await getAppointmentById(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/startconsultation:
 *   post:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Set appointment as started
 *     description: Marks the appointment as started based on the appointment ID and user information.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The ID of the appointment to mark as started
 *                     example: "60d5ec49c67e4d8f889a4d65"
 *     responses:
 *       200:
 *         description: Successfully marked the appointment as started
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   description: The ID of the appointment
 *                   example: "60d5ec49c67e4d8f889a4d65"
 *                 status:
 *                   type: string
 *                   description: Status of the appointment
 *                   example: "started"
 *                 startedBy:
 *                   type: string
 *                   description: The user who started the appointment
 *                   example: "user123"
 *       400:
 *         description: Bad request, invalid or missing appointment ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Appointment ID is required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while marking the appointment as started"
 */

appointment.post(
  "/startconsultation",
  authorizationCheck,
  validateSetAppointmentStarted,
  async (req, res, next) => {
    try {
      return await setAppointmentStarted(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/endconsultation:
 *   post:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Set appointment as ended and remove associated blobs
 *     description: Marks the appointment as ended by updating the appointment status and removing any associated blob files in Azure Blob Storage.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The ID of the appointment to be marked as ended.
 *                     example: "60d5ec49c67e4d8f889a4d65"
 *                   clientId:
 *                     type: string
 *                     description: The client ID associated with the appointment.
 *                     example: "123456"
 *                   removeRecords:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of blob records to remove from Azure Blob Storage.
 *                     example: [ "blob1", "blob2" ]
 *     responses:
 *       200:
 *         description: Successfully marked the appointment as ended and removed blobs.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indicates if the operation was successful
 *                   example: true
 *                 appointments:
 *                   type: array
 *                   description: The updated appointments after marking as ended.
 *                   items:
 *                     type: object
 *                     properties:
 *                       appointmentId:
 *                         type: string
 *                         description: The ID of the appointment.
 *                         example: "60d5ec49c67e4d8f889a4d65"
 *                       status:
 *                         type: string
 *                         description: Status of the appointment.
 *                         example: "ended"
 *       400:
 *         description: Bad request, invalid or missing appointment ID or client ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Appointment ID and client ID are required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while marking the appointment as ended"
 */
appointment.post(
  "/endconsultation",
  authorizationCheck,
  validateSetAppointmentEnded,
  async (req, res, next) => {
    try {
      return await setAppointmentEnded(req, res);
    } catch (e) {
      next(e);
    }
  }
);
/**
 * @swagger
 * /appointment/draftrecords:
 *   post:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Update draft records and remove associated blobs
 *     description: Updates the appointment records with draft data and removes associated blob files from Azure Blob Storage.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The ID of the appointment to update
 *                     example: "6751673b6a1cab1423faf1af"
 *                   clientId:
 *                     type: string
 *                     description: The client ID associated with the appointment
 *                     example: "662ca0a41a2431e16c41ebaa"
 *                   removeRecords:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of blob records to remove from Azure Blob Storage
 *                     example: ["xK2_g7B4MLCPJBL4-_i9L.xlsx"]
 *                   medicalRecords:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         fileName:
 *                           type: string
 *                           example: "MedicalReport.pdf"
 *                         blobName:
 *                           type: string
 *                           example: "abc123.pdf"
 *                   procedureRecords:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         fileName:
 *                           type: string
 *                           example: "ProcedureReport.pdf"
 *                         blobName:
 *                           type: string
 *                           example: "xyz456.pdf"
 *                   prescriptionRecords:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         fileName:
 *                           type: string
 *                           example: "Prescription.pdf"
 *                         blobName:
 *                           type: string
 *                           example: "pres789.pdf"
 *     responses:
 *       200:
 *         description: Successfully updated the draft records and removed blobs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indicates if the operation was successful
 *                   example: true
 *                 appointments:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: The ID of the updated appointment
 *                       example: "6751673b6a1cab1423faf1af"
 *                     modified:
 *                       type: object
 *                       properties:
 *                         on:
 *                           type: string
 *                           description: Modification timestamp
 *                           example: "2024-12-05T08:55:27.007Z"
 *                         by:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               example: "662ca0ab1a2431e16c41ebae"
 *                             name:
 *                               type: string
 *                               example: "Venkatesh Raja"
 *       400:
 *         description: Bad request, invalid or missing appointment ID or client ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Appointment ID and client ID are required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while updating the draft records"
 */

appointment.post(
  "/draftrecords",
  authorizationCheck,
  validateDraftRecord,
  async (req, res, next) => {
    try {
      return await draftRecords(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/schedule-google-meet:
 *   post:
 *     tags:
 *       - appointment
 *     summary: Schedule a Google Meet meeting for an appointment
 *     description: Schedules a Google Meet session between a doctor and a patient based on the provided data.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   doctorEmail:
 *                     type: string
 *                     description: The email address of the doctor
 *                     example: "<EMAIL>"
 *                   patientEmail:
 *                     type: string
 *                     description: The email address of the patient
 *                     example: "<EMAIL>"
 *                   doctorName:
 *                     type: string
 *                     description: The name of the doctor
 *                     example: "Dr. John Doe"
 *                   meetingDescription:
 *                     type: string
 *                     description: The description of the meeting
 *                     example: "Consultation with the doctor"
 *                   scheduleTime:
 *                     type: string
 *                     format: date-time
 *                     description: The scheduled time for the meeting (in ISO 8601 format)
 *                     example: "2024-10-25T10:00:00Z"
 *                   scheduleDuration:
 *                     type: integer
 *                     description: The duration of the meeting in minutes
 *                     example: 30
 *                   appointmentID:
 *                     type: string
 *                     description: The ID of the appointment
 *                     example: "12345"
 *     responses:
 *       200:
 *         description: Successfully scheduled the Google Meet meeting
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indicates if the operation was successful
 *                   example: true
 *                 link:
 *                   type: string
 *                   description: The Google Meet link for the meeting
 *                   example: "https://meet.google.com/xyz-abc"
 *                 id:
 *                   type: string
 *                   description: The ID of the scheduled meeting
 *                   example: "meet123"
 *       400:
 *         description: Bad request or error occurred while scheduling the meeting
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Unable to schedule the meeting"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An unexpected error occurred"
 */
appointment.post(
  "/schedule-google-meet",
  validateScheduleGoogleMeet,
  async (req, res, next) => {
    try {
      return await scheduleGoogleMeet(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/withdraw-google-meet:
 *   post:
 *     tags:
 *       - appointment
 *     summary: Withdraw a scheduled Google Meet meeting
 *     description: Cancels a scheduled Google Meet session using the event ID provided in the request.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   eventID:
 *                     type: string
 *                     description: The ID of the Google Meet event to cancel
 *                     example: "abc123xyz"
 *     responses:
 *       200:
 *         description: Successfully withdrew the scheduled Google Meet meeting
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indicates if the operation was successful
 *                   example: true
 *       400:
 *         description: Bad request or error occurred while withdrawing the meeting
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Unable to cancel the meeting"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An unexpected error occurred"
 */
appointment.post(
  "/withdraw-google-meet",
  validateWithdrawGoogleMeet,
  async (req, res, next) => {
    try {
      return await withDrawGoogleMeet(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/updatedocument:
 *   post:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Upload and update patient documents
 *     description: |
 *       Updates patient documents including procedure, medical, and prescription documents. These files are uploaded to Azure Blob storage.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               procedureName:
 *                 type: string
 *                 description: A JSON string containing document names and corresponding blob names for procedure documents.
 *                 example: '[{"fileName": "procedure1.pdf", "blobName": "procedure1_blob"}]'
 *               medicalName:
 *                 type: string
 *                 description: A JSON string containing document names and corresponding blob names for medical documents.
 *                 example: '[{"fileName": "medical1.pdf", "blobName": "medical1_blob"}]'
 *               prescriptionName:
 *                 type: string
 *                 description: A JSON string containing document names and corresponding blob names for prescription documents.
 *                 example: '[{"fileName": "prescription1.pdf", "blobName": "prescription1_blob"}]'
 *               procedure:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Array of procedure files to be uploaded.
 *               medical:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Array of medical files to be uploaded.
 *               prescription:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Array of prescription files to be uploaded.
 *               clientId:
 *                 type: string
 *                 description: The client ID to associate the uploaded documents with.
 *                 example: "**********abcdef12345678"
 *     responses:
 *       200:
 *         description: Documents updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Unexpected error during document upload.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unexpected error during document upload."
 */

appointment.post(
  "/updatedocument",
  upload.fields([
    {
      name: "procedure",
    },
    {
      name: "medical",
    },
    {
      name: "prescription",
    },
    {
      name: "prescriptionReport",
    },
    {
      name: "invoiceReport",
    },
     {
      name: "vaccineCertificate",
    },
  ]),
  validateUpdateDoc,
  async (req, res, next) => {
    try {
      return await updateDoc(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/getcurrentrecords:
 *   get:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Retrieve current medical record
 *     description: Fetches the current medical record for a specific appointment based on the appointment ID.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the appointment for which to retrieve the medical record
 *         example: "60d5ec49c67e4d8f889a4d65"
 *     responses:
 *       200:
 *         description: Successfully retrieved the current medical record
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 appointmentId:
 *                   type: string
 *                   description: The ID of the appointment
 *                   example: "60d5ec49c67e4d8f889a4d65"
 *                 medicalRecord:
 *                   type: object
 *                   description: The details of the current medical record
 *                   properties:
 *                     condition:
 *                       type: string
 *                       description: The medical condition
 *                       example: "Hypertension"
 *                     treatment:
 *                       type: string
 *                       description: Treatment details
 *                       example: "Prescribed medication for blood pressure control"
 *       400:
 *         description: Bad request, invalid or missing appointment ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Appointment ID is required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while retrieving the medical record"
 */

appointment.get(
  "/getcurrentrecords",
  authorizationCheck,
  validateGetCurrentMedicalRecord,
  async (req, res, next) => {
    try {
      return await getCurrentMedicalRecord(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/update-advice-notes/:id:
 *   put:
 *     tags:
 *       - appointment
 *     summary: Edit appointment advice and notes
 *     description: Updates the advice and notes for a specific appointment based on the appointment ID.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the appointment to update
 *         example: "60d5ec49c67e4d8f889a4d65"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               advice:
 *                 type: string
 *                 description: The advice given for the appointment
 *                 example: "Follow a low-sodium diet"
 *               notes:
 *                 type: string
 *                 description: Additional notes regarding the appointment
 *                 example: "Patient should monitor blood pressure daily."
 *     responses:
 *       200:
 *         description: Successfully updated the appointment advice and notes
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                   example: "Appointment updated successfully"
 *                 appointment:
 *                   type: object
 *                   description: The updated appointment details
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: The ID of the appointment
 *                       example: "60d5ec49c67e4d8f889a4d65"
 *                     advice:
 *                       type: string
 *                       description: The updated advice
 *                       example: "Follow a low-sodium diet"
 *                     notes:
 *                       type: string
 *                       description: The updated notes
 *                       example: "Patient should monitor blood pressure daily."
 *       400:
 *         description: Bad request, invalid or missing appointment ID or data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong"
 *                 message:
 *                   type: string
 *                   example: "Invalid appointment ID or data"
 *       404:
 *         description: Appointment not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "appointment not found"
 *                 message:
 *                   type: string
 *                   example: "Something went wrong"
 */
appointment.put(
  "/update-advice-notes/:id",
  authorizationCheck,
  validateEditAppointmentAdviceNotes,
  async (req, res, next) => {
    try {
      return await editAppointmentAviceNotes(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/reschedule:
 *   post:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Reschedule an existing appointment
 *     description: Updates the appointment date and time slot for a specific appointment ID. Sends a notification if rescheduled successfully.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The ID of the appointment to reschedule
 *                     example: "60d5ec49c67e4d8f889a4d65"
 *                   appointmentDate:
 *                     type: string
 *                     format: date-time
 *                     description: The new date for the appointment
 *                     example: "2024-10-30T10:00:00Z"
 *                   timeSlot:
 *                     type: string
 *                     description: The new time slot for the appointment
 *                     example: "10:00 AM - 10:30 AM"
 *     responses:
 *       200:
 *         description: Appointment successfully rescheduled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Appointment rescheduled successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: The ID of the rescheduled appointment
 *                       example: "60d5ec49c67e4d8f889a4d65"
 *                     patientId:
 *                       type: string
 *                       description: The ID of the patient associated with the appointment
 *                       example: "12345"
 *       400:
 *         description: Bad request, appointment is already started or invalid data provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Already in Started"
 *       404:
 *         description: Appointment not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "appointment not found"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
appointment.post(
  "/reschedule",
  authorizationCheck,
  validateReScheduleAppointment,
  async (req, res, next) => {
    try {
      return await reScheduleAppointment(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/cancelled:
 *   post:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Cancel an existing appointment
 *     description: Marks an appointment as canceled based on the provided appointment ID. Sends a notification if cancellation is successful.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The ID of the appointment to cancel
 *                     example: "60d5ec49c67e4d8f889a4d65"
 *     responses:
 *       200:
 *         description: Appointment successfully canceled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Appointment canceled successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: The ID of the canceled appointment
 *                       example: "60d5ec49c67e4d8f889a4d65"
 *       400:
 *         description: Bad request, appointment is already started or invalid data provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Already in Started"
 *       404:
 *         description: Appointment not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "appointment not found"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
appointment.post(
  "/cancelled",
  authorizationCheck,
  validateCancelAppointment,
  async (req, res, next) => {
    try {
      return await cancelAppointment(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/followup:
 *   post:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Schedule a follow-up appointment
 *     description: Sets a follow-up appointment based on the provided appointment ID and data. Sends a notification if successful.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The ID of the original appointment
 *                     example: "60d5ec49c67e4d8f889a4d65"
 *                   appointmentDate:
 *                     type: string
 *                     format: date-time
 *                     description: The date of the follow-up appointment
 *                     example: "2024-12-01T10:00:00Z"
 *                   timeSlot:
 *                     type: string
 *                     description: The time slot for the follow-up appointment
 *                     example: "10:00 AM - 11:00 AM"
 *     responses:
 *       200:
 *         description: Follow-up appointment successfully scheduled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: The ID of the new follow-up appointment
 *                       example: "60d5ec49c67e4d8f889a4d67"
 *       400:
 *         description: Bad request, appointment is already started or invalid data provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Already in Started"
 *       404:
 *         description: Appointment not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "appointment not found"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

appointment.post(
  "/followup",
  authorizationCheck,
  validateSetFollowUpAppointment,
  async (req, res, next) => {
    try {
      return await setFollowUpAppointment(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/getappointmentconsultation:
 *   get:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Retrieve appointment details for a specific patient
 *     description: Fetches appointment details using the appointment ID provided in the query string.
 *     security:
 *       - bearerAuth: []  # Adds the Bearer token authentication
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the appointment to retrieve
 *         example: "60d5ec49c67e4d8f889a4d65"
 *     responses:
 *       200:
 *         description: Appointment details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     mobile:
 *                       type: string
 *                       example: "**********"
 *                     name:
 *                       type: string
 *                       example: "John Doe"
 *                     gender:
 *                       type: string
 *                       example: "Male"
 *                     age:
 *                       type: integer
 *                       example: 30
 *                     birthDate:
 *                       type: string
 *                       format: date
 *                       example: "1994-05-15"
 *                     appointmentDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-12-01T10:00:00Z"
 *                     timeSlot:
 *                       type: string
 *                       example: "10:00 AM - 11:00 AM"
 *                     reason:
 *                       type: string
 *                       example: "Follow-up check-up"
 *                     virtualConsultation:
 *                       type: boolean
 *                       example: false
 *                     doctorName:
 *                       type: string
 *                       example: "Dr. Smith"
 *                     doctorId:
 *                       type: string
 *                       example: "60d5ec49c67e4d8f889a4d60"
 *                     patientId:
 *                       type: string
 *                       example: "60d5ec49c67e4d8f889a4d61"
 *                     medicalRecords:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           recordId:
 *                             type: string
 *                             example: "60d5ec49c67e4d8f889a4d70"
 *                           details:
 *                             type: string
 *                             example: "Blood test results"
 *                     procedureRecords:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           procedureId:
 *                             type: string
 *                             example: "60d5ec49c67e4d8f889a4d80"
 *                           procedureName:
 *                             type: string
 *                             example: "X-ray"
 *                     prescriptionRecords:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           prescriptionId:
 *                             type: string
 *                             example: "60d5ec49c67e4d8f889a4d90"
 *                           medication:
 *                             type: string
 *                             example: "Amoxicillin"
 *                     started:
 *                       type: boolean
 *                       example: true
 *                     ended:
 *                       type: boolean
 *                       example: false
 *       400:
 *         description: Bad request, appointment ID is missing or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid appointment ID"
 *       404:
 *         description: Appointment not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Appointment not found"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

appointment.get(
  "/getappointmentconsultation",
  authorizationCheck,
  validateGetPatientAppointmentById,
  async (req, res, next) => {
    try {
      return await getPatientAppointmentById(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/getappointmentcount:
 *   get:
 *     tags:
 *       - appointment (mobile)
 *       - appointment
 *     summary: Get today's appointments for a clinic
 *     description: Retrieves all appointments for today in the specified clinic, including the number of received (incomplete) and completed appointments.
 *     parameters:
 *       - in: query
 *         name: clinicId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the clinic to get today's appointments for.
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: The date for which to fetch appointments formate YYYY-MM-DD.
 *     responses:
 *       200:
 *         description: A list of today's appointments with received and completed counts.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 received:
 *                   type: integer
 *                   example: 5
 *                 completed:
 *                   type: integer
 *                   example: 10
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       appointmentId:
 *                         type: string
 *                         example: "**********abcdef12345678"
 *                       patientName:
 *                         type: string
 *                         example: "John Doe"
 *                       paymentStatus:
 *                         type: boolean
 *                         example: true
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
appointment.get(
  "/getappointmentcount",
  authorizationCheck,
  validateGetTodayAppointment,
  async (req, res, next) => {
    try {
      return await getTodayAppointment(req, res);
    } catch (e) {
      next(e);
    }
  }
);
export default appointment;
