import multer from "multer";
import {
  validateClientUpsert,
  validateClientSetting,
  validateGetClientOverview,
  validateGetClient,
  validateGeneratePatientId,
  validateGenerateStaffId,
  validateGenerateInvoiceId,
  validateGetClinicTimeSlots,
  validateCreateClinicGroup,
  validateGetAllClinicGroup,
  validateGetAllClinics,
} from "./../../validation/clinic/clinic.validation.js";
import {
  clientSetting,
  clientUpsert,
  generateInvoiceId,
  generatePatientId,
  generateStaffId,
  getAllClinics,
  getClient,
  getClientOverview,
  getClinicTimeSlots,
  updateDoc,
} from "../../controllers/clinic/client.controller.js";
import { Router } from "express";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";
import {
  createClinicGroup,
  getAllClinicGroup,
} from "../../controllers/clinic/group.controller.js";

const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

const client = Router();



// client.delete("/deletestaff", DeleteStaff); client.get("/searchstaffname",
// SearchStaffName);

/**
 * @swagger
 * /clinic/upsert:
 *   post:
 *     tags:
 *       - clinic
 *     summary: Upsert Client Information
 *     description: Creates a new client or updates an existing client based on the provided ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: The ID of the client to update (if exists).
 *                 example: "60d0fe4f5311236168a109ca"
 *               value:
 *                 type: object
 *                 properties:
 *                   ClinicName:
 *                     type: string
 *                     example: "Healthcare Clinic"
 *                   Address:
 *                     type: string
 *                     example: "123 Health St, Wellness City"
 *                   PatientId_Prefix:
 *                     type: string
 *                     example: "PT"
 *                   PatientId_Suffix:
 *                     type: string
 *                     example: "0001"
 *                   StaffId_Prefix:
 *                     type: string
 *                     example: "ST"
 *                   StaffId_Suffix:
 *                     type: string
 *                     example: "0001"
 *                   Phonepe_MerchantId:
 *                     type: string
 *                     example: "merchant_123"
 *                   Phonepe_SaltKey:
 *                     type: string
 *                     example: "salt_key_example"
 *                   Phonepe_SaltIndex:
 *                     type: string
 *                     example: "salt_index_example"
 *                   TimeSlots:
 *                     type: string
 *                     description: JSON formatted string containing an array of time slot objects.
 *                     example: "[{\"startTime\":{\"hours\":3,\"min\":0,\"tt\":\"PM\"},\"endTime\":{\"hours\":5,\"min\":59,\"tt\":\"PM\"}}]"
 *                   LogoName:
 *                     type: string
 *                     example: "logo.png"
 *                   AdminFirstName:
 *                     type: string
 *                     example: "John"
 *                   AdminLastName:
 *                     type: string
 *                     example: "Doe"
 *                   AdminMobile:
 *                     type: string
 *                     example: "9876543210"
 *                   AdminEmail:
 *                     type: string
 *                     example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: Successfully upserted client information.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Bad Request. Invalid input.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid input data."
 *       500:
 *         description: Internal Server Error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal server error."
 */

client.post("/upsert", authorizationCheck,
  validateClientUpsert,
  async (req, res, next) => {
   try {
       return await clientUpsert(req, res);
   }
   catch (e) {
       next(e)
   }
 });

 /**
* @swagger
* /clinic/settings:
*   put:
*     tags:
*       - clinic
*     summary: Update Client Settings
*     description: Updates the settings for a specific client based on the provided ID and data.
*     requestBody:
*       required: true
*       content:
*         application/json:
*           schema:
*             type: object
*             properties:
*               id:
*                 type: string
*                 description: The ID of the client whose settings are to be updated.
*                 example: "60d0fe4f5311236168a109ca"
*               value:
*                 type: object
*                 properties:
*                   PatientId_Prefix:
*                     type: string
*                     example: "PT"
*                   PatientId_Suffix:
*                     type: string
*                     example: "0001"
*                   StaffId_Prefix:
*                     type: string
*                     example: "ST"
*                   StaffId_Suffix:
*                     type: string
*                     example: "0001"
*                   Phonepe_MerchantId:
*                     type: string
*                     example: "merchant_123"
*                   Phonepe_SaltKey:
*                     type: string
*                     example: "salt_key_example"
*                   Phonepe_SaltIndex:
*                     type: string
*                     example: "salt_index_example"
*                   TimeSlots:
*                     type: string
*                     description: JSON formatted string containing an array of time slot objects.
*                     example: "[{\"startTime\":{\"hours\":3,\"min\":0,\"tt\":\"PM\"},\"endTime\":{\"hours\":5,\"min\":59,\"tt\":\"PM\"}}]"
*     responses:
*       200:
*         description: Successfully updated client settings.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: true
*       400:
*         description: Bad Request. Invalid input data.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 message:
*                   type: string
*                   example: "Invalid input data."
*       404:
*         description: Client not found.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 message:
*                   type: string
*                   example: "Client not found."
*       500:
*         description: Internal Server Error.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 message:
*                   type: string
*                   example: "Internal server error."
*/

client.put(
  "/updatesetting",
  authorizationCheck,
  validateClientSetting,
  async (req, res, next) => {
    try {
        return await clientSetting(req, res);
    }
    catch (e) {
        next(e)
    }
  }  
  
 );

/**
* @swagger
* /clinic/updatedocument:
*   post:
*     tags:
*       - clinic
*     summary: Update Document
*     description: Updates a document by uploading a logo file and saving the relevant data.
*     requestBody:
*       required: true
*       content:
*         multipart/form-data:
*           schema:
*             type: object
*             properties:
*               Logo:
*                 type: array
*                 items:
*                   type: string
*                   format: binary
*                 description: The logo file to be uploaded.
*               logoName:
*                 type: string
*                 description: The name to save the logo as in the blob storage.
*                 example: "client_logo.png"
*     responses:
*       200:
*         description: Successfully updated the document.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: true
*       400:
*         description: Bad Request. Invalid input data.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 message:
*                   type: string
*                   example: "Invalid input data."
*       500:
*         description: Internal Server Error.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Internal Server Error"
*/


client.post(
  "/updatedocument",
  authorizationCheck,
  upload.fields([
    {
      name: "Logo",
      maxCount: 1,
    },
  ]),
  async (req, res, next) => {
    try {
        return await updateDoc(req, res);
    }
    catch (e) {
        next(e)
    }
  } 
  
 );

client.get(
  "/getclinictimeslots",
  authorizationCheck,
  validateGetClinicTimeSlots,
  async (req, res, next) => {
    try {
        return await getClinicTimeSlots(req, res);
    }
    catch (e) {
        next(e)
    }
  } 
  
 );

/**
* @swagger
* /clinic/getclients:
*   get:
*     tags:
*       - clinic
*     summary: Get Client Overview
*     description: Fetches a paginated overview of clients, with an optional keyword search.
*     parameters:
*       - in: query
*         name: page
*         required: true
*         schema:
*           type: integer
*         description: The page number to retrieve.
*         example: 0
*       - in: query
*         name: size
*         required: true
*         schema:
*           type: integer
*         description: The number of clients to retrieve per page.
*         example: 10
*       - in: query
*         name: keyword
*         required: false
*         schema:
*           type: string
*         description: A keyword to search clients by their clinic name.
*         example: "Health"
*     responses:
*       200:
*         description: Successfully fetched client overview.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 data:
*                   type: array
*                   items:
*                     type: object
*                     properties:
*                       _id:
*                         type: string
*                         example: "60d0fe4f5311236168a109ca"
*                       clinicName:
*                         type: string
*                         example: "Health Clinic"
*                       adminUserId:
*                         type: object
*                         properties:
*                           firstName:
*                             type: string
*                             example: "John"
*                           lastName:
*                             type: string
*                             example: "Doe"
*                           email:
*                             type: string
*                             example: "<EMAIL>"
*                           mobile:
*                             type: string
*                             example: "+**********"
*                 totalCount:
*                   type: integer
*                   example: 100
*       500:
*         description: Internal server error
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Internal Server Error"
*/

 client.get(
  "/getclients",
  authorizationCheck,
  validateGetClientOverview,
  async (req, res, next) => {
    try {
        return await getClientOverview(req, res);
    }
    catch (e) {
        next(e)
    }
  } 
  
 );

/**
* @swagger
* /clinic/getclient:
*   get:
*     tags:
*       - clinic
*     summary: Get Client Details
*     description: Fetches detailed information of a client by its ID.
*     parameters:
*       - in: query
*         name: id
*         required: true
*         schema:
*           type: string
*         description: The ID of the client to retrieve.
*         example: "60d0fe4f5311236168a109ca"
*     responses:
*       200:
*         description: Successfully fetched the client details.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 _id:
*                   type: string
*                   example: "60d0fe4f5311236168a109ca"
*                 clinicName:
*                   type: string
*                   example: "Health Clinic"
*                 address:
*                   type: string
*                   example: "1234 Elm St, Springfield, IL"
*                 adminUserId:
*                   type: object
*                   properties:
*                     firstName:
*                       type: string
*                       example: "John"
*                     lastName:
*                       type: string
*                       example: "Doe"
*                     email:
*                       type: string
*                       example: "<EMAIL>"
*                     mobile:
*                       type: string
*                       example: "+**********"
*                 phonepeSetting:
*                   type: object
*                   properties:
*                     merchantId:
*                       type: string
*                       example: "12345"
*                     saltKey:
*                       type: string
*                       example: "sdfg$%^&*sdg"
*                     saltIndex:
*                       type: integer
*                       example: 1
*                 timeSlots:
*                   type: array
*                   items:
*                     type: string
*                   example: ["09:00-10:00", "14:00-15:00"]
*       500:
*         description: Internal server error
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Internal Server Error"
*/

 client.get("/getclient", authorizationCheck, validateGetClient, getClient);
 
/**
* @swagger
* /clinic/getclinicpatientid:
*   get:
*     tags:
*       - clinic
*     summary: Generate Patient ID
*     description: Fetches the patient ID settings for a clinic by its ID and generates a new patient ID.
*     parameters:
*       - in: query
*         name: id
*         required: true
*         schema:
*           type: string
*         description: The ID of the clinic to retrieve the patient ID settings for.
*         example: "60d0fe4f5311236168a109ca"
*     responses:
*       200:
*         description: Successfully fetched and generated the patient ID.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 patientId:
*                   type: object
*                   properties:
*                     prefix:
*                       type: string
*                       example: "PAT"
*                     suffix:
*                       type: string
*                       example: "21"
*                     currentPatientId:
*                       type: integer
*                       example: 1024
*       404:
*         description: Clinic not found
*         content:
*           application/json:
*             schema:
*               type: string
*               example: ""
*       500:
*         description: Internal server error
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Internal Server Error"
*/

client.get(
  "/getclinicpatientid",
  authorizationCheck,
  validateGeneratePatientId,
  async (req, res, next) => {
    try {
        return await generatePatientId(req, res);
    }
    catch (e) {
        next(e)
    }
  } 
  
 );

/**
* @swagger
* /clinic/getclinicstaffid:
*   get:
*     tags:
*       - clinic
*     summary: Generate Staff ID
*     description: Fetches the staff ID settings for a clinic by its ID and generates a new staff ID.
*     parameters:
*       - in: query
*         name: id
*         required: true
*         schema:
*           type: string
*         description: The ID of the clinic to retrieve the staff ID settings for.
*         example: "60d0fe4f5311236168a109ca"
*     responses:
*       200:
*         description: Successfully fetched and generated the staff ID.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 staffId:
*                   type: object
*                   properties:
*                     prefix:
*                       type: string
*                       example: "STAFF"
*                     suffix:
*                       type: string
*                       example: "21"
*                     currentStaffId:
*                       type: integer
*                       example: 1050
*       404:
*         description: Clinic not found
*         content:
*           application/json:
*             schema:
*               type: string
*               example: ""
*       500:
*         description: Internal server error
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Internal Server Error"
*/


client.get(
  "/getclinicstaffid",
  authorizationCheck,
  validateGenerateStaffId,
  async (req, res, next) => {
    try {
        return await generateStaffId(req, res);
    }
    catch (e) {
        next(e)
    }
  } 
  
 );
/**
* @swagger
* /clinic/getbillnumber:
*   get:
*     tags:
*       - clinic
*     summary: Generate Invoice ID
*     description: Fetches the current invoice number for a clinic by its ID and generates a new invoice number.
*     parameters:
*       - in: query
*         name: id
*         required: true
*         schema:
*           type: string
*         description: The ID of the clinic to retrieve the invoice number for.
*         example: "60d0fe4f5311236168a109ca"
*     responses:
*       200:
*         description: Successfully fetched and generated the invoice number.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 invoiceNumber:
*                   type: integer
*                   example: 1001
*       404:
*         description: Clinic not found
*         content:
*           application/json:
*             schema:
*               type: string
*               example: ""
*       500:
*         description: Internal server error
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Internal Server Error"
*/

client.get(
  "/getbillnumber",
  authorizationCheck,
  validateGenerateInvoiceId,
  async (req, res, next) => {
    try {
        return await generateInvoiceId(req, res);
    }
    catch (e) {
        next(e)
    }
  } 
  
 );

/**
* @swagger
* /clinic/createclinicgroup:
*   post:
*     tags:
*       - clinic
*     summary: Create a Clinic Group
*     description: Creates a new clinic group using the provided group name and user information.
*     requestBody:
*       required: true
*       content:
*         application/json:
*           schema:
*             type: object
*             properties:
*               data:
*                 type: object
*                 required:
*                   - clinicGroupName
*                 properties:
*                   ClinicGroupName:
*                     type: string
*                     description: Name of the clinic group
*                     example: "Group A"
*     security:
*       - bearerAuth: []  # Assuming you're using Bearer Token for authentication
*     responses:
*       200:
*         description: Successfully created the clinic group.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: true
*       400:
*         description: Bad Request. Invalid input.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Invalid input data."
*       500:
*         description: Internal Server Error.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 message:
*                   type: string
*                   example: "Internal server error."
*/



client.post(
  "/createclinicgroup",
  authorizationCheck,
  validateCreateClinicGroup,
  async (req, res, next) => {
    try {
        return await createClinicGroup(req, res);
    }
    catch (e) {
        next(e)
    }
  } 
  
 );


/**
* @swagger
* /clinic/getallclinicgroup:
*   get:
*     tags:
*       - clinic
*     summary: Get all clinic groups
*     description: Retrieves a list of all clinic groups.
*     responses:
*       200:
*         description: Successfully retrieved all clinic groups.
*         content:
*           application/json:
*             schema:
*               type: array
*               items:
*                 type: object
*                 properties:
*                   _id:
*                     type: string
*                     example: "60d0fe4f5311236168a109ca"
*                   groupName:
*                     type: string
*                     example: "Dental Clinics"
*                   created:
*                     type: object
*                     properties:
*                       on:
*                         type: string
*                         format: date-time
*                         example: "2024-10-23T12:34:56.789Z"
*                       by:
*                         type: string
*                         example: "admin"
*                   deleted:
*                     type: boolean
*                     example: false
*       500:
*         description: Internal server error
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 message:
*                   type: string
*                   example: "Internal Server Error"
*/

client.get(
  "/getallclinicgroup",
  authorizationCheck,
  validateGetAllClinicGroup,
  async (req, res, next) => {
    try {
        return await getAllClinicGroup(req, res);
    }
    catch (e) {
        next(e)
    }
  } 
  
 );

/**
* @swagger
* /clinic/getallclients:
*   get:
*     tags:
*       - clinic
*     summary: Get all clinics
*     description: Retrieves a list of all clinics from the database.
*     responses:
*       200:
*         description: Successfully retrieved all clinics.
*         content:
*           application/json:
*             schema:
*               type: array
*               items:
*                 type: object
*                 properties:
*                   _id:
*                     type: string
*                     example: "60d0fe4f5311236168a109ca"
*                   clinicName:
*                     type: string
*                     example: "Sunshine Clinic"
*                   address:
*                     type: object
*                     properties:
*                       street:
*                         type: string
*                         example: "123 Main St"
*                       city:
*                         type: string
*                         example: "New York"
*                       zipCode:
*                         type: string
*                         example: "10001"
*                   contactInfo:
*                     type: object
*                     properties:
*                       phone:
*                         type: string
*                         example: "******-567-890"
*                       email:
*                         type: string
*                         example: "<EMAIL>"
*                   createdOn:
*                     type: string
*                     format: date-time
*                     example: "2024-10-23T12:34:56.789Z"
*                   isDeleted:
*                     type: boolean
*                     example: false
*       500:
*         description: Internal server error
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Internal Server Error"
*/

client.get(
  "/getallclients",
  authorizationCheck,
  validateGetAllClinics,
  async (req, res, next) => {
    try {
        return await getAllClinics(req, res);
    }
    catch (e) {
        next(e)
    }
  });
export default client;
