import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { getDoctorsWithTimeSlots } from '../../../helpers/staff/staff.helper.js'; 
import { Staff } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); // Set up MongoDB connection
});

afterAll(async () => {
  await teardown(); 
});

describe('getDoctorsWithTimeSlots function', () => {
  let testClinicId, doctorId;

  beforeEach(async () => {
    await Staff.deleteMany({});

    const doctor = new Staff({
      firstName: 'Test',
      lastName: 'Doctor',
      staffId:"testStaffId",
      mobile: '1234567890',
      specialization: 'General',
      prefix:"Mr.",
      isDoctor: true,
      deleted: false,
      clinic: new mongoose.Types.ObjectId(), 
    });
    doctorId = doctor._id;
    testClinicId = doctor.clinic;
    await doctor.save();
  });

  afterEach(async () => {
    await Staff.deleteMany({});
  });

  it('should return doctors with time slots for the specified clinic', async () => {
    const result = await getDoctorsWithTimeSlots(testClinicId);

    expect(result.length).toBe(1); // Should return one doctor
    expect(result[0].firstName).toBe('Test');
    expect(result[0].lastName).toBe('Doctor');
    expect(result[0].specialization).toBe('General');

  });

  it('should return an empty array if no doctors are available for the clinic', async () => {
    const newClinicId = new mongoose.Types.ObjectId();
    const result = await getDoctorsWithTimeSlots(newClinicId); 

    expect(result.length).toBe(0);
  });


});
