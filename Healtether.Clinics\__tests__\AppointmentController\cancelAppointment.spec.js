import { jest } from "@jest/globals";
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");

mockApointmentHelper();
mockCommonUtils();
mockFirebaseMethod();

const { cancelled } = await import("../../helpers/appointment/appointment.helper.js");
const { cancelAppointment } = await import("../../controllers/appointments/appointment.controller"); // Adjust import path
const { buildNotificationText, resultObject, formatTodayDate } = await import("../../utils/common.utils.js");
const { sendNotificationViaToken } = await import("../../config/firebase.admin.js");

let req, res;
beforeEach(() => {
  req = {
    body: {
      data: {
        id: "appointmentId",
      },
    },
    Notificationkey: "mockNotificationKey",
    user: {
      id: "userId",
    },
  };

  res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn(),
  };
});


describe("Cancel Appointment Tests", () => {
  it("should cancel the appointment and send a notification", async () => {
    // Mock data
    const mockAppointment = {
      _id: "appointmentId",
      mobile: "**********",
      name: "test23",
      gender: "Female",
      age: "15",
      birthDate: "2009-07-10",
      appointmentDate: "2024-09-15",
      timeSlot: "07:30 PM - 07:50 PM",
      reason: "test",
      virtualConsultation: "true",
      patientId: "patientId123",
      doctorId: "662ca0ad1a2431e16c41ebb1",
      doctorName: "Venkatesh Raja",
      clientId: "662ca0a41a2431e16c41ebaa",
      clinicPatientId: "clinicPatientId123",
    };

    cancelled.mockResolvedValue(mockAppointment);
    buildNotificationText.mockReturnValue("Notification message");
    sendNotificationViaToken.mockResolvedValue();
    resultObject.mockReturnValue({
      statusCode: 200,
      success: true,
      data: { id: mockAppointment._id },
    });
    await cancelAppointment(req, res);
    expect(cancelled).toHaveBeenCalledWith(req.body.data.id, req.user);
    expect(buildNotificationText).toHaveBeenCalledWith(
      "Appointment for",
      mockAppointment.name,
      " on " +
      formatTodayDate(mockAppointment.appointmentDate) +
      " has been cancelled successfully",
      req.user
    );
    expect(sendNotificationViaToken).toHaveBeenCalledWith(
      req.Notificationkey,
      "Notification message",
      "Appointment",
      true,
      mockAppointment.clinic,
      req.user.id
    );
    expect(resultObject).toHaveBeenCalledWith(200, null, true, {
      id: mockAppointment._id,
    });
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      statusCode: 200,
      success: true,
      data: { id: mockAppointment._id },
    });
  });

  it('should return "Already in Started" if appointment is not null but already started', async () => {
    const mockAppointment = "Already in Started";
    req.body.data._id = "appointmentId";
    cancelled.mockResolvedValue(mockAppointment);
    resultObject.mockReturnValue({
      statusCode: 200,
      success: false,
      message: "Already in Started",
      data: { id: req.body.data._id },
    });
    await cancelAppointment(req, res);
    expect(cancelled).toHaveBeenCalledWith(req.body.data._id, req.user);
    expect(resultObject).toHaveBeenCalledWith(
      200,
      "Already in Started",
      false,
      { id: req.body.data._id }
    );
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      statusCode: 200,
      success: false,
      message: "Already in Started",
      data: { id: "appointmentId" },
    });
  });

});
