import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { getClientById } from '../../../helpers/clinic/client.helper.js';
import { Client } from '../../../model/clinics.model.js';
import { User } from '../../../model/clinics.model.js'; 
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000); 

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('getClient function', () => {
  let clinicId, adminUserId;

  beforeEach(async () => {
    await Client.deleteMany({});
    await User.deleteMany({});

    adminUserId = new mongoose.Types.ObjectId();

    await User.create({
      _id: adminUserId,
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      mobile: '1234567890',
      countryCode: "+91",
      password:"admin",
      active: true,
      isdeleted: false,
      isSuperAdmin: true
    });

    clinicId = new mongoose.Types.ObjectId();
    await Client.create({
      _id: clinicId,
      clinicName: 'Test Clinic',
      address: '123 Test Street',
      adminUserId: adminUserId, 
      isDeleted: false,
      logo: '',
      created: { on: new Date('2024-04-27T06:52:20.110Z') },
      modified: { on: new Date('2024-09-17T09:15:18.594Z') },
    });
  });

  afterEach(async () => {
    await Client.deleteMany({});
    await User.deleteMany({});
  });

  it('should return the client with populated adminUserId', async () => {
    const result = await getClientById(clinicId);

    expect(result._id).toStrictEqual(clinicId); 
    expect(result.clinicName).toBe('Test Clinic'); 
    expect(result.adminUserId.firstName).toStrictEqual('Admin');
    expect(result.adminUserId.lastName).toStrictEqual('User');
    expect(result.adminUserId.email).toStrictEqual('<EMAIL>');
    expect(result.adminUserId.mobile).toStrictEqual('1234567890');
  });

  it('should return null if client does not exist', async () => {
    const invalidClientId = new mongoose.Types.ObjectId(); 
    const result = await getClientById(invalidClientId);

    expect(result).toBeNull(); 
  });
});
