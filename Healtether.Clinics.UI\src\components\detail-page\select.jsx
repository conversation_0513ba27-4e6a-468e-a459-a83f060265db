import React, { useEffect, useState } from "react";
import { useClickOut } from "../../utils/hooks/useClickout";
import { Icons } from "./icons";
import { DefaultSelectboxClass } from "../../utils/Classes";

export default function CustomSelect({
  handleClick,
  data,
  label,
  medium,
  placeholder,
  bottom,
  isDisabled,
  value,
  isSearch
}) {
  const { targetRef, isOpen, setIsOpen } = useClickOut.auto();
  const [title, setTitle] = useState(value || placeholder);
  const [searchText, setSearchText] = useState(value?value:"");
  const [filteredData, setFilteredData] = useState(data);
  const is_active = data.length > 0;

  useEffect(() => {
    setTitle(value || placeholder);
  }, [value, placeholder]);

  useEffect(() => {
    setFilteredData(
      data.filter((prop) =>
        `${prop?.firstName} ${prop?.lastName} ${prop?.value}`
          .toLowerCase()
          .includes(searchText.toLowerCase())
      )
    );
  }, [searchText, data]);

  return (
    <div className={`relative w-full`}>
      <div
        onClick={() => is_active && !isDisabled && setIsOpen(true)}
        className={`rounded-sm text-sm font-normal h-10 ${
          is_active && !isDisabled && "cursor-pointer"
        } flex items-center justify-between p-4 ${DefaultSelectboxClass}`}
      >
        {title || "Select"}
        <Icons.arrow
          className={`w-4 h-2 sm:h-2.5 duration-200 ${isOpen ? "rotate-180" : ""}`}
        />
      </div>
      {is_active && !isDisabled && (
        <section
          ref={targetRef}
          className={`${
            isOpen
              ? "visible opacity-100 scale-100 bg-text_bg_primary"
              : "invisible opacity-0 scale-0"
          } ${medium ? "bg-backcolor_grey mt-2 border border-color_muted/20" : "bg-backcolor_light"} ${
            bottom ? "bottom-full mb-2 origin-bottom-right" : "top-11 origin-top-right"
          } duration-300 w-full h-fit absolute right-0 z-30 overflow-hidden rounded-sm shadow-xl p-1 `}
        >
          {
            isSearch&&
            <div className="p-2">
            <input
              type="text"
              className="w-full px-2 py-1 border rounded-md text-sm"
              placeholder="Search..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </div>
          }
        
          <div className="overflow-y-auto custom-scrollbar max-h-60">
            {label && (
              <div className="text-xs font-normal px-4 py-2 bg-Primary/20 cursor-auto">
                {label}
              </div>
            )}
            {filteredData.length > 0 ? (
              filteredData.map((prop, idx) => (
                <div
                  key={idx}
                  onClick={() => {
                    handleClick(prop);
                    setIsOpen(false);
                    setTitle(
                      prop?.value || `${prop?.firstName?.toUpperCase()} ${prop?.lastName?.toUpperCase()}`
                    );
                    setSearchText("");
                  }}
                  className="text-xs sm:text-base font-normal justify-between h-11 px-4 pt-2.5 cursor-pointer hover:bg-color_dark/10 duration-200 capitalize"
                >
                  {prop?.firstName && `Dr. ${prop?.firstName}`} {prop?.lastName} {prop?.value}
                </div>
              ))
            ) : (
              <div className="text-xs text-center text-gray-500 py-2">No results found</div>
            )}
          </div>
        </section>
      )}
    </div>
  );
}
