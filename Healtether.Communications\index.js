import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import bodyParser from "body-parser";
import mongoose from "mongoose";
import userRouter from "./routes/userroute.js";
import messageRouter from "./routes/messageroute.js";
import whatsappChatRouter from "./routes/whatsappchatroute.js";
import { SocketConnection } from "./config/socket-io.js";
import { createServer } from "http";
import errorHandler from "./middleware/errorhandler.middleware.js";
import { initializeAppInsights } from "./config/appinsight.config.js";
import abhaRouter from "./routes/abha.route.js";
import { swaggerSpec, swaggerUi } from "./config/swagger.config.js";
import { abdmWebHookRouter } from "./routes/abdm.webhook.route.js";
import { CloseConnection } from "./config/clinics.collections.config.js";
import { LogDBCloseConnection } from "./config/whatsapp.collections.config.js";
import abhaM3Router from "./routes/abha.m3.route.js";
mongoose.set("strictQuery", true);
import { PROFILE_SHARE_WEBHOOK } from "./utils/abha.api.js";
import abhaM2Router from "./routes/abha.m2.routes.js";

const app = express();

//initializeAppInsights();

//const bodyparser = require("body-parser");
dotenv.config();
const port = process.env.PORT;
// app.use(express.json());
app.use(express.json({ limit: '100mb' })); // Increase JSON payload size
app.use(express.urlencoded({ limit: '100mb', extended: true }));
// app.use(
//   bodyParser.urlencoded({
//     extended: true,
//   })
// );
app.use((err, req, res, next) => {
  if (err.type === 'entity.too.large' || err.message === 'request aborted') {
    return res.status(413).json({ error: "Request aborted or too large" });
  }
  next(err);
});
app.use(
  cors({
    origin: process.env.CORS_URL,
    methods: ["GET", "POST", "PUT", "DELETE"],
    credentials: true,
  })
);
// app.post(PROFILE_SHARE_WEBHOOK, (req, res) => {
//   console.log(req.body);

//   res.sendStatus(200)
// })

app.get("/", (req, res) => {
  res.send("Healtether.Communication running !!!");
});

app.use("", abdmWebHookRouter);
app.use("/api/abha", abhaRouter);
//app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
app.use("/api/user", userRouter);
app.use("/api/message", messageRouter);
app.use("/api/whatsappchat", whatsappChatRouter);
app.use("/api/m3", abhaM3Router);
app.use("/api/m2", abhaM2Router);
app.use(errorHandler);

const httpServer = createServer(app);
SocketConnection(httpServer);
const server = httpServer.listen(port, () => {
  console.log(`Environment: ${process.env.ENV}`);
  console.log(`Server is running on port ` + port);
});

server.on("close", async () => {
  await CloseConnection();
  await LogDBCloseConnection();
  app;
});

process.on("uncaughtException", (error) => {
  console.log("Uncaught Exception:", error);
});

export { app, server };
