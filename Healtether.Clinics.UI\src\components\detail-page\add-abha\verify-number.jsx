import React from "react";
import { Input } from "../input";
import { OtpInputs } from "./otp-inputs";
import { Buttons } from "../appointment/button";
import Spinner from "../../loader/Spinner";


export default function VerifyNumber({ verify, otp, setOtp, handleCancel,setpatientMobile ,patientMobile,timer,isResendDisabled, onResendOtp, error, busy,handleBack,showInput,resendAttemptsCount}) {

  // console.log(busy, "busy"  );
  return (
    <article className=" h-80 flex flex-col gap-4 font-primary">
      {showInput && 
       <div className="">
        <div className="mb-2 text-sm font-semibold text-dark">
          Mobile number *
        </div>
        <Input.number
          value={patientMobile}
          handleChange={(e) => console.log(e)}
          placeholder="**********"
        />
      </div>
      }
     
      <div className=" flex flex-col gap-2">
        <OtpInputs otp={otp} setOtp={setOtp} verify={(e) => console.log(e)} timer={timer} isResendDisabled={isResendDisabled} onResendOtp={onResendOtp} resendAttemptsCount={resendAttemptsCount}/>
          {
            error && (
              <div className="text-red-500 text-xs">
                {error}
              </div>
            )
          }
      </div>
      <footer className=" flex justify-between gap-2 mt-auto">
        <Buttons.light
          onClick={handleCancel}
          title="cancel"
          classname="w-fit font-semibold"
        />
        <div>
        {handleBack && (
          <Buttons.light
            onClick={handleBack}
            title="Back"
            classname="w-fit font-semibold"
          />
        )}
        <Buttons.primary
          onClick={verify}
          title= {busy ? <Spinner show={true} /> : "Submit"}
          classname="w-fit font-semibold"
        />
        </div>
        
      </footer>
    </article>
  );
}
