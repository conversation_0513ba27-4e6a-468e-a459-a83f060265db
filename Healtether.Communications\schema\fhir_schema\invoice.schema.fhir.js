import { Schema } from 'mongoose';

const telecomSchema = new Schema({
    system: String,
    value: String,
    use: String
});

const licenseSchema = new Schema({
    code: String,
    display: String,
    licNo: String
});


const signatureSchema = new Schema({
    who: {
        type: { type: String },
        name: String
    },
    sigFormat: String,
    data: String
});

const addressSchema = new Schema({
    use: String,
    type: String,
    text: String,
    // line: [String],
    city: String,
    state: String,
    district: String,
    postalCode: String,
    country: String
});

const allergyIntoleranceSchema = new Schema({
    type: String,
    clinicalStatus: String,
    verificationStatus: String,
    doctor: String,
    notes: [String]
});

const patientSchema = new Schema({
    id:String,
    abhaNumber: String,
    abhaAddress: String,
    name: {
        text:String,
        prefix:[String]
    },
    gender: String,
    dob: String,
    doctors: [String],
    allergyIntolerances: [allergyIntoleranceSchema],
    telecom: [telecomSchema],
    address:[addressSchema]
});

const generalSchema = new Schema({
    artifact: String,
    hipUrl: String,
    hipIds: [String],
    status: String,
    clientId: String
});

const practitionerSchema = new Schema({
    names: [String],
    licenses: [licenseSchema],
    patient: String,
    gender: String,
    birthDate: String,
    address: [addressSchema],
    telecom: [telecomSchema],
});

const encounterSchema = new Schema({
    status: String,
    startTime: String,
    endTime: String
});

const organizationSchema = new Schema({
    name: String,
    telecom: [telecomSchema],
    licenses: [licenseSchema]
});

const chargeItemSchema = new Schema({
    id:String,
    type: String,
    status: String,
    quantity: Number
});

// Binary schema for invoice attachments
const binarySchema = new Schema({
    contentType: String,
    data: String
});

// const invoiceLineItemSchema = new Schema({
//     priceComponent: [{
//         type: String,
//         display:String,
//         amount: {
//             type: Number,
//             currency: String
//         },
//     }]
// });

// Sub-schema for amount
const amountSchema = new Schema({
  value: { type: Number, required: true },
  currency: { type: String, required: true }
}, { _id: false });

// Sub-schema for priceComponent
const priceComponentSchema = new Schema({
  type: { type: String, required: true },
  display: { type: String }, // Optional, for things like CGST, SGST
  amount: amountSchema
}, { _id: false });

// Sub-schema for lineItem
const invoiceLineItemSchema = new Schema({
  type: { type: String, required: true },
  priceComponent: [priceComponentSchema]
}, { _id: false });

// Main invoice schema
const invoiceSchema = new Schema({
  id: { type: String, required: true },
  status: { type: String, required: true },
  date: { type: String, required: true },
  totalNet: {
    value: { type: Number, required: true },
    currency: { type: String, required: true }
  },
  totalGross: {
    value: { type: Number, required: true },
    currency: { type: String, required: true }
  },
  lineItem: [invoiceLineItemSchema]
});


const InvoiceRecordSchema = new Schema({
    fhirId: {
        type: String,
        required: true,
        index: true
    },
    general: generalSchema,
    patient: patientSchema,
    practitioners: [practitionerSchema],
    encounter: encounterSchema,
    organization: organizationSchema,
    invoice:invoiceSchema,
    chargeItems: [chargeItemSchema],
    binary: binarySchema,
    signature: signatureSchema,
    abhaCareContextLinked:{
        type: Boolean,
        default: false
    }
});



export { InvoiceRecordSchema };
