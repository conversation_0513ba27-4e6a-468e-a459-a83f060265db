import { jest } from "@jest/globals";
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");
const { mockBlobHelper } = await import("../mocks/mock.blob.helper.js");
mockBlobHelper()
mockApointmentHelper();
mockCommonUtils();
mockFirebaseMethod();

const { setAppointmentEnded } = await import("../../controllers/appointments/appointment.controller.js"); 
const { setEnded } = await import("../../helpers/appointment/appointment.helper.js");
const { BlobHelper } = await import("../../helpers/storage/blob.helper.js"); 

describe('setAppointmentEnded', () => {
    let req, res;
    let mockRemoveBlob;

    beforeEach(() => {
        req = {
            body: {
                data: {
                    id: 'appointmentId123',
                    removeRecords: ['record1.pdf', 'record2.pdf'],
                    clientId: 'clientId123'
                }
            },
            user: {
                id: '507f1f77bcf86cd799439011' // Valid ObjectId format
            }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };

        mockRemoveBlob = jest.fn();
        BlobHelper.mockImplementation(() => ({
            RemoveBlob: mockRemoveBlob
        }));
    });

    it('should return 200 with the updated appointment data and remove specified blobs', async () => {
        const mockAppointments = {
            _id: 'appointmentId123',
            status: 'Ended'
        };

        setEnded.mockResolvedValue(mockAppointments);
        mockRemoveBlob.mockResolvedValue(); 

        await setAppointmentEnded(req, res);

        expect(setEnded).toHaveBeenCalledWith(req.body.data, req.body.data.id, req.user);
        
        if (req.body.data.removeRecords) {
            const removeBlobs = req.body.data.removeRecords;
            removeBlobs.forEach(blob => {
                const blobNameWithFolder = `${process.env.PATIENT_BLOB_FOLDER}${blob}`;
                expect(mockRemoveBlob).toHaveBeenCalledWith(blobNameWithFolder);
            });
        }

        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockAppointments);
    });
});