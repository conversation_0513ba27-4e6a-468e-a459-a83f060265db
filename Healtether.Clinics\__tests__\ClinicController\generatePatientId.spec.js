import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
const { mockAutoIdHelper } = await import("../mocks/clinicAutoId.helper.js");
mockClientHelper();
mockAutoIdHelper();
const { generatePatientId } = await import("../../controllers/clinic/client.controller.js");
const { getPatientAutoId } = await import("../../helpers/clinicautoidentifier/clinicautoid.helper.js");
import mongoose from "mongoose";

describe("generatePatientId", () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: { id: new mongoose.Types.ObjectId().toString() },
        };
        res = {
            json: jest.fn().mockReturnThis(),
            status: jest.fn().mockReturnThis(),
        };
        jest.clearAllMocks();
    });

    it("should return the current patient ID with status 200 if clinic data is found", async () => {
        const mockClinic = {
            clinic: {
                patientId: {
                    prefix: "PAT",
                    suffix: "2024",
                },
            },
            currentPatientId: "56789",
        };

        getPatientAutoId.mockResolvedValueOnce(mockClinic);

        await generatePatientId(req, res);

        expect(getPatientAutoId).toHaveBeenCalledWith(req.query.id);
        expect(res.json).toHaveBeenCalledWith({
            patientId: {
                prefix: mockClinic.clinic.patientId.prefix,
                suffix: mockClinic.clinic.patientId.suffix,
                currentPatientId: mockClinic.currentPatientId,
            },
        });
        expect(res.status).toHaveBeenCalledWith(200);
    });

    it("should return an empty string with status 404 if clinic data is not found", async () => {
        getPatientAutoId.mockResolvedValueOnce(null);

        await generatePatientId(req, res);

        expect(getPatientAutoId).toHaveBeenCalledWith(req.query.id);
        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith("");
    });

    
});
