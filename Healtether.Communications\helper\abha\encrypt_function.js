import crypto from "crypto";
import { existsSync, readFileSync } from "fs";
import { join } from "path";

const PUBLIC_KEY_FILE_PATH = join(process.cwd(), "keys/abha/public_key.pem");

const encryptData = (data) => {
  try {
    if (!existsSync(PUBLIC_KEY_FILE_PATH)) {
      throw new Error("Public key file does not exist.");
    }
    const publicKey = readFileSync(PUBLIC_KEY_FILE_PATH, "utf8");
    const key = crypto.createPublicKey({
      key: publicKey,
      format: "pem",
    });
    const encryptedData = crypto.publicEncrypt(
      {
        key,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: "sha1",
      },
      Buffer.from(data, "utf8")
    );
    return encryptedData.toString("base64");
  } catch (error) {
    console.error("Error encrypting data:", error.message);
    throw error;
  }
};
export default encryptData;
