import { v4 as uuidv4 } from "uuid";
import { AbdmAccessToken } from "./abdm-access-token.js";

export const fetchAbhaApi = async (url, body, method, txnId, tToken, xToken) => {
    const base_url = process.env.ABHA_BASE_URL;
    const token = await AbdmAccessToken();
    const isoTimestamp = new Date().toISOString();
    const randomUUID = uuidv4();

    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        TIMESTAMP: isoTimestamp,
        "REQUEST-ID": randomUUID,
        Transaction_Id: txnId || "",
        "T-token": tToken || "",
        "X-token": xToken || "",
    };

    // Define request options
    const requestOptions = {
        method: method || "POST",
        headers,
    };

    // Only include body for non-GET/HEAD methods
    if (body && method !== "GET" && method !== "HEAD") {
        requestOptions.body = JSON.stringify(body);
    }

    const result = await fetch(`${process.env.ABHA_BASE_URL}${url}`, requestOptions);
    return result;
};


export const fetchAbdmSession = async (data) => {
    const result = await fetch(process.env.AUTH_TOKEN_URL, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
    });

    if (!result.ok) {
        throw new Error(`Failed to fetch token. Status: ${result.status}`);
    }

    const response = await result.json();
    console.log("response",response)
    return response;
}

export const ackProfileShare = async (path, req, payload) => {
    const body = req.body;
    const headers = { ...req.headers };
    const token = await AbdmAccessToken();
    const isoTimestamp = new Date().toISOString();
    const randomUUID = uuidv4();
    const cmId = process.env.CM_ID;

    if (!body) {
        return {
            isSuccess: false,
            status: 400,
            data: { error: { message: "Invalid request body" } },
        };
    }

    const postHeaders = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        TIMESTAMP: isoTimestamp,
        "REQUEST-ID": randomUUID,
        "X-CM-ID": cmId
    };

    console.log("postHeaders",postHeaders)
    const requestOptions = {
        method: "POST",
        body: JSON.stringify(payload),
        headers:postHeaders,
    };

    const result = await fetch(`${process.env.ABHA_M2_BASE_URL}${path}`, requestOptions);
    return result;
};
