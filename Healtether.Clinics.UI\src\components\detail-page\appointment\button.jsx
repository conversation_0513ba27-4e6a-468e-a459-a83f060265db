import React from "react";

export const Buttons = {
  primary: (props) => (
    <button
      {...props}
      className={`${props.classname} ${
        props.disabled ? "bg-gray text-dark" : "bg-Primary text-white"
      } h-10 px-4 py-2 capitalize tracking-wider rounded-lg font-medium text-base disabled:cursor-not-allowed`}
    >
      {props.title}
    </button>
  ),
  secondary: (props) => (
    <button
      {...props}
      className={`${props.classname} h-10 px-4 py-2 bg-gray text-dark rounded-md font-medium text-base font-roboto shadow-sm`}
    >
      {props.title}
    </button>
  ),
  light: (props) => (
    <button
      {...props}
      className={`${props.classname} h-10 px-4 py-2 text-color_muted capitalize rounded-xs font-medium text-base hover:shadow-sm duration-300`}
    >
      {props.title}
    </button>
  ),
  lg: (props) => (
    <button
      {...props}
      className={`${props.classname} ${
        props.active ? " bg-Primary text-white" : "bg-gray text-color_muted"
      } h-12 px-4 flex items-center justify-center rounded-lg cursor-pointer text-lg font-medium duration-500 shadow-md`}
    >
      {props.title}
    </button>
  ),
};
