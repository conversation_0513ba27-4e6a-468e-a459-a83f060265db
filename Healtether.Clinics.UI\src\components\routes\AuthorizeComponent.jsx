import CheckJWT from "utils/CheckJWT";
import { useSelector } from "react-redux";
import { SetHeaderToken } from "../../services/axios/axios";
import { Navigate } from "react-router-dom";

export function AuthorizeComponent(props) {
    let { component: Component } = props;
    let extraValidation = true;
    let { checkIsDoctor } = props;
    let { checkIsAdmin } = props;
    let { checkIsAdminOrDoctor } = props;
    const { token } = useSelector((state) => state.auth);
    const { user } = useSelector((state) => state.user);
    var { clinic } = useSelector((state) => state.currentClinic);
    const isLoggedIn = token != null && token != undefined && !CheckJWT(token);

    if (token != null) {
        SetHeaderToken(token);
    }
    if (checkIsDoctor) {
        extraValidation = user.isDoctor;
    }
    if (extraValidation && checkIsAdmin) {
        extraValidation = user.isAdmin;
    }
    if (extraValidation && checkIsAdminOrDoctor) {
        extraValidation = user.isDoctor || user.isAdmin;
    }
    return isLoggedIn && (clinic == null || clinic?._id == null) ? <Navigate to="/select-clinic" /> :
        isLoggedIn && extraValidation ? <Component /> : <Navigate to="/login" />
}
