const conditionValidator = {
  conditions: {
    in: ["body"],
    isArray: true,
    errorMessage: "conditions must be an array",
  },

  "conditions.*.type": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each condition must have a non-empty type",
  },

  "conditions.*.status": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each condition must have a non-empty status",
  },

  "conditions.*.recordedDate": {
    in: ["body"],
    isISO8601: true,
    toDate: true,
    errorMessage:
      "Each condition must have a valid recordedDate (ISO 8601 format)",
  },

  "conditions.*.startDate": {
    in: ["body"],
    isISO8601: true,
    toDate: true,
    errorMessage:
      "Each condition must have a valid startDate (ISO 8601 format)",
  },

  "conditions.*.endDate": {
    in: ["body"],
    isISO8601: true,
    toDate: true,
    errorMessage: "Each condition must have a valid endDate (ISO 8601 format)",
  },
}

export default conditionValidator;
