import { jest } from "@jest/globals";
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");

mockApointmentHelper();
mockCommonUtils();
mockFirebaseMethod();
const { buildNotificationText, resultObject, formatTodayDate } = await import("../../utils/common.utils.js");
const { sendNotificationViaToken } = await import("../../config/firebase.admin.js");
// Import the controller and mocked dependencies
const { appointmentUpsert } = await import("../../controllers/appointments/appointment.controller.js");
const { upsertAppointment } = await import("../../helpers/appointment/appointment.helper.js");
describe('appointmentUpsert', () => {
  let req, res;

  beforeEach(() => {
      req = {
          body: {
              data: {
                  mobile: "**********",
                  name: "test23",
                  gender: "Female",
                  age: "15",
                  birthDate: "2009-07-10",
                  appointmentDate: "2024-09-15",
                  timeSlot: "07:30 PM - 07:50 PM",
                  reason: "test",
                  virtualConsultation: "true",
                  patientId: "patientId123",
                  doctorId: "662ca0ad1a2431e16c41ebb1",
                  doctorName: "Venkatesh Raja",
                  clientId: "662ca0a41a2431e16c41ebaa",
                  clinicPatientId: "clinicPatientId123"
                }
          },
          user: {
              id: 'userId',
              name: 'Test User'
          },
          Notificationkey: 'notificationKey'
      };
      res = {
          json: jest.fn().mockReturnThis(),
          status: jest.fn().mockReturnThis()
      };
  });

  it('should send a notification if the appointment is not null and not "Already in Started"', async () => {
      const mockAppointment = {
          _id: 'appointmentId',
          name: 'Test Appointment',
          appointmentDate: '2024-09-02',
          clinic: 'clinicId'
      };
  
      // Mocking the dependent functions
      upsertAppointment.mockResolvedValue([true, mockAppointment]);
      buildNotificationText.mockReturnValue('Notification message');
      sendNotificationViaToken.mockResolvedValue();
      resultObject.mockReturnValue({ statusCode: 200, success: true, data: { id: mockAppointment._id ,patientId:req.body.data.patientId} });
  
      await appointmentUpsert(req, res);
  
      expect(upsertAppointment).toHaveBeenCalledWith(req.body.data, req.body.data.id, req.user);
  
      expect(buildNotificationText).toHaveBeenCalledWith(
          "Appointment for",
          mockAppointment.name,
          ` on ${formatTodayDate(mockAppointment.appointmentDate)} has been scheduled`,
          req.user
      );
      expect(sendNotificationViaToken).toHaveBeenCalledWith(
          req.Notificationkey,
          'Notification message',
          "Appointment",
          true,
          mockAppointment.clinic,
          req.user.id
      );
  
      expect(resultObject).toHaveBeenCalledWith(200, null, true, { id: mockAppointment._id, patientId:req.body.data.patientId });
      
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ statusCode: 200, success: true, data: { id: mockAppointment._id, patientId:req.body.data.patientId } });
  });
  
  it('should return 400 if upsertAppointment fails', async () => {
      upsertAppointment.mockResolvedValue([false, 'Error in upserting appointment']);

      await appointmentUpsert(req, res);

      expect(upsertAppointment).toHaveBeenCalledWith(req.body.data, req.body.data.id, req.user);
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ success: false, error: 'Error in upserting appointment' });
  });


});
