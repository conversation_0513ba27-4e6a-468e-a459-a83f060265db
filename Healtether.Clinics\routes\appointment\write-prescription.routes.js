import { Router } from "express";
import {
  getPrescriptionVitalsWithClinicAndDoctorDetails,
  getWholePrescriptionAndVitalsForAppointment,
  masterAllergiesSearch,
  masterMedicationSearch,
  updatePrescription,
  upsertDrugsPrescription,
  labTestUpsert,
  symptomDiagnosisUpsert,
  upsertUserMedicalHistory,
  upsertVitalsNdPersonalHistory,
  getVitalsPersonalHistory,
  snomedCtMedication,
  getMedicationDetails
} from "../../controllers/appointments/write-prescription.controller.js";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";
import { getWholeMedicalHistoryForPatient } from "../../controllers/appointments/medical-histories.controller.js";
import {
  validateDrugs,
  validateGetVitalsPersonalHistory,
  validateGetWholeMedicalHistoryForPatient,
  validateGetWholePrescriptionAndVitalsForAppointment,
  validateLabTests,
  validateSearchMasterAllergies,
  validateSearchMasterMedication,
  validateSymptomDiagnosis,
  validateUpdatePrescription,
  validateUpsertMedicalHistory,
  validateVitals,
} from "../../validation/appointment/writePrescription.validation.js";
import { makeReciept } from "../../controllers/appointments/appointment.controller.js";
import { ValidateMarkReceipt } from "../../validation/appointment/appointment.validation.js";
import { bundleFhir } from "../../utils/fhir.data.js";

const writePrescription = Router();
writePrescription.post(
  "/update",
  authorizationCheck,
  validateUpdatePrescription,
  async (req, res, next) => {
    try {
      return await updatePrescription(req, res);
    } catch (e) {
      next(e);
    }
  }
);
writePrescription.post(
  '/fhir-bundle',
  authorizationCheck,
  async(req, res,next)=>{
    try{
     return await bundleFhir(req, res)
    }catch (e) {
      next(e);
    }
  }
)

/**
 * @swagger
 * /appointment/write-prescription/getwholeprescription:
 *   get:
 *     tags:
 *       - write-prescription (mobile)
 *       - write-prescription
 *     summary: Get prescription and vitals for a specific appointment
 *     description: Fetches the prescription and vitals associated with a given appointment ID and client ID.
 *     parameters:
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *           example: "609e1f3b8b75b8b4a9f9d4b1"
 *         description: The ID of the client.
 *       - in: query
 *         name: appointment
 *         required: true
 *         schema:
 *           type: string
 *           example: "609e1f3b8b75b8b4a9f9d4b2"
 *         description: The ID of the appointment.
 *     responses:
 *       200:
 *         description: Successfully retrieved the prescription and vitals for the specified appointment.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                   example: "609e1f3b8b75b8b4a9f9d4b2"
 *                 prescriptions:
 *                   type: object
 *                   properties:
 *                     symptoms:
 *                       type: array
 *                       items:
 *                         type: string
 *                         example: "Headache"
 *                     diagnosis:
 *                       type: string
 *                       example: "Migraine"
 *                     labTests:
 *                       type: array
 *                       items:
 *                         type: string
 *                         example: "Blood Test"
 *                     drugPrescriptions:
 *                       type: array
 *                       items:
 *                         type: string
 *                         example: "Paracetamol 500mg"
 *                     patientAdvice:
 *                       type: string
 *                       example: "Drink plenty of water"
 *                     privateNotes:
 *                       type: string
 *                       example: "Patient shows improvement"
 *                     followUpDate:
 *                       type: string
 *                       format: date
 *                       example: "2024-10-10"
 *                     followUpTimeSlot:
 *                       type: string
 *                       example: "10:00 AM - 11:00 AM"
 *                 vitals:
 *                   type: object
 *                   properties:
 *                     bloodPressure:
 *                       type: string
 *                       example: "120/80"
 *                     spo2:
 *                       type: number
 *                       example: 98
 *                     temperature:
 *                       type: number
 *                       example: 36.6
 *                     height:
 *                       type: number
 *                       example: 175
 *                     weight:
 *                       type: number
 *                       example: 70
 *                     pulseRate:
 *                       type: number
 *                       example: 72
 *                     rbs:
 *                       type: number
 *                       example: 100
 *                     heartRate:
 *                       type: number
 *                       example: 75
 *                     respiratoryRate:
 *                       type: number
 *                       example: 18
 *                 doctorId:
 *                   type: string
 *                   example: "609e1f3b8b75b8b4a9f9d4b3"
 *                 doctorName:
 *                   type: string
 *                   example: "Dr. Smith"
 *                 patientId:
 *                   type: string
 *                   example: "609e1f3b8b75b8b4a9f9d4b4"
 *                 name:
 *                   type: string
 *                   example: "John Doe"
 *                 age:
 *                   type: integer
 *                   example: 45
 *                 gender:
 *                   type: string
 *                   example: "Male"
 *                 mobile:
 *                   type: string
 *                   example: "**********"
 *       500:
 *         description: Server error while fetching the prescription and vitals.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
writePrescription.get(
  "/getwholeprescription",
  authorizationCheck,
  validateGetWholePrescriptionAndVitalsForAppointment,
  async (req, res, next) => {
    try {
      return await getWholePrescriptionAndVitalsForAppointment(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/write-prescription/getwholemedicalhistories:
 *   get:
 *     tags:
 *       - write-prescription (mobile)
 *       - write-prescription
 *     summary: Get complete medical history for a specific patient
 *     description: Retrieves the medical history, including allergies, medications, family history, past medical history, past procedures, and personal history for the given patient ID.
 *     parameters:
 *       - in: query
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *           example: "609e1f3b8b75b8b4a9f9d4b1"
 *         description: The ID of the patient whose medical history is to be retrieved.
 *     responses:
 *       200:
 *         description: Successfully retrieved the medical history for the specified patient.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 allergies:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         example: "Penicillin"
 *                       duration:
 *                         type: string
 *                         example: "Lifelong"
 *                       notes:
 *                         type: string
 *                         example: "Severe reaction"
 *                 medication:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         example: "Aspirin"
 *                       duration:
 *                         type: string
 *                         example: "6 months"
 *                       notes:
 *                         type: string
 *                         example: "Daily dose"
 *                 familyHistory:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         example: "Diabetes"
 *                       duration:
 *                         type: string
 *                         example: "Mother"
 *                       notes:
 *                         type: string
 *                         example: "Diagnosed at 50"
 *                 pastHistory:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         example: "Hypertension"
 *                       duration:
 *                         type: string
 *                         example: "5 years"
 *                       notes:
 *                         type: string
 *                         example: "Controlled with medication"
 *                 pastProcedureHistory:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         example: "Appendectomy"
 *                       duration:
 *                         type: string
 *                         example: "1 year ago"
 *                       notes:
 *                         type: string
 *                         example: "No complications"
 *                 personalHistory:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       activity:
 *                         type: string
 *                         example: "Smoker"
 *                       nature:
 *                         type: string
 *                         example: "Social"
 *                       notes:
 *                         type: string
 *                         example: "1 pack per day"
 *       500:
 *         description: Server error while fetching the medical history.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
writePrescription.get(
  "/getwholemedicalhistories",
  authorizationCheck,
  validateGetWholeMedicalHistoryForPatient,
  async (req, res, next) => {
    try {
      return await getWholeMedicalHistoryForPatient(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/write-prescription/searchmasterallergies:
 *   get:
 *     tags:
 *       - write-prescription (mobile)
 *       - write-prescription
 *     summary: Search for master allergies
 *     description: Retrieves a list of master allergies based on a search query. Optionally, filters results by name and limits the number of returned records.
 *     parameters:
 *       - in: query
 *         name: name
 *         required: false
 *         schema:
 *           type: string
 *           example: "Peanut"
 *         description: The name of the allergy to search for. Supports partial matches.
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           example: 10
 *         description: The maximum number of allergy records to return. Default is 10.
 *     responses:
 *       200:
 *         description: Successfully retrieved the list of allergies.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "609e1f3b8b75b8b4a9f9d4b1"
 *                       name:
 *                         type: string
 *                         example: "Peanut"
 *                       description:
 *                         type: string
 *                         example: "A type of legume that can cause severe allergic reactions."
 *       500:
 *         description: Server error while searching for allergies.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */

writePrescription.get(
  "/searchmasterallergies",
  authorizationCheck,
  validateSearchMasterAllergies,
  async (req, res, next) => {
    try {
      return await masterAllergiesSearch(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/write-prescription/snomedctmedication:
 *   get:
 *     tags:
 *       - write-prescription (mobile)
 *       - write-prescription
 *     summary: Search for master medications
 *     description: Retrieves a list of master medications based on a search query. Optionally, filters results by name and limits the number of returned records.
 *     parameters:
 *       - in: query
 *         name: name
 *         required: false
 *         schema:
 *           type: string
 *           example: "Aspirin"
 *         description: The name of the medication to search for. Supports partial matches.
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           example: 10
 *         description: The maximum number of medication records to return. Default is 10.
 *     responses:
 *       200:
 *         description: Successfully retrieved the list of medications.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "609e1f3b8b75b8b4a9f9d4b2"
 *                       name:
 *                         type: string
 *                         example: "Aspirin"
 *                       content:
 *                         type: string
 *                         example: "Acetylsalicylic acid"
 *                       description:
 *                         type: string
 *                         example: "A medication used to reduce pain, fever, or inflammation."
 *       500:
 *         description: Server error while searching for medications.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
writePrescription.get(
  "/searchmastermedication",
  authorizationCheck,
  validateSearchMasterMedication,
  async (req, res, next) => {
    try {
      return await masterMedicationSearch(req, res);
    } catch (e) {
      next(e);
    }
  }
);
writePrescription.get(
  "/snomedctmedication",
  authorizationCheck,
  validateSearchMasterMedication,
  async (req, res, next) => {
    try {
      return await snomedCtMedication(req, res);
    } catch (e) {
      next(e);
    }
  }
)

writePrescription.get(
  "/getmedicationdetails",
  authorizationCheck,
  validateSearchMasterMedication,
  async (req, res, next) => {
    try {
      return await getMedicationDetails(req, res);
    } catch (e) {
      next(e);
    }
  }
)


/**
 * @swagger
 * /appointment/write-prescription/getprescriptionforreport:
 *   get:
 *     tags:
 *       - write-prescription (mobile)
 *       - write-prescription
 *     summary: Retrieve prescription and vitals with clinic and doctor details
 *     description: Retrieves prescription and vital records for a specified appointment, along with details of the associated clinic and doctor.
 *     parameters:
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d21b4667d0d8992e610c85"
 *         description: The ID of the client associated with the appointment.
 *       - in: query
 *         name: appointment
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d21b4667d0d8992e610c86"
 *         description: The ID of the appointment for which to retrieve the records.
 *       - in: query
 *         name: patient
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d21b4667d0d8992e610c86"
 *         description: The ID of the patient for which to retrieve the records.
 *     responses:
 *       200:
 *         description: Successfully retrieved prescription and vital records along with clinic and doctor details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                   example: "60d21b4667d0d8992e610c87"
 *                 prescriptions:
 *                   type: object
 *                   nullable: true
 *                   properties:
 *                     symptoms:
 *                       type: string
 *                       example: "Cough, Fever"
 *                     diagnosis:
 *                       type: string
 *                       example: "Flu"
 *                     labTests:
 *                       type: string
 *                       example: "Blood Test"
 *                     drugPrescriptions:
 *                       type: array
 *                       items:
 *                         type: string
 *                         example: "Paracetamol"
 *                     patientAdvice:
 *                       type: string
 *                       example: "Drink plenty of fluids."
 *                     privateNotes:
 *                       type: string
 *                       example: "Patient has a history of asthma."
 *                     followUpDate:
 *                       type: string
 *                       format: date
 *                       example: "2024-10-30"
 *                     followUpTimeSlot:
 *                       type: string
 *                       example: "10:00 AM"
 *                 vitals:
 *                   type: object
 *                   nullable: true
 *                   properties:
 *                     bloodPressure:
 *                       type: string
 *                       example: "120/80"
 *                     spo2:
 *                       type: number
 *                       example: 98
 *                     temperature:
 *                       type: number
 *                       example: 98.6
 *                     height:
 *                       type: number
 *                       example: 170
 *                     weight:
 *                       type: number
 *                       example: 70
 *                     pulseRate:
 *                       type: number
 *                       example: 72
 *                     rbs:
 *                       type: number
 *                       example: 90
 *                     heartRate:
 *                       type: number
 *                       example: 72
 *                     respiratoryRate:
 *                       type: number
 *                       example: 16
 *                 doctorId:
 *                   type: string
 *                   example: "60d21b4667d0d8992e610c88"
 *                 clinic:
 *                   type: object
 *                   properties:
 *                     clinicName:
 *                       type: string
 *                       example: "Health Clinic"
 *                     logo:
 *                       type: string
 *                       example: "https://example.com/logo.png"
 *                     address:
 *                       type: string
 *                       example: "123 Main St, Cityville"
 *                     adminUserId:
 *                       type: object
 *                       properties:
 *                         mobile:
 *                           type: string
 *                           example: "************"
 *                         email:
 *                           type: string
 *                           example: "<EMAIL>"
 *                 doctorName:
 *                   type: string
 *                   example: "Dr. John Doe"
 *                 patientName:
 *                   type: string
 *                   example: "Jane Smith"
 *                 patientAge:
 *                   type: number
 *                   example: 30
 *                 patientGender:
 *                   type: string
 *                   example: "Female"
 *                 patientMobile:
 *                   type: string
 *                   example: "************"
 *                 clinicPatientId:
 *                   type: string
 *                   example: "CP-123456"
 *                 appointmentDate:
 *                   type: string
 *                   format: date
 *                   example: "2024-10-15"
 *                 timeSlot:
 *                   type: string
 *                   example: "09:00 AM - 09:30 AM"
 *       500:
 *         description: Server error while retrieving the data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
writePrescription.get(
  "/getprescriptionforreport",
  authorizationCheck,
  validateGetWholePrescriptionAndVitalsForAppointment,
  async (req, res, next) => {
    try {
      return await getPrescriptionVitalsWithClinicAndDoctorDetails(req, res);
    } catch (e) {
      next(e);
    }
  }
);
/**
 * @swagger
 * /appointment/write-prescription/getvitalsversonalHistory:
 *   get:
 *     tags:
 *       - write-prescription (mobile)
 *     summary: Retrieve Vitals and Personal History
 *     description: Fetches the vitals and personal history of a patient for a specified appointment.
 *     parameters:
 *       - in: query
 *         name: appointmentId
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d21b4667d0d8992e610c85"
 *         description: The ID of the appointment.
 *       - in: query
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d21b4667d0d8992e610c86"
 *         description: The ID of the patient.
 *     responses:
 *       200:
 *         description: Successfully retrieved vitals and personal history.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Contains the vitals and personal history data.
 *                   properties:
 *                     vitals:
 *                       type: object
 *                       properties:
 *                         bloodPressure:
 *                           type: object
 *                           properties:
 *                             systolic:
 *                               type: number
 *                               example: 343
 *                             diastolic:
 *                               type: number
 *                               example: null
 *                         _id:
 *                           type: string
 *                           example: "674ec26689fd6724e0b4aafb"
 *                         appointment:
 *                           type: string
 *                           example: "674ea8729b90983595e0d342"
 *                         pulseRate:
 *                           type: number
 *                           example: 93
 *                         rbs:
 *                           type: number
 *                           example: 34
 *                         respiratoryRate:
 *                           type: number
 *                           example: 343
 *                         temperature:
 *                           type: number
 *                           example: 24
 *                     personalHistory:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           _id:
 *                             type: string
 *                             example: "674ec6629b90983595e0e2a0"
 *                           activity:
 *                             type: string
 *                             example: "testdf"
 *                           nature:
 *                             type: string
 *                             example: "dfd"
 *                           notes:
 *                             type: string
 *                             example: "dfd"
 *                           patient:
 *                             type: string
 *                             example: "67459f522b599876bb28eba4"
 *       400:
 *         description: Bad request. Missing or invalid parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Missing appointmentId or patientId."
 *       500:
 *         description: Server error while retrieving the data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */

writePrescription.get(
  "/getvitalsversonalHistory",
  authorizationCheck,
  validateGetVitalsPersonalHistory,
  async (req, res, next) => {
    try {
      return await getVitalsPersonalHistory(req, res);
    } catch (e) {
      next(e);
    }
  }
)

/**
 * @swagger
 * /appointment/write-prescription/upsertvitals:
 *   post:
 *     tags:
 *       - write-prescription (mobile)
 *     summary: Upsert vitals and personal history for a patient
 *     description: Updates or inserts vital information and personal history for a patient based on the provided appointment, client, and patient IDs.
 *     parameters:
 *       - in: query
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d21b4667d0d8992e610c85"
 *         description: The ID of the patient for whom vitals and personal history are being upserted.
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d21b4667d0d8992e610c86"
 *         description: The ID of the clinic or client associated with the patient.
 *       - in: query
 *         name: appointmentId
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d21b4667d0d8992e610c87"
 *         description: The ID of the appointment associated with the vitals and personal history.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               vitals:
 *                 type: object
 *                 nullable: true
 *                 properties:
 *                   bloodPressure:
 *                     type: string
 *                     example: "120/80"
 *                   spo2:
 *                     type: number
 *                     example: 98
 *                   temperature:
 *                     type: number
 *                     example: 98.6
 *                   height:
 *                     type: number
 *                     example: 170
 *                   weight:
 *                     type: number
 *                     example: 70
 *                   pulseRate:
 *                     type: number
 *                     example: 72
 *                   rbs:
 *                     type: number
 *                     example: 90
 *                   respiratoryRate:
 *                     type: number
 *                     example: 16
 *               personalHistories:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     activity:
 *                       type: string
 *                       example: "Smoking"
 *                     nature:
 *                       type: string
 *                       example: "Occasional"
 *                     notes:
 *                       type: string
 *                       example: "Patient has reduced frequency recently."
 *     responses:
 *       201:
 *         description: Successfully upserted vitals and personal history.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: The updated vitals and personal history data.
 *                 message:
 *                   type: string
 *                   example: "Vital added successfully"
 *       400:
 *         description: Bad request or validation error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid input data."
 *       500:
 *         description: Server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */

writePrescription.post(
  "/upsertvitals",
  authorizationCheck,
  validateVitals,
  async (req, res, next) => {
    try {
      return await upsertVitalsNdPersonalHistory(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/write-prescription/upsertmedicalhistory:
 *   post:
 *     tags:
 *       - write-prescription (mobile)
 *     summary: Add or update user medical history
 *     description: Upsert various sections of a patient's medical history including allergies, medication, family history, past history, and past procedures.
 *     parameters:
 *       - in: query
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the patient.
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the clinic/client.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               allergies:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     duration:
 *                       type: string
 *                     notes:
 *                       type: string
 *               medication:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     duration:
 *                       type: string
 *                     notes:
 *                       type: string
 *               familyHistory:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     duration:
 *                       type: string
 *                     notes:
 *                       type: string
 *               pastHistory:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     duration:
 *                       type: string
 *                     notes:
 *                       type: string
 *               pastProcedureHistory:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     duration:
 *                       type: string
 *                     notes:
 *                       type: string
 *               personalHistory:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     activity:
 *                       type: string
 *                     nature:
 *                       type: string
 *                     notes:
 *                       type: string
 *     responses:
 *       '201':
 *         description: Medical history added or updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Result of the upsert operation.
 *                 message:
 *                   type: string
 *                   example: Medication added successfully.
 *       '400':
 *         description: Bad request due to invalid input.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid input data."
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */


writePrescription.post(
  "/upsertmedicalhistory",
  authorizationCheck,
  validateUpsertMedicalHistory,
  async (req, res, next) => {
    try {
      return await upsertUserMedicalHistory(req, res);
    } catch (e) {
      next(e);
    }
  }
);


/**
 * @swagger
 * /appointment/write-prescription/upsertsymptomdiagnosis:
 *   post:
 *     tags:
 *       - write-prescription (mobile)
 *     summary: Add or update symptom and diagnosis data
 *     description: Upserts symptom and diagnosis information for a specific patient, clinic, and appointment. If a record exists, it updates the existing data; otherwise, it creates a new record.
 *     parameters:
 *       - in: query
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the patient.
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the clinic/client.
 *       - in: query
 *         name: appointmentId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the appointment.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               symptoms:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: "Cough"
 *               diagnosis:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: "Viral Fever"
 *     responses:
 *       '201':
 *         description: Symptom and diagnosis data added or updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: The newly created or updated symptom and diagnosis record.
 *                 message:
 *                   type: string
 *                   example: Symptom & Diagnosis added successfully.
 *       '400':
 *         description: Bad request due to invalid input.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid input data."
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */



writePrescription.post(
  "/upsertsymptomdiagnosis",
  authorizationCheck,
  validateSymptomDiagnosis,
  async (req, res, next) => {
    try {
      return await symptomDiagnosisUpsert(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/write-prescription/upsertlabtest:
 *   post:
 *     tags:
 *       - write-prescription (mobile)
 *     summary: Add or update lab test data
 *     description: Upserts lab test information for a specific patient, clinic, and appointment. If a record exists, it updates the existing data; otherwise, it creates a new record.
 *     parameters:
 *       - in: query
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the patient.
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the clinic/client.
 *       - in: query
 *         name: appointmentId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the appointment.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               labTests:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: "Blood Test"
 *                     duration:
 *                       type: string
 *                       example: "1 week"
 *                     notes:
 *                       type: string
 *                       example: "Urgent test required."
 *     responses:
 *       '201':
 *         description: Lab test data added or updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: The newly created or updated lab test record.
 *                 message:
 *                   type: string
 *                   example: Lab test added successfully.
 *       '400':
 *         description: Bad request due to invalid input.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid input data."
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */

writePrescription.post(
  "/upsertlabtest",
  authorizationCheck,
  validateLabTests,
  async (req, res, next) => {
    try {
      return await labTestUpsert(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/write-prescription/upsertdrugprescriptions:
 *   post:
 *     tags:
 *       - write-prescription (mobile)
 *     summary: Add or update drug prescription data
 *     description: Upserts drug prescription information for a specific patient, clinic, and appointment. If a record exists, it updates the existing data; otherwise, it creates a new record.
 *     parameters:
 *       - in: query
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the patient.
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the clinic/client.
 *       - in: query
 *         name: appointmentId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the appointment.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               drugs:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: "Paracetamol"
 *                     dosage:
 *                       type: string
 *                       example: "500mg"
 *                     frequency:
 *                       type: string
 *                       example: "Once a day"
 *                     duration:
 *                       type: string
 *                       example: "5 days"
 *               patientAdvice:
 *                 type: string
 *                 example: "Take the medication after meals."
 *               privateNotes:
 *                 type: string
 *                 example: "Patient has allergies to aspirin."
 *               followUpDate:
 *                 type: string
 *                 format: date
 *                 example: "2024-12-15"
 *               followUpTimeSlot:
 *                 type: string
 *                 example: "10:00 AM - 11:00 AM"
 *     responses:
 *       '201':
 *         description: Drug prescription data added or updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: The newly created or updated drug prescription record.
 *                 message:
 *                   type: string
 *                   example: "Drugs added successfully."
 *       '400':
 *         description: Bad request due to invalid input.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid input data."
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */

writePrescription.post(
  "/upsertdrugprescriptions",
  authorizationCheck,
  validateDrugs,
  async (req, res, next) => {
    try {
      return await upsertDrugsPrescription(req, res);
    } catch (e) {
      next(e);
    }
  }
);

/**
 * @swagger
 * /appointment/write-prescription/makereciept:
 *   post:
 *     tags:
 *       - write-prescription
 *       - write-prescription (mobile)
 *     summary: Create a receipt for a clinic appointment
 *     description: Generates a receipt for a specified clinic appointment. If the appointment has ended, it retrieves the existing invoice; otherwise, it updates the appointment status and creates a new invoice.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   clinicId:
 *                     type: string
 *                     example: "60d21b4667d0d8992e610c85"
 *                     description: The ID of the clinic for which the receipt is generated.
 *                   appointmentId:
 *                     type: string
 *                     example: "60d21b4667d0d8992e610c86"
 *                     description: The ID of the appointment for which the receipt is created.
 *     responses:
 *       200:
 *         description: Successfully created receipt.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isValid:
 *                   type: boolean
 *                   example: true
 *                 invoiceId:
 *                   type: string
 *                   example: "60d21b4667d0d8992e610c87"
 *       400:
 *         description: Bad request or validation error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal error occurred."
 *                 message:
 *                   type: string
 *                   example: "Something went wrong"
 */
writePrescription.post(
  "/makereciept",
  authorizationCheck,
  ValidateMarkReceipt,
  async (req, res, next) => {
    try {
      return await makeReciept(req, res);
    } catch (e) {
      next(e);
    }
  }
);

export default writePrescription;
