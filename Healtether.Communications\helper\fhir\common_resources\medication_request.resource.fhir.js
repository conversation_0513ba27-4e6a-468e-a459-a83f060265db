
import { v4 as uuidv4 } from "uuid";
import { medicationRequestMetadata, medicationRequestDiv } from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";
import { getSnomedCtCode } from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";
import portalDb from "../../../config/clinics.collections.config.js";
const Prescription = portalDb.model("Prescription");

export const generateMedicationRequestResource = async (
    status, intent, authoredOn, medication, forCondition, forReasons, dosageInstruction,
    patientResource, practitionerResources, conditionResources, doctors, patientId
) => {
    const requestId = uuidv4();
    console.log("forReasons", forReasons);
        const prescription = await Prescription.findOne({ patient: patientId }).sort({
      "created.on": -1,
    });

    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    const matchingPractitioner = practitionerResources.find(practitioner =>
        practitioner.resource.name.some(nameObj => normalizedDoctors.includes(normalize(nameObj.text)))
    );

    const getSnomedDataMedication = await generateSnomedCtCode(medication);
 
    // Process forReasons as an array asynchronously
    let getSnomedDataMedicationReasons = [];
    if (forReasons && forReasons.length > 0) {
      getSnomedDataMedicationReasons = await Promise.all(
        forReasons.map(async (reason) => {
          const snomedData = await generateSnomedCtCode(reason);
          const duration = prescription.symptoms
            .filter((sym) => sym.name === snomedData.term)
            .map((diag) => `${diag.duration.value} ${diag.duration.unit}`)
            .join(" ");
          const notes = `(Duration: ${duration}) (Notes: ${prescription.symptoms
            .filter((sym) => sym.name === snomedData.term)
            .map((d) => d.notes)
            .join(" ")})`;

          return getSnomedCtCode(
            snomedData.conceptId,
            toTitleCase(snomedData.term),
            notes
          );
        })
      );
    }

    // Process forCondition as an array
    let matchedConditions = [];
    if (forCondition && forCondition.length > 0) {
        matchedConditions = forCondition.map(condition => {
            const normalizedCondition = condition.trim().toLowerCase();
            return conditionResources.find(cond =>
                cond.resource.code.coding.some(coding => coding.display.trim().toLowerCase() === normalizedCondition)
            );
        }).filter(Boolean); // Remove any undefined values
    }

    let finalDosageInstructions = [];
    let dosageInstructions = {};

    for (const instruction of dosageInstruction) {
        for (const [key, value] of Object.entries(instruction)) {
            if (key === "text") {
                dosageInstructions.text = value;
            } else if (key === "repeat" && typeof value === "object") {
                dosageInstructions.timing = { repeat: value };
            } 
            // else if (typeof value === "string") {
            //     let snomedFormatted = null;
            //     let snomedData = null;

            //     if(value!== ""&&!mongoose.Types.ObjectId.isValid(value)) {
            //      snomedData = await generateSnomedCtCode(toTitleCase(value.trim()));
            //      snomedFormatted = getSnomedCtCode(snomedData.conceptId, toTitleCase(snomedData.term));
            //     }

                // if (key === "additionalInstruction") {
                //     if (!dosageInstructions.additionalInstruction) {
                //         dosageInstructions.additionalInstruction = [];
                //     }
                //     dosageInstructions.additionalInstruction.push(snomedFormatted);
                // } else if (key === "route") {
                //     dosageInstructions.route = snomedFormatted;
                // }
                //  else {
                //     dosageInstructions[key] = snomedFormatted;
                // }
            // } 
            // else {
            //     dosageInstructions[key] = value;
            // }
        }
    }
    if (Object.keys(dosageInstructions).length > 0) {
        finalDosageInstructions.push(dosageInstructions);
    }

    return {
        fullUrl: `urn:uuid:${requestId}`,
        resource: {
            resourceType: 'MedicationRequest',
            id: requestId,
            meta: medicationRequestMetadata(),
            status,
            intent: intent,
            authoredOn: authoredOn,
            medicationCodeableConcept: getSnomedCtCode(getSnomedDataMedication.conceptId, toTitleCase(getSnomedDataMedication.term)),
            subject: {
                reference: `urn:uuid:${patientResource.resource.id}`,
                display: patientResource.resource.resourceType
            },
            requester: matchingPractitioner ? {
                reference: `urn:uuid:${matchingPractitioner.resource.id}`,
                display: matchingPractitioner.resource.resourceType
            } : null,
            reasonReference: matchedConditions.map(condition => ({
                reference: `urn:uuid:${condition.resource.id}`,
                display: toTitleCase(condition.resource.resourceType)
            })),
            dosageInstruction: finalDosageInstructions,
            reasonCode: getSnomedDataMedicationReasons
            // text: medicationRequestDiv()
        }
    };
};
