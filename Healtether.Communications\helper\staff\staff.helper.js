import mongoose from "mongoose";
import portalDb from "../../config/portalmongodb.config.js";

export const getDoctor = async (clinicId) => {
  var staffModel = portalDb.model("Staff");
  const doctor = await staffModel
    .find({
      deleted: false,
      clinic: new mongoose.Types.ObjectId(clinicId),
    })
    .select({
      _id: 1,
      staffId: 1,
      firstName: 1,
      lastName: 1,
      mobile: 1,
      specialization: 1,
    });

  return doctor;
};
