import React from "react";
import { Icons } from "./icons";
import { Buttons } from "./appointment/button";

export default function SuccessfullOrFailed({
  success,
  error,
  nextButtonTitle,
  nextButton,
  closeButtonTitle,
  closeButton,
}) {
  return (
    <article className="h-full flex flex-col items-center justify-center gap-3 py-14">
      {success ? (
        <Icons.success_check className="w-8 h-8" />
      ) : (
        <Icons.cancel color="#F44444" className="w-8 h-8" />
      )}
      <div className=" text-lg font-semibold text-dark max-w-[212px] text-center">
        {success || error}
      </div>
      {nextButtonTitle && (
        <Buttons.primary
          title={nextButtonTitle}
          classname=" mt-14 w-full rounded-md!"
          onClick={nextButton}
        />
      )}
      {closeButtonTitle && (
        <Buttons.secondary
          title={closeButtonTitle}
          classname="mt-1 w-full rounded-md!"
          onClick={closeButton}
        />
      )}
    </article>
  );
}
