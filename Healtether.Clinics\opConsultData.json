{"general": {"artifact": "OPConsultRecord", "hipUrl": "https://www.healtether.com", "hipIds": ["hip1", "hip2"], "status": "final", "clientId": "SBX_003515"}, "patient": {"id": "677f5f98f8d2a9af571d00e8", "abhaNumber": "91-1248-5708-0632", "abhaAddress": "testm_233@sbx", "name": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["Miss"]}, "gender": "female", "dob": "2003-12-05", "address": [{"use": "home", "type": "physical", "postalCode": "474001", "country": "india", "district": "MADHYA PRADESH", "city": "GWALIOR", "state": "MADHYA PRADESH", "text": "nimma ji ji kho , b<PERSON><PERSON> mandi gate, jiwajiganj, Gird, Gird, Gwalior, Madhya Pradesh"}], "doctors": ["ka<PERSON><PERSON><PERSON><PERSON><PERSON>"], "allergyIntolerances": [{"type": "peanut", "clinicalStatus": "active", "verificationStatus": "confirmed", "notes": ["test"], "doctor": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}]}, "encounter": {"status": "finished", "startTime": "2025-03-22T10:35:34.669Z", "endTime": "2025-03-22T10:36:36.294Z"}, "organization": {"name": "Health Organization", "telecom": [{"system": "phone", "value": "**********", "use": "work"}], "licenses": [{"code": "PRN", "display": "Provider number", "licNo": "1234567"}]}, "appointment": {"status": "booked", "serviceCategories": ["Consultation"], "serviceTypes": ["General"], "specialty": ["Cardiology"], "appointmentType": "consultation", "description": "Follow-up consultation", "start": "2025-03-22T10:35:34.669Z", "end": "2023-10-01T11:00:00+05:30", "created": "2025-03-20T13:46:23.230Z", "reasonReference": ["Hypertension"], "basedOnServices": ["blood test"]}, "practitioners": [{"names": ["ka<PERSON><PERSON><PERSON><PERSON><PERSON>"], "licenses": [{"code": "MD", "display": "Medical License number", "licNo": "12345678"}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "male", "birthDate": "2025-04-05", "patient": "677f5f98f8d2a9af571d00e8", "address": [{"use": "home", "type": "physical", "postalCode": "533101", "country": "india", "district": "<PERSON>hya pradesh", "city": "rajahmundry", "state": "<PERSON>hya pradesh", "text": ""}]}], "serviceRequests": [{"status": "completed", "intent": "order", "categories": ["blood test"], "type": "laboratory Test"}], "conditions": [{"type": "Hypertension", "status": "active", "recordedDate": "2025-04-05T11:13:45.202Z", "startDate": "2025-04-05T11:13:45.202Z", "endDate": "2025-04-05T11:13:45.202Z"}], "medicationStatements": [{"status": "completed", "type": "Telmisartan 20 mg oral tablet"}], "medicationRequests": [{"status": "active", "intent": "order", "authoredOn": "2025-04-05T11:13:45.202Z", "medication": "Paracetamol", "forCondition": ["Hypertension"], "reason": ["Pain relief"], "dosageInstruction": [{"text": "500mg every 6 hours", "repeat": {"frequency": "1-1-1", "period": 1, "periodUnit": "Weeks"}, "route": "Oral", "doseQuantity": {"value": "1", "unit": "tablet"}, "site": "Mouth", "additionalInstruction": "test"}]}], "procedures": [{"status": "completed", "type": "Electrocardiogram", "performedDateTime": "2023-10-01T10:30:00+05:30", "followUp": ["Follow-up consultation"]}], "documentReferences": [], "signature": {"who": {"type": "Practitioner", "name": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ=="}}