await jest.unstable_mockModule("../../helper/abha/abha.milestone.three.helper.js", () => ({
  initiateConsentRequestHelper: jest.fn(),
  fetchAbhaApi: jest.fn(),
  checkConsentStatusHelper:jest.fn(),
  fetchConsentDetailsHelper:jest.fn(),
  initiateConsentRequestHelper:jest.fn(),
  notifyConsentRequestHelper:jest.fn(),
  sendHealthInformationRequest:jest.fn(),
  sendNotificationRequest:jest.fn(),
}));


const { initiateConsentRequestHelper} = await import('../../helper/abha/abha.milestone.three.helper.js');
const {fetchConsentDetailsHIP} = await import('../../controllers/abha.milestone.three.controller.js')
import { jest, beforeAll, afterAll } from "@jest/globals"
const { app, server } = await import("../../index.js");
const { LogDBCloseConnection } = await import("../../config/whatsapp.collections.config.js");
const { CloseConnection } = await import("../../config/clinics.collections.config.js");
afterAll(async () => {
  await CloseConnection();
  await LogDBCloseConnection();
  server.close();
});

import request from "supertest";

describe("POST /api/m3/initiateconsentrequesthip", () => {
  test("should return 202 when consent request is initiated successfully", async () => {
    initiateConsentRequestHelper.mockResolvedValueOnce({
      isSuccess: true,
      requestId: "req-12345",
    });

    const response = await request(app).post("/api/m3/initiateconsentrequesthip").send({
      data: {
        abhaAddress: "testm_233@sbx",
        hiu: "IN2410000949",
        hiTypes: ["OPConsultation"],
        purposeCode: "CAREMGT",
        purposeText: "Care Management",
        requesterName: "Test Requester",
        dateRange: { startDate: "2025-01-01", endDate: "2025-12-31" },
        expiryDate: "2026-01-01",
      },
    });

    expect(response.status).toBe(202);
    expect(response.body).toEqual({
      message: "Consent Request Initiated",
      data: {
        isSuccess: true,
        requestId: "req-12345",
      },
    });
    expect(initiateConsentRequestHelper).toHaveBeenCalled();
  });

  test("should return 500 when consent request fails", async () => {
    initiateConsentRequestHelper.mockResolvedValueOnce({
      isSuccess: false,
    });

    const response = await request(app).post("/api/m3/initiateconsentrequesthip").send({
      data: {
        abhaAddress: "invalid_address",
        hiu: "IN2410000949",
        hiTypes: ["OPConsultation"],
        purposeCode: "CAREMGT",
        purposeText: "Care Management",
        requesterName: "Test Requester",
        dateRange: { startDate: "2025-01-01", endDate: "2025-12-31" },
        expiryDate: "2026-01-01",
      },
    });

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: "Consent Request Initiated",
      data: {
        "isSuccess": false,
      },
    });
    expect(initiateConsentRequestHelper).toHaveBeenCalled();
  });
});


// describe("POST /api/m3/checkconsentstatuship", () => {
//   test("should return 202 when consent status is retrieved successfully", async () => {
//     checkConsentStatusHelper.mockResolvedValueOnce({
//       isSuccess: true,
//       data: { status: "GRANTED" },
//     });

//     const response = await request(app)
//       .post("/api/m3/checkconsentstatuship")
//       .set("x-hiu-id", "IN2410000949")
//       .send({
//         consentRequest: { id: "b053eb8f-0d0a-4ebc-85cc-d617c188a6b5" },
//       });

//     });

//     expect(checkConsentStatusHelper).toHaveBeenCalledWith(
//       { consentRequestId: "b053eb8f-0d0a-4ebc-85cc-d617c188a6b5" },
//       "IN2410000949",
//       expect.any(Object)
//     );
//   });





