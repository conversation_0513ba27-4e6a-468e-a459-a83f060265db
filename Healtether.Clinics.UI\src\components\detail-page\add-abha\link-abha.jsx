import React from "react";
import { Icons } from "../icons";
import { Buttons } from "../appointment/button";
import { Input } from "../input";

export default function LinkAbha({
  onCreate,
  patientMobile,
  abhaData,
  LinkAbha,
  setSelectedAbha,
  selectedAbha,
  handleBack
}) {
  return (
    <div>
      <div className="">
        <div className="mb-2 text-sm font-semibold text-dark">
          Mobile number *
        </div>
        <Input.number
          value={patientMobile}
          handleChange={(e) => console.log(e)}
          placeholder="**********"
        />
      </div>
      <div className="mt-4 text-sm font-semibold text-dark">
        ABHA addresses associated with this mobile number
      </div>
      <section className="mt-2 gap-2 max-h-64 overflow-y-auto">
        {abhaData?.length > 0 &&
          abhaData.map((responseItem, responseIndex) =>
            responseItem.ABHA.map((item) => (
              <div
                key={item.ABHANumber}
                className="flex items-start gap-3 p-2 border-b border-color_muted/20"
              >
                <input
                  type="radio"
                  name="abha-number"
                  checked={selectedAbha?.ABHANumber === item.ABHANumber}
                  onChange={() => {
                    setSelectedAbha(item);
                    localStorage.setItem("txnId", responseItem.txnId);
                  }}
                  className="mt-1.5"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium mr-2">{item.name}</span>
                    <span className="text-sm text-gray-500">
                      {item.gender === "M" ? "Male" : "Female"}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Icons.abha_num className="w-4 h-4" />
                      {item.ABHANumber}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
      </section>
      <footer className="flex justify-end gap-3 mt-3">
        <Buttons.secondary
          onClick={handleBack}
          title={"Back"}
          classname="float-right mt-4"
        />
        <Buttons.primary
          onClick={() => {
            LinkAbha();
          }}
          title={"Link ABHA"}
          classname="float-right mt-4"
        />
      </footer>
      <div
        onClick={onCreate}
        className="flex items-center justify-between px-4 py-2 font-roboto font-normal cursor-pointer text-lg text-[#5351C7]"
      >
        Create New ABHA
      </div>
    </div>
  );
}
