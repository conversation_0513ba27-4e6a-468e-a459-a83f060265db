import { useClickOut } from "./../../utils/hooks/useClickout";

export default function dropdown() {
  const { targetRef, isOpen, setIsOpen } = useClickOut.auto();

  const DropdownMenuItem = ({ handleClick, className, children }) => {
    return (
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          handleClick();
        }}
        className={`${className} text-sm! sm:text-base font-medium justify-between h-11 px-4 pt-2.5 cursor-pointer hover:bg-color_dark/10 duration-200 capitalize`}
      >
        {children}
      </div>
    );
  };

  const DropdownMenuContent = ({ className, bottom, children }) => (
    <div
      ref={targetRef}
      className={`${className} ${
        isOpen
          ? " visible opacity-100 scale-100"
          : " invisible opacity-0 scale-0"
      } ${
        bottom ? "mb-3 bottom-full" : "top-11 origin-top mt-1"
      } bg-white border border-color_muted/20 duration-300 w-full min-w-max fixed z-50 rounded-sm shadow-xl overflow-y-auto`}
      style={{
        width: "auto",
        minWidth: "350px",
      }}
    >
      <div className="p-1">{children}</div>
    </div>
  );

  const DropdownMenu = ({ className, children }) => (
    <div
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsOpen(!isOpen);
      }}
      className={`${className} relative`}
    >
      {children}
    </div>
  );
  return {
    isOpen,
    setIsOpen,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
  };
}
