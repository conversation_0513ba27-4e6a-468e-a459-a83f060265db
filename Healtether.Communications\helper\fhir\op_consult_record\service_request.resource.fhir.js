import { v4 as uuidv4 } from "uuid";
import { getSnomedCtCode, serviceRequestMetadata, serviceRequestDiv } from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "../common_resources/snomed_ct_code.generator.fhir.js";
import portalDb from "../../../config/clinics.collections.config.js";
const Prescription = portalDb.model("Prescription");

export const generateServiceRequestResource = async (status, intent, categories, type, currentTime, patientResource, practitionerResources, doctors, patientId) => {
    const id = uuidv4();
    const prescription = await Prescription.findOne({ patient: patientId }).sort({
        "created.on": -1,
    });
    const getSnomedDataType = await generateSnomedCtCode(type);

    const categoryData = await Promise.all(
        categories.map(async (category) => {
            const trimmedCategory = category.trim();
            const snomedCategory = await generateSnomedCtCode(trimmedCategory);
             const notes = `(Notes: ${prescription.labTests.filter(test=>test.name==trimmedCategory).map(d => d.notes).join(" ")})`;
            return getSnomedCtCode(snomedCategory.conceptId, trimmedCategory, notes);
        })
    );

    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    const matchingPractitioner = practitionerResources.find(practitioner =>
        practitioner.resource.name.some(nameObj => normalizedDoctors.includes(normalize(nameObj.text)))
    );

    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'ServiceRequest',
            id,
            meta: serviceRequestMetadata(),
            status,
            intent,
            authoredOn: currentTime,
            category: categoryData,
            code: getSnomedCtCode(getSnomedDataType.conceptId, getSnomedDataType.term),
            subject: {
                reference: `urn:uuid:${patientResource.resource.id}`,
                display: patientResource.resource.resourceType
            },
            requester: matchingPractitioner ? {
                reference: `urn:uuid:${matchingPractitioner.resource.id}`,
                display: matchingPractitioner.resource.resourceType
            } : null,
            text: serviceRequestDiv()
        }
    };
};
