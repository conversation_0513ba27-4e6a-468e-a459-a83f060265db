
import { getPatientByMobileAndClinic } from "../../helper/patient/patient.helper.js";
import { getCacheDetailForAppointmentTemplate } from "../../helper/whatsapp/whatsapp.helper.js";
import { WHATSAPP_SCHEDULE_APPOINTMENT_FLOWTOKEN } from "../../keys/whatsapp/flowtoken.js";
import {  getProperMobile } from "../../utils/common.js";

export const getAppointmentSummaryTemplate = (to, code, clinicName, patientName, doctorName, scheduleDate, timeSlots, isOnline) => {
    var toMobile = getProperMobile(to);
    var summaryTemplate = {
        messaging_product: "whatsapp",
        recipient_type: "individual",
        to: toMobile,
        type: "template",
        template: {
            name: process.env.WHATSAPP_TEMPLATE_APPOINTMENT_SCHEDULE, 
            language: {
                code: (code != null && code != ""
                    ? code
                    : "en")
            },
            components: [
                {
                    type: "body",
                    parameters: [
                        {
                            type: "text",
                            text: clinicName
                        }, {
                            type: "text",
                            text: patient<PERSON><PERSON>
                        }, {
                            type: "text",
                            text: doctor<PERSON><PERSON>
                        }, {
                            type: "text",
                            text: scheduleDate + (isOnline
                                ? " (online)"
                                : "")
                        }, {
                            type: "text",
                            text: timeSlots
                        }
                    ]
                }
            ]
        }
    };

    return summaryTemplate;
}



export const getAppointmentLinkTemplate = (to, code, clinicName, patientName, doctorName, scheduleDate, timeSlots, googleMeetLink) => {
    var toMobile = getProperMobile(to);
    var summaryTemplate = {
        messaging_product: "whatsapp",
        recipient_type: "individual",
        to: toMobile,
        type: "template",
        template: {
            name: "healtether_virtual_appointment_link ",
            language: {
                code: (code != null && code != ""
                    ? code
                    : "en")
            },
            components: [
                {
                    type: "body",
                    parameters: [
                        {
                            type: "text",
                            text: doctorName
                        }, {
                            type: "text",
                            text: `${scheduleDate} ${timeSlots}`
                        }, {
                            type: "text",
                            text: patientName
                        },
                        {
                            type: "text",
                            text: clinicName
                        }
                    ]

                },
                {
                    type: "button",
                    sub_type: "URL",
                    index: "0",
                    parameters: [
                        {
                            type: "TEXT",
                            text: googleMeetLink
                        }
                    ]
                }
            ]
        }
    };

    return summaryTemplate;
}



export const getScheduleAppointmentTemplate = async (to, code, clinicName, clinicId) => {
    var toMobile = getProperMobile(to);
    var minDate = new Date().getTime();
    var calcYear = parseInt(new Date().getFullYear()) + 2;
    var maxDate = new Date().setFullYear(calcYear);

    var scheduleHeader = "Schedule Appointment - " + clinicName;
    var scheduleBody = "Please fill form to book appointment";
    var scheduleFooter = "Healtether";
    var doctorTimeSlotFromClinicCache = await getCacheDetailForAppointmentTemplate(clinicId);
    var patients = await getPatientByMobileAndClinic(to, clinicId);

    var patientDetail = patients
        ?.map((item) => {

            return {
                id: item._id,
                title: item.firstName + " " + item.lastName,
                description: "Patient ID : " + item.patientId
            };

        });

    switch (code) {
        case "ta":
            {
                scheduleHeader = "முன்பதிவு நியமனம் - " + clinicName;
                scheduleBody = "சந்திப்பை முன்பதிவு செய்ய படிவத்தை நிரப்பவும்";
                scheduleFooter = "ஹீல்டெதர்";
            }
            break;
        case "hi":
            {

                scheduleHeader = "अपॉइंटमेंट शेड्यूल करें - " + clinicName;
                scheduleBody = "कृपया अपॉइंटमेंट बुक करने के लिए फॉर्म भरें";
                scheduleFooter = "हीलटेथर";
            }
            break;
    }

    var appointmentTemplate = {
        recipient_type: "individual",
        messaging_product: "whatsapp",
        to: toMobile,
        type: "interactive",
        interactive: {
            type: "flow",
            header: {
                type: "text",
                text: scheduleHeader
            },
            body: {
                text: scheduleBody
            },
            footer: {
                text: scheduleFooter
            },
            action: {
                name: "flow",
                parameters: {
                   // mode: "draft",
                    flow_message_version: "3",
                    flow_token: WHATSAPP_SCHEDULE_APPOINTMENT_FLOWTOKEN,
                    flow_id: process.env.WHATSAPP_SCHEDULE_APPOINTMENT_FLOWID,
                    flow_cta: "Book!",
                    flow_action: "navigate",
                    flow_action_payload: {
                        screen: "HEALTETHER_APPOINTMENT_TWO",
                        data: {
                            data_patient: patientDetail != null
                                ? patientDetail
                                : [],
                            data_doctors: doctorTimeSlotFromClinicCache.doctors != null
                                ? doctorTimeSlotFromClinicCache.doctors
                                : [],
                            data_min_date: minDate.toString(),
                            data_max_date: maxDate.toString(),
                            data_clinic:clinicId,
                            is_visible_available:false,
                            is_visible_timeslots:false
                        }
                    }
                }
            }
        }
    };

    return appointmentTemplate;
}