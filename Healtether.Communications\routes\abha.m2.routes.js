import { Router } from "express";
import { GenerateLinkingTokenM22, LinkCareContextM2 } from "../controllers/abha.milestone.two.js";
// import { getPatientData } from "../helper/abha/milestone.two.helper.js";

const abhaM2Router = Router();

abhaM2Router.route("/link-care-context").post((req, res, next) => {
  try {
    return LinkCareContextM2(req, res);
  } catch (e) {
    next(e);
  }
});
abhaM2Router.route("/generate-linking-token").post((req, res, next) => {
  try {
    return GenerateLinkingTokenM22(req, res);
  } catch (e) {
    next(e);
  }
});

export default abhaM2Router;
