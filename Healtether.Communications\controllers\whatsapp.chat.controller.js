
import {
  getChatMessage,
  saveChatMessage,
} from "../helper/whatsapp/messagelog.helper.js";
import {
  addRecentChat,
  getRecentChatByMobile,
  getRecentChats,
  setChatReaded,
} from "../helper/whatsapp/recentchat.helper.js";
import { getDoctorDetailDataExchange, sendReply } from "../helper/whatsapp/whatsapp.helper.js";
import { sendTemplateTextMessage } from "../template/whatsapp/interactive.template.js";
import { formTextJson } from "../template/whatsapp/text.message.js";
import { getHoursDiff } from "../utils/common.js";
import {
  checkItIsPing,
  decryptWhatsappRequest,
  encryptResponse,
} from "../utils/whatsapp.js";

export const getRecentChatsForClinic = async (req, res) => {
  const data = req.query;
  var recents = await getRecentChats(data.clinicId);
  res.json(recents).status(200);
};

export const setReaded = async (req, res) => {
  const data = req.body.data;
  var recents = await setChatReaded(data.mobile, data.clinicId);
  res.json({ data: "success" }).status(200);
};

export const addRecent = async (req, res) => {
  const data = req.body.data;
  var recents = await addRecentChat(data.mobile, data.clinicId);
  res.json({ data: "success" }).status(200);
};

export const getRecentChatMessageForClinic = async (req, res) => {
  const data = req.query;
  var recents = await getChatMessage(
    data.mobile,
    data.clinicId,
    data.pg,
    data.pgSize
  );
  res.json(recents).status(200);
};

export const appointmentDataExchange = async ({ body }, res) => {
  const { decryptedBody, aesKeyBuffer, initialVectorBuffer } =
    decryptWhatsappRequest(body);
  let isPing = checkItIsPing(decryptedBody);

  if (isPing) {
    var responseData = {
      data: {
        status: "active",
      },
    };

    res.send(encryptResponse(responseData, aesKeyBuffer, initialVectorBuffer));
  } else {
    let doctorTimeSlots = await getDoctorDetailDataExchange(
      decryptedBody?.data?.clinic,
      decryptedBody?.data?.doctor,
      decryptedBody?.data?.scheduleDate
    );
    console.log(
      "Process exchangedata doctor : " + JSON.stringify(doctorTimeSlots)
    );
    let responseData = {
      screen: decryptedBody.screen,
      data: {
        data_time_slots: doctorTimeSlots?.timeSlots,
        availabledetail: doctorTimeSlots?.availableText,
        is_visible_available: doctorTimeSlots?.availableText != "",
        is_visible_timeslots: doctorTimeSlots?.timeSlots.length > 0,
      },
    };
    console.log(
      "Process exchangedata doctor response : " + JSON.stringify(responseData)
    );
    res.send(encryptResponse(responseData, aesKeyBuffer, initialVectorBuffer));
  }
};

export const addChatMessageForClinic = async (req, res) => {
  const data = req.body.data;
  var response = { status: 1 };

  var messageLog = await saveChatMessage(
    data.message,
    data.clinicId,
    data.mobile,
    false,
    false
  );
  res
    .json({
      data: {
        message: {
          message: messageLog.message,
          isDelivered: messageLog.isDelivered,
          isReceived: messageLog.isReceived,
          type: messageLog.type,
          createdOn: messageLog.createdOn,
        },
        success: true,
      },
    })
    .status(200);

  var recentChatForMobile = await getRecentChatByMobile(
    data.mobile,
    data.clinicId
  );
  if (
    recentChatForMobile != null &&
    getHoursDiff(recentChatForMobile.timeStamp) < 20
  ) {
    var textMessage = formTextJson(data.mobile, data.message);
    response = await sendReply(textMessage);
  } else {
    var textTemplate = sendTemplateTextMessage(
      data.mobile,
      "TEST",
      data.message
    );
    response = await sendReply(textTemplate);
  }
  if (response.status == 200) {
    messageLog.isDelivered = true;
    await messageLog.save();
  }
};
