import { jest } from "@jest/globals";
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");
mockApointmentHelper();
mockCommonUtils();
mockFirebaseMethod();
const { buildNotificationText, resultObject, formatTodayDate } = await import("../../utils/common.utils.js");
const { sendNotificationViaToken } = await import("../../config/firebase.admin.js");
const { reScheduleAppointment } = await import("../../controllers/appointments/appointment.controller.js"); 
const { reschedule } = await import("../../helpers/appointment/appointment.helper.js");

describe("reScheduleAppointment", () => {
  let req, res;

  beforeEach(() => {
    req = {
      body: {
        data: {
          id: "66e2f3524f7f4ee268b4c9a5",
          mobile: "**********",
          name: "test23",
          gender: "Female",
          age: "15",
          birthDate: "2009-07-10",
          appointmentDate: "2024-09-15",
          timeSlot: "07:30 PM - 07:50 PM",
          reason: "test",
          virtualConsultation: "true",
          patientId: "patientId123",
          doctorId: "662ca0ad1a2431e16c41ebb1",
          doctorName: "Venkatesh Raja",
          clientId: "662ca0a41a2431e16c41ebaa",
          clinicPatientId: "clinicPatientId123",
        },
      },
      Notificationkey: "mockNotificationKey",
      user: {
        id: "userId",
      },
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  });

  it("should successfully reschedule the appointment and send a notification", async () => {
    const mockAppointment = {
      name: req.body.data.name,
      appointmentDate: req.body.data.appointmentDate,
      clinic: req.body.data.clientId,
      _id: req.body.data.id,
      patientId: req.body.data.patientId,
    };

    reschedule.mockResolvedValue(mockAppointment);
    formatTodayDate.mockReturnValue("Sep 15, 2024"); 
    buildNotificationText.mockReturnValue("Notification message");
    sendNotificationViaToken.mockResolvedValue();
    resultObject.mockReturnValue({
      statusCode: 200,
      success: true,
      data: { id: req.body.data.id, patientId: req.body.data.patientId },
    });

    await reScheduleAppointment(req, res);

    expect(reschedule).toHaveBeenCalledWith(
      req.body.data,
      req.body.data.id,
      req.user
    );

    expect(buildNotificationText).toHaveBeenCalledWith(
      "Appointment for",
      req.body.data.name,
      " has been reschedule on Sep 15, 2024", 
      req.user
    );

    expect(sendNotificationViaToken).toHaveBeenCalledWith(
      req.Notificationkey,
      "Notification message",
      "Appointment",
      true,
      req.body.data.clientId,
      req.user.id
    );

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      statusCode: 200,
      success: true,
      data: { id: req.body.data.id, patientId: req.body.data.patientId },
    });
  });

  it('should return "Already in Started" if the appointment has already started', async () => {
    const mockAppointment = "Already in Started";

    reschedule.mockResolvedValue(mockAppointment);
    resultObject.mockReturnValue({
      statusCode: 200,
      success: false,
      message: "Already in Started",
      data: { id: req.body.data.id },
    });

    await reScheduleAppointment(req, res);

    expect(reschedule).toHaveBeenCalledWith(
      req.body.data,
      req.body.data.id,
      req.user
    );

    expect(resultObject).toHaveBeenCalledWith(200, "Already in Started", false, {
      id: req.body.data.id,
    });

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      statusCode: 200,
      success: false,
      message: "Already in Started",
      data: { id: req.body.data.id },
    });
  });

});
