import Layout from "layout/main/Layout.jsx";
import ActionPropType from "utils/ActionPropType.js";
import ErrorPage from "components/error-page/ErrorPage.jsx";
import Login from "pages/login/Login.jsx";
import Welcome from "pages/login/Welcome.jsx";
import { createBrowserRouter, RouterProvider, useLocation,BrowserRouter  } from "react-router-dom";
import { LoginAction } from "pages/login/Login";
import { CHILD_ROUTES } from "./components/routes/ClinicChildRoutes";
import { AuthorizeComponent } from "./components/routes/AuthorizeComponent";
import { PrescriptionModal } from "components/PrescriptionTemplate.jsx";
import VaccinationCertificate from "./components/VaccinationTemplate";

export default function App() {

  // Add BrowserRouter and RouterProvider to the app
  // This will allow you to use the router in your app
  const router = createBrowserRouter([
    {
      path: "/login",
      element: <Login />,
      errorElement: <ErrorPage />,
      handle: {
        crumb: {
          actionButton: new ActionPropType(
            "addpatient",
            "Add New Patient",
            () => { }
          ),
          breadcrumb: [new ActionPropType("dashboard", "Dashboard", () => { })],
        },
      },
      action: LoginAction,
    },
    {
      path: "/select-clinic",
      element: <Welcome />,
      handle: {
        crumb: {
          actionButton: new ActionPropType(
            "addpatient",
            "Add New Patient",
            () => { }
          ),
          breadcrumb: [new ActionPropType("dashboard", "Dashboard", () => { })],
        },
      },
    },
      {
      path: "/vaccine",
      element: <VaccinationCertificate />,
      handle: {
        crumb: {},
      },
    },
    {
      path: "/",
      element: <AuthorizeComponent component={Layout} />,
      errorElement: <ErrorPage />,
      handle: {
        crumb: {
          actionButton: new ActionPropType(
            "addpatient",
            "Add New Patient",
            () => { }
          ),
          breadcrumb: [new ActionPropType("dashboard", "Dashboard", () => { })],
        },
      },

      children: CHILD_ROUTES,
    },
    {
      path: "/reportTemplate/:appointmentId/:patientId",
      element: <AuthorizeComponent component={PrescriptionModal} />,
      errorElement: <ErrorPage />,
    },
  ]);

  return <RouterProvider router={router} />;
}
