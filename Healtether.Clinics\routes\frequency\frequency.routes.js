import { Router } from "express";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";
import {
    getFrequentTextForPrescription
} from "../../controllers/frequency/frequency.controller.js";
import { validateGetFrequentTextForPrescription } from "../../validation/frequency/frequency.validation.js";
const frequency = Router();

/**
 * @swagger
 * /frequency/getfrequenttextforfrescription:
 *   get:
 *     tags:
 *       - write-prescription (mobile)
 *       - write-prescription
 *     summary: Get frequently used symptoms, diagnosis, lab tests, and drugs for prescriptions
 *     description: Retrieve frequently used texts for symptoms, diagnosis, lab tests, and drugs based on the clinic ID.
 *     security:
 *       - bearerAuth: []  # Ensure bearerAuth is added for security
 *     parameters:
 *       - in: query
 *         name: clinicId
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d5ec49c67e4d8f889a4d65"
 *         description: The ID of the clinic to retrieve the frequent texts for
 *     responses:
 *       200:
 *         description: Successfully retrieved frequent texts for prescriptions
 *       400:
 *         description: Invalid clinicId provided
 *       401:
 *         description: Unauthorized, missing or invalid token
 *       500:
 *         description: Internal server error
 */

frequency.get("/getfrequenttextforfrescription", authorizationCheck, validateGetFrequentTextForPrescription, async (req, res, next) => {
    try {
        return await getFrequentTextForPrescription(req, res);
    }
    catch (e) {
        next(e)
    }
});

export default frequency;