import mongoose from "mongoose";
import { CLIENT_COLLECTION, PATIENT_COLLECTION } from "../../mongodb.collection.name.js";
const personalHistorySchema = new mongoose.Schema(
    {
        activity: {
            type: String,
            required: true,
        },
        nature: {
            type: String,
            required: true,
        },
        notes: {
            type: String,
        },
        patient: {
            type: mongoose.Schema.Types.ObjectId,
            ref: PATIENT_COLLECTION
        },
        clinic: {
            type: mongoose.Schema.Types.ObjectId,
            ref: CLIENT_COLLECTION
        },
        created: {
            on: {
                type: Date,
                default: Date.now,
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255,
                },
            },
        },
    },
    {
        versionKey: "1.0",
        timestamps: true,
    }
);

personalHistorySchema.index({ name:1,clinic: 1, patient: 1 });

//const PersonalHistories = mongoose.model("history_personaldiseases", personalHistorySchema);

export { personalHistorySchema };
