import { User,Staff } from "../../model/clinics.model.js";
import { upsertUserOTP } from "./userotp.helper.js";

export const userVerfication = async (emailOrPhone) => {
  let phone = +emailOrPhone;
  if (isNaN(phone)) {
    phone = "";
  }
  const authLogin = await User.findOne({
    $or: [
      {
        email: emailOrPhone,
      },
      {
        mobile: phone,
      },
    ],
  })
    .populate({
      path: "linkedClinics",
      populate: {
        path: "clinic",
        select: {
          clinicName: 1,
          logo: 1,
          isActive: 1,
        },
      },
      select: {
        clinicId: 1,
        isAdmin: 1,
        _id: 0,
        userId: 0,
      },
    })
    .populate("staffDetail")
    .exec();

  return authLogin;
};

export const forgotPasswordSendOTP = async (emailOrPhone) => {
  let phone = +emailOrPhone;
  console.log(phone);
  if (isNaN(phone)) {
    phone = "";
  }
  const authLogin = await User.findOne({
    $or: [
      {
        email: emailOrPhone,
      },
      {
        mobile: phone,
      },
    ],
  })
    .populate({
      path: "linkedClinics",
      populate: {
        path: "clinic",
        select: {
          clinicName: 1,
          isActive: 1,
          _id: 1,
        },
      },
      select: {
        linkedClinics: 1,
        isAdmin: 1,
        isdeleted: 1,
        email: 1,
        mobile: 1,
      },
    })
    .exec();

  if (authLogin?._id != null && authLogin?.linkedClinics.length > 0) {
    await sendVerificationTokenWp(authLogin.mobile, authLogin.email);
    return [true, authLogin._id];
  }

  return [false, null];
};
const createOTP = () => {
  let otp = Math.floor(1000 + Math.random() * 9000);
  return otp;
};

const sendVerificationTokenWp = async (mobile, email) => {
  var otp = createOTP();
  var data = {
    mobile: mobile,
    email: email != "" ? email : undefined,
    otp: otp.toString(),
  };
  var postData = JSON.stringify({ data: data });
  await upsertUserOTP(data);
  const sendOTP_URL = `${process.env.WHATSAPP_API_URL}/message/sendotp`;
  const post = await fetch(sendOTP_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: postData,
  });
  console.log("Added in the message controller");
};
