const generalValidator = (artifactName) => ({
  
    general: {
      in: ["body"],
      exists: { errorMessage: "general is required" },
      isObject: true,
    },
    "general.artifact": {
      in: ["body"],
      isString: true,
      notEmpty: true,
      equals: {
        options: artifactName,
      },
      errorMessage: `artifact is required and must be a string and must be equal to ${artifactName}`,
    },
    "general.hipUrl": {
      in: ["body"],
      isURL: true,
      errorMessage: "hipUrl must be a valid URL",
    },
    "general.hipIds": {
      in: ["body"],
      isArray: true,
      errorMessage: "hipIds must be an array of strings",
    },
    "general.hipIds.*": {
      in: ["body"],
      isString: true,
      notEmpty: true,
      errorMessage: "Each hipId must be a non-empty string",
    },
    "general.status": {
      in: ["body"],
      isString: true,
      notEmpty: true,
      isIn: {
        options: [["preliminary", "final", "amended", "entered-in-error"]],
      },
      errorMessage:
        "Status must be one of preliminary , final , amended , entered-in-error",
    },
    "general.clientId": {
      in: ["body"],
      isString: true,
      notEmpty: true,
      errorMessage: "clientId is required and must be a string",
    },
  });

export default generalValidator;
