import ContainerHeading from "components/detail-page/ContainerHeading";
import RequiredLabel from "components/detail-page/RequiredLabel";
import { format } from "date-fns";
import DefaultTextboxClass from "utils/Classes";
import { CalculateAge } from "../../../utils/CommonMethods";
import { useState } from "react";
import { DefaultSelectboxClass } from "../../../utils/Classes";
import SelectWithSearch from "../../flyonui/SelectWithSearch";
function PatientPersonalDetails({
  patientId,
  firstName,
  lastName,
  birthday,
  age,
  gender,
  height,
  weight,
  abhaAddress,
  abhaNumber,
  prefix
}) {
  const [patientAge, setPatientAge] = useState(age);
  if (birthday != null) {
    birthday = format(new Date(birthday), "yyyy-MM-dd");
  }
  const Dob_Change = (e) => {
    var result = CalculateAge(e.target.value);
    setPatientAge(result);
  };
  return (
    <div className="flex flex-col space-y-3">
      <ContainerHeading heading={"Personal details"} />
      <div className="flex w-full font-primary font-medium space-x-3 align-center justify-between">
        <label className="text-md ">Patient ID:&nbsp;&nbsp;{patientId}</label>
        <input type="hidden" name="patientId" value={patientId} />
      </div>
      <div className="flex gap-5">
        {/* Prefix Dropdown */}
        <div className="w-1/6">
          <label className="label-text" for="prefix">Prefix</label>
          <SelectWithSearch
          name="prefix"
          placeholder="Prefix"
          selectedValue={prefix}
          options={[
            <option value="Mr." key="mr">Mr.</option>,
            <option value="Miss." key="miss">Miss</option>,
            <option value="Mrs." key="mrs">Mrs.</option>,
            <option value="Dr." key="dr">Dr.</option>,
            <option value="Prof." key="prof">Prof.</option>]}
          />
   
        </div>
        {/* First Name Field */}
        <div className="w-1/3">
          <label className="label-text" for="firstName">
            First Name <RequiredLabel />
          </label>
          <input
            type="text"
            name="firstName"
            id="firstName"
            placeholder="First Name"
            autoComplete="off"
            defaultValue={firstName}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        {/* Last Name Field */}
        <div className="w-1/2">
          <label className="label-text" for="lastName">
            Last Name <RequiredLabel />
          </label>
          <input
            type="text"
            name="lastName"
            id="lastName"
            placeholder="Last Name"
            autoComplete="off"
            defaultValue={lastName}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
      </div>

      <div className="flex gap-5">
        <div className="w-1/2">
          <label className="label-text" for="birthday">Birth Date</label>
          <input
            type="date"
            name="birthday"
            id="birthday"
            placeholder="Birthdate"
            autoComplete="off"
            defaultValue={birthday}
            onChange={(e) => {
              Dob_Change(e);
            }}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        <div className="w-1/2">
          <label className="label-text" for="age">
            Age
            <RequiredLabel />
          </label>
          <input
            type="number"
            name="age"
            id="age"
            placeholder="Age"
            autoComplete="off"
            defaultValue={patientAge}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
      </div>

      
      <div className="flex gap-5">
        <div className="w-4/12">
          <label className="label-text" for="gender">Gender</label>
          <SelectWithSearch
          name="gender"
          id="gender"
          placeholder="Gender"
          selectedValue={gender}
          options={[
            <option value="Male" key="Male">Male</option>,
            <option value="Female" key="Female">Female</option>,
            <option value="Others" key="Others">Others</option>]}
          />
          
        </div>
        <div className="w-4/12">
          <label className="label-text" for="height">Height (cm)</label>
          <input
            type="number"
            name="height"
            id="height"
            placeholder="Height"
            autoComplete="off"
            defaultValue={height}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        <div className="w-4/12">
          <label className="label-text" for="weight">Weight (kg)</label>
          <input
            type="number"
            name="weight"
            id="weight" 
            placeholder="Weight"
            autoComplete="off"
            defaultValue={weight}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
      </div>
      <div className="flex gap-5">
        <div className="w-1/2">
          <label className="label-text" for="abhaAddress">ABHA Address</label>
          <input
            type="text"
            name="abhaAddress"
            id="abhaAddress"
            placeholder="ABHA Address"
            autoComplete="off"
            defaultValue={abhaAddress}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        <div className="w-1/2">
          <label className="label-text" for="abhaNumber">ABHA Number</label>
          <input
            type="text"
            name="abhaNumber"
             id="abhaNumber"
            placeholder="ABHA Number"
            autoComplete="off"
            defaultValue={abhaNumber}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
      </div>
    
      
    </div>
  );
}

export default PatientPersonalDetails;
