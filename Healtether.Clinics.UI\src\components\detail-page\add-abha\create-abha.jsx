import React from "react";
import { Buttons } from "../appointment/button";
import { Input } from "../input";

export default function CreateAbha({
  handleChancel,
  onComplete,
  profileData, 
  onViewAbhaCard,
  address,
  patientMobile
}) {

  const formatDateOfBirth = (year, month, day) => {
    const formattedMonth = String(month).padStart(2, '0');
    const formattedDay = String(day).padStart(2, '0');
    return `${formattedDay}-${formattedMonth}-${year}`;
  };

  // Extracting year, month, and day from profileData
  const yearOfBirth = profileData?.yearOfBirth;
  const monthOfBirth = profileData?.monthOfBirth;
  const dayOfBirth = profileData?.dayOfBirth;

  // Formatting the date of birth
  const formattedDob = yearOfBirth && monthOfBirth && dayOfBirth 
    ? formatDateOfBirth(yearOfBirth, monthOfBirth, dayOfBirth) 
    : "";

  return (
    <>
    <article className=" ">
      <div className="w-full flex items-center justify-center">
        <div className="w-24 h-24 rounded-full bg-gray-300 text-dark text-xl font-semibold overflow-hidden">
          {profileData ? (
            <img
              src={`data:image/jpeg;base64,${
                profileData.profilePhoto
                  ? profileData.profilePhoto
                  : profileData.photo
              }`}
              alt="Profile"
              className="w-full h-full object-cover"
            />
          ) : (
            <img
              src="https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_640.png"
              alt="Default Profile"
              className="w-full h-full object-cover"
            />
          )}
        </div>
      </div>

      <section className="mt-3 flex flex-col gap-2">
        {profileData ? (
          profileData?.firstName || profileData?.lastName || profileData?.middleName ? (
            <>
              <div className="flex flex-col gap-1">
                <label className="text-sm text-gray-600">First Name</label>
                <Input.text
                  placeholder="First name"
                  value={profileData?.firstName || ""}
                  readOnly
                />
              </div>
              <div className="flex flex-col gap-1">
                <label className="text-sm text-gray-600">Middle Name</label>
                <Input.text
                  placeholder="Last name"
                  value={profileData?.middleName || ""}
                  readOnly
                />
              </div>
              <div className="flex flex-col gap-1">
                <label className="text-sm text-gray-600">Last Name</label>
                <Input.text
                  placeholder="Last name"
                  value={profileData?.lastName || ""}
                  readOnly
                />
              </div>
            </>
          ) : (
            <div className="flex flex-col gap-1">
              <label className="text-sm text-gray-600">Full Name</label>
              <Input.text
                placeholder="Full name"
                value={profileData?.fullName || profileData?.name || ""}
                readOnly
              />
            </div>
          )
        ) : null}
        <div className="flex gap-2">
          <div className="flex flex-col gap-1 w-1/2">
            <label className="text-sm text-gray-600">Date of Birth</label>
            <Input.number
              placeholder="Birthdate"
              value={profileData?.dob ||profileData?.dateOfBirth || formattedDob ||""}
              readOnly
              width="w-full"
            />
          </div>
          <div className="flex flex-col gap-1 w-1/2">
            <label className="text-sm text-gray-600">Gender</label>
            <Input.text
              placeholder="Gender"
              value={profileData?.gender || ""}
              readOnly
              width="w-full"
            />
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-sm text-gray-600">Address</label>
          <Input.text
            placeholder="Address"
            value={profileData?.address || ""}
            readOnly
            className="min-h-fit"
          />
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-sm text-gray-600">Pincode</label>
          <Input.number
            placeholder="Pincode"
            value={profileData?.pincode || profileData?.pinCode ||""}
            readOnly
          />
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-sm text-gray-600">ABHA Address</label>
          <Input.text
            placeholder="ABHA Address"
            value={
              profileData?.preferredAbhaAddress ||
              profileData?.abhaAddress ||address||(profileData?.phrAddress?.[0])||profileData?.phrAddress ||
              ""
            }
            readOnly
          />
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-sm text-gray-600">ABHA Number</label>
          <Input.number
            placeholder="ABHA Number"
            value={profileData.ABHANumber || profileData.abhaNumber || ""}
            readOnly
          />
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-sm text-gray-600">Mobile Number</label>
          <Input.number
            placeholder="Mobile"
            value={patientMobile||profileData.mobile || ""}
            readOnly
          />
        </div>

        <p className="text-[#5351C7] cursor-pointer" onClick={onViewAbhaCard}>View ABHA Card</p>
      </section>
      <footer className="flex justify-end gap-3 mt-3">
        <Buttons.secondary
          title={"Cancel"}
          classname=""
          onClick={handleChancel}
        />
        <Buttons.primary title={"Submit"} classname="" onClick={onComplete} />
      </footer>
    </article>
    </>
  );
}