import ContainerHeading from "components/detail-page/ContainerHeading";
import RequiredLabel from "components/detail-page/RequiredLabel";
import DefaultTextboxClass from "utils/Classes";
import defaultUploadImg from "assets/images/upload_image.jpg";
import { useRef, useState } from "react";
import { format } from "date-fns";
import { DEFAULT_UPLOAD_IMAGE } from "utils/Classes";
import { CalculateAge } from "utils/CommonMethods";
import { DefaultSelectboxClass } from "../../../utils/Classes";
import { useSelector } from "react-redux";
import { SPECIALISATION_LIST } from "../../../utils/OptionList";
import SelectWithSearch from "../../flyonui/SelectWithSearch";
function StaffPersonalDetails({
  staffId,
  firstName,
  lastName,
  birthday,
  age,
  gender,
  specialization,
  profilePic,
  click,
  isDoctor,
  hprId,
  prefix
}) {
  const { clinic } = useSelector((state) => state.currentClinic);
  let staff = {};
  let profileName = undefined;
  let profileImg = defaultUploadImg;
  const [patientAge, setPatientAge] = useState(age);
  const blob_URL = `${import.meta.env.VITE_BLOB_URL}${import.meta.env.VITE_CLINICBLOB_CONTAINER_PREFIX
    }${clinic._id}/staff/`;
  if (profilePic != null) {
    profileName = profilePic;
    profileImg = blob_URL + profilePic;
  }
  if (birthday != null) {
    birthday = format(new Date(birthday), "yyyy-MM-dd");
  }
  const [imgSrc, setImgSrc] = useState(profileImg);
  const imageRef = useRef(null);

  function profilePhotoClicked() {
    imageRef.current.click();
  }
  function profilePhotoUpdate(e) {
    setImgSrc(URL.createObjectURL(e.target.files[0]));
    //  setprofilePic(e.target.files[0]);
  }
  const Dob_Change = (e) => {
    var result = CalculateAge(e.target.value);
    setPatientAge(result);
  };
  return (
    <div className="flex flex-col space-y-2">
      <ContainerHeading heading={"Personal details"} />
      <div className="flex align-center justify-between w-full">
        <div className="w-1/2 grid place-items-center">
          <img
            className="h-[150px] w-[160px] rounded-[40%] object-cover"
            src={imgSrc}
            alt={DEFAULT_UPLOAD_IMAGE}
            onClick={profilePhotoClicked}
          />
          <input
            className="hidden"
            name="profilepic"
            type="file"
            ref={imageRef}
            accept="image/*"
            onChange={profilePhotoUpdate}
          />
          <input
            type="hidden"
            className="hidden"
            name="profileName"
            value={profileName}
          />
        </div>
        <div className="flex  flex-col w-1/2">
          <div className="w-full">
            <label className="label-text" htmlFor="staffId">
              Staff ID
              <RequiredLabel />
            </label>
            <input
              type="text"
              id="staffId"
              name="staffId"
              placeholder="Staff ID"
              autoComplete="off"
              defaultValue={staffId}
              className={DefaultTextboxClass + " w-full text-md"}
            />
          </div>
          <div className="w-full flex gap-5">
            <div className="w-1/4">
              <label className="label-text" htmlFor="prefix">Prefix</label>
              <SelectWithSearch
                name="prefix"
                placeholder="Prefix"
                selectedValue={prefix}
                options={[
                  <option value="Mr." key="mr">Mr.</option>,
                  <option value="Miss" key="miss">Miss</option>,
                  <option value="Mrs." key="mrs">Mrs.</option>,
                  <option value="Dr." key="dr">Dr.</option>,
                  <option value="Prof." key="prof">Prof.</option>,
                  <option value="Rev." key="rev">Prof.</option>,
                  <option value="Hon." key="hon">Prof.</option>
                ]}
              />
            </div>

            <div className="w-3/4 ">
              <label className="label-text" htmlFor="firstName">
                First Name
                <RequiredLabel />
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                placeholder="First Name"
                autoComplete="off"
                defaultValue={firstName}
                className={DefaultTextboxClass + " w-full text-md"}
              />
            </div>
          </div>

          <div className="w-full">
            <label className="label-text " htmlFor="lastName">
              Last Name
              <RequiredLabel />
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              placeholder="Last Name"
              autoComplete="off"
              defaultValue={lastName}
              className={DefaultTextboxClass + " w-full text-md"}
            />
          </div>
        </div>
      </div>

      <div className="flex gap-5">
        <div className="w-1/2">
          <label className="label-text" htmlFor="birthday">Birthdate</label>
          <input
            type="date"
            id="birthday"
            name="birthday"
            placeholder="Birthdate"
            autoComplete="off"
            defaultValue={birthday}
            onChange={(e) => {
              Dob_Change(e);
            }}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
        <div className="w-1/2">
          <label className="label-text" htmlFor="age">
            Age
            <RequiredLabel />
          </label>
          <input
            id="age"
            type="number"
            name="age"
            placeholder="Age"
            autoComplete="off"
            defaultValue={patientAge}
            className={DefaultTextboxClass + " w-full text-md"}
          />
        </div>
      </div>
      <div className="flex gap-5">
        <div className="w-1/2">
          <label className="label-text" htmlFor="specialization">Specialization</label>
          <SelectWithSearch
            name="specialization"
            placeholder="Select Specialization"
            selectedValue={specialization}
            options={SPECIALISATION_LIST.map((spec, index) => (
              <option key={index} value={spec.spec}>
                {spec.value}
              </option>
            ))}
          />
        </div>

        {isDoctor && (
          <div className="w-1/2">
            <label className="label-text" htmlFor="hprId">HPR ID</label>
            <input
              type="text"
              name="hprId"
              placeholder="HPR ID"
              autoComplete="off"
              defaultValue={hprId}
              className={DefaultTextboxClass + " w-full text-md"}
            />
          </div>
        )}


        <div className="w-1/2">
          <label className="label-text" htmlFor="gender">Gender</label>
          <SelectWithSearch
            name="gender"
            id="gender"
            placeholder="Gender"
            selectedValue={gender}
            options={[
              <option value="Male" key="Male">Male</option>,
              <option value="Female" key="Female">Female</option>,
              <option value="Others" key="Others">Others</option>]}
          />
        </div>
      </div>


    </div>
  )
}


export default StaffPersonalDetails;
