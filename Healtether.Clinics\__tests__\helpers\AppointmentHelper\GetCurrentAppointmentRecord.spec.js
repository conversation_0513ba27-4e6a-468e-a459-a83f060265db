import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { getCurrentAppointmentRecord } from '../../../helpers/appointment/appointment.helper.js';
import { Appointment } from '../../../model/clinics.model.js'; 
import { setup, teardown } from '../../../setup.js'; 

jest.setTimeout(30000); 
beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('getCurrentAppointmentRecord', () => {
  beforeEach(async () => {
   
    await Appointment.deleteMany({});
  });

  it('should return the appointment with medical, procedure, and prescription records', async () => {
    const appointmentId = new mongoose.Types.ObjectId();
    
    const appointmentData = {
      _id: appointmentId,
      mobile: "**********",
      name: "First Patient",
      gender: "Male",
      age: 33,
      appointmentDate: new Date('2024-08-21T18:30:00.000Z'),
      email: "<EMAIL>",
     
      medicalRecords: [
        {
          fileName: "diabetes_report.pdf",
          blobName: "45678_diabetes_report",
        },
      ],
      procedureRecords: [
        {
          fileName: "procedurefile.pdf",
          blobName: "45678_diabetes_report1",
        },
      ],
      prescriptionRecords: [
        {
          fileName: "prescriptionRecords.pdf",
          blobName: "45678_diabetes_report2",
        },
      ],
    };

    await Appointment.create(appointmentData);

    const result = await getCurrentAppointmentRecord(appointmentId);
    const appointmentQuery = await Appointment.findById(appointmentId)
    .select({ medicalRecords: 1, procedureRecords: 1, prescriptionRecords: 1 })
    .exec();

    expect(result._id.toString()).toBe(appointmentId.toString());
    expect(result.medicalRecords).toEqual(appointmentQuery.medicalRecords);
    expect(result.procedureRecords).toEqual(appointmentQuery.procedureRecords);
    expect(result.prescriptionRecords).toEqual(appointmentQuery.prescriptionRecords);
  });

  it('should return null if no appointment is found', async () => {
    const appointmentId = new mongoose.Types.ObjectId();

    const result = await getCurrentAppointmentRecord(appointmentId);

    expect(result).toBeNull();
  });
});
