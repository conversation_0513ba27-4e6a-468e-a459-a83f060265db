import { useEffect, useRef, useState } from "react";
import { validateAadhaarNumber } from "../../../utils/CommonMethods";

export const AadhaarInput = ({ aadhaar, setAadhaar, verify, setError, error }) => {
  const inputs = useRef([]);
  const [maskedValues, setMaskedValues] = useState(["", "", ""]); // Masked display values
  const [focusedIndex, setFocusedIndex] = useState(null); // Track which input is focused
  const [showInput, setShowInput] = useState(false); // Track whether to show unmasked input

  const handleChange = (e, index) => {
    const inputValue = e.target.value;
    const newAadhaar = [...aadhaar];
    newAadhaar[index] = inputValue;
    setAadhaar(newAadhaar);

    // Mask input except for the last field or when showInput is true
    const newMaskedValues = [...maskedValues];
    if (index < 2 && !showInput) {
      newMaskedValues[index] = inputValue.replace(/./g, "•");
    } else {
      newMaskedValues[index] = inputValue;
    }
    
    if (inputValue.length === 4 && inputs.current[index + 1]) {
      inputs.current[index + 1].focus();
      setMaskedValues(newMaskedValues);
    }
    
    const aadhaarNumber = newAadhaar.join(""); // Combine all parts
    if (aadhaarNumber.length === 12) {
      setError(!validateAadhaarNumber(aadhaarNumber));
    } else {
      setError(false);
    }
  };

  const handleFocus = (index) => {
    setFocusedIndex(index);
  };

  const handleBlur = (index) => {
    setFocusedIndex(null);
    // Reapply masking only after losing focus, unless showInput is true
    if (!showInput) {
      const newMaskedValues = [...maskedValues];
      if (index < 2) {
        newMaskedValues[index] = aadhaar[index].replace(/./g, "•");
      } else {
        newMaskedValues[index] = aadhaar[index];
      }
      setMaskedValues(newMaskedValues);
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && e.currentTarget.value === "" && index > 0) {
      inputs.current[index - 1].focus();
    }
    if (e.key === "ArrowLeft" && index > 0) {
      inputs.current[index - 1].focus();
    } else if (e.key === "ArrowRight" && index < aadhaar.length - 1) {
      inputs.current[index + 1].focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim();
    if (/^\d{12}$/.test(pastedData)) {
      const newAadhaar = [
        pastedData.slice(0, 4),
        pastedData.slice(4, 8),
        pastedData.slice(8, 12),
      ];
      setAadhaar(newAadhaar);

      // Apply masking based on showInput state
      const newMaskedValues = showInput 
        ? [...newAadhaar] 
        : [
            newAadhaar[0].replace(/./g, "•"),
            newAadhaar[1].replace(/./g, "•"),
            newAadhaar[2],
          ];
      setMaskedValues(newMaskedValues);

      inputs.current[2].focus();
      if (!validateAadhaarNumber(newAadhaar.join(""))) {
        setError("Aadhaar number is invalid");
      } else {
        setError(false);
      }
    } else {
      console.error("Invalid paste content. Only a 12-digit number is allowed.");
    }
  };

  // Toggle visibility of all fields
  const toggleVisibility = () => {
    setShowInput(!showInput);
    
    // If showing input, show the actual values; if hiding, mask the first two parts
    if (!showInput) {
      setMaskedValues([...aadhaar]);
    } else {
      setMaskedValues([
        aadhaar[0].replace(/./g, "•"),
        aadhaar[1].replace(/./g, "•"),
        aadhaar[2],
      ]);
    }
  };

  useEffect(() => {
    console.log("Aadhaar updated:", aadhaar);
  }, [aadhaar]);

  // Update masked values when showInput changes
  useEffect(() => {
    if (showInput) {
      setMaskedValues([...aadhaar]);
    } else {
      setMaskedValues([
        aadhaar[0].replace(/./g, "•"),
        aadhaar[1].replace(/./g, "•"),
        aadhaar[2],
      ]);
    }
  }, [showInput, aadhaar]);

  return (
    <main>
      <section className="flex relative">
        <div className="flex gap-2 h-12 overflow-hidden rounded-sm flex-1">
          {aadhaar?.map((value, index) => (
            <input
              key={index}
              type="text"
              maxLength={4}
              autoFocus={index === 0}
              required
              value={focusedIndex === index ? aadhaar[index] : maskedValues[index]} // Show unmasked when focused or showInput is true
              onChange={(e) => handleChange(e, index)}
              onFocus={() => handleFocus(index)}
              onBlur={() => handleBlur(index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              onPaste={index === 0 ? handlePaste : undefined}
              ref={(input) => (inputs.current[index] = input)}
              className={`flex h-full w-1/3 items-center justify-center ${
                error
                  ? "border-b-2 border-red-600 focus:outline-red-600"
                  : value
                  ? value.length > 4
                    ? "border-b-2 border-destructive"
                    : "border-b-4 border-primary"
                  : "border-b-2 border-color_muted/20"
              } bg-light text-center font-body text-base font-semibold text-color_muted-foreground focus:outline-hidden`}
            />
          ))}
        </div>
        <button
          type="button"
          onClick={toggleVisibility}
          className="absolute right-2 top-3 text-gray-500 hover:text-gray-700 focus:outline-none"
          aria-label={showInput ? "Hide Aadhaar number" : "Show Aadhaar number"}
        >
          {showInput ? (
          <span className="icon-[mdi--eye]">  </span>
          ) : (
          <span className="icon-[mdi--eye-off]"> </span> 
          )}
        </button>
      </section>
    </main>
  );
};