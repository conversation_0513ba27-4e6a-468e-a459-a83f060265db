import React, { useEffect, useState } from "react";
import { format, addDays, addMonths, addWeeks, formatISO } from "date-fns";
import Modal from "../modal";
import { Icons } from "../icons";
import dropdown from "../dropdown";
import { Buttons } from "../appointment/button";
import SuccessfullOrFailed from "../../../components/detail-page/successfull-or-failed";
import { initiateConsent } from "../../../services/abha/abha-m3";
export default function RequestAbha({
  consent,
  setConsent,
  tab,
  setActiveTab,
  patientData,
  consultation,
}) {
  const {
    isOpen,
    setIsOpen,
    DropdownMenu,
    DropdownMenuItem,
    DropdownMenuContent,
  } = dropdown();

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showExpiryDatePicker, setShowExpiryDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null,
  });
  
  const [expiryDate, setExpiryDate] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const purposeOptions = [
    { code: "CAREMGT", display: "Care Management" },
    { code: "BTG", display: "Break the Glass" },
    { code: "PUBHLTH", display: "Public Health" },
    { code: "HPAYMT", display: "Healthcare Payment" },
    { code: "DSRCH", display: "Disease Specific Healthcare Research" },
    { code: "PATRQT", display: "Self Requested" },
  ];

  const [requestData, setRequestData] = useState([
    {
      label: "Request records from",
      value: "Last 6 months",
      options: ["Last 3 months", "Last 6 months", "Last 12 months", "Custom"],
    },
    {
      label: "Records expires in",
      value: "12 months",
      options: ["1 week", "1 month", "3 months", "6 months", "12 months", "Custom"],
    },
    {
      label: "Purpose of request",
      value: "Care Management",
      options: purposeOptions.map((option) => option.display),
    },
  ]);

  const [recordType, setRecordType] = useState({
    label: "Record type",
    values: [],
    options: [
      "Diagnostic report",
      "Prescription",
      "Discharge summary",
      "OP consultation",
      "Immunization record",
      "Wellness record",
      "Health document record",
    ],
  });

  const [activeDropdown, setActiveDropdown] = useState("");

  const calculateDateRange = (option) => {
    const endDate = new Date();
    let startDate = new Date();

    switch (option) {
      case "Last 3 months":
        startDate = addMonths(endDate, -3);
        break;
      case "Last 6 months":
        startDate = addMonths(endDate, -6);
        break;
      case "Last 12 months":
        startDate = addMonths(endDate, -12);
        break;
      case "Custom":
        return { startDate: null, endDate: null };
      default:
        startDate = addMonths(endDate, -6);
    }

    return {
      startDate: formatISO(startDate),
      endDate: formatISO(endDate),
    };
  };

  const calculateExpiryDate = (option) => {
    const today = new Date();
    let expiry;
  
    switch (option) {
      case "1 week":
        expiry = addWeeks(today, 1);
        break;
      case "1 month":
        expiry = addMonths(today, 1);
        break;
      case "3 months":
        expiry = addMonths(today, 3);
        break;
      case "6 months":
        expiry = addMonths(today, 6);
        break;
      case "12 months":
        expiry = addMonths(today, 12);
        break;
      case "Custom":
        return null; // Return null for custom, will be handled by date picker
      default:
        expiry = addMonths(today, 12); 
    }
     return formatISO(expiry);
  };  

  useEffect(() => {
    // Set initial date range
    const requestPeriod = requestData.find(item => item.label === "Request records from");
    if (requestPeriod && requestPeriod.value !== "Custom") {
      setDateRange(calculateDateRange(requestPeriod.value));
    }
    
    // Set initial expiry date
    const expiryPeriod = requestData.find(item => item.label === "Records expires in");
    if (expiryPeriod && expiryPeriod.value !== "Custom") {
      setExpiryDate(calculateExpiryDate(expiryPeriod.value));
    }
  }, [requestData]);

  const selectOption = (value, idx) => {
    setRequestData((prev) => {
      const updated = [...prev];
      updated[idx].value = value;
      return updated;
    });

    if (idx === 0 && value === "Custom") { // Request records from
      setShowDatePicker(true);
    } else if (idx === 0) {
      setShowDatePicker(false);
      setDateRange(calculateDateRange(value));
    }
    
    if (idx === 1 && value === "Custom") { // Records expires in
      setShowExpiryDatePicker(true);
    } else if (idx === 1) {
      setShowExpiryDatePicker(false);
      setExpiryDate(calculateExpiryDate(value));
    }
  };

  const selectRecordType = (value) => {
    setRecordType((prev) => ({
      ...prev,
      values: prev.values.includes(value)
        ? prev.values.filter((v) => v !== value)
        : [...prev.values, value],
    }));
  };

  const handleDateChange = (type, value) => {
    setDateRange((prev) => ({
      ...prev,
      [type]: value,
    }));
  };
  
  const handleExpiryDateChange = (value) => {
    setExpiryDate(value);
  };

  const next = () => {
    setActiveTab(3);
    setConsent((prev) => ({ ...prev, request: false, successfull: false }));
  };

  const tryAgain = () => {
    setConsent((prev) => ({ ...prev, failed: false }));
  };

  const toggleDropdown = (label) => {
    setActiveDropdown((prev) => (prev === label ? "" : label));
  };

  const getPurposeCode = () => {
    const purposeDisplay = requestData.find(
      (item) => item.label === "Purpose of request"
    )?.value;
    const purposeOption = purposeOptions.find(
      (option) => option.display === purposeDisplay
    );
    return purposeOption ? purposeOption.code : "CAREMGT";
  };

  const getPurposeText = () => {
    const purposeData = requestData.find(
      (item) => item.label === "Purpose of request"
    );
    return purposeData ? purposeData.value : "Care Management";
  };

  const getExpiryDate = () => {
    const expiryData = requestData.find(item => item.label === "Records expires in");
    
    if (expiryData && expiryData.value === "Custom") {
      return expiryDate;
    }
    
    return calculateExpiryDate(expiryData ? expiryData.value : "12 months");
  };

  const getHiTypes = () => {
    const hiTypeMap = {
      "Diagnostic report": "DiagnosticReport",
      "Prescription": "Prescription",
      "Discharge summary": "DischargeSummary",
      "OP consultation": "OPConsultation",
      "Immunization record": "ImmunizationRecord",
      "Wellness record": "WellnessRecord",
      "Health document record": "HealthDocumentRecord",
    };

    return recordType.values.map((type) => hiTypeMap[type] || type);
  };

  const handleInitiateConsent = async () => {
    try {
      setIsLoading(true);
      
      const formattedStartDate = dateRange.startDate ? new Date(dateRange.startDate).toISOString() : "";
      const formattedEndDate = dateRange.endDate ? new Date(dateRange.endDate).toISOString() : "";
      const formattedExpiryDate = expiryDate ? new Date(expiryDate).toISOString() : 
                                new Date(getExpiryDate()).toISOString();
      
      const data = {
        patientId: patientData._id,
        clinicPatientId: patientData.patientId,
        patientName: consultation?.name,
        clinicId: consultation?.clinic,
        abhaAddress: patientData.abhaAddress,
        requesterName: consultation?.doctorName,
        purposeCode: getPurposeCode(),
        purposeText: getPurposeText(),
        hiTypes: getHiTypes(),
        dateRange: {
          startDate: formattedStartDate,
          endDate: formattedEndDate,
        },
        expiryDate: formattedExpiryDate,
      };

      console.log("Sending consent request with data:", data);

      const result = await initiateConsent(data);
      console.log("Consent request result:", result);
      if (result.data && result.data.isSuccess) {
        setConsent((prev) => ({ ...prev, successfull: true }));
      } else {
        setConsent((prev) => ({ ...prev, failed: true }));
      }
    } catch (error) {
      console.error("Error initiating consent:", error);
      setConsent((prev) => ({ ...prev, failed: true }));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log(
      "patientData",
      patientData,
      "doctorName",
      consultation?.doctorName
    );
  }, []);

  return (
    <Modal
      isOpen={consent.request}
      setIsOpen={(value) => setConsent((prev) => ({ ...prev, request: value }))}
      plain={true}
      classname="font-primary h-110 w-300 px-4!"
    >
      {consent.successfull ? (
        <SuccessfullOrFailed
          success="ABHA request for medical records has been sent successfully"
          nextButtonTitle="View consent status"
          nextButton={next}
        />
      ) : consent.failed ? (
        <SuccessfullOrFailed
          error="ABHA request couldn't be sent now."
          nextButtonTitle="Try again"
          nextButton={tryAgain}
        />
      ) : (
        <section className="flex flex-col gap-2">
          <div className="flex items-center gap-2 h-11 px-3 text-base font-medium text-dark">
            Request to:
            <div className="font-semibold">{patientData?.abhaAddress}</div>
          </div>

          {requestData.map(({ label, value, options }, idx) => (
            <div key={idx}>
              <div
                onClick={() => toggleDropdown(label)}
                className="cursor-pointer hover:bg-light rounded-md duration-300 flex items-center gap-2 h-11 px-3 text-base font-medium text-dark"
              >
                {label}:<div className="font-semibold">{value}</div>
                <Icons.arrow
                  className={`ml-auto transition-transform duration-200 ${
                    activeDropdown === label ? "rotate-90" : "-rotate-90"
                  }`}
                />
              </div>

              <div
                className={`${
                  activeDropdown === label ? "h-auto py-2" : "h-0"
                } overflow-hidden duration-200`}
              >
                <div className="flex flex-wrap gap-1.5 px-3">
                  {options.map((name, id) => (
                    <div
                      onClick={() => selectOption(name, idx)}
                      key={id}
                      className={`bg-light px-3 py-1 rounded-lg cursor-pointer ${
                        value === name ? "bg-primary text-white" : ""
                      }`}
                    >
                      {name}
                    </div>
                  ))}
                </div>

                {showDatePicker && label === "Request records from" && (
                  <div className="mt-2 px-3 flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <span className="w-16">From:</span>
                      <input
                        type="datetime-local"
                        value={dateRange.startDate || ""}
                        onChange={(e) =>
                          handleDateChange("startDate", e.target.value)
                        }
                        className="border rounded-sm p-1 flex-1"
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-16">To:</span>
                      <input
                        type="datetime-local"
                        value={dateRange.endDate || ""}
                        onChange={(e) =>
                          handleDateChange("endDate", e.target.value)
                        }
                        min={dateRange.startDate || ""}
                        className="border rounded-sm p-1 flex-1"
                      />
                    </div>
                  </div>
                )}
                
                {showExpiryDatePicker && label === "Records expires in" && (
                  <div className="mt-2 px-3 flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <span className="w-16">Expires:</span>
                      <input
                        type="datetime-local"
                        value={expiryDate || ""}
                        onChange={(e) =>
                          handleExpiryDateChange(e.target.value)
                        }
                        min={new Date().toISOString().split('T')[0] + "T00:00"}
                        className="border rounded-sm p-1 flex-1"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}

          <DropdownMenu>
            <div className="cursor-pointer hover:bg-light rounded-md duration-300 flex items-center gap-2 h-11 px-3 text-base font-medium text-dark">
              {recordType.label}:
              <div className="px-2 py-0.5 bg-gray rounded-lg font-semibold">
                {recordType.values.length} Selected
              </div>
              <Icons.arrow className="ml-auto -rotate-90" />
            </div>
            <DropdownMenuContent
              bottom={false}
              className="max-h-screen overflow-y-auto z-50"
            >
              {recordType.options.map((name) => (
                <DropdownMenuItem
                  key={name}
                  handleClick={() => selectRecordType(name)}
                  className="flex items-center gap-2 justify-start!"
                >
                  <input
                    type="checkbox"
                    id={name}
                    checked={recordType.values.includes(name)}
                    onChange={() => {}}
                  />
                  <label htmlFor={name}>{name}</label>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Buttons.primary
            onClick={handleInitiateConsent}
            title={isLoading ? "Sending..." : "Send Request"}
            classname="mt-8 ml-auto mr-3 w-fit"
            disabled={isLoading}
          />
        </section>
      )}
    </Modal>
  );
}
