import {whatsapplogDb} from "../../config/whatsapp.collections.config.js";
import {getExpiry, getProperMobile} from "../../utils/common.js";

export const addUpdateSessionWithCacheClinic = async(mobile, clinic) => {
    var whatsappmodel = whatsapplogDb.model("WhatsappSession");
    if (mobile != null) {
        var session = await whatsappmodel
            .find({mobile: mobile})
            .exec();

        if (session._id != null) {
            session.cache = {
                clinic: clinic
            };
            session.currentClinic = clinic.length > 1
                ? null
                : clinic[0];
            session.timeStamp = new Date().toISOString();
            session.expireAt = getExpiry(1);
            await session.save();
        } else {

            var session = new whatsappmodel({
                mobile: mobile,
                cache: {
                    clinic: clinic
                },
                currentClinic: clinic.length > 1
                    ? null
                    : clinic[0],
                timeStamp: new Date().toISOString(),
                expireAt: getExpiry(1)
            });
            await session.save();
        }

    }

}

export const getSession = async(mob) => {
    var whatsappmodel = whatsapplogDb.model("WhatsappSession");
    if (mob != null) {
        const session = await whatsappmodel
            .find({mobile: mob})
            .exec();
        if (session != null && session.length > 0) {
            return session[0];
        }
        return null;
    }
}

export const setSessionLanguage = async(mobile, languageCode) => {
    var whatsappmodel = whatsapplogDb.model("WhatsappSession");
    if (mobile != null) {
        var session = await whatsappmodel
            .findOne({mobile: mobile})
            .exec();

        if (session
            ?._id != null) {
            session.language = languageCode;
            session.timeStamp = new Date().toISOString();
            session.expireAt = getExpiry(1);
            await session.save();
        }
    }
}

export const setSessionCurrentClinic = async(mobile, clinic) => {
    var toMobile = getProperMobile(mobile);
    var whatsappmodel = whatsapplogDb.model("WhatsappSession");
    if (mobile != null) {
        var session = await whatsappmodel
            .findOne({mobile: toMobile})
            .exec();

        if (session
            ?._id != null) {
            session.currentClinic = clinic;
            session.timeStamp = new Date().toISOString();
            session.expireAt = getExpiry(1);
            await session.save();
        }
    }

}
export const setSessionLive = async(mobile) => {
    var toMobile = getProperMobile(mobile);
    var whatsappmodel = whatsapplogDb.model("WhatsappSession");
    if (mobile != null) {
        var session = await whatsappmodel
            .findOne({mobile: mobile})
            .exec();

        if (session
            ?._id != null) {
            session.isLive = true;
            session.timeStamp = new Date().toISOString();
            session.expireAt = getExpiry(1);
            await session.save();
        }
    }
}
export const setLastWhatsappMessageId = async(mobile, result) => {
    if (result
        ?.messages != null && mobile != null) {
        var messageId = result
            ?.messages[0].id

        var whatsappmodel = whatsapplogDb.model("WhatsappSession");
        var session = await whatsappmodel
            .findOne({mobile: mobile})
            .exec();

        if (session
            ?._id != null) {
            session.lastWhatsappMessageId = messageId;
            session.timeStamp = new Date().toISOString();
            session.expireAt = getExpiry(1);
            await session.save();
        }

    }
}