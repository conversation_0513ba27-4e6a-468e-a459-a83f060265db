import updateInvoice, {
  addPaymentByCash,
  invoiceById,
  overview,
  phonePeCallback,
} from "../../helpers/payment/payment.helper.js";
import { getDecimal } from "../../utils/common.utils.js";
import { createPaymentLink } from "../../helpers/payment/payment.helper.js";
import { sendNotificationViaToken } from "../../config/firebase.admin.js";
import { sendPhonepeLinkInWhatsapp } from "../../helpers/whatsapp/whatsapp.helper.js";
import { LogMessageInCloudWatch } from "../../config/cloudWatchLogger.js";
export const getPaymentOverview = async (req, res) => {
  const data = req.query;

  var overviewData = await overview(
    data.clientId,
    data.page,
    data.size,
    data.keyword,
    data.sortby,
    data.direction
  );
  res.json(overviewData).status(200);
};

export default async function addInvoice(req, res) {
  var query = req.query;
  var data = req.body.data;
  var treatment = convertData(data);
  console.log(treatment);

  await updateInvoice(
    query.id,
    treatment.treatment,
    query.clientId,
    data.discount,
    req.user?.id,
    req.user?.name
  );
  res.json(true).status(200);
}
export async function addInvoiceForMobile(req, res) {
  var query = req.query;
  var data = req.body.data;
  let result = await updateInvoice(
    query.id,
    data.treatments,
    0,
    query.clientId,
    data.discount,
    req.user.id,
    req.user.name
  );
  res.json(result).status(200);
}
export async function getInvoiceById(req, res) {
  let query = req.query;
  let invoice = await invoiceById(query.id);
  let result = convertDecimalPerInvoice(invoice);
  let patientDetail = invoice?.appointmentId?.patientId;
  result.patient = {
    firstName: patientDetail?.firstName,
    lastName: patientDetail?.lastName,
    patientId: patientDetail?.patientId,
    id: patientDetail?._id,
  };
  result.appointment = {
    appointmentDate: invoice?.appointmentId?.appointmentDate,
    appointmentId: invoice?.appointmentId?._id,
  };

  res.json(result).status(200);
}
// export async function sendPaymentLink(req, res) {
//     var query = req.query;

//     var invoice = await invoiceById(query.id);
//     res.json(invoice).status(200);
// }
export async function payByCash(req, res) {
  var data = req.body;
  let result;
  var invoice = await addPaymentByCash(
    data.invoiceId,
    req.user,
    data.clientId,
    data.amount,
    data?.paymentMode
  );
  if (invoice != "Invalid Amount") {
    result = convertDecimalPerInvoice(invoice);
    res.json(result).status(200);
  } else {
    res.json(invoice).status(400);
  }
}
export async function addPayment(req, res) {
  var query = req.query;
  var data = req.body.data;

  var invoice = await invoiceById(query.id);
  res.json(invoice).status(200);
}
function convertData(data) {
  var convertedData = {
    treatment: [],
  };

  for (var key in data) {
    var parts = key.split("_");
    if (parts.length > 1) {
      var index = parseInt(parts[1]);

      if (!convertedData.treatment[index]) {
        convertedData.treatment[index] = {};
      }

      var field = parts[2];
      convertedData.treatment[index][field] = data[key];
    }
  }
  return convertedData;
}
function convertDecimalPerInvoice(invoice) {
  if (invoice != null) {
    var treatments = [];
    for (let index = 0; index < invoice.treatments.length; index++) {
      const element = invoice.treatments[index];
      var treatment = {
        treatment: element.treatment,
        quantity: element.quantity,
        amount: getDecimal(element.amount),
        discRate: getDecimal(element.discRate),
        // Add the new tax fields
        sgst: getDecimal(element.sgstRate),
        cgst: getDecimal(element.cgstRate),
        sgstAmount: getDecimal(element.sgstAmount),
        cgstAmount: getDecimal(element.cgstAmount),
        taxAmount: getDecimal(element.taxAmount),
      };
      treatments.push(treatment);
    }

    var result = {
      totalAmount: getDecimal(invoice.totalAmount),
      totalTax: getDecimal(invoice.totalTax),
      totalCost: getDecimal(invoice.totalCost),
      discountRate: getDecimal(invoice.discountRate),
      discount: getDecimal(invoice.discount),
      paidAmount: getDecimal(invoice.paidAmount),
      completed: invoice.completed,
      treatments: treatments,
      invoiceNumber: invoice.invoiceNumber,
      created: invoice.created,
    };

    return result;
  }
}

export const formAndSendPaymentLink = async (req, res) => {
  var requestData = req.body;
  var invoiceWithClinic = await invoiceById(requestData.id);
  var paymentLink = await createPaymentLink(invoiceWithClinic, req.user);

  if (paymentLink.success) {
    var link = paymentLink?.data?.instrumentResponse?.redirectInfo?.url;
    var sendLink = await sendPhonepeLinkInWhatsapp(
      invoiceWithClinic?.clinic,
      invoiceWithClinic?.appointmentId,
      invoiceWithClinic?.appointmentId?.patientId,
      link
    );

    if (sendLink.status == 200 && sendLink?.data?.success) {
      await sendNotificationViaToken(
        req.Notificationkey,
        `Payment link send whatsapp mobile no. ${invoiceWithClinic?.appointmentId?.patientId?.mobile}`,
        "Payment",
        true,
        requestData.clinicId,
        req.user.id
      );
    }
    res.json({ success: true }).status(200);
    return;
  } else if (!paymentLink.success) {
    await sendNotificationViaToken(
      req.Notificationkey,
      `Problem in creating url, "${paymentLink.message}"`,
      "Payment",
      false,
      requestData.clinicId,
      req.user.id
    );
  }
  res.json({ success: false }).status(500);
};

export const handlePhonePeCallback = async (req, res) => {
  // Extract the base64 encoded response
  const data = req.body;
  var verify = req.headers["x-verify"];
  LogMessageInCloudWatch(
    `phonepe body: ${JSON.stringify(data)} another:${verify}`
  );
  await phonePeCallback(data, verify);
  return res.status(200).json({ success: true });
};
