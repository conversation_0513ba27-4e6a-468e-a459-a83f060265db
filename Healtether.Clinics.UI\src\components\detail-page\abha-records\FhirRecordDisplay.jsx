import { useRef } from "react";

// Renamed component to be generic
export const FhirRecordDisplay = ({
  isOpen,
  onClose,
  recordData, // This prop receives the output of extractFhirBundleInfo
}) => {
  if (!isOpen) return null;

  const contentToPrint = useRef(null);

  // Destructure all potential relevant parts from the extractedData structure
  const {
    bundle,
    composition,
    patient,
    practitioners,
    organization,
    encounter,
    chiefComplaints,
    allergies,
    medicalHistory,
    investigationAdvice,
    medications, // Used for both Consultation and Prescription
    procedure,
    followUp,
    documentReferences, // Used for Health Document and Consultation
    vitalSigns, // Used for Wellness
    observations, // General observations
    invoice, // Used for Invoice
    chargeItems, // Used for Invoice
    immunizations, // Example for Immunization
    diagnosticReports, // Example for Diagnostic Report
    // Add other extracted arrays/objects here as needed
  } = recordData || {}; // Provide a default empty object

  const openBase64File = (base64Data, contentType) => {
  const byteCharacters = atob(base64Data);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  const blob = new Blob(byteArrays, { type: contentType });
  const blobUrl = URL.createObjectURL(blob);
  window.open(blobUrl, '_blank');
};

const showNotes = (text) => {
  const pattern = /^\(Notes:.*?\)\s\(Duration:.*?\)$/;
  if (pattern.test(text)) {
  // Extract values using RegEx
  const notesMatch = text.match(/\(Notes:(.*?)\)/);
  const durationMatch = text.match(/\(Duration:(.*?)\)/);

  // Get the actual values
  const notesValue = notesMatch?.[1]?.trim();
  const durationValue = durationMatch?.[1]?.trim();

  // Show only if at least one value is not empty
  const showText = notesValue || durationValue ? text : null;
  return showText;
  }
  return text;
};

  const allSymptoms = medications.flatMap(req => req.symptoms || []);
// Deduplicate by `code`
const uniqueSymptoms = Array.from(
  new Map(allSymptoms.map(symptom => [symptom.code, symptom])).values()
);

  // Determine the record title dynamically from the composition
  const recordTitle =
    composition?.title || composition?.type || bundle?.type || "FHIR Record";

  // Helper function to format date/time (copied)
  const formatDateTime = (isoString) => {
    if (!isoString) return "N/A";
    try {
      const options = {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      };
      return new Date(isoString).toLocaleString(undefined, options);
    } catch (error) {
      console.error("Error formatting date:", isoString, error);
      return isoString; // Return original if parsing fails
    }
  };

  // Helper function to format date (without time) (copied)
  const formatDate = (isoString) => {
    if (!isoString) return "N/A";
    try {
      const options = {
        year: "numeric",
        month: "long",
        day: "numeric",
      };
      return new Date(isoString).toLocaleDateString(undefined, options);
    } catch (error) {
      // Handle cases where date might be in "Mon Apr 28 2025..." format
      if (typeof isoString === "string") {
        return isoString.split(" GMT")[0]; // Simple split for that specific format
      }
      console.error("Error formatting date:", isoString, error);
      return isoString; // Return original if parsing fails
    }
  };

  return (
    // The modal wrapper structure (kept from the original template)
    <div className="fixed inset-0 bg-color_dark/10 backdrop-blur-xs flex items-center justify-center z-50 overflow-auto p-4">
      {" "}
      {/* Added padding for small screens */}
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-auto flex flex-col">
        {" "}
        {/* Used flex-col for internal layout */}
        {/* Modal Header */}
        <div className="flex justify-between items-center p-4 border-b sticky top-0 bg-white z-10">
          {" "}
          {/* Sticky header */}
          <h2 className="text-xl font-bold">{recordTitle} Preview</h2>{" "}
          {/* Dynamic Title */}
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        {/* Modal Body - Scrollable Content */}
        <div className="p-4 flex-grow overflow-y-auto">
          {" "}
          {/* Added flex-grow and overflow-y-auto */}
          <div
            ref={contentToPrint}
            data-record-content="true" // Generic data attribute
            className="bg-white p-6 border border-gray-300"
          >
            {/* --- Common Sections --- */}

            {/* Bundle Information */}
            {bundle?.id && ( // Only show section if bundle data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Bundle Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-semibold">Record ID:</span>{" "}
                    {bundle?.identifier?.value || "N/A"} (
                    {bundle?.identifier?.system || "N/A"})
                  </div>
                  <div>
                    <span className="font-semibold">Type:</span>{" "}
                    {bundle?.type || "N/A"}
                  </div>
                  <div>
                    <span className="font-semibold">Generated Timestamp:</span>{" "}
                    {formatDateTime(bundle?.timestamp)}
                  </div>
                  <div>
                    <span className="font-semibold">Last Updated:</span>{" "}
                    {formatDateTime(bundle?.lastUpdated)}
                  </div>
                  <div>
                    <span className="font-semibold">Confidentiality:</span>{" "}
                    {bundle?.security?.[0]?.display || "N/A"}
                  </div>
                  <div>
                    <span className="font-semibold">Version ID:</span>{" "}
                    {bundle?.versionId || "N/A"}
                  </div>
                </div>
              </div>
            )}

            {/* Composition/Record Header Information */}
            {composition?.id && ( // Only show section if composition data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">
                  Record Header Details
                </h3>{" "}
                {/* Generic title */}
                {composition ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="font-semibold">Title:</span>{" "}
                      {composition?.title || "N/A"}
                    </div>
                    <div>
                      <span className="font-semibold">Date:</span>{" "}
                      {formatDateTime(composition?.date)}
                    </div>
                    <div>
                      <span className="font-semibold">Status:</span>{" "}
                      {composition?.status || "N/A"}
                    </div>
                    <div>
                      <span className="font-semibold">Language:</span>{" "}
                      {composition?.language || "N/A"}
                    </div>
                    <div>
                      <span className="font-semibold">Author(s):</span>{" "}
                      {composition?.author?.join(", ") || "N/A"}
                    </div>
                    {composition?.attester?.length > 0 && (
                      <div>
                        <span className="font-semibold">Attester(s):</span>{" "}
                        {composition?.attester
                          ?.map((att) => att.party)
                          .join(", ") || "N/A"}
                      </div>
                    )}
                    <div>
                      <span className="font-semibold">Custodian:</span>{" "}
                      {composition?.custodian || "N/A"}
                    </div>
                    <div>
                      <span className="font-semibold">Related Encounter:</span>{" "}
                      {composition?.encounter || "N/A"}
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">
                    No composition details found.
                  </p>
                )}
              </div>
            )}

            {/* Patient Information */}
            {patient?.name && ( // Only show section if patient data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Patient Information</h3>
                {patient?.name ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="font-semibold">Name:</span>{" "}
                      {patient.prefix ? `${patient.prefix} ` : ""}
                      {patient.name}
                    </div>
                    <div>
                      <span className="font-semibold">ABHA ID:</span>{" "}
                      {patient.abhaId || "N/A"}
                    </div>
                    <div>
                      <span className="font-semibold">ABHA Address:</span>{" "}
                      {patient.abhaAddress || "N/A"}
                    </div>
                    <div>
                      <span className="font-semibold">Gender:</span>{" "}
                      {patient.gender || "N/A"}
                    </div>
                    <div>
                      <span className="font-semibold">Date of Birth:</span>{" "}
                      {formatDate(patient.birthDate)}
                    </div>
                    {patient.telecom?.length > 0 && (
                      <div>
                        <span className="font-semibold">Contact:</span>
                        {patient.telecom.map((t, index) => (
                          <span key={index} className="ml-1">
                            {t.value} ({t.use})
                          </span>
                        ))}
                      </div>
                    )}
                    {patient.address && (
                      <div className="md:col-span-2">
                        <span className="font-semibold">Address:</span>

                        <div className="ml-1 inline">
                          {patient.address.text
                            ? patient.address.text + ", "
                            : ""}
                          {patient.address.postalCode
                            ? patient.address.postalCode + ", "
                            : ""}
                          {patient.address.country || ""} ({patient.address.use}
                          )
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">
                    No patient information recorded.
                  </p>
                )}
              </div>
            )}

            {/* Practitioner Information */}
            {practitioners?.length > 0 && ( // Only show section if practitioners are present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Practitioner(s)</h3>
                {practitioners?.length > 0 ? (
                  <div className="space-y-2 text-sm">
                    {practitioners.map((practitioner, index) => (
                      <div
                        key={index}
                        className="border-b last:border-b-0 pb-2 mb-2"
                      >
                        <div>
                          <span className="font-semibold">Name:</span>{" "}
                          {practitioner.prefix ? `${practitioner.prefix} ` : ""}
                          {practitioner.name || "N/A"}
                        </div>
                        <div>
                          <span className="font-semibold">
                            Medical License:
                          </span>{" "}
                          {practitioner.medicalLicense || "N/A"}
                        </div>
                        <div>
                          <span className="font-semibold">Gender:</span>{" "}
                          {practitioner.gender || "N/A"}
                        </div>
                        {practitioner.telecom?.length > 0 && (
                          <div>
                            <span className="font-semibold">Contact:</span>
                            {practitioner.telecom.map((t, tIndex) => (
                              <span key={tIndex} className="ml-1">
                                {t.value} ({t.use})
                              </span>
                            ))}
                          </div>
                        )}
                        {practitioner.address && (
                          <div>
                            <span className="font-semibold">Address:</span>
                            <div className="ml-1 inline">
                              {practitioner.address.text
                                ? practitioner.address.text + ", "
                                : ""}
                              {practitioner.address.city
                                ? practitioner.address.city + ", "
                                : ""}
                              {practitioner.address.district
                                ? practitioner.address.district + ", "
                                : ""}
                              {practitioner.address.state
                                ? practitioner.address.state + ", "
                                : ""}
                              {practitioner.address.postalCode
                                ? practitioner.address.postalCode + ", "
                                : ""}
                              {practitioner.address.country || ""} (
                              {practitioner.address.use})
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">
                    No practitioner information recorded.
                  </p>
                )}
              </div>
            )}

            {/* Organization Information */}
            {organization?.name && ( // Only show section if organization data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Organization</h3>
                {organization?.name ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="font-semibold">Name:</span>{" "}
                      {organization.name}
                    </div>
                    {organization.providerNumber && (
                      <div>
                        <span className="font-semibold">Provider Number:</span>{" "}
                        {organization.providerNumber}
                      </div>
                    )}
                    {organization.identifier?.length > 0 &&
                      !organization.providerNumber && (
                        <div>
                          <span className="font-semibold">Identifiers:</span>
                          {organization.identifier.map((id, index) => (
                            <span key={index} className="ml-1">
                              {id.value} ({id.type || id.system})
                            </span>
                          ))}
                        </div>
                      )}
                    {organization.telecom?.length > 0 && (
                      <div>
                        <span className="font-semibold">Contact:</span>
                        {organization.telecom.map((t, index) => (
                          <span key={index} className="ml-1">
                            {t.value} ({t.use})
                          </span>
                        ))}
                      </div>
                    )}
                    {organization.address?.length > 0 && (
                      <div className="md:col-span-2">
                        <span className="font-semibold">Address:</span>
                        {organization.address.map((a, index) => (
                          <div key={index} className="ml-1 inline">
                            {a.text ? a.text + ", " : ""}
                            {a.city ? a.city + ", " : ""}
                            {a.district ? a.district + ", " : ""}
                            {a.state ? a.state + ", " : ""}
                            {a.postalCode ? a.postalCode + ", " : ""}
                            {a.country || ""} ({a.use})
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">
                    No organization information recorded.
                  </p>
                )}
              </div>
            )}

            {/* Encounter Details */}
            {encounter?.id && ( // Only show section if encounter data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Encounter Details</h3>
                {encounter?.id ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="font-semibold">Status:</span>{" "}
                      {encounter.status || "N/A"}
                    </div>
                    {encounter.serviceType && (
                      <div>
                        <span className="font-semibold">Service Type:</span>{" "}
                        {encounter.serviceType}
                      </div>
                    )}
                    {encounter.reasonCode?.length > 0 && (
                      <div>
                        <span className="font-semibold">Reason:</span>{" "}
                        {encounter.reasonCode.join(", ")}
                      </div>
                    )}
                    {encounter.period?.start && (
                      <div>
                        <span className="font-semibold">Start Time:</span>{" "}
                        {formatDateTime(encounter.period.start)}
                      </div>
                    )}
                    <ul>
                      {encounter.diagnosis && (
                        <span className="font-semibold ml-2 mb-2">
                          Diagnosis
                        </span>
                      )}
                      {encounter.diagnosis &&
                        encounter.diagnosis.map((diag, index) => {

                          return (<li
                            key={index}
                            className="list-disc list-inside space-y-1 text-sm ml-2 "
                          >
                            {diag.use || "N/A"} - {showNotes(diag.note)}
                          </li>)
})}
                    </ul>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">
                    No encounter details recorded.
                  </p>
                )}
              </div>
            )}

            {/* --- Consultation Specific Sections --- */}

            {chiefComplaints?.length > 0 && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Chief Complaints</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {chiefComplaints.map((complaint, index) => (
                    <li key={index}>
                      {complaint.code || "N/A"}
                      {complaint.onsetPeriod && (
                        <span>
                          - Onset:{" "}
                          {complaint.onsetPeriod?.start
                            ? formatDate(complaint.onsetPeriod.start)
                            : "N/A"}
                        </span>
                      )}{" "}
                      <div className="ml-3">- {showNotes(complaint.note)}</div>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {allergies?.length > 0 && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Allergies</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {allergies.map((allergy, index) => (
                    <li key={index}>
                      {allergy.code || "N/A"} - Status: (
                      {allergy.verificationStatus || "N/A"})
                      {allergy.note && (
                        <span className="italic ml-2">
                          ( Notes: {allergy.note})
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {medicalHistory?.length > 0 && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Medical History</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {medicalHistory.map((condition, index) => (
                    <li key={index}>
                      {condition.code || "N/A"}
                      {condition.recordedDate && (
                        <span>
                          - Recorded:{" "}
                          {condition.recordedDate
                            ? formatDate(condition.recordedDate)
                            : "N/A"}
                        </span>
                      )}{" "}
                      - {showNotes(condition.note)}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {investigationAdvice?.length > 0 && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Lab Tests</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {investigationAdvice.map(
                    (advice, index) =>
                      advice.category?.length > 0 &&
                      advice.category.map((cat) => (
                        <li key={index}>
                          {cat.displayName} - Status: {advice.status || "N/A"}{" "}
                          {cat.note && showNotes(cat.note)}
                          {advice.requester && (
                            <span className="ml-2 italic">
                              {" "}
                              (Requested by: {advice.requester})
                            </span>
                          )}
                        </li>
                      ))
                  )}
                </ul>
              </div>
            )}

            {/* Medications (Can be in Consultation or Prescription) */}
            {medications?.length > 0 && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Medications</h3>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  {medications.map((med, index) => (
                    <li key={index}>
                      <span className="font-semibold">
                        {med.medication || "N/A"}
                      </span>{" "}
                      (
                      {med.resourceType === "MedicationStatement"
                        ? "Statement"
                        : "Request"}
                      ) - Status: {med.status || "N/A"}
                      {med.authoredOn && (
                        <span className="ml-2 text-gray-600">
                          (Prescribed: {formatDateTime(med.authoredOn)})
                        </span>
                      )}
                      {med.effectivePeriod?.start &&
                        med.effectivePeriod.end && (
                          <span className="ml-2 text-gray-600">
                            (Effective: {formatDate(med.effectivePeriod.start)}{" "}
                            - {formatDate(med.effectivePeriod.end)})
                          </span>
                        )}
                      {med.effectiveDateTime && !med.effectivePeriod?.start && (
                        <span className="ml-2 text-gray-600">
                          (Effective: {formatDateTime(med.effectiveDateTime)})
                        </span>
                      )}
                      {med.dosage?.length > 0 && (
                        <div className="ml-4 mt-1 space-y-1">
                          <span className="font-semibold">Dosage:</span>
                          {med.dosage.map((d, dIndex) => (
                            <div key={dIndex}>
                              {d.text ||
                                (d.doseAndRate?.[0]?.doseQuantity
                                  ? `${
                                      d.doseAndRate[0].doseQuantity.value || ""
                                    } ${
                                      d.doseAndRate[0].doseQuantity.unit || ""
                                    }`
                                  : "Dosage Info N/A")}
                              {d.timing && (
                                <span className="ml-2"> ({d.timing})</span>
                              )}
                              {d.route && (
                                <span className="ml-2"> via {d.route}</span>
                              )}
                              {d.method && (
                                <span className="ml-2"> ({d.method})</span>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                      {/* Display MedicationRequest specific details if available */}
                      {med.resourceType === "MedicationRequest" && (
                        <div className="ml-4 mt-1 space-y-1">
                          {med.priority && (
                            <div>
                              <span className="font-semibold">Priority:</span>{" "}
                              {med.priority}
                            </div>
                          )}
                          {med.requester && (
                            <div>
                              <span className="font-semibold ml-2">
                                Requester:
                              </span>{" "}
                              {med.requester}
                            </div>
                          )}
                          {med.courseOfTherapyType && (
                            <div>
                              <span className="font-semibold">
                                Course of Therapy:
                              </span>{" "}
                              {med.courseOfTherapyType}
                            </div>
                          )}
                          {/* Add more MedicationRequest specific fields here */}
                        </div>
                      )}
                      {/* Display MedicationStatement specific details if available */}
                      {med.resourceType === "MedicationStatement" && (
                        <div className="ml-4 mt-1 space-y-1">
                          {med.dateAsserted && (
                            <div>
                              <span className="font-semibold">
                                Asserted On:
                              </span>{" "}
                              {formatDateTime(med.dateAsserted)}
                            </div>
                          )}
                          {med.informationSource && (
                            <div>
                              <span className="font-semibold">
                                Information Source:
                              </span>{" "}
                              {med.informationSource}
                            </div>
                          )}
                        </div>
                      )}
                      {med.note?.length > 0 && (
                        <div className="ml-4 mt-1">
                          (<span className="font-semibold">Notes:</span>
                          <ul className="list-disc list-inside ml-2">
                            {med.note.map((n, nIndex) => (
                              <li key={nIndex}>{n}</li>
                            ))}
                          </ul>
                          )
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {uniqueSymptoms?.length > 0 && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Symptoms</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {uniqueSymptoms.map((med, index) => (
                    <div key={index + 1} className="ml-4 mt-1">
                      <span className="font-semibold"></span>
                      <div className="list-disc list-inside ml-2">
                        <li key={index}>
                          {med.code} {med.note}
                        </li>
                      </div>
                    </div>
                  ))}
                </ul>
              </div>
            )}

            {procedure.length > 0 && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">
                  Past Procedure History
                </h3>
                {procedure.map((procedure) => (
                  <div key={procedure.id} className="text-sm mb-2">
                    <div>
                      <span className="font-semibold">Procedure:</span>{" "}
                      {procedure.code}
                      {"  "}
                      {procedure.note?.length > 0 && (
                        <span>{procedure.note}</span>
                      )}
                    </div>
                    <div>
                      <span className="font-medium ml-2">Status:</span>{" "}
                      {procedure.status || "N/A"}
                    </div>
                    {procedure.performedPeriod?.start && (
                      <div>
                        <span className="font-medium ml-2">Date:</span>{" "}
                        {formatDate(procedure.performedPeriod.start)}
                      </div>
                    )}
                    {procedure.performedDateTime &&
                      !procedure.performedPeriod?.start && (
                        <div>
                          <span className="font-medium ml-2">Date & Time:</span>{" "}
                          {formatDateTime(procedure.performedDateTime)}
                        </div>
                      )}
                    {procedure.reasonReference?.length > 0 && (
                      <div>
                        <span className="font-medium ml-2">Reason:</span>{" "}
                        {procedure.reasonReference.join(", ")}
                      </div>
                    )}
                    {procedure.performer?.length > 0 && (
                      <div>
                        <span className="font-medium ml-2">Performer(s):</span>
                        <ul className="list-disc list-inside ml-2">
                          {procedure.performer.map((p, pIndex) => (
                            <li key={pIndex}>
                              {p.actor} ({p.function || "N/A"})
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {procedure.bodySite?.length > 0 && (
                      <div>
                        <span className="font-medium ml-2">Body Site:</span>{" "}
                        {procedure.bodySite.join(", ")}
                      </div>
                    )}
                    {procedure.outcome && (
                      <div>
                        <span className="font-medium ml-2">Outcome:</span>{" "}
                        {procedure.outcome}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {followUp?.id && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Follow Up</h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-semibold">Description:</span>{" "}
                    {followUp.description || "N/A"}
                  </div>
                  <div>
                    <span className="font-semibold">Status:</span>{" "}
                    {followUp.status || "N/A"}
                  </div>
                  <div>
                    <span className="font-semibold">Type:</span>{" "}
                    {followUp.appointmentType || "N/A"}
                  </div>
                  <div>
                    <span className="font-semibold">Specialty:</span>{" "}
                    {followUp.specialty?.join(", ") || "N/A"}
                  </div>
                  {followUp.start && (
                    <div>
                      <span className="font-semibold">Date & Time:</span>{" "}
                      {formatDateTime(followUp.start)}
                    </div>
                  )}
                  {followUp.end && (
                    <div>
                      <span className="font-semibold">End Time:</span>{" "}
                      {formatDateTime(followUp.end)}
                    </div>
                  )}
                  {followUp.reasonReference?.length > 0 && (
                    <div>
                      <span className="font-semibold">Reason:</span>{" "}
                      {followUp.reasonReference.join(", ")}
                    </div>
                  )}
                  {followUp.participant?.length > 0 && (
                    <div>
                      <span className="font-semibold">Participants:</span>
                      <ul className="list-disc list-inside ml-2">
                        {followUp.participant.map((p, index) => (
                          <li key={index}>
                            {p.actor || "N/A"} ({p.status || "N/A"}){" "}
                            {p.required && `(Required: ${p.required})`}{" "}
                            {p.type?.length > 0 && `(${p.type.join(", ")})`}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {followUp.comment && (
                    <div>
                      <span className="font-semibold">Comment:</span>{" "}
                      {followUp.comment}
                    </div>
                  )}
                  {followUp.patientInstruction && (
                    <div>
                      <span className="font-semibold">
                        Patient Instruction:
                      </span>{" "}
                      {followUp.patientInstruction}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* --- Wellness Specific Sections --- */}

            {vitalSigns?.length > 0 && ( // Only show section if data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Vital Signs</h3>
                {vitalSigns?.length > 0 ? (
                  <ul className="list-disc list-inside space-y-2 text-sm">
                    {vitalSigns.map((observation, index) => (
                      <li key={index}>
                        <span className="font-semibold">
                          {observation.code || "Observation"}
                        </span>
                        :
                        {/* Display components for grouped observations (like BP) */}
                        {observation.components?.length > 0 ? (
                          <ul className="list-none list-inside ml-4 space-y-1">
                            {" "}
                            {/* Use list-none for sub-items */}
                            {observation.components.map((comp, compIndex) => (
                              <li key={compIndex}>
                                <span className="font-medium">
                                  {comp.code || "Component"}
                                </span>
                                : {comp.value || "N/A"} {comp.unit || ""}
                                {comp.interpretation?.length > 0 && (
                                  <span className="ml-2 italic">
                                    ({comp.interpretation.join(", ")})
                                  </span>
                                )}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          // Display single value for simple observations
                          <span>
                            {" "}
                            {observation.value || "N/A"}{" "}
                            {observation.unit || ""}
                          </span>
                        )}
                        {observation.effectiveDateTime && (
                          <span className="ml-3 text-gray-600">
                            (Recorded:{" "}
                            {formatDateTime(observation.effectiveDateTime)})
                          </span>
                        )}
                        {observation.effectivePeriod?.start &&
                          observation.effectivePeriod.end && (
                            <span className="ml-2 text-gray-600">
                              (Period:{" "}
                              {formatDate(observation.effectivePeriod.start)} -{" "}
                              {formatDate(observation.effectivePeriod.end)})
                            </span>
                          )}
                        {observation.issued && (
                          <span className="ml-2 text-gray-600">
                            (Issued: {formatDateTime(observation.issued)})
                          </span>
                        )}
                        {observation.performer?.length > 0 && (
                          <span className="ml-2 text-gray-600 italic">
                            {" "}
                            (By: {observation.performer.join(", ")})
                          </span>
                        )}
                        {observation.interpretation?.length > 0 &&
                          observation.components?.length === 0 && (
                            <span className="ml-2 italic">
                              ({observation.interpretation.join(", ")})
                            </span>
                          )}{" "}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500 text-sm">
                    No vital signs recorded in this record.
                  </p>
                )}
              </div>
            )}

            {/* --- Invoice Specific Sections --- */}

            {(invoice?.id || chargeItems?.length > 0) && ( // Only show section if invoice or charge items data is present
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Invoice Details</h3>
                {invoice?.id && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm mb-4">
                    <div>
                      <span className="font-semibold">Invoice ID:</span>{" "}
                      {invoice.id || "N/A"}
                    </div>
                    <div>
                      <span className="font-semibold">Status:</span>{" "}
                      {invoice.status || "N/A"}
                    </div>
                    {invoice.type && (
                      <div>
                        <span className="font-semibold">Type:</span>{" "}
                        {invoice.type}
                      </div>
                    )}
                    {invoice.date && (
                      <div>
                        <span className="font-semibold">Date:</span>{" "}
                        {formatDate(invoice.date)}
                      </div>
                    )}
                    {invoice.issuer && (
                      <div>
                        <span className="font-semibold">Issuer:</span>{" "}
                        {invoice.issuer}
                      </div>
                    )}
                    {invoice.recipient && (
                      <div>
                        <span className="font-semibold">Recipient:</span>{" "}
                        {invoice.recipient}
                      </div>
                    )}
                    {invoice.account && (
                      <div>
                        <span className="font-semibold">Account:</span>{" "}
                        {invoice.account}
                      </div>
                    )}
                    {invoice.totalNet && (
                      <div>
                        <span className="font-semibold">Total Net:</span>{" "}
                        {invoice.totalNet.value} {invoice.totalNet.currency}
                      </div>
                    )}
                    {invoice.totalTax && (
                      <div>
                        <span className="font-semibold">Total Tax:</span>{" "}
                        {invoice.totalTax.value} {invoice.totalTax.currency}
                      </div>
                    )}
                    {invoice.totalGross && (
                      <div>
                        <span className="font-semibold">Total Gross:</span>{" "}
                        {invoice.totalGross.value} {invoice.totalGross.currency}
                      </div>
                    )}
                    {invoice.paymentTerms && (
                      <div className="md:col-span-2">
                        <span className="font-semibold">Payment Terms:</span>{" "}
                        {invoice.paymentTerms}
                      </div>
                    )}
                    {invoice.note?.length > 0 && (
                      <div className="md:col-span-2">
                        ( <span className="font-semibold">Notes:</span>
                        <ul className="list-disc list-inside ml-2">
                          {invoice.note.map((n, nIndex) => (
                            <li key={nIndex}>{n}</li>
                          ))}
                        </ul>
                        )
                      </div>
                    )}
                  </div>
                )}

                {chargeItems?.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2 text-md">Line Items</h4>
                    <ul className="list-disc list-inside space-y-2 text-sm">
                      {chargeItems.map((item, index) => (
                        <li key={index}>
                          <span className="font-semibold">
                            {item.code || "Charge Item"}
                          </span>{" "}
                          - Status: {item.status || "N/A"}
                          {item.quantity && (
                            <span>
                              {" "}
                              - Quantity: {item.quantity.value}{" "}
                              {item.quantity.unit}
                            </span>
                          )}
                          {item.priceOverride && (
                            <span>
                              {" "}
                              - Price: {item.priceOverride.value}{" "}
                              {item.priceOverride.currency}
                            </span>
                          )}
                          {item.costFactor && (
                            <span> - Factor: {item.costFactor}</span>
                          )}
                          {item.occurrenceDateTime && (
                            <span className="ml-2 text-gray-600">
                              (Occurred:{" "}
                              {formatDateTime(item.occurrenceDateTime)})
                            </span>
                          )}
                          {item.occurrencePeriod?.start && (
                            <span>
                              <span className="ml-2 text-gray-600">
                                (Period:{" "}
                                {formatDate(item.occurrencePeriod.start)} -{" "}
                                {formatDate(item.occurrencePeriod.end)})
                              </span>
                            </span>
                          )}
                          {item.performer?.length > 0 && (
                            <span className="ml-2 text-gray-600 italic">
                              {" "}
                              (By:{" "}
                              {item.performer.map((p) => p.actor).join(", ")})
                            </span>
                          )}
                          {item.note?.length > 0 && (
                            <div className="ml-4 mt-1">
                              ( <span className="font-semibold">Notes:</span>
                              <ul className="list-disc list-inside ml-2">
                                {item.note.map((n, nIndex) => (
                                  <li key={nIndex}>{n}</li>
                                ))}
                              </ul>
                              )
                            </div>
                          )}
                          {/* Add more charge item fields here */}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {!invoice?.id && chargeItems?.length === 0 && (
                  <p className="text-gray-500 text-sm">
                    No invoice details or line items found.
                  </p>
                )}
              </div>
            )}

            {/* --- Health Document Specific Sections --- */}

            {documentReferences?.length > 0 && ( // Only show section if data is present
              <div className="mb-6">
                {" "}
                <h3 className="font-bold mb-2 text-lg">Document References</h3>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  {documentReferences.map((docRef, index) => (
                    <li key={index}>
                      <span className="font-semibold">
                        {docRef.description ||
                          docRef.type ||
                          `Document ${index + 1}`}
                      </span>{" "}
                      {docRef.author?.length > 0 && (
                        <span className="ml-2 italic">
                          (Author(s): {docRef.author.join(", ")})
                        </span>
                      )}
                      {docRef.custodian && (
                        <span className="ml-2 italic">
                          (Custodian: {docRef.custodian})
                        </span>
                      )}
                      {docRef.context?.encounter?.length > 0 && (
                        <span className="ml-2">
                          (Encounter: {docRef.context.encounter.join(", ")})
                        </span>
                      )}
                      {docRef.content?.length > 0 && (
                        <div className="ml-4 mt-1 space-y-1">
                          <span className="font-semibold">Content:</span>
                          {docRef.content.map((content, cIndex) => (
                            <div key={cIndex}>
                              Date: {formatDate(content.creation)} Type:{" "}
                              {content.contentType.split("/")[1] || "N/A"}
                              {content.title && (
                                <div> Title: {content.title}</div>
                              )}
                              {content.data && (
                                <button
                                  onClick={() =>
                                    openBase64File(
                                      content.data,
                                      content.contentType
                                    )
                                  }
                                  className="text-blue-600 hover:underline ml-2"
                                >
                                  View Document
                                </button>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {immunizations?.length > 0 && (
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Immunizations</h3>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  {immunizations.map((imm, index) => (
                    <li key={index}>
                      <span className="font-semibold">
                        {imm.vaccineCode || "Immunization"}
                      </span>{" "}
                      <div className="ml-5 text-gray-600">
                        <span>- Status: {imm.status || "N/A"}</span>{" "}
                        {imm.occurrenceDateTime && (
                          <span>
                            (Administered:{" "}
                            {formatDateTime(imm.occurrenceDateTime)})
                          </span>
                        )}
                      </div>
                      {imm.lotNumber && (
                        <div className="ml-5 text-gray-600">
                          {" "}
                          - Lot: {imm.lotNumber}
                        </div>
                      )}
                      {imm.notes && (
                        <div className="ml-5 text-gray-600"> - {showNotes(imm.notes)}</div>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {diagnosticReports?.length > 0 && (
              <div className="mb-6 pb-4 border-b">
                <h3 className="font-bold mb-2 text-lg">Diagnostic Reports</h3>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  {diagnosticReports.map((report, index) => (
                    <li key={index}>
                      <span className="font-semibold">
                        {report.code || "Diagnostic Report"}
                      </span>{" "}
                      - Status: {report.status || "N/A"}
                      {report.effectiveDateTime && (
                        <span className="ml-2 text-gray-600">
                          (Effective: {formatDateTime(report.effectiveDateTime)}
                          )
                        </span>
                      )}
                      {report.effectivePeriod?.start && (
                        <span>
                          <span className="ml-2 text-gray-600">
                            (Period: {formatDate(report.effectivePeriod.start)}{" "}
                            - {formatDate(report.effectivePeriod.end)})
                          </span>
                        </span>
                      )}
                      {report.issued && (
                        <span className="ml-2 text-gray-600">
                          (Issued: {formatDateTime(report.issued)})
                        </span>
                      )}
                      {report.performer?.length > 0 && (
                        <span className="ml-2 text-gray-600 italic">
                          (By: {report.performer.join(", ")})
                        </span>
                      )}
                      {report.conclusion && (
                        <div className="ml-4 mt-1 italic">
                          Conclusion: {report.conclusion}
                        </div>
                      )}
                      {report.presentedForm?.length > 0 && (
                        <div className="ml-4 mt-1">
                          <span className="font-semibold">Forms:</span>
                          {report.presentedForm.map((form, fIndex) => (
                            <div key={fIndex}>
                              Type: {form.contentType.split("/")[1]}
                              {form.title && (
                                <span> - Title: {form.title}</span>
                              )}
                              {form.data && (
                                <button
                                  onClick={() =>
                                    openBase64File(form.data, form.contentType)
                                  }
                                  className="text-blue-600 hover:underline ml-2"
                                >
                                  View Report
                                </button>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                      {/* Add more report fields */}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Fallback Message if no specific data sections are rendered */}
            {!bundle?.id &&
              !composition?.id &&
              !patient?.name &&
              practitioners?.length === 0 &&
              !organization?.name &&
              !encounter?.id &&
              chiefComplaints?.length === 0 &&
              allergies?.length === 0 &&
              medicalHistory?.length === 0 &&
              investigationAdvice?.length === 0 &&
              medications?.length === 0 &&
              !procedure?.code &&
              !followUp?.id &&
              documentReferences?.length === 0 &&
              vitalSigns?.length === 0 &&
              observations?.length === 0 &&
              !invoice?.id &&
              chargeItems?.length === 0 &&
              immunizations?.length === 0 &&
              diagnosticReports?.length === 0 && (
                <p className="text-gray-500 text-sm">
                  No detailed record data found in the extracted information.
                </p>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};
