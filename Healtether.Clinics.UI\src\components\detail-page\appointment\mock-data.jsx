import { Icons } from "../icons";

export const customer_support = (
  appointment,
  navigation,
  MakeReceiptInAppointmentOverview,
  alertBox,
  doctor,
  SuperAdmin,
  admin,
  cancel,
  no_show
) => [
  {
    label: (
      <>
        Check <br /> out
      </>
    ),
    Icon: Icons.check_out,
  },
  {
    label: (
      <>
        Write <br /> Rx
      </>
    ),
    Icon: Icons.write_x,
    handleClick: (e) => {
      navigation(
        `/appointment/${appointment._id}/${appointment.patientId}/writeprescription`
      );
      e.stopPropagation();
    },
  },
  {
    label: "Vitals and History",
    Icon: Icons.vitals,
    handleClick: (e) => {
      if (doctor || admin || SuperAdmin) {
        navigation(
          `/appointment/${appointment._id}/${appointment.patientId}/medicalhistory`
        );
      } else {
        navigation(
          `/appointment/${appointment._id}/${appointment.patientId}/medicalhistoryforstaffs`
        );
      }
      e.stopPropagation();
    },
  },
  {
    label: "Make Receipts",
    Icon: Icons.receipt,
    handleClick: async () => {
      let response = await MakeReceiptInAppointmentOverview(appointment._id);
      if (response?.isValid) {
        navigation(`/payments/${response?.invoiceId}/manage`);
      } else {
        alertBox({
          show: true,
          title: "Info.",
          proceed: undefined,
          confirmation: "Something went wrong",
        });
      }
    },
  },
  {
    label: "Chat",
    Icon: Icons.whatsapp,
    handleClick: () => navigation("/chats"),
  },
  {
    label: "Manage Apt.",
    Icon: Icons.settings,
    handleClick: () =>
      navigation(
        `/patient/${appointment.patientId}/appointment/${appointment._id}/details`
      ),
  },
  {
    label: "Check in",
    Icon: Icons.check_in,
    handleClick: () => {},
  },
  {
    label: "No Show",
    Icon: Icons.no_show,
    handleClick: no_show,
  },
  {
    label: "Cancel",
    Icon: Icons.cancel,
    handleClick: cancel,
  },
  {
    label: "Reschedule",
    Icon: Icons.reschedule,
    handleClick: () => {},
  },
  {
    label: "Chat",
    Icon: Icons.whatsapp,
    handleClick: () => navigation("/chats"),
  },
];

// export const abha_address = [
//   { email: "PatientA@abdm", id: "Patient A" },
//   { email: "PatientB@abdm", id: "Patient B" },
//   { email: "PatientC@abdm", id: "Patient C" },
// ];
export const abha_address = [
  {
    name: "Ashok Kumar",
    dob: "08/10/1999",
    gender: "Male",
    id: "1",
    abhaAddress: "ABHA address",
    abhaNumber: "ABHA number"
  },
  {
    name: "Ashok Kumar",
    dob: "08/10/1999",
    gender: "Male",
    id: "2",
    abhaAddress: "ABHA address",
    abhaNumber: "ABHA number"
  },
  {
    name: "Ashok Kumar",
    dob: "08/10/1999",
    gender: "Male",
    id: "3",
    abhaAddress: "ABHA address",
    abhaNumber: "ABHA number"
  }
];
export const new_abha_inputs = [
  { label: "First name", type: "text", handleChange: (e) => console.log(e) },
  { label: "Last name", type: "text", handleChange: (e) => console.log(e) },
  { label: "Gender", type: "text", handleChange: (e) => console.log(e) },
  { label: "Birthdate", type: "date", handleChange: (e) => console.log(e) },
  { label: "Address", type: "text", handleChange: (e) => console.log(e) },
  { label: "Pincode", type: "number", handleChange: (e) => console.log(e) },
  { label: "ABHA Address", type: "email", handleChange: (e) => console.log(e) },
  { label: "ABHA Number", type: "number", handleChange: (e) => console.log(e) },
  { label: "Mobile ", type: "number", handleChange: (e) => console.log(e) },
 
];
