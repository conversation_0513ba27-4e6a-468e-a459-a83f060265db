import { jest } from "@jest/globals";

const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
mockApointmentHelper();
const { getAppointmentById } = await import("../../controllers/appointments/appointment.controller.js"); 
const { getAppointment } = await import("../../helpers/appointment/appointment.helper.js");  

describe('getAppointmentById', () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: {
                id: 'appointmentId123'
            }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should return 200 with the appointment data', async () => {
        const mockAppointment = {
            _id: "appointmentId",
            mobile: "**********",
            name: "test23",
            gender: "Female",
            age: "15",
            birthDate: "2009-07-10",
            appointmentDate: "2024-09-15",
            timeSlot: "07:30 PM - 07:50 PM",
            reason: "test",
            virtualConsultation: "true",
            patientId: "patientId123",
            doctorId: "662ca0ad1a2431e16c41ebb1",
            doctorName: "Venkatesh Raja",
            clientId: "662ca0a41a2431e16c41ebaa",
            clinicPatientId: "clinicPatientId123",
        };
        getAppointment.mockResolvedValue(mockAppointment);
        await getAppointmentById(req, res);
        expect(getAppointment).toHaveBeenCalledWith(req.query.id);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockAppointment);
    });

    
});
