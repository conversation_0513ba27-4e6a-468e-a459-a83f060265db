import React, { useEffect, useRef, useState } from "react";
import { SearchPatientMobileApi } from "services/patient/patient";
import debounce from "lodash/debounce";
import AutocompleteModel from "utils/AutocompleteModel";
import { Input } from "../detail-page/input";

export default function Autocomplete({
  onSelect,
  name,
  value,
  defaultValue,
  onChange,
  customClass,
  placeHolder,
  search,
  searchMapper,
  setDisable = false,
  maxLength,
  avoidImg = false,
}) {
  const [showOptions, setShowOptions] = useState(false);
  const [filteredOptions, setfilteredOptions] = useState([]);
  const [cursor, setCursor] = useState(-1);
  const [loading, SetLoading] = useState(false);
  const ref = useRef();

  const select = (option) => {
    onSelect(option);
    setShowOptions(false);
  };
  let onChangeDebounced = (text) => {
    if (text.length > 3) {
      setfilteredOptions([]);
      SetLoading(true);
      search(text, 50).then((res) => {
        var data = res.data;
        var options = searchMapper(data);
        setfilteredOptions(options);
        setCursor(-1);
        if (!showOptions) {
          setShowOptions(true);
        }
        SetLoading(false);
      });
    }
  };
  onChangeDebounced = debounce(onChangeDebounced, 200);
  const handleChange = (text) => {
    onChange(text);
    onChangeDebounced(text);
  };

  const moveCursorDown = () => {
    if (cursor < filteredOptions.length - 1) {
      var c = cursor;
      c = c + 1;
      setCursor(c);
    }
  };

  const moveCursorUp = () => {
    if (cursor > 0) {
      var c = cursor;
      c = c - 1;
      setCursor(c - 1);
    }
  };

  const handleNav = (e) => {
    switch (e.key) {
      case "ArrowUp":
        moveCursorUp();
        break;
      case "ArrowDown":
        moveCursorDown();
        break;
      case "Enter":
        if (cursor >= 0 && cursor < filteredOptions.length) {
          select(filteredOptions[cursor].Value);
          return false;
        }
        break;
    }
  };

  useEffect(() => {
    const listener = (e) => {
      if (!ref.current.contains(e.target)) {
        setShowOptions(false);
        setCursor(-1);
      }
    };

    document.addEventListener("click", listener);
    document.addEventListener("focusin", listener);
    return () => {
      document.removeEventListener("click", listener);
      document.removeEventListener("focusin", listener);
    };
  }, []);

  let className = "px-4 hover:bg-gray-100 ";

  return (
    <div className="relative w-full " ref={ref}>
      <Input.text
        type="text"
        value={value}
        onChange={(e) => handleChange(e.target.value)}
        onFocus={(e) => {
          setShowOptions(false);
          handleChange(e.target.value);
        }}
        onKeyDown={handleNav}
        readOnly={setDisable}
        maxLength={maxLength || 100}
        defaultValue={defaultValue}
        autoComplete="off"
        aria-autocomplete="list"
        required
        placeholder={"Mobile number"}
        name={name}
      />
      {loading ? (
        <div
          className=" absolute end-2.5 bottom-2.5 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-backcolor_secondary motion-reduce:animate-[spin_1.5s_linear_infinite]"
          role="status"
        >
          <span className="absolute! -m-px! h-px! w-px! overflow-hidden! whitespace-nowrap! border-0! p-0! [clip:rect(0,0,0,0)]!">
            Loading...
          </span>
        </div>
      ) : (
        <></>
      )}
      <section
        // ref={targetRef}
        className={`${
          showOptions
            ? " visible opacity-100 scale-100"
            : " invisible opacity-0 scale-0"
        } bg-gray mt-2 border border-color_muted/20 origin-top-right duration-300 flex w-full flex-col absolute top-11 left-0 z-30 rounded-sm overflow-hidden shadow-xl`}
      >
        {filteredOptions?.map((prop, idx) => (
          <div
            key={idx}
            onClick={() => select(prop.Value)}
            className="text-xs sm:text-base  font-normal justify-between h-11 px-4 pt-2.5 cursor-pointer hover:bg-dark/10 duration-200 capitalize"
          >
            {prop?.Value?.mobile || prop.MainText}
          </div>
        ))}
      </section>
    </div>
  );
}
