import { useEffect, useState } from "react";
import { Form, redirect, useLoaderData } from "react-router-dom";
import Documents from "components/detail-page/Documents";
import ContactDetails from "components/detail-page/ContactDetails";
import PatientPersonalDetails from "components/detail-page/patient/PatientPersonalDetails";
import ValidatePatient from "validation/Patient";
import { AddUpdatePatientApi } from "services/patient/patient";
import { useSelector } from "react-redux";
import { GetPatient } from "../../services/patient/patient";
import { GetCurrentPatientId } from "../../services/client/client";
import { NavigationButton } from "../../components/detail-page/Navigation";
export async function PatientAction({ request, params }) {
  const formData = await request.formData();

  const updates = Object.fromEntries(
    Array.from(formData.keys()).map((key) => [
      key,
      formData.getAll(key).length > 1
        ? formData.getAll(key)
        : formData.get(key),
    ])
  );
  const validation = ValidatePatient(updates);
  if (!validation) return false;

  var response = await AddUpdatePatientApi(updates, params.id);
  if (!!response && response.success) return redirect(`/patient/managepatient`);
  else return false;
}
export async function PatientLoader({ params }) {
  var patientData = undefined;
  if (params?.id != undefined) {
    patientData = await GetPatient(params.id);
    return { patientData };
  }
  return { patientData };
}
function adjustName(obj) {
  const lastNameParts = obj.lastName.trim().split(" ");
  if (lastNameParts.length === 1) return obj;
  obj.lastName = lastNameParts.splice(-1).join("");
  obj.firstName = obj.firstName + " " + lastNameParts.join(" ");
  return obj;
}

function EditPatient() {
  var { clinic } = useSelector((store) => store.currentClinic);
  const blob_URL = `${import.meta.env.VITE_BLOB_URL}${import.meta.env.VITE_CLINICBLOB_CONTAINER_PREFIX
    }${clinic._id}/patient/`;
  var { patientData } = useLoaderData();
  patientData = adjustName(patientData);
  const [autoId, SetAutoId] = useState(patientData?.patientId);
  const [documentNames, setDocumentNames] = useState(
    patientData?.documents || []
  );
  if (patientData == undefined) {
    patientData = {};
  }
  var isLoadingAutoid = false;

  useEffect(() => {
    if (patientData?.patientId == null && !isLoadingAutoid) {
      isLoadingAutoid = true;
      const fetchAutoId = async () => {
        let data = await GetCurrentPatientId();
        let generatedId = "";
        if (data?.patientId?.prefix != null && data?.patientId?.prefix != "") {
          generatedId = data.patientId.prefix + "_";
        }
        generatedId = generatedId + data.patientId.currentPatientId;
        if (data?.patientId?.suffix != null && data.patientId.suffix != "") {
          generatedId = generatedId + "_" + data.patientId.suffix;
        }
        SetAutoId(generatedId);
      };
      fetchAutoId();
    }
  }, [isLoadingAutoid]);
  const settingsArray = ["Personal details", "Contact details", "Documents"];
  const [activeTab, setActiveTab] = useState(0);

  const handleTabClick = (tabNumber) => {
    setActiveTab(tabNumber);
  };
  return (
    <div className="h-full">
      <Form
        className="flex flex-row h-full pb-4 gap-3"
        method="post"
        encType="multipart/form-data"
        noValidate
      >

        <nav className="tabs flex-col border rounded-lg items-start p-3 font-primary text-base font-medium gap-3 w-xs" aria-label="Tabs" role="tablist" data-tabs-vertical="true" aria-orientation="horizontal" >
          <button type="button" className="btn btn-text btn-secondary active-tab:bg-secondary/10 active-tab:text-black hover:text-secondary hover:bg-secondary/10 active w-full px-10 py-7 justify-start" id="tab-patientdetails" data-tab="#tab-patientdetails-data" aria-controls="tab-patientdetails-data" role="tab" aria-selected="true" >
            Personal Details
          </button>
          <button type="button" className="btn btn-text btn-secondary active-tab:bg-secondary/10 active-tab:text-black hover:text-secondary hover:bg-secondary/10 w-full px-10 py-7 justify-start" id="tab-medicalrecords" data-tab="#tab-medicalrecords-data" aria-controls="tab-medicalrecords-data" role="tab" aria-selected="false" >
            Contact Details
          </button>
          <button type="button" className="btn btn-text btn-secondary active-tab:bg-secondary/10 active-tab:text-black hover:text-secondary hover:bg-secondary/10 w-full px-10 py-7 justify-start" id="tab-abharecords" data-tab="#tab-abharecords-data" aria-controls="tab-abharecords-data" role="tab" aria-selected="false" >
            Documents
          </button>
        </nav>

        <div className="w-10/12 ml-2 h-full">
          <div id="tab-patientdetails-data" className=" flex flex-col h-full px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-patientdetails">
            <PatientPersonalDetails
              patientId={autoId}
              firstName={patientData.firstName}
              lastName={patientData.lastName}
              age={patientData.age}
              gender={patientData.gender}
              birthday={patientData.birthday}
              height={patientData.height}
              weight={patientData.weight}
              abhaAddress={patientData.abhaAddress}
              abhaNumber={patientData.abhaNumber}
              prefix={patientData.prefix}
              click={() => {

              }}
            />
            <NavigationButton
              next={() => {
                window.HSTabs.open(document.querySelector('#tab-medicalrecords'));
                return 1;
              }}
            />
          </div>
          <div id="tab-medicalrecords-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-medicalrecords">
            <ContactDetails
              mobile={patientData.mobile}
              email={patientData.email}
              address={patientData.address}
            />
            <NavigationButton
              prev={() => {
                window.HSTabs.open(document.querySelector('#tab-patientdetails'));
                return 1;
              }}
              next={() => {
                window.HSTabs.open(document.querySelector('#tab-abharecords'));
                return 1;
              }}
            />
          </div>
          <div id="tab-abharecords-data" className="hidden flex flex-col h-full px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-abharecords">
            <Documents
              documentType={patientData.documentType}
              documentNames={documentNames}
              setDocumentsNames={setDocumentNames}
              documentNo={patientData.documentNumber}
              blob_url={blob_URL}
            />
          </div>
        </div>
      </Form>
    </div>
  );
}

export default EditPatient;
