import { SNSClient, PublishCommand } from "@aws-sdk/client-sns";

export const sendSMSMessage = async (sns, params) => {
  console.log(params);
  const command = new PublishCommand(params);
  const message = await sns.send(command);
  return message;
};
export const sns = new SNSClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

export const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000)
    .toString()
    .substring(0, 6);
};
