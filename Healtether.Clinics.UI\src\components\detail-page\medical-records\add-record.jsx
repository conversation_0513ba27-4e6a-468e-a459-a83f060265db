import React, { useState, useRef, useEffect } from "react";
import UploadFile from "./upload-file";
import SuccessfullOrFailed from "../../../components/detail-page/successfull-or-failed";
import Modal from "../modal";
import { Input } from "../input";
import { Buttons } from "../appointment/button";
import { SaveAsDraftApi } from "../../../services/appointment/appointment";
import { useParams } from "react-router-dom";

export default function AddRecord({ isAddRecord, setIsAddRecord, consultation }) {
  let params= useParams();
  const [file, setFile] = useState({});
  const [fileName, setFileName] = useState("");
  const [selectedDate, setSelectedDate] = useState(formatDate(new Date()));
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [records, setRecords] = useState({
    selected: "",
    data: [
      { name: "Prescription Record" },
      { name: "Procedure Record" },
      { name: "Medical Record" }
    ],
  });

  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };

  }, []);

  // Format date as YYYY-MM-DD for input field
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Set file name when file changes
  useEffect(() => {
    if (file?.name) {
      setFileName(file.name);
    }
  }, [file]);

  const next = () =>
    setIsAddRecord({ ...isAddRecord, success: false, failed: false });

  const cancel = () =>
    setIsAddRecord({ state: false, success: false, failed: false });

  const submit = async () => {
    if (!file || !records.selected) {
      // Show error message
      setIsAddRecord({ ...isAddRecord, failed: true });
      return;
    }

    try {
      // Determine which record type is selected
      const recordType = records.selected.name;
      
      // Create a new list based on the record type
      let prescriptionList = [];
      let medicalRecordList = [];
      let procedureRecordList = [];
      
      if (recordType === "Prescription Record") {
        prescriptionList = [file];
      } else if (recordType === "Medical Record") {
        medicalRecordList = [file];
      } else if (recordType === "Procedure Record") {
        procedureRecordList = [file];
      }
      
      // Call the API to save the record
      const result = await SaveAsDraftApi(
        prescriptionList,
        medicalRecordList,
        procedureRecordList,
        [],  // removeRecordList
        params.id,
      );
      
      // Show success message
      setIsAddRecord({ ...isAddRecord, success: true });
    } catch (error) {
      console.error("Error uploading record:", error);
      setIsAddRecord({ ...isAddRecord, failed: true });
    }
  };

  // Function to save record - placeholder for your actual API call
  const saveRecord = async (prescriptionList, medicalRecordList, procedureRecordList, removeRecordList, appointmentId, date) => {
    // This would be your actual API call 
    console.log("Saving record with date:", date);
    console.log("Records:", { prescriptionList, medicalRecordList, procedureRecordList });
    
    // Return mock successful response
    return { status: 200 };
  };

  const handleSelectItem = (item) => {
    setRecords({ ...records, selected: item });
    setIsDropdownOpen(false);
  };

  const uploadProps = { 
    file, 
    setFile,
    allowedFileTypes: ['.png', '.jpg', '.jpeg', '.pdf', '.docx'], 
    maxFileSize: 50 // MB
  };

  return (
    <Modal
      isOpen={isAddRecord.state}
      setIsOpen={() => setIsAddRecord({ ...isAddRecord, state: false })}
      plain={true}
      classname={`font-primary h-fit w-96!`}
    >
      <>
        {isAddRecord.success ? (
          <SuccessfullOrFailed
            success={"Record has been uploaded successfully"}
            nextButtonTitle={"Upload Another Record"}
            nextButton={next}
            closeButtonTitle={"Close"}
            closeButton={cancel}
          />
        ) : isAddRecord.failed ? (
          <SuccessfullOrFailed
            error={"Record has not been uploaded successfully."}
            nextButtonTitle={"Try again"}
            nextButton={next}
          />
        ) : (
          <article className="grid grid-cols-1 gap-4">
            <UploadFile {...uploadProps} />
            <section className="flex flex-col gap-1">
              <div className="text-sm font-bold text-dark">File name</div>
              <Input.text
                placeholder="Ex. Prescription_jane"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
              />
            </section>
            <section className="flex flex-col gap-1" ref={dropdownRef}>
              <div className="text-sm font-bold text-dark">Record type*</div>
              {/* Custom Select Implementation */}
              <div className="relative">
                <button
                  type="button"
                  className="w-full px-4 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                >
                  {records.selected ? records.selected.name : "Ex. Prescription"}
                  <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg
                      className="w-5 h-5 text-gray-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </span>
                </button>

                {isDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md max-h-60 overflow-auto">
                    <ul className="py-1">
                      {records.data.map((item, index) => (
                        <li
                          key={index}
                          className="px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer"
                          onClick={() => handleSelectItem(item)}
                        >
                          {item.name}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </section>
            {/* <section className="flex flex-col gap-1">
              <div className="text-sm font-bold text-dark">
                Date of the Record
              </div>
              <Input.date 
                placeholder="24/08/24" 
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </section> */}
            <div className="font-light text-xs text-Primary mt-1">
              Note: Please upload image/document (<b>.png, .jpg, .docx & .pdf</b>) of size
              <b> less than 50MB</b>
            </div>
            <footer className="flex justify-end gap-2 mt-5">
              <Buttons.light onClick={cancel} title="Cancel" />
              <Buttons.primary onClick={submit} title="Done" />
            </footer>
          </article>
        )}
      </>
    </Modal>
  );
}