{"resourceType": "Bundle", "id": "93c50048-7ae0-42e8-bc6c-c80a4931cda4", "meta": {"versionId": "1", "lastUpdated": "2025-04-19T13:57:36.383+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"], "security": [{"system": "http://terminology.hl7.org/CodeSystem/v3-Confidentiality", "code": "V", "display": "very restricted"}]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "type": "document", "timestamp": "2025-04-19T13:57:36.383+05:30", "entry": [{"fullUrl": "urn:uuid:7afe8c7d-2b38-4235-959a-635ec19f1a9e", "resource": {"resourceType": "Composition", "id": "7afe8c7d-2b38-4235-959a-635ec19f1a9e", "meta": {"versionId": "1", "lastUpdated": "2025-04-19T13:57:36.383+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord"]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "language": "en", "status": "final", "type": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}], "text": "Clinical consultation report"}, "date": "2025-04-19T13:57:36.383+05:30", "title": "Consultation Report", "subject": {"reference": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "display": "Patient"}, "encounter": {"reference": "urn:uuid:fee6c883-36ce-4500-a4fa-************", "display": "Encounter"}, "author": [{"reference": "urn:uuid:3c743050-01fb-43f6-ad45-2bf8a37926cd", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "attester": [{"mode": "legal", "party": {"reference": "urn:uuid:3c743050-01fb-43f6-ad45-2bf8a37926cd", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"mode": "legal", "party": {"reference": "urn:uuid:167675a8-f516-44da-8cde-a9b8d6274095", "display": "Health Organization"}}], "custodian": {"reference": "urn:uuid:167675a8-f516-44da-8cde-a9b8d6274095", "display": "Health Organization"}, "section": [{"title": "Chief complaints", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422843007", "display": "Chief complaint section"}], "text": "Chief complaint section"}, "entry": [{"reference": "urn:uuid:cf16d71d-ce26-4752-9488-5adba2d36c01", "display": "Condition"}, {"reference": "urn:uuid:2ee55489-4e9b-4734-81bb-b6ebcdb7f486", "display": "Condition"}]}, {"title": "Allergies", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "722446000", "display": "Allergy record"}]}, "entry": [{"reference": "urn:uuid:0f641018-3489-4747-8ca1-a2dca17cecf0", "display": "AllergyIntolerance"}]}, {"title": "Medical History", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371529009", "display": "History and physical report"}]}, "entry": [{"reference": "urn:uuid:cf16d71d-ce26-4752-9488-5adba2d36c01", "display": "Condition"}, {"reference": "urn:uuid:2ee55489-4e9b-4734-81bb-b6ebcdb7f486", "display": "Condition"}]}, {"title": "Investigation Advice", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721963009", "display": "Order document"}]}, "entry": [{"reference": "urn:uuid:cec0cedd-2615-402f-a6cd-33e220c56cac", "display": "ServiceRequest"}]}, {"title": "Medications", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721912009", "display": "Medication summary document"}]}, "entry": [{"reference": "urn:uuid:f4490c8d-8cd4-4863-91fe-3349f82e06a3", "display": "MedicationStatement"}, {"reference": "urn:uuid:e09fdf5b-cc1f-4a7e-959e-05c20917db5e", "display": "MedicationRequest"}]}, {"title": "Procedure", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371525003", "display": "Clinical procedure report"}]}, "entry": [{"reference": "urn:uuid:d117cd12-8967-43e8-8195-58341364cec5", "display": "Procedure"}]}, {"title": "Follow Up", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "736271009", "display": "Outpatient care plan"}]}, "entry": [{"reference": "urn:uuid:dad24a0e-5d3c-42b9-b749-4467be4a6a20", "display": "Appointment"}]}, {"title": "Document Reference", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}]}, "entry": []}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Composition Record</p></div>"}}}, {"fullUrl": "urn:uuid:3c743050-01fb-43f6-ad45-2bf8a37926cd", "resource": {"resourceType": "Practitioner", "id": "3c743050-01fb-43f6-ad45-2bf8a37926cd", "meta": {"versionId": "1", "lastUpdated": "2025-04-19T13:57:36.466+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MD", "display": "Medical License number"}]}, "system": "https://doctor.ndhm.gov.in", "value": "12345678"}], "name": [{"use": "official", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["Dr."]}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "male", "address": [{"use": "home", "type": "physical", "postalCode": "533101", "country": "india", "district": "<PERSON>hya pradesh", "city": "rajahmundry", "state": "<PERSON>hya pradesh", "text": ""}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Practitioner Record</p></div>"}}}, {"fullUrl": "urn:uuid:167675a8-f516-44da-8cde-a9b8d6274095", "resource": {"resourceType": "Organization", "id": "167675a8-f516-44da-8cde-a9b8d6274095", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "PRN", "display": "Provider number"}]}, "system": "https://facility.ndhm.gov.in", "value": "1234567"}], "name": "Health Organization", "telecom": [{"system": "phone", "value": "**********", "use": "work"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Organization Record</p></div>"}}}, {"fullUrl": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "resource": {"resourceType": "Patient", "id": "23160f52-1fb4-410b-97ef-0aef0d3194d9", "meta": {"versionId": "1", "lastUpdated": "2025-04-19T13:57:36.466+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAID"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "91-1248-5708-0632"}, {"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAADDRESS"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "monikakushwah12@sbx"}], "name": [{"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["Mr."]}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "male", "birthDate": "2000-09-09", "address": [{"use": "home", "type": "physical", "postalCode": "213445", "country": "india", "district": "park", "city": "delhi", "state": "park", "text": "12"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Patient Record</p></div>"}}}, {"fullUrl": "urn:uuid:fee6c883-36ce-4500-a4fa-************", "resource": {"resourceType": "Encounter", "id": "fee6c883-36ce-4500-a4fa-************", "meta": {"lastUpdated": "2025-04-19T13:57:36.383+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]}, "identifier": [{"system": "https://ndhm.in", "value": "hip1"}, {"system": "https://ndhm.in", "value": "hip2"}], "status": "finished", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "AMB", "display": "ambulatory"}, "subject": {"reference": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "display": "Patient"}, "period": {"start": "2023-10-01T10:00:00+05:30", "end": "2023-11-01T10:00:00+05:30"}, "diagnosis": [{"condition": {"reference": "urn:uuid:cf16d71d-ce26-4752-9488-5adba2d36c01", "display": "Condition"}, "use": {"coding": [{"system": "http://snomed.info/sct", "code": "38341003", "display": "Hypertension"}], "text": "Hypertension"}}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang=en'><p>Encounter Record</p></div>"}}}, {"fullUrl": "urn:uuid:0f641018-3489-4747-8ca1-a2dca17cecf0", "resource": {"resourceType": "AllergyIntolerance", "id": "0f641018-3489-4747-8ca1-a2dca17cecf0", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/AllergyIntolerance"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "762952008", "display": "Peanuts"}], "text": "Peanuts"}, "recordedDate": "2025-04-19T13:57:36.383+05:30", "patient": {"reference": "urn:uuid:3cbf6c0b-d379-432d-9f67-e651be4684ca", "display": "Patient"}, "recorder": {"reference": "urn:uuid:3c743050-01fb-43f6-ad45-2bf8a37926cd", "display": "Practitioner"}, "note": [{"text": ""}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>AllergyIntolerance Record</p></div>"}}}, {"fullUrl": "urn:uuid:dad24a0e-5d3c-42b9-b749-4467be4a6a20", "resource": {"resourceType": "Appointment", "id": "dad24a0e-5d3c-42b9-b749-4467be4a6a20", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment"]}, "status": "booked", "description": "hello", "start": "2023-10-01T10:00:00+05:30", "end": "2023-10-01T11:00:00+05:30", "created": "2025-04-18T11:26:51.390Z", "serviceCategory": [{"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}], "serviceType": [{"coding": [{"system": "http://snomed.info/sct", "code": "60132005", "display": "General"}], "text": "General"}], "appointmentType": {"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}, "reasonReference": [{"reference": "urn:uuid:cf16d71d-ce26-4752-9488-5adba2d36c01", "display": "Condition"}], "basedOn": [{"reference": "urn:uuid:cec0cedd-2615-402f-a6cd-33e220c56cac", "display": "ServiceRequest"}], "specialty": [{"coding": [{"system": "http://snomed.info/sct", "code": "394579002", "display": "Cardiology"}], "text": "Cardiology"}], "participant": [{"actor": {"reference": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "display": "Patient"}, "status": "accepted"}, {"actor": {"reference": "urn:uuid:3c743050-01fb-43f6-ad45-2bf8a37926cd", "display": "Practitioner"}, "status": "accepted"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Appointment Record</p></div>"}}}, {"fullUrl": "urn:uuid:cf16d71d-ce26-4752-9488-5adba2d36c01", "resource": {"resourceType": "Condition", "id": "cf16d71d-ce26-4752-9488-5adba2d36c01", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "38341003", "display": "Hypertension"}], "text": "Hypertension"}, "recordedDate": "2025-04-19T08:27:36.000Z", "onsetPeriod": {"start": "2025-04-19T08:27:36.000Z", "end": "2025-04-19T08:27:36.000Z"}, "subject": {"reference": "urn:uuid:e6b9c1e3-99b3-4af5-b3be-ee90bdb5b0df", "display": "Patient"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Condition Record</p></div>"}}}, {"fullUrl": "urn:uuid:2ee55489-4e9b-4734-81bb-b6ebcdb7f486", "resource": {"resourceType": "Condition", "id": "2ee55489-4e9b-4734-81bb-b6ebcdb7f486", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "38341003", "display": "Hypertension"}], "text": "Hypertension"}, "recordedDate": "2025-04-19T08:27:35.525Z", "onsetPeriod": {"start": "2025-04-19T08:27:35.525Z", "end": "2025-04-19T08:27:35.525Z"}, "subject": {"reference": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "display": "Patient"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Condition Record</p></div>"}}}, {"fullUrl": "urn:uuid:d117cd12-8967-43e8-8195-58341364cec5", "resource": {"resourceType": "Procedure", "id": "d117cd12-8967-43e8-8195-58341364cec5", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"]}, "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "29303009", "display": "Electrocardiogram"}], "text": "Electrocardiogram"}, "subject": {"reference": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "display": "Patient"}, "performedDateTime": "2023-10-01T10:30:00+05:30", "followUp": [{"coding": [{"system": "http://snomed.info/sct", "code": "281036007", "display": "Follow-up consultation"}], "text": "Follow-up consultation"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Procedure Record</p></div>"}}}, {"fullUrl": "urn:uuid:cec0cedd-2615-402f-a6cd-33e220c56cac", "resource": {"resourceType": "ServiceRequest", "id": "cec0cedd-2615-402f-a6cd-33e220c56cac", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ServiceRequest"]}, "status": "completed", "intent": "order", "authoredOn": "2025-04-19T13:57:36.383+05:30", "category": [{"coding": [{"system": "http://snomed.info/sct", "code": "396550006", "display": "blood test"}], "text": "blood test"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "15220000", "display": "Laboratory test"}], "text": "Laboratory test"}, "subject": {"reference": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "display": "Patient"}, "requester": {"reference": "urn:uuid:3c743050-01fb-43f6-ad45-2bf8a37926cd", "display": "Practitioner"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>ServiceRequest Record</p></div>"}}}, {"fullUrl": "urn:uuid:f4490c8d-8cd4-4863-91fe-3349f82e06a3", "resource": {"resourceType": "MedicationStatement", "id": "f4490c8d-8cd4-4863-91fe-3349f82e06a3", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationStatement"]}, "status": "completed", "dateAsserted": "2025-04-19T13:57:36.383+05:30", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "134463001", "display": "Telmisartan 20 mg oral tablet"}], "text": "Telmisartan 20 mg oral tablet"}, "subject": {"reference": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "display": "Patient"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>MedicationStatement Record</p></div>"}}}, {"fullUrl": "urn:uuid:e09fdf5b-cc1f-4a7e-959e-05c20917db5e", "resource": {"resourceType": "MedicationRequest", "id": "e09fdf5b-cc1f-4a7e-959e-05c20917db5e", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]}, "status": "active", "intent": "order", "authoredOn": "2025-04-19T08:27:35.525Z", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "387517004", "display": "Paracetamol"}], "text": "Paracetamol"}, "subject": {"reference": "urn:uuid:23160f52-1fb4-410b-97ef-0aef0d3194d9", "display": "Patient"}, "requester": {"reference": "urn:uuid:3c743050-01fb-43f6-ad45-2bf8a37926cd", "display": "Practitioner"}, "reasonReference": [{"reference": "urn:uuid:cf16d71d-ce26-4752-9488-5adba2d36c01", "display": "Condition"}], "dosageInstruction": [{"text": "500mg every 6 hours", "timing": {"repeat": {"frequency": "", "period": 1, "periodUnit": "Hours"}}, "route": {"coding": [{"system": "http://snomed.info/sct", "code": "738956005", "display": "Oral"}], "text": "Oral"}, "doseQuantity": {"value": "1", "unit": "tablet"}, "site": {"coding": [{"system": "http://snomed.info/sct", "code": "123851003", "display": "Mouth"}], "text": "Mouth"}, "additionalInstruction": [{"coding": [{"system": "http://snomed.info/sct", "code": "272393004", "display": "Tests"}], "text": "Tests"}]}], "reasonCode": [{"coding": [{"system": "http://snomed.info/sct", "code": "182970005", "display": "Pain Relief"}], "text": "Pain Relief"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>MedicationRequest Record</p></div>"}}}], "signature": {"type": [{"system": "urn:iso-astm:E1762-95:2013", "code": "1.2.840.10065.1.12.1.1", "display": "Author's Signature"}], "when": "2025-04-19T13:57:36.383+05:30", "who": {"reference": "urn.:3c743050-01fb-43f6-ad45-2bf8a37926cd", "display": "Practitioner"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ=="}}