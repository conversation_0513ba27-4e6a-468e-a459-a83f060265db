# NDHM FHIR R4 Implementation Examples

## Table of Contents
1. [Complete Consultation Workflow](#complete-consultation-workflow)
2. [Pharmacy Billing Example](#pharmacy-billing-example)
3. [Diagnostic Report Example](#diagnostic-report-example)
4. [Immunization Record Example](#immunization-record-example)
5. [Wellness Tracking Example](#wellness-tracking-example)
6. [Integration Patterns](#integration-patterns)

## Complete Consultation Workflow

This example demonstrates a complete outpatient consultation with billing.

### 1. Patient Resource
```json
{
  "resourceType": "Patient",
  "id": "patient-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]
  },
  "identifier": [
    {
      "type": {
        "coding": [{
          "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-identifier-type-code",
          "code": "ABHA",
          "display": "ABHA ID"
        }]
      },
      "value": "12-3456-7890-1234"
    }
  ],
  "name": [{
    "text": "<PERSON><PERSON>",
    "family": "<PERSON>",
    "given": ["<PERSON><PERSON>"]
  }],
  "gender": "male",
  "birthDate": "1985-06-15",
  "address": [{
    "text": "123 MG Road, Bangalore, Karnataka, 560001",
    "city": "Bangalore",
    "state": "Karnataka",
    "postalCode": "560001",
    "country": "IN"
  }]
}
```

### 2. Practitioner Resource
```json
{
  "resourceType": "Practitioner",
  "id": "doctor-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner"]
  },
  "identifier": [
    {
      "type": {
        "coding": [{
          "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-identifier-type-code",
          "code": "MCI",
          "display": "MCI Registration"
        }]
      },
      "value": "MCI-12345"
    }
  ],
  "name": [{
    "text": "Dr. Priya Sharma",
    "family": "Sharma",
    "given": ["Priya"],
    "prefix": ["Dr."]
  }],
  "qualification": [{
    "code": {
      "coding": [{
        "system": "http://snomed.info/sct",
        "code": "309343006",
        "display": "Physician"
      }]
    }
  }]
}
```

### 3. Encounter Resource
```json
{
  "resourceType": "Encounter",
  "id": "encounter-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]
  },
  "status": "finished",
  "class": {
    "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
    "code": "AMB",
    "display": "ambulatory"
  },
  "type": [{
    "coding": [{
      "system": "http://snomed.info/sct",
      "code": "11429006",
      "display": "Consultation"
    }]
  }],
  "subject": {"reference": "Patient/patient-001"},
  "participant": [{
    "individual": {"reference": "Practitioner/doctor-001"}
  }],
  "period": {
    "start": "2024-01-15T10:00:00+05:30",
    "end": "2024-01-15T10:30:00+05:30"
  }
}
```

### 4. Condition Resource
```json
{
  "resourceType": "Condition",
  "id": "condition-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]
  },
  "clinicalStatus": {
    "coding": [{
      "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
      "code": "active"
    }]
  },
  "verificationStatus": {
    "coding": [{
      "system": "http://terminology.hl7.org/CodeSystem/condition-ver-status",
      "code": "confirmed"
    }]
  },
  "code": {
    "coding": [{
      "system": "http://hl7.org/fhir/sid/icd-10",
      "code": "I10",
      "display": "Essential hypertension"
    }]
  },
  "subject": {"reference": "Patient/patient-001"},
  "encounter": {"reference": "Encounter/encounter-001"},
  "onsetDateTime": "2024-01-15"
}
```

### 5. Vital Signs Observation
```json
{
  "resourceType": "Observation",
  "id": "bp-observation-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]
  },
  "status": "final",
  "category": [{
    "coding": [{
      "system": "http://terminology.hl7.org/CodeSystem/observation-category",
      "code": "vital-signs"
    }]
  }],
  "code": {
    "coding": [{
      "system": "http://loinc.org",
      "code": "85354-9",
      "display": "Blood pressure panel"
    }]
  },
  "subject": {"reference": "Patient/patient-001"},
  "encounter": {"reference": "Encounter/encounter-001"},
  "effectiveDateTime": "2024-01-15T10:15:00+05:30",
  "component": [
    {
      "code": {
        "coding": [{
          "system": "http://loinc.org",
          "code": "8480-6",
          "display": "Systolic blood pressure"
        }]
      },
      "valueQuantity": {
        "value": 150,
        "unit": "mmHg",
        "system": "http://unitsofmeasure.org",
        "code": "mm[Hg]"
      }
    },
    {
      "code": {
        "coding": [{
          "system": "http://loinc.org",
          "code": "8462-4",
          "display": "Diastolic blood pressure"
        }]
      },
      "valueQuantity": {
        "value": 95,
        "unit": "mmHg",
        "system": "http://unitsofmeasure.org",
        "code": "mm[Hg]"
      }
    }
  ]
}
```

### 6. Medication Request
```json
{
  "resourceType": "MedicationRequest",
  "id": "medication-request-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]
  },
  "status": "active",
  "intent": "order",
  "medicationCodeableConcept": {
    "coding": [{
      "system": "http://snomed.info/sct",
      "code": "387506000",
      "display": "Amlodipine"
    }],
    "text": "Amlodipine 5mg tablet"
  },
  "subject": {"reference": "Patient/patient-001"},
  "encounter": {"reference": "Encounter/encounter-001"},
  "requester": {"reference": "Practitioner/doctor-001"},
  "dosageInstruction": [{
    "text": "Take one tablet daily in the morning",
    "timing": {
      "repeat": {
        "frequency": 1,
        "period": 1,
        "periodUnit": "d"
      }
    },
    "route": {
      "coding": [{
        "system": "http://snomed.info/sct",
        "code": "26643006",
        "display": "Oral route"
      }]
    },
    "doseAndRate": [{
      "doseQuantity": {
        "value": 1,
        "unit": "tablet"
      }
    }]
  }],
  "dispenseRequest": {
    "quantity": {
      "value": 30,
      "unit": "tablet"
    }
  }
}
```

### 7. ChargeItem for Consultation
```json
{
  "resourceType": "ChargeItem",
  "id": "charge-consultation-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ChargeItem"]
  },
  "status": "billable",
  "code": {
    "coding": [{
      "system": "http://snomed.info/sct",
      "code": "11429006",
      "display": "Consultation"
    }]
  },
  "subject": {"reference": "Patient/patient-001"},
  "context": {"reference": "Encounter/encounter-001"},
  "occurrenceDateTime": "2024-01-15T10:00:00+05:30",
  "performer": [{
    "actor": {"reference": "Practitioner/doctor-001"}
  }],
  "quantity": {
    "value": 1,
    "unit": "visit"
  },
  "priceOverride": {
    "value": 500,
    "currency": "INR"
  }
}
```

### 8. Invoice for Consultation
```json
{
  "resourceType": "Invoice",
  "id": "invoice-consultation-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Invoice"]
  },
  "identifier": [{
    "value": "CONS/2024/001"
  }],
  "status": "issued",
  "type": {
    "coding": [{
      "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
      "code": "00",
      "display": "Consultation"
    }]
  },
  "subject": {"reference": "Patient/patient-001"},
  "date": "2024-01-15T10:30:00+05:30",
  "participant": [{
    "actor": {"reference": "Practitioner/doctor-001"}
  }],
  "lineItem": [{
    "sequence": 1,
    "chargeItemReference": {"reference": "ChargeItem/charge-consultation-001"},
    "priceComponent": [
      {
        "type": "base",
        "code": {
          "coding": [{
            "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
            "code": "01",
            "display": "Rate"
          }]
        },
        "amount": {"value": 500, "currency": "INR"}
      },
      {
        "type": "tax",
        "code": {
          "coding": [{
            "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
            "code": "03",
            "display": "CGST"
          }]
        },
        "amount": {"value": 45, "currency": "INR"}
      },
      {
        "type": "tax",
        "code": {
          "coding": [{
            "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
            "code": "04",
            "display": "SGST"
          }]
        },
        "amount": {"value": 45, "currency": "INR"}
      }
    ]
  }],
  "totalNet": {"value": 590, "currency": "INR"},
  "totalGross": {"value": 500, "currency": "INR"}
}
```

### 9. OPConsultRecord (Composition)
```json
{
  "resourceType": "Composition",
  "id": "op-consult-record-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord"]
  },
  "status": "final",
  "type": {
    "coding": [{
      "system": "http://snomed.info/sct",
      "code": "*********",
      "display": "Clinical consultation report"
    }]
  },
  "subject": {"reference": "Patient/patient-001"},
  "encounter": {"reference": "Encounter/encounter-001"},
  "date": "2024-01-15T10:30:00+05:30",
  "author": [{"reference": "Practitioner/doctor-001"}],
  "title": "Outpatient Consultation Record",
  "section": [
    {
      "title": "Chief Complaint",
      "code": {
        "coding": [{
          "system": "http://snomed.info/sct",
          "code": "422843007",
          "display": "Chief complaint section"
        }]
      },
      "text": {
        "status": "generated",
        "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\">Patient complains of headache and dizziness for the past week.</div>"
      }
    },
    {
      "title": "Vital Signs",
      "code": {
        "coding": [{
          "system": "http://snomed.info/sct",
          "code": "118227000",
          "display": "Vital signs"
        }]
      },
      "entry": [{"reference": "Observation/bp-observation-001"}]
    },
    {
      "title": "Assessment and Plan",
      "code": {
        "coding": [{
          "system": "http://snomed.info/sct",
          "code": "51847004",
          "display": "Assessment"
        }]
      },
      "entry": [
        {"reference": "Condition/condition-001"},
        {"reference": "MedicationRequest/medication-request-001"}
      ]
    }
  ]
}
```

### 10. DocumentBundle
```json
{
  "resourceType": "Bundle",
  "id": "consultation-bundle-001",
  "meta": {
    "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"]
  },
  "type": "document",
  "timestamp": "2024-01-15T10:30:00+05:30",
  "entry": [
    {"resource": "Composition/op-consult-record-001"},
    {"resource": "Patient/patient-001"},
    {"resource": "Practitioner/doctor-001"},
    {"resource": "Encounter/encounter-001"},
    {"resource": "Condition/condition-001"},
    {"resource": "Observation/bp-observation-001"},
    {"resource": "MedicationRequest/medication-request-001"},
    {"resource": "ChargeItem/charge-consultation-001"},
    {"resource": "Invoice/invoice-consultation-001"}
  ]
}
```

## Pharmacy Billing Example

### Medication ChargeItems
```json
[
  {
    "resourceType": "ChargeItem",
    "id": "charge-amlodipine-001",
    "status": "billable",
    "code": {
      "coding": [{
        "system": "http://snomed.info/sct",
        "code": "387506000",
        "display": "Amlodipine"
      }]
    },
    "subject": {"reference": "Patient/patient-001"},
    "quantity": {"value": 30, "unit": "tablet"},
    "priceOverride": {"value": 120, "currency": "INR"}
  },
  {
    "resourceType": "ChargeItem",
    "id": "charge-metformin-001",
    "status": "billable",
    "code": {
      "coding": [{
        "system": "http://snomed.info/sct",
        "code": "387567004",
        "display": "Metformin"
      }]
    },
    "subject": {"reference": "Patient/patient-001"},
    "quantity": {"value": 60, "unit": "tablet"},
    "priceOverride": {"value": 80, "currency": "INR"}
  }
]
```

### Pharmacy Invoice
```json
{
  "resourceType": "Invoice",
  "id": "pharmacy-invoice-001",
  "identifier": [{"value": "PHARM/2024/001"}],
  "status": "issued",
  "type": {
    "coding": [{
      "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
      "code": "01",
      "display": "Pharmacy"
    }]
  },
  "subject": {"reference": "Patient/patient-001"},
  "date": "2024-01-15T15:00:00+05:30",
  "lineItem": [
    {
      "sequence": 1,
      "chargeItemReference": {"reference": "ChargeItem/charge-amlodipine-001"},
      "priceComponent": [
        {
          "type": "base",
          "code": {"coding": [{"code": "01", "display": "Rate"}]},
          "amount": {"value": 120, "currency": "INR"}
        },
        {
          "type": "tax",
          "code": {"coding": [{"code": "03", "display": "CGST"}]},
          "amount": {"value": 7.2, "currency": "INR"}
        },
        {
          "type": "tax",
          "code": {"coding": [{"code": "04", "display": "SGST"}]},
          "amount": {"value": 7.2, "currency": "INR"}
        }
      ]
    },
    {
      "sequence": 2,
      "chargeItemReference": {"reference": "ChargeItem/charge-metformin-001"},
      "priceComponent": [
        {
          "type": "base",
          "code": {"coding": [{"code": "01", "display": "Rate"}]},
          "amount": {"value": 80, "currency": "INR"}
        },
        {
          "type": "tax",
          "code": {"coding": [{"code": "03", "display": "CGST"}]},
          "amount": {"value": 4.8, "currency": "INR"}
        },
        {
          "type": "tax",
          "code": {"coding": [{"code": "04", "display": "SGST"}]},
          "amount": {"value": 4.8, "currency": "INR"}
        }
      ]
    }
  ],
  "totalNet": {"value": 224, "currency": "INR"},
  "totalGross": {"value": 200, "currency": "INR"}
}
```

## Integration Patterns

### 1. ABHA Integration Pattern
```javascript
// Example integration with your ABHA helper
const createFHIRBundle = (patientData, consultationData) => {
  return {
    resourceType: "Bundle",
    type: "document",
    entry: [
      {
        resource: createPatientResource(patientData.abhaAddress)
      },
      {
        resource: createCompositionResource(consultationData)
      }
      // ... other resources
    ]
  };
};

const sendToABDM = async (bundle) => {
  // Use your existing ABHA helper functions
  const consentData = await initiateConsentRequestHelper({
    abhaAddress: bundle.entry[0].resource.identifier[0].value,
    hiTypes: ["OPConsultation"],
    // ... other consent parameters
  });
  
  if (consentData.ok) {
    // Send health information using your helper
    await sendHealthInformationRequest({
      consent: consentData,
      bundle: bundle
    });
  }
};
```

### 2. Validation Pattern
```javascript
const validateFHIRResource = (resource) => {
  // Validate against NDHM profiles
  const requiredFields = {
    Patient: ['identifier', 'name', 'gender'],
    Invoice: ['identifier', 'status', 'type', 'subject', 'date'],
    // ... other resource requirements
  };
  
  return requiredFields[resource.resourceType].every(
    field => resource[field] !== undefined
  );
};
```

---

**Related Documents:**
- [NDHM FHIR R4 Overview](./NDHM-FHIR-R4-Overview.md)
- [Clinical Profiles Guide](./NDHM-Clinical-Profiles.md)
- [Billing Implementation](./NDHM-Billing-Implementation.md)
- [Terminology Guide](./NDHM-Terminology.md)
