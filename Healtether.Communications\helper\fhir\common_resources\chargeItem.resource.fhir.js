import { v4 as uuidv4 } from "uuid";
import {
  chargeItemDiv,
  chargeItemMetaData,
  getSnomedCtCode,
  invoiceCodeConstant,
} from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

export const generateChargeItemResource = async (
  type,
  chargeItem,
  patientResource,
  practitionerResources
) => {
  const id = uuidv4();
 const getSnomedData = await generateSnomedCtCode(type);
  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "ChargeItem",
      id,
      meta:chargeItemMetaData(),
      status: chargeItem.status,
      code: invoiceCodeConstant(),
      subject: {
        reference: patientResource.fullUrl,
        display: "Patient",
      },
      performer: practitionerResources.map((practitioner) => ({
        actor: { reference: practitioner.fullUrl, display: "Practitioner" },
      })),
      quantity: chargeItem.quantity
        ? { value: chargeItem.quantity }
        : undefined,
      productCodeableConcept: getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term)
      // text: chargeItemDiv(),
    },
  };
};
