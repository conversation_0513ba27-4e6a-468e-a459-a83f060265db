export const parseFhirBundle = (bundleData) => {
    // Ensure we're working with an array
    const bundleArray = Array.isArray(bundleData) ? bundleData : [bundleData];
    
    // Map through each bundle in the array
    return bundleArray.map(bundleItem => {
      // Parse the item if it's a string
      const bundle = typeof bundleItem === 'string' ? JSON.parse(bundleItem) : bundleItem;
      
      // Extract basic info
      const timestamp =bundle&& bundle.timestamp;
      const documentType = bundle&&bundle.entry?.find(e => 
        e.resource?.resourceType === 'Composition'
      )?.resource?.type?.text || "Medical Record";
      
      // Get patient info if available
      const patient =bundle&& bundle.entry?.find(e => 
        e.resource?.resourceType === 'Patient'
      )?.resource;
      
      const patientName = patient?.name?.[0]?.text || "Unknown Patient";
      
      // Format the date
      const formattedDate = timestamp ? 
        new Date(timestamp).toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'short'
        }) : "Unknown Date";
      
      // Return processed bundle with key information
      return {
        id:bundle&& bundle.id,
        type: documentType,
        patientName,
        date: formattedDate,
        raw:bundle&& bundle // Include raw data for further processing if needed
      };
    });
  };