import mongoose from "mongoose";
import { CLIENT_COLLECTION, HISTORY_ALLERGIES_COLLECTION, HISTORY_FAMILY_DISEASES_COLLECTION, HISTORY_MEDICATIONS_COLLECTION, HISTORY_PAST_DISEASES_COLLECTION, HISTORY_PERSONAL_DISEASES_COLLECTION, PAST_PROCEDURES_COLLECTION, QUEUED_APPOINTMENT_COLLECTION, VITALS_COLLECTION } from "../mongodb.collection.name.js";

const patientSchema = new mongoose.Schema({
        patientId: {
            type: String,
            index: true,
            maxLength: 255,
            required: true
        },
        prefix:{
            type: String,
            enum: ['Mr.', 'Mrs.', 'Miss', 'Dr.', 'Prof.', 'Rev.', 'Hon.', 'Other'],
            // required: true,
            maxLength: 50

        },
        firstName: {
            type: String,
            index: true,
            required: true
        },
        lastName: {
            type: String,
            index: true,
        },
        age: {
            type: Number,
            min: 1,
            max: 100
        },
        height: {
            type: Number,
        },
        weight: {
            type: Number,
        },
        birthday: {
            type: Date
        },
        gender: {
            type: String
        },
        mobile: {
            type: String,
            maxLength: 15,
            required: true,
            index: true
        },
        abhaAddress:{
            type: String,
            maxLength: 255,
        },
        abhaNumber:{
            type: String,
            maxLength: 255,
        },
        profilePic: String,
        whatsapp: {
            type: String
        },
        countryCode: {
            type: String,
            default: '+91',
            maxLength: 10
        },
        email: {
            type: String
        },
        address: {
            house: {
                type: String,
                maxLength: 255
            },
            street: {
                type: String,
                maxLength: 1000
            },
            landmarks: {
                type: String,
                maxLength: 1000
            },
            city: {
                type: String,
                maxLength: 500
            },
            district: {
                type: String,
                maxLength: 250
            },
            state: {
                type: String,
                maxLength: 100
            },
            pincode: {
                type: String,
                maxLength: 50
            }
        },
        documentType: {
            type: String
        },
        documentNumber: {
            type: String
        },
        created: {
            on: {
                type: Date,
                default: Date.Now
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255
                },
            }
        },
        modified: {
            on: {
                type: Date
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255
                },
            }
        },
        documents: [{
            fileName: {
                type: String,
                maxLength: 255
            },
            blobName: {
                type: String,
                maxLength: 255
            },
            uploadedOn: Date
        }],

        deleted: {
            type: Boolean,
            default: false
        },
        clinic: {
            type: mongoose.Schema.Types.ObjectId,
            ref: CLIENT_COLLECTION,
            index: true
        },
        linkingToken:{
            type:String,
            default:""

        },
    },
    {versionKey: '1.5', toJSON: {virtuals: true}, toObject: {virtuals: true}}
)

patientSchema.virtual('appointments', {
    ref: QUEUED_APPOINTMENT_COLLECTION,
    localField: '_id',
    foreignField: 'patientId'
});
patientSchema.virtual('vitals', {
    ref: VITALS_COLLECTION,
    localField: '_id',
    foreignField: 'patientId'
});
patientSchema.virtual('allergies', {
    ref: HISTORY_ALLERGIES_COLLECTION,
    localField: '_id',
    foreignField: 'patient'
});
patientSchema.virtual('medication', {
    ref: HISTORY_MEDICATIONS_COLLECTION,
    localField: '_id',
    foreignField: 'patient'
});
patientSchema.virtual('familyHistory', {
    ref: HISTORY_FAMILY_DISEASES_COLLECTION,
    localField: '_id',
    foreignField: 'patient'
});
patientSchema.virtual('pastHistory', {
    ref: HISTORY_PAST_DISEASES_COLLECTION,
    localField: '_id',
    foreignField: 'patient'
});
patientSchema.virtual('pastProcedureHistory', {
    ref: PAST_PROCEDURES_COLLECTION,
    localField: '_id',
    foreignField: 'patient'
});
patientSchema.virtual('personalHistory', {
    ref: HISTORY_PERSONAL_DISEASES_COLLECTION,
    localField: '_id',
    foreignField: 'patient'
});
// Create the patient model
//const Patient = new mongoose.model("Patient", patientSchema);
export {patientSchema};
