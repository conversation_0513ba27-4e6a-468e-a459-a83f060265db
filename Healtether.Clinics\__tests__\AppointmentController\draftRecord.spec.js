import { jest } from "@jest/globals";
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");
const { mockBlobHelper } = await import("../mocks/mock.blob.helper.js");
mockBlobHelper()
mockApointmentHelper();
mockCommonUtils();
mockFirebaseMethod();

const { draftRecords } = await import('../../controllers/appointments/appointment.controller.js'); 
const { updateRecords }= await import('../../helpers/appointment/appointment.helper.js'); 
const { BlobHelper } = await import("../../helpers/storage/blob.helper.js"); 


describe('draftRecords', () => {
    let req, res;
    let mockRemoveBlob;

    beforeEach(() => {
        req = {
            body: {
                data: {
                    id: 'appointmentId123',
                    removeRecords: ['record1.pdf', 'record2.pdf'],
                    clientId: 'clientId123'
                }
            },
            user: {
                id: 'userId123'
            }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };

        mockRemoveBlob = jest.fn();
        BlobHelper.mockImplementation(() => ({
            RemoveBlob: mockRemoveBlob
        }));
    });

    it('should return 200 with the updated records and remove specified blobs', async () => {
        const mockAppointments = {
            _id: 'appointmentId123',
            status: 'Draft'
        };

        updateRecords.mockResolvedValue(mockAppointments);
        mockRemoveBlob.mockResolvedValue(); 

        await draftRecords(req, res);

        expect(updateRecords).toHaveBeenCalledWith(req.body.data, req.body.data.id, req.user);

        const removeBlobs = req.body.data.removeRecords;
        removeBlobs.forEach(blob => {
            const blobNameWithFolder = `${process.env.PATIENT_BLOB_FOLDER}${blob}`;
            expect(mockRemoveBlob).toHaveBeenCalledWith(blobNameWithFolder);
        });

        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockAppointments);
    });

});
