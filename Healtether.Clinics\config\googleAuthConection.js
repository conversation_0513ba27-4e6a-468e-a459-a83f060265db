import { google } from 'googleapis';

// Path to your service account key file
const SERVICE_ACCOUNT_FILE = 'credentials/google_console.json';
const ADMIN_EMAIL = process.env.GOOGLE_MEET_ADMIN_EMAIL

let calendarClient;

async function initGoogleCalendarClient() {
    try {
        const SCOPES = ['https://www.googleapis.com/auth/calendar'];
        const auth = new google.auth.GoogleAuth({
            keyFile: SERVICE_ACCOUNT_FILE,
            scopes: SCOPES,
        });
        calendarClient = await auth.getClient();
    } catch (error) {
        console.error('Error initializing Google Calendar client:', error);
        throw error;
    }
}

async function getClientDelegation() {
    try {
        const SCOPES = 'https://www.googleapis.com/auth/calendar';
        const auth = new google.auth.GoogleAuth({
            keyFile: SERVICE_ACCOUNT_FILE,
            scopes: SCOPES,
        });
        const authClient = await auth.getClient();
        const delegatedAuth = await authClient.createScoped(SCOPES);
        delegatedAuth.subject = ADMIN_EMAIL;
        return google.calendar({ version: 'v3', auth: delegatedAuth });
    } catch (error) {
        console.error('Error in getClientDelegation:', error);
        throw error;
    }
}

export { initGoogleCalendarClient, getClientDelegation };