import mongoose from "mongoose";
import { LogExceptionInCloudWatch } from "./cloudWatchLogger.js";
mongoose.set('strictQuery', false)
const mongodb = async (DB_URL = process.env.DB_URL)=>{
    try {
        await mongoose.connect(DB_URL).then(()=>{
              console.log("Mongodb Connected");
        }).catch((error)=>{
            LogExceptionInCloudWatch(error);
            console.log(error);
        })
    } catch (error) {
        console.log(error);

    }
}
export  {mongodb}

