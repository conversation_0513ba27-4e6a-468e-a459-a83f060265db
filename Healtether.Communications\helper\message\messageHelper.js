// import { WhatsappRecentChatModel } from "../../config/mongodb.config.js";
// import {messagelogSchema} from "../../whatsapp_api_model/messagelog.model.js";
// import mongoose from "mongoose";


// export const getAllMessage = async(clientId, tableName) => {
//     try {
//         var messageModel = mongoose.model(tableName, messagelogSchema);
//         const message = await messageModel
//             .find({clientId: clientId})
//             .sort({timeStamp: -1})
//             .select({_id: 1, message: 1, timeStamp: 1, isDelivered: 1})
//             .exec();
//         return message;
//     } catch (error) {
//         console.log(error);
//         return error;
//     }
// };
// export const getMessage = async(clientId, tableName, size, pg) => {
//     try {
//         var messageModel = mongoose.model(tableName, messagelogSchema);
//         const message = await messageModel
//             .find({clientId: clientId})
//             .limit(size)
//             .sort({timeStamp: -1})
//             .skip(pg * size)
//             .select({_id: 1, message: 1, timeStamp: 1, isDelivered: 1})
//             .exec();
//         return message;
//     } catch (error) {
//         console.log(error);
//         return error;
//     }
// };


// export const saveReceivedMessage = async(mobile, message,type) => {
//     try {
//         var mbl= mobile.replace(/\D/g, '').slice(-10);
//         const user = await WhatsappRecentChatModel
//             .find()
//             .populate({
//                 path: 'userDetail',
//                 match: {
//                     mobile: {
//                         $eq: mbl
//                     }
//                 },
//                 options: {
//                     limit: 1
//                 }
//             })
//             .sort({timeStamp: -1})
//             .limit(1)
//             .exec()
//         if (user && user[0] && user[0].userDetail) {
//             let tableName = user[0].userDetail.logger;
//             let clientId = user[0].clientId;
//             var messageModel = mongoose.model(tableName, messagelogSchema);
//             const messageLog = new messageModel({message: message, isDelivered: false, clientId: clientId,isReceived:true,type:type });
//             await messageLog.save();
//             return user[0];
//         }
//         else
//         {
//             return null;
//         }

//     } catch (error) {
//         console.log(error);
//         return error;
//     }
// };