import ContainerHeading from "components/detail-page/ContainerHeading"
import { useState } from "react"

function Status({ setIsAdmin, isAdmin, setIsDoctor, isDoctor }) {
  return (<div className='flex flex-col space-y-4 font-primary'>
    <ContainerHeading heading={"Select Status"} />
    <div>
      The status allows the members to enjoy certain privileges for the use of application.
    </div>
    <div className="flex flex-nowrap w-full gap-9 font-medium">
      <input type="hidden" name="isAdmin" value={isAdmin} />
      <input type="hidden" name="isDoctor" value={isDoctor} />
      <button type="button" className={(isAdmin ? "bg-accent text-white" : "bg-white text-text_primary") + " w-1/2 py-4 rounded-full " + (isDoctor ? "cursor-not-allowed" : "cursor-pointer")} disabled={isDoctor} onClick={() => setIsAdmin(!isAdmin)}>Admin</button>
      <button type="button" className={(isDoctor ? "bg-accent text-white" : "bg-white text-text_primary") + " w-1/2 py-4 rounded-full " + (isAdmin ? "cursor-not-allowed" : "cursor-pointer")} disabled={isAdmin} onClick={() => setIsDoctor(true)}>Doctor</button>
      <button type="button" className={(!isDoctor ? "bg-accent text-white" : "bg-white text-text_primary") + " w-1/2 py-4 rounded-full cursor-pointer"} onClick={() => setIsDoctor(false)}>Staff</button>

    </div>
  </div>)
}

export default Status