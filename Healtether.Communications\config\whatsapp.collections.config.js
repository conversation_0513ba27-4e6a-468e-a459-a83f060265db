import mongoose from "mongoose";
import { WhatsappSessionSchema } from "healtether.models/whatsapp.schema/whatsapp-session.schema.js";
import { WhatsappAppointmentSchema } from "healtether.models/whatsapp.schema/whatsapp-appointment-cache.schema.js";
import { MessagelogSchema } from "healtether.models/whatsapp.schema/messagelog.schema.js";
import { RecentchatSchema } from "healtether.models/whatsapp.schema/whatsapp-recent.schema.js";
import { sendMessage } from "./socket-io.js";  // Add this import
import dotenv from 'dotenv';
import { AbdmAPIAccessToken } from "healtether.models/clinics.schema/abha/abdm-accesstoken.schema.js";


let env = dotenv.config();
mongoose.set('strictQuery', false);
let whatsappDbConnectionString = process.env.MONGODB_WHATSAPP_URI;

const whatsapplogDb = mongoose.createConnection(whatsappDbConnectionString);

// Initialize models
whatsapplogDb.model("WhatsappSession", WhatsappSessionSchema);
whatsapplogDb.model("CacheWhatsappAppointmentDetail", WhatsappAppointmentSchema);
var messageLog = whatsapplogDb.model("WhatsappMessageLog", MessagelogSchema);
var recentLog = whatsapplogDb.model("WhatsappRecentChat", RecentchatSchema);

const socketCollection = whatsapplogDb.collection('socket.io-adapter-events');

whatsapplogDb.model("AbdmAccessToken", AbdmAPIAccessToken );


// Database connection events
whatsapplogDb.on('connected', () => {
});
whatsapplogDb.on('reconnected', () => console.log('whatsapp db reconnected'));
whatsapplogDb.on('close', () => { console.log('-> whatsapp db lost connection'); });

// Watch for changes in recent chat
let recentChatStream = recentLog.watch([{ $match: { operationType: { $in: ['insert', 'update'] } } }], { fullDocument: 'updateLookup' });
recentChatStream.on('change', function(data) {
    console.log("recent log  --", data);
    var roomId = "Room-" + data.fullDocument.clinicId;
    var payload = {
        "_id": data.fullDocument._id,
        "patientName": data.fullDocument.patientName,
        "timeStamp": data.fullDocument.timeStamp,
        "mobile": data.fullDocument.mobile,
        "unReaded": data.fullDocument.unReaded
    };
    sendMessage(roomId, "recentchatlog", payload);
});

// Watch for changes in message log
let logStream = messageLog.watch([{ $match: { operationType: { $in: ['insert', 'update'] } } }], { fullDocument: 'updateLookup' });
logStream.on('change', function(data) {
    console.log("log  --", data);  
    var chatRoomId = "ChatRoom-" + data.fullDocument.clinicId + "-" + data.fullDocument.mobile;
    var payload = {
        "_id": data.fullDocument._id,
        "message": data.fullDocument.message,
        "createdOn": data.fullDocument.createdOn,
        "isDelivered": data.fullDocument.isDelivered,
        "isReceived": data.fullDocument.isReceived,
        "type": data.fullDocument.type
    };
    sendMessage(chatRoomId, "message", payload);
});

export async function LogDBCloseConnection(){
   if(whatsapplogDb.readyState === 1)
    await whatsapplogDb.close();
}

export { whatsapplogDb, socketCollection };