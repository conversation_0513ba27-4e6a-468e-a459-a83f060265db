import React, { useState, useEffect } from "react";
import Modal from "../modal";
import { Input } from "../input";
import { Buttons } from "../appointment/button";
import VerifyNumber from "./verify-number";
import <PERSON><PERSON><PERSON><PERSON> from "./create-abha";
import ABHACard from "./ABHACard";

import {
  fetchAbhaAddressCard,
  getOtpForAbha,
  getUserAbhaProfile,
  verifyAbhaNumberAddressOtp,
} from "../../../services/appointment/abha-m1";
import {
  CalculateAge,
  formatDateToUTC,
  startTimer,
  updateResendAttempts,
} from "../../../utils/CommonMethods";
import { PatientProfileWithAbhaDetails } from "../../../services/patient/patient";
import Spinner from "../../loader/Spinner";
import PatientUpdateAbort from "./PatientUpdateAbort";
import { OtpInputs } from "./otp-inputs";

// Define constants for steps
const Steps = {
  ADDRESS_INPUT: "addressInput",
  VERIFY_NUMBER: "verifyNumber",
  VERIFY_AADHAR_MOBILENO: "verifyAadharMobileNo",
  VERIFY_MOBILENO: "verifyMobileNo",
  CREATE_ABHA: "createAbha",
  VIEW_ABHA_CARD: "viewAbhaCard",
  PATIENT_EXIST: "patientexist",
};

export default function AddAbhaUsingAddress({
  addAbhaUsing,
  setAddAbhaUsing,
  patientMobile,
  OnSelectPatient,
}) {
  const [currentStep, setCurrentStep] = useState(Steps.ADDRESS_INPUT);
  const [otp, setOtp] = useState(Array(6).fill(""));
  const [address, setAddress] = useState("");
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const [otpType, setOtpType] = useState("mobile");
  const [isModalOpen, setIsModalOpen] = useState(addAbhaUsing === "address");
  const [abhaCard, setAbhaCard] = useState(null);
  const [mobileEnding, setMobileEnding] = useState("");
  const [profileData, setProfileData] = useState(null);
  const [patientData, setPatientData] = useState(null);
  const [error, setError] = useState(null);
  const [timer, setTimer] = useState(60);
  const [txnId, setTxnId] = useState("");
  const [resendAttemptsMap, setResendAttemptsMap] = useState(new Map());
  const [busy, setBusy] = useState(false);

  const resetState = () => {
    setCurrentStep(Steps.ADDRESS_INPUT);
    setOtp(Array(6).fill(""));
    setAddress("");
    setAddAbhaUsing("");
  };

  const props = {
    otp,
    setOtp,
    address,
    setAddress,
    patientMobile,
    busy,
    nextStep: setCurrentStep,
    handleCancel: resetState,
  };

  const handleSubmit = async (isResend) => {
    const { canResend, updatedMap, message } = updateResendAttempts(
      address,
      isResend,
      resendAttemptsMap
    );

    try {
      if (!canResend) {
        handleError(message);
        return;
      }
      setBusy(true);
      let result = await getOtpForAbha(address, otpType);

      if (result.isSuccess) {
        if (otpType === "mobile") {
          setCurrentStep(Steps.VERIFY_MOBILENO);
          const message = result.response.message;
          const match = message.match(/ending (.+)$/);
          if (match) {
            setMobileEnding(match[1]);
          }
        } else if (otpType === "aadhar") {
          setCurrentStep(Steps.VERIFY_AADHAR_MOBILENO);
          const message = result.response.message;
          const match = message.match(/ending (.+)$/);
          if (match) {
            setMobileEnding(match[1]);
          }
        }
        setResendAttemptsMap(updatedMap);
        startTimer(60, setTimer, setIsResendDisabled);
        setTxnId(result.response.txnId);
        setError("");
      } else {
        handleError(result.response.message);
      }
      setBusy(false);
    } catch (error) {
      setBusy(false);
      if (
        error.response?.data?.response?.code == "ABDM-1204" ||
        error.response?.data?.response?.code == "900901" ||
        error.response?.data?.response?.code == "ABDM-9999" ||
        error.response?.data?.response?.code == "ABDM-1211"
      )
        handleError(error.response.data.response.message);
      else handleError("Invalid ABHA Number or ABHA Address");
      console.log(error);
    }
  };

  const handleVerifyOTP = async () => {
    try {
      setBusy(true);
      let response = await verifyAbhaNumberAddressOtp(otp, address, otpType);
      if (response.response.authResult === "success") {
        if (response.response?.tokens?.token) {
          localStorage.setItem(
            "verifyUserToken",
            response.response.tokens.token
          );
        }
        if (response.response?.token) {
          localStorage.setItem("verifyUserToken", response.response.token);
        }

        let profile = await getUserAbhaProfile(address);
        if (profile.isSuccess) {
          setProfileData(profile.response);
        }
        setCurrentStep(Steps.CREATE_ABHA);
      } else {
        setOtp(Array(6).fill(""));
        handleError("Please enter a valid OTP. Entered OTP is either expired or incorrect"||response.response.message || "Failed to verify OTP");
      }
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };
  const AbhaCard = async () => {
    try {
      setBusy(true);
      let result = await fetchAbhaAddressCard(address);
      setAbhaCard(result);
      setCurrentStep(Steps.VIEW_ABHA_CARD);
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError("Failed to fetch ABHA Card");
    }
  };

  const handleAddPatientProfile = async (update) => {
    console.log("in")
    let data;
    try {
      let birthDay;
      if (profileData.dob) {
        birthDay = formatDateToUTC(profileData.dob);
      } else if (profileData.dateOfBirth) {
        birthDay = formatDateToUTC(profileData.dateOfBirth);
      } else if (
        profileData.yearOfBirth &&
        profileData.monthOfBirth &&
        profileData.dayOfBirth
      ) {
        birthDay = new Date(
          Date.UTC(
            parseInt(profileData.yearOfBirth),
            parseInt(profileData.monthOfBirth) - 1, // Month is zero-based
            parseInt(profileData.dayOfBirth)
          )
        );
      } else {
        throw new Error("Insufficient data to calculate birth date.");
      }
      let addresshouse = {
        house: profileData.address,
        city: profileData.districtName,
        district: profileData.districtName,
        state: profileData.stateName,
        pincode: profileData.pinCode || profileData.pincode,
        landmarks: profileData.stateName,
      };
      data = {
        firstName:
          `${profileData.firstName} ${profileData.middleName || ""}`.trim() ||
          profileData.name?.split(" ")[0],

        lastName: profileData.lastName || profileData.name?.split(" ")[1],
        abhaAddress:
          address ||
          profileData?.preferredAbhaAddress ||
          profileData?.phrAddress[0],
        gender: profileData.gender?.toUpperCase() === "M" ? "Male" : "Female",
        birthday: birthDay,
        address: addresshouse,
        age: CalculateAge(birthDay),
        profilePic: `data:image/jpeg;base64,${
          profileData?.photo ||
          profileData.profilePic ||
          profileData.profilePhoto
        }`,
        abhaNumber: profileData.ABHANumber,
        mobile: profileData.mobile || patientMobile,
      };
      setBusy(true);
      let result = await PatientProfileWithAbhaDetails(data, update);
      if (result.success) {
        if (result.isExist && !update) {
          setCurrentStep(Steps.PATIENT_EXIST);
          setPatientData(result.data);
        } else {
          OnSelectPatient(result.data);
          resetState();
        }
      }
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };

  const handleError = (message) => {
    setError(message);
    setTimeout(() => setError(""), 5000);
  };

  const renderStep = () => {
    switch (currentStep) {
      case Steps.ADDRESS_INPUT:
        return (
          <article className=" h-70">
            <div className={`text-sm font-semibold text-dark mb-2 `}>
              Enter ABHA Address/ABHA Number
            </div>
            <Input.text
              className={`${error ? "border-red-600 " : ""}`}
              placeholder="ABHA Address/ABHA Number"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
            />
            {error && <div className="text-red-500 text-xs">{error}</div>}

            <div className="flex gap-5 items-center mb-4 mt-3 p-5">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="otpType"
                  value="mobile"
                  checked={otpType === "mobile"}
                  onChange={() => setOtpType("mobile")}
                />
                <span className="text-md text-dark">Mobile OTP</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="otpType"
                  value="aadhar"
                  checked={otpType === "aadhar"}
                  onChange={() => setOtpType("aadhar")}
                />
                <span className="text-md text-dark">Aadhaar OTP</span>
              </label>
            </div>
            <div className="flex justify-center">
              <Buttons.primary
                onClick={handleSubmit}
                title={busy ? <Spinner show={true} /> : "Submit"}
                classname="mt-8"
              />
            </div>
          </article>
        );
      case Steps.VERIFY_AADHAR_MOBILENO:
        return (
          <div className=" h-100">
            <p>
              We just sent an OTP to the Mobile Number {mobileEnding} linked
              with Aadhar. Enter the OTP below to proceed
            </p>
            <VerifyNumber
              {...props}
              verify={handleVerifyOTP}
              timer={timer}
              showInput={false}
              isResendDisabled={isResendDisabled}
              onResendOtp={() => handleSubmit(true)}
              resendAttemptsCount={resendAttemptsMap.get(address)}
              error={error}
            />
          </div>
        );
      case Steps.VERIFY_MOBILENO:
        return (
          <div className=" h-100">
            <p>
              Please enter the OTP sent to your communication mobile number{" "}
              {mobileEnding}
            </p>

            <OtpInputs
              {...props}
              error={error}
              otp={otp}
              setOtp={setOtp}
              // verify={() => setCurrentStep(Steps.NEW_ADDRESS)}
              onResendOtp={() => handleSubmit(true)}
              timer={timer}
              isResendDisabled={isResendDisabled}
              resendAttemptsCount={resendAttemptsMap.get(address)}
            />

            <button
              onClick={handleVerifyOTP}
              className="mt-7 mx-auto h-10 w-20 flex items-center justify-center text-white bg-Primary rounded-lg"
            >
              {busy ? <Spinner show={true} /> : "Submit"}
            </button>
          </div>
        );
      case Steps.CREATE_ABHA:
        return (
          <CreateAbha
            {...props}
            handleChancel={resetState}
            onComplete={() => {
              handleAddPatientProfile(false);
              setIsModalOpen(false);
            }}
            profileData={profileData}
            onViewAbhaCard={AbhaCard}
          />
        );
      case Steps.VIEW_ABHA_CARD:
        return (
          <ABHACard
            {...props}
            abhaCard={abhaCard}
            onCancel={() => setCurrentStep(Steps.CREATE_ABHA)}
          />
        );
      case Steps.PATIENT_EXIST:
        return (
          <PatientUpdateAbort
            handlePatientAutoFill={(value) => {
              OnSelectPatient(value);
              resetState();
            }}
            address={address}
            handleBack={()=>setCurrentStep(Steps.CREATE_ABHA)}
            onComplete={() => handleAddPatientProfile(true)}
            patientData={patientData}
            profileData={profileData}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      isOpen={addAbhaUsing === "address"}
      setIsOpen={resetState}
      isCard={currentStep == Steps.VIEW_ABHA_CARD}
      classname={`font-primary ${
        currentStep == Steps.VIEW_ABHA_CARD
          ? "w-[400px] "
          : "min-w-[350px]! min-h-96!"
      }`}
    >
      {renderStep()}
    </Modal>
  );
}
