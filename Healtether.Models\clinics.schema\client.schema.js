import mongoose from "mongoose";
import { CLINIC_GROUP_COLLECTION } from "../mongodb.collection.name.js";

const clinicSchema = new mongoose.Schema(
  {
    clinicName: {
      type: String,
      required: true,
      maxLength: 255,
    },
    consultationCharge: {
      type: Number,
    },
    phonepeSetting: {
      merchantId: {
        type: String,
        maxLength: 255,
      },
      saltKey: {
        type: String,
        maxLength: 255,
      },
      saltIndex: {
        type: String,
        maxLength: 1000,
      },
    },
    patientId: {
      prefix: String,
      suffix: String,
    },
    staffId: {
      prefix: String,
      suffix: String,
    },
    googleMeetEmail:{
      type: String,
      maxLength: 255,
    },
    logo: {
      type: String,
      maxLength: 255,
    },
    hfrId:{
      type: String,
      maxLength: 50,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    // adminName: {
    //     firstName: {
    //         type: String,
    //         maxLength: 255
    //     },
    //     lastName: {
    //         type: String,
    //         maxLength: 255
    //     }
    // },
    // adminMobile: {
    //     type: String,
    //     maxLength: 13
    // },
    // adminEmail: {
    //     type: String,
    //     maxLength: 255
    // },
    address: {
      type: String,
    },
    created: {
      on: {
        type: Date,
        default: Date.Now,
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255,
        },
      },
    },
    modified: {
      on: {
        type: Date,
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255,
        },
      },
    },
    isdeleted: {
      type: Boolean,
      default: false,
    },
    groupId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: CLINIC_GROUP_COLLECTION,
    },
    adminUserId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  },
  { versionKey: "1.3" }
);


export { clinicSchema };
