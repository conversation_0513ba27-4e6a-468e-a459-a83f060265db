import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { getPatientWithAllMedicalDetails } from '../../../helpers/patient/patient.helper.js'; 
import { Patient } from '../../../model/clinics.model.js';
import { Appointment } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('getPatientWithAllMedicalDetails function', () => {
  let patientId;
  let appointmentId;

  beforeEach(async () => {
    await Patient.deleteMany({}); 
    await Appointment.deleteMany({}); 
    const patient = new Patient({
      firstName: 'Jane',
      lastName: 'Doe',
      mobile: '**********',
      patientId: 'PAT001',
      age: 30,
      prefix:"Mr.",
      birthday: new Date('1993-01-01'),
      gender: 'Male',
      email: '<EMAIL>',
      address: '123 Main St',
      height: 180,
      weight: 75,
      documentType: 'ID',
      documentNumber: 'ID123456',
      deleted: false,
    });
    
    patientId = (await patient.save())._id;

    const appointment = new Appointment({
        mobile: "**********",
        name: "First Patient",
        gender: "Male",
        age: 33,
      patientId: patientId,
      doctorName: 'Dr. Smith',
      timeSlot: '10:00 AM',
      reason: 'Checkup',
      paymentStatus: true,
      virtualConsultation: false,
      isCanceled: false,
      appointmentDate: new Date(),
      medicalRecords: [{ fileName: 'Record1.pdf', blobName: 'blob1' }],
      procedureRecords: [{ fileName: 'Procedure1.pdf', blobName: 'blob2' }],
      prescriptionRecords: [{ fileName: 'Prescription1.pdf', blobName: 'blob3' }],
    });

    appointmentId = (await appointment.save())._id;

    patient.appointments = [appointmentId];
    await patient.save();
  });

  afterEach(async () => {
    await Patient.deleteMany({});
    await Appointment.deleteMany({}); 
  });

  it('should return patient with all medical details', async () => {
    const result = await getPatientWithAllMedicalDetails(patientId);
    
    expect(result).toHaveProperty('firstName', 'Jane'); 
    expect(result).toHaveProperty('appointments'); 
    expect(result.appointments).toHaveLength(1); 
    expect(result.appointments[0]).toHaveProperty('medicalRecords'); 
    expect(result.appointments[0].medicalRecords).toHaveLength(1); 
  });

  it('should return null when no patient is found', async () => {
    const result = await getPatientWithAllMedicalDetails(new mongoose.Types.ObjectId());
    
    expect(result).toBeNull(); 
  });


});
