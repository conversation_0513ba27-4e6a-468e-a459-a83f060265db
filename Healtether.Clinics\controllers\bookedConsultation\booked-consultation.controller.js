import { sendNotificationViaToken } from "../../config/firebase.admin.js";
import { checkIn, bookedConsultOverview, bookedConsultation, cancelConsultation } from "../../helpers/bookedConsultation/bookedConsultation.helper.js";
import {
  buildNotificationText,
  formatTodayDate,
  resultObject,
} from "../../utils/common.utils.js";

export const bookConsultation = async (req, res) => {
  const data = req.body.data;
  var [status, consultation] = await bookedConsultation(
    data,
    req.user
  );
  if (!status) {
    res.json({ success: false, error: consultation }).status(400);
  }else{
     var message = buildNotificationText(
          "Appointment for",
          consultation?.name,
          " on " + formatTodayDate(consultation?.appointmentDate) + " has been scheduled",
          req?.user
        );
        await sendNotificationViaToken(
          req.Notificationkey,
          message,
          "Appointment",
          true,
          consultation?.clinic,
          req?.user?.id
        );
  }
  var result = resultObject(200, null, true, { id: consultation._id});
  res.json(result).status(200);
};


export const getBookedConsultationOverview = async (req, res) => {
  const data = req.query;
  var overviewData = await bookedConsultOverview(
    data.clinicId,
    data.page,
    data.size,
    data.keyword,
    data.date,
    data.sortby,
    data.direction,
    data.status,
  );
  res.status(200).json(overviewData);
};
export const cancelBookedConsultation=async(req , res)=>{
  const {id}= req.query;
  let result = await cancelConsultation(id);
  if(result){
    var message = buildNotificationText(
      "Appointment for",
      result.name,
      " on " + formatTodayDate(result.appointmentDate) + " has been cencelled",
      req.user
    );
    await sendNotificationViaToken(
      req.Notificationkey,
      message,
      "Appointment",
      true,
      result.clinic,
      req.user.id
    );
  }
  res.status(200).json(result);
}

export const BookedConsultationCheckIn=async(req , res)=>{
  const {id}= req.query;
  let result = await checkIn(id);
  res.status(200).json(result);
}