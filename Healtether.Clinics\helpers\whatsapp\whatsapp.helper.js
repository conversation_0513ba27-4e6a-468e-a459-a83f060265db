import axios from '../../utils/axios.js'
import { LogMessageInCloudWatch } from '../../config/cloudWatchLogger.js';
import { lastPathInUrl } from '../../utils/common.utils.js';
import { getClientSetting } from '../clinic/client.helper.js';
import { format } from "date-fns";
import crypto from "crypto";

export async function sendAppointmentGoogleLinkInWhatsapp(appointment) {
    var clinicDetail = await getClientSetting(appointment?.clinic);
    var data = {
        to: appointment.mobile,
        clinicName: clinicDetail.clinicName,
        patientName: appointment.name,
        doctorName: appointment.doctorName,
        scheduleDate: format(new Date(appointment?.appointmentDate), "dd MMMM, yy"),
        timeSlots: appointment.timeSlot,
        googleMeetLink: lastPathInUrl(appointment?.googleLink?.link)
    }

    await axios
        .post('/message/sendappointmentlink', { data })
        .then((response) => {
            LogMessageInCloudWatch(`Sent whatsapp Message data ${JSON.stringify(data)}`)
        })
        .catch((err) => {
            throw err;
        });
}


export async function sendPhonepeLinkInWhatsapp(clinicDetail, appointment, patient, paymentLink) {

    let link = paymentLink?.split("token=");
    if (link?.length == 2) {
        var data = {
            to: patient?.mobile,
            clinicName: clinicDetail?.clinicName,
            patientName: appointment?.name,
            doctorName: appointment?.doctorName,
            paymentLink: link[1],
            scheduleDate: format(new Date(appointment?.appointmentDate), "dd MMMM, yy"),
            timeSlots: appointment.timeSlot,
        }

        var result = await axios
            .post('/message/sendpaymentlink', { data })
            .then((response) => {
                LogMessageInCloudWatch(`sent data to communication ${JSON.stringify(data)} with link ${paymentLink}`);
                return response;
            })
            .catch((err) => {
                throw err;

            });

        return result;
    }
}

function getTokenFromUrl(url) {
    // Parse the URL
    const parsedUrl = new URL(url);

    // Get the token parameter from the query string
    const token = parsedUrl.searchParams.get('token');

    return token;
}