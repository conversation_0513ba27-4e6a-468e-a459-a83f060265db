
# ABDM API Documentation

This document provides detailed information about the various endpoints available in the ABDM (Ayushman Bharat Digital Mission) system. Each section describes the purpose, headers, request body, and response body for each endpoint.

## Table of Contents

1. [Base URL and X-CM-ID](#base-url-and-x-cm-id)
2. [Gateway Flow](#gateway-flow)
    1. [Auth Token API](#auth-token-api)
    2. [OpenID Configuration API](#openid-configuration-api)
    3. [OAuth Certificate API](#oauth-certificate-api)
    4. [Update Bridge URL API](#update-bridge-url-api)
    5. [Registration of Facility & Software Linkage](#registration-of-facility--software-linkage)
    6. [Find Bridge by Service ID](#find-bridge-by-service-id)
    7. [Find Services by Bridge ID](#find-services-by-bridge-id)
3. [Consent Flow](#consent-flow)
    1. [HIE-CM - Consent Request Init](#hie-cm---consent-request-init)
    2. [HIE-CM - Consent Request Init - Callback](#hie-cm---consent-request-init---callback)
    3. [HIE-CM - Callback API to HIU when a Consent Request is APPROVED/REVOKED/DENIED](#hie-cm---callback-api-to-hiu-when-a-consent-request-is-approvedrevokeddenied)
    4. [HIE-CM - API for HIU to Respond Back to Consent HIU Callback](#hie-cm---api-for-hiu-to-respond-back-to-consent-hiu-callback)
    5. [HIE-CM - Consent Request Status](#hie-cm---consent-request-status)
    6. [HIE-CM - Consent Request On-Status (Callback)](#hie-cm---consent-request-on-status-callback)
    7. [HIE-CM - Consent Request Fetch](#hie-cm---consent-request-fetch)
    8. [HIE-CM - Consent Request On-Fetch (Callback)](#hie-cm---consent-request-on-fetch-callback)
4. [Data Flow](#data-flow)
    1. [Data Flow - Data Request Invoked by HIU](#data-flow---data-request-invoked-by-hiu)
    2. [Data Flow - Callback to HIU](#data-flow---callback-to-hiu)
    3. [Notify](#notify)
5. [Subscription Flow](#subscription-flow)
    1. [Users Get Subscription Requests](#users-get-subscription-requests)
    2. [User Subscription Request Initiate](#user-subscription-request-initiate)
    3. [User Subscription Request Initiate - Callback](#user-subscription-request-initiate---callback)
    4. [Approve Subscription Request](#approve-subscription-request)
    5. [Approve Subscription - Callback](#approve-subscription---callback)
    6. [Subscription Request HIU - On Notify](#subscription-request-hiu---on-notify)
    7. [Deny Subscription Request](#deny-subscription-request)
    8. [Deny Subscription - Callback](#deny-subscription---callback)
    9. [Edit Subscription](#edit-subscription)
    10. [Edit Subscription - Callback](#edit-subscription---callback)
    11. [Subscription HIU - Notify](#subscription-hiu---notify)
    12. [Subscription HIU - On-Notify](#subscription-hiu---on-notify)
6. [API Listing](#api-listing)
7. [Error Codes Listing](#error-codes-listing)

## Base URL and X-CM-ID

| Environment | Base URL | X-CM-ID |
| --- | --- | --- |
| Sandbox | https://dev.abdm.gov.in | sbx |
| Production | https://apis.abdm.gov.in | abdm |

## Gateway Flow

### Auth Token API

**Purpose:** This API is used to generate an auth token.

**URL:** `/api/hiecm/gateway/v3/sessions`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | Sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "clientId": "SBX_XXXXXX",
    "clientSecret": "XXXX-XXX-XXXX-XXXX-XXXXXXX",
    "grantType": "client_credentials"
}
```

**Response:**

```json
{
    "accessToken": "eyJhbGciOiJSUzI1NilsInR5cCIgOiAiSldUliwia2lkIiA6ICJBbFJINVdDbThUbTIFSl9JZk85ejA2ajlvQ3Y1MXBLS 0ZrbkdiX1RCdkswIn0...",
    "expiresIn": 1200,
    "refreshExpiresIn": 1800,
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUliwia2lkIiA6ICIyMWU5NzA4OS00ZTcxLTQyNGEtOTAzYS1jOTAyMW M1NmFINWYifQ...",
    "tokenType": "bearer"
}
```

### OpenID Configuration API

**Purpose:** Provides configuration information about the Identity Provider (IDP).

**URL:** `/api/hiecm/gateway/v3/.well-known/openid-configuration`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | Sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Response:**

```json
{
    "jwks_uri": "https://dev.abdm.gov.in/api/hiecm/gateway/v3/certs"
}
```

### OAuth Certificate API

**Purpose:** Provides an OAuth certificate that can be used to validate which received gateway session token in Header on the call back.

**URL:** `/api/hiecm/gateway/v3/certs`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Response:**

```json
{
    "keys": [
        {
            "e": "AQAB",
            "kid": "AlRb5WCm8Tm9EJ_IfO9z06j9oCv51pKKFknGb_TBvK0",
            "kty": "RSA",
            "n": "mgmW7W5ZGF_G5cJevwYi8HiPcl-6qS_psnZxa4v3bkwAkyOoOd8-6ketrOIZA2PbRbGnxFfZHiI94rdFXJ4Q9ampscsz9NocTIPMPmWydJ8A50pZaYWyikYDSJiDltq7i3WspPKSOuQHrC Sh9dMcCVveXSoeg0tO68Z79gwDlpcxiqDbFaphsqDvx-5XkfwiqvOBaybK6_8CBPuTqWMUEuUkILYXu2X7ESHdVNFMFAjxCcCXUtP7LFdvT3nnFekRmG82QbSQSVe 4N5tPH8q0MCxSWWn2c15bDnzOF-dvfRCVPRabCzw0M-utHR9diTrWtq6Koi5buxgwM1rbk0p8Q",
            "use": "sig",
            "x5c": [
                "MIICrzCCAZcC8gFy/3WZBjANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDDBBjZW50cmFsLXJlZ2lzdHJ5MB4XDTIwMDYyOTASNDEzNloXDTMwMDYyOTASNDMxNlowGzEZMBcGA1UEAwwQY2VudHJhbC1yZWdpc3RyeTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJoJlu1uWRhfxuXCXr8GIvB4j3CPuqkv6bJ2cWuL925MAJMjqDnfPupHraziPmQNj20Wxp8RX2R4iPeK3RVyeEPWpqbHLM/TaHEyDzD5lsnSfAOdKWWmFsopGA0iYg5bau4t1rKTykjrkB6wuYfXTHAlb3I+aHoNLTuvGe/YMA5aXMYqg2xWqYbKg78fuV5H8IqrzgWsmyuvwQgT7k6ljFBLUJS2F7tl+xEh3VTRTBQI8QnAl1LT+yxXb0955xXpEZhvNkG0kEIXuDebTx/KtDAsUllp9nNeWw58zhfnb30QIT0Wmws8NDPrrR0fXYk61rauiqIuW7sYMDNa25NKfECAwEAATANBgkqhkiG9w0BAQsFAAOCAQEACkC3TijrXlgi4vn+l1uL1nfdK6vOIL5UZ6yCjSOq7zYW6b3Qe8j7NrPb9RJC+pbIERyNbB+t9hsa5g1L7lkjCNlUuxfJprsJ9LJKlM5g7dYEA6XPCJ7C6AVlarj72vlWXQvwjnQMO2/CM9/Jp5Hnv2Qwjn7NME2OWM0iblc/TD+DEZK5L5mIWMyuBSQo2o/AcOmfG4MoE5Gm/CaOJ47rSrf+Iq83e5+dyKh7uLVAa+5WK8Im5nEs6BLSGyo2KlaV0mW9yCkoRLLbipjH8+rJwkUU6iu7QVjz0peGZzYldya5n35gMWH7Bu4HqFneKNRwwD6w8rGNC+uWtgWejDZ3yQ=="
            ],
            "x5t": "EaMhYGUIvMkp8tvSM3QoaqaF8xM",
            "x5t2": "vGer6Pt8AhZn8RIbHhAFksOCcGf3u1UWU7Qq-Doy7ro",
            "alg": "RS256"
        }
    ]
}
```

### Update Bridge URL API

**Purpose:** This API is used to update the bridge base URL.

**URL:** `/api/hiecm/gateway/v3/bridge/url`

**Method:** PATCH

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "url": "https://webhook.site/b799c0b8-4e75-4545-8eb2-d8c2d0f0c9f6"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Registration of Facility & Software Linkage

**Purpose:** This API is used to link multiple bridges against a facility.

**URL:** `https://facilitysbx.abdm.gov.in/v1/bridges/MutipleHRPAddUpdateServices`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "facilityId": "INXXXXXXXXXX",
    "facilityName": "Facility Name",
    "bridgeId": "SBX_XXXXXX",
    "hipName": "Hospital Name",
    "type": "HIP",
    "active": true
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Find Bridge by Service ID

**Purpose:** This API fetches the bridge details for the given service ID.

**URL:** `/api/hiecm/gateway/v3/bridge-service/serviceld/{serviceld}`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Response:**

```json
{
    "bridge": {
        "id": "SBX_XXXX",
        "name": "Testing",
        "url": "https://abdcb.doctor9.com",
        "active": true,
        "blocklisted": false
    }
}
```

### Find Services by Bridge ID

**Purpose:** This API fetches all the service ID details linked with the respective bridge ID.

**URL:** `/api/hiecm/gateway/v3/bridge-services`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Response:**

```json
{
    "bridge": {
        "id": "SBX_XXXX",
        "name": "Testing",
        "url": "https://abdcb.doctor9.com",
        "active": true,
        "blocklisted": false
    },
    "services": [
        {
            "id": "@#$%^&*{",
            "name": "hello",
            "types": [
                "HIP",
                "HIU"
            ],
            "endpoints": {
                "hipEndpoints": [
                    {
                        "use": "registration",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/registration"
                    },
                    {
                        "use": "data-upload",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/dataupload"
                    }
                ],
                "hiuEndpoints": [
                    {
                        "use": "registration",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/registration"
                    },
                    {
                        "use": "data-upload",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/dataupload"
                    }
                ],
                "healthLockerEndpoints": [
                    {
                        "use": "registration",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/registration"
                    },
                    {
                        "use": "data-upload",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/dataupload"
                    }
                ]
            },
            "active": true
        }
    ]
}
```

## Consent Flow

### HIE-CM - Consent Request Init

**Purpose:** This API is used to initiate a consent request to get data about a patient.

**URL:** `/api/hiecm/consent/v3/request/init`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "consent": {
        "hip": {
            "id": "HIP_ID"
        },
        "hiu": {
            "id": "HIU_ID"
        },
        "hiTypes": [
            "Prescription",
            "DiagnosticReport",
            "DischargeSummary",
            "ImmunizationRecord",
            "HealthDocumentRecord",
            "WellnessRecord",
            "OPConsultation"
        ],
        "patient": {
            "id": "abhaaddress@sbx"
        },
        "purpose": {
            "code": "CAREMGT",
            "text": "Care Management",
            "refUri": "www.abdm.gov.in"
        },
        "requester": {
            "name": "Dr. Manju",
            "identifier": {
                "type": "REGNO",
                "value": "MH1001",
                "system": "https://www.mciindia.org"
            }
        },
        "permission": {
            "dateRange": {
                "to": "2024-07-17T12:05:57.151Z",
                "from": "1924-07-09T12:05:57.151Z"
            },
            "frequency": {
                "unit": "DAY",
                "value": 0,
                "repeats": 0
            },
            "accessMode": "VIEW",
            "dataEraseAt": "2124-11-09T00:00:00.000Z"
        },
        "careContexts": [
            {
                "patientReference": "xxxx@sbx",
                "careContextReference": "COCa496bc2f-ca6c-4af5-b973-02e915fd9815"
            }
        ]
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM - Consent Request Init - Callback

**Purpose:** This API is initiated by HIE-CM to get the consent request callback to HIU.

**URL:** `{callback}/api/v3/hiu/consent/request/on-init`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| X-HIU-ID | HIU_ID | Yes | Identifier of the health information user by which the request was initiated. |

**Request Body:**

```json
{
    "consentRequest": {
        "id": "05f14b1d-4465-453a-8249-1382d79d271d"
    },
    "error": null,
    "response": {
        "requestId": "4213ebf8-5f8a-45e4-a014-7a2eb875f213"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM - Callback API to HIU when a Consent Request is APPROVED/REVOKED/DENIED

**Purpose:** Once the patient grants consent to the HIU, the CM notifies the HIU system of the consent grant via the gateway. If the patient grants for multiple HIPs, then multiple consent artefacts are generated - one for each HIP. The HIU now first fetches all the consent-artefacts that were generated for his request.

**URL:** `{callback}/api/v3/hiu/consent/request/notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | eyJhbGciOiJSUzUxMiJ9... | Yes | Identifier to the health information user. |

**Request Body:**

```json
{
    "notification": {
        "consentRequestId": "e3c74829-3f82-4f94-959e-e10f57bcd57b",
        "status": "GRANTED",
        "reason": null,
        "consentArtefacts": [
            {
                "id": "<consent-artefact-id>"
            }
        ]
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM - API for HIU to Respond Back to Consent HIU Callback

**Purpose:** This API is used by HIU to respond back to HIE-CM when they received notify call after approve/deny/revoke.

**URL:** `/api/hiecm/consent/v3/request/hiu/on-notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "acknowledgement": {
        "status": "OK",
        "consentId": "e3c74829-3f82-4f94-959e-e10f57bcd57b"
    },
    "error": {
        "code": "ABDM-1001",
        "message": "unable to connect database"
    },
    "response": {
        "requestId": "6f0b4665-a915-4c92-aa36-65afb4a2cd71"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM - Consent Request Status

**Purpose:** This API is used to get the status of the consent request.

**URL:** `/api/hiecm/consent/v3/request/status`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| X-HIU-ID | HIU_ID | Yes | Identifier of the health information user to which the request was intended. |

**Request Body:**

```json
{
    "consentRequestId": "05f14b1d-4465-453a-8249-1382d79d271d"
}
```

**Response:**

```json
{
    "status": "200 OK"
}
```

### HIE-CM - Consent Request On-Status (Callback)

**Purpose:** This API is used to send the status of consent request back to HIU through HIE-CM.

**URL:** `{callback_url}/api/v3/hiu/consent/request/on-status`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Identifier of the health information user to which the request was intended. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "consentRequest": {
        "id": "7d52fcd0-a52a-4d82-b9f5-a548e5053088",
        "status": "REQUESTED"
    },
    "error": null,
    "response": {
        "requestId": "e1f08798-8949-4a23-a04e-fe0054397cf5"
    },
    "resp": null
}
```

**Response:**

```json
{
    "status": "200 OK"
}
```

### HIE-CM - Consent Request Fetch

**Purpose:** This API is used to fetch the consent artifact details.

**URL:** `/api/hiecm/consent/v3/fetch`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "consentId": "d6a83f24-6c96-421e-b8b8-844e5344ef69"
}
```

**Response:**

```json
{
    "status": "200 OK"
}
```

### HIE-CM - Consent Request On-Fetch (Callback)

**Purpose:** This API is used to send the consent artifact details to HIU through HIE-CM.

**URL:** `{callback_url}/api/v3/hiu/consent/on-fetch`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "consent": {
        "status": "GRANTED",
        "consentDetail": {
            "consentId": "d6a83f24-6c96-421e-b8b8-844e5344ef69",
            "hip": {
                "id": "HIP_ID"
            },
            "hiu": {
                "id": "HIU_ID"
            },
            "hiTypes": [
                "Prescription",
                "DiagnosticReport",
                "DischargeSummary",
                "ImmunizationRecord",
                "HealthDocumentRecord",
                "WellnessRecord",
                "OPConsultation"
            ],
            "patient": {
                "id": "xxxxxx@sbx"
            },
            "purpose": {
                "code": "CAREMGT",
                "text": "Care Management",
                "refUri": "www.test.com"
            },
            "requester": {
                "name": "Dr. Manju",
                "identifier": {
                    "type": "REGNO1",
                    "value": "MH1001",
                    "system": "https://www.mciindia.org"
                }
            },
            "permission": {
                "dateRange": {
                    "from": "2023-05-09T08:58:09.738Z",
                    "to": "2023-05-10T08:58:09.738Z"
                },
                "dataEraseAt": "2023-05-25T08:58:09.738Z",
                "frequency": {
                    "unit": "HOUR",
                    "value": 0,
                    "repeats": 0
                },
                "accessMode": "VIEW"
            },
            "careContexts": [
                {
                    "patientReference": "xxxx@sbx",
                    "careContextReference": "Episode11"
                }
            ],
            "createdAt": "2023-05-25T08:58:09.738Z",
            "lastUpdated": "2023-05-25T08:58:09.738Z",
            "schemaVersion": "v3",
            "signature": "bAJUnf7nY6Yn6A7JbR1ZFHtBmqCjXDWZaQteF+XNgEImUchTgA4qp4i5KnUBXYsWuTKBeUSf1cLFMUXGpQuD9OZzrMqA1PRnEWyh01V9i1bsEm5VMBkeZa0ghQBc4Fj8g=="
        },
        "response": {
            "requestId": "36de611a-c3ab-4794-b803-5eff9c94ddbf"
        }
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

## Data Flow

### Data Flow - Data Request Invoked by HIU

**Purpose:** This API is used to invoke a data request by HIU.

**URL:** `/api/hiecm/dataflow/v3/request`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "dataRequest": {
        "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "dateRange": {
            "from": "2023-05-09T08:58:09.738Z",
            "to": "2023-05-10T08:58:09.738Z"
        },
        "dataPushUrl": "https://data-push-url.com",
        "hiTypes": [
            "Prescription",
            "DiagnosticReport",
            "DischargeSummary",
            "ImmunizationRecord",
            "HealthDocumentRecord",
            "WellnessRecord",
            "OPConsultation"
        ],
        "patient": {
            "id": "xxxxxx@sbx"
        },
        "hip": {
            "id": "HIP_ID"
        },
        "consent": {
            "id": "consent-id"
        }
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Data Flow - Callback to HIU

**Purpose:** This API is used to send a callback to HIU with the data request response.

**URL:** `{callback_url}/api/v3/hiu/data/request/on-data`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "dataRequest": {
        "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "SUCCESS",
        "hip": {
            "id": "HIP_ID"
        },
        "entries": [
            {
                "content": "data-content",
                "mediaType": "application/json"
            }
        ]
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Notify

**Purpose:** This API is used to notify about the data request.

**URL:** `{callback_url}/api/v3/data/notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIP-ID | HIP_ID | Yes | Health information provider unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "notification": {
        "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "SUCCESS",
        "hip": {
            "id": "HIP_ID"
        }
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

## Subscription Flow

### Users Get Subscription Requests

**Purpose:** This API is used to get subscription requests for users.

**URL:** `/api/hiecm/subscription/v3/requests`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "subscriptionRequestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4"
}
```

**Response:**

```json
{
    "status": "200 OK"
}
```

### User Subscription Request Initiate

**Purpose:** This API is used to initiate a subscription request.

**URL:** `/api/hiecm/subscription/v3/request/init`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "subscriptionRequest": {
        "id": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "patient": {
            "id": "xxxxxx@sbx"
        },
        "hip": {
            "id": "HIP_ID"
        },
        "hiu": {
            "id": "HIU_ID"
        },
        "hiTypes": [
            "Prescription",
            "DiagnosticReport",
            "DischargeSummary",
            "ImmunizationRecord",
            "HealthDocumentRecord",
            "WellnessRecord",
            "OPConsultation"
        ],
        "purpose": {
            "code": "CAREMGT",
            "text": "Care Management",
            "refUri": "www.abdm.gov.in"
        },
        "requester": {
            "name": "Dr. Manju",
            "identifier": {
                "type": "REGNO",
                "value": "MH1001",
                "system": "https://www.mciindia.org"
            }
        },
        "permission": {
            "dateRange": {
                "from": "2023-05-09T08:58:09.738Z",
                "to": "2023-05-10T08:58:09.738Z"
            },
            "frequency": {
                "unit": "DAY",
                "value": 0,
                "repeats": 0
            },
            "accessMode": "VIEW",
            "dataEraseAt": "2124-11-09T00:00:00.000Z"
        }
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### User Subscription Request Initiate - Callback

**Purpose:** This API is used to send a callback for the subscription request initiation.

**URL:** `{callback_url}/api/v3/hiu/subscription/request/on-init`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "subscriptionRequest": {
        "id": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "REQUESTED"
    },
    "error": null,
    "response": {
        "requestId": "e1f08798-8949-4a23-a04e-fe0054397cf5"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Approve Subscription Request

**Purpose:** This API is used to approve a subscription request.

**URL:** `/api/hiecm/subscription/v3/request/approve`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "subscriptionRequestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Approve Subscription - Callback

**Purpose:** This API is used to send a callback for the approved subscription request.

**URL:** `{callback_url}/api/v3/hiu/subscription/request/on-approve`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "subscriptionRequest": {
        "id": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "APPROVED"
    },
    "error": null,
    "response": {
        "requestId": "e1f08798-8949-4a23-a04e-fe0054397cf5"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Subscription Request HIU - On Notify

**Purpose:** This API is used to notify HIU about the subscription request.

**URL:** `{callback_url}/api/v3/hiu/subscription/notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "subscriptionRequest": {
        "id": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "APPROVED"
    },
    "error": null,
    "response": {
        "requestId": "e1f08798-8949-4a23-a04e-fe0054397cf5"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Deny Subscription Request

**Purpose:** This API is used to deny a subscription request.

**URL:** `/api/hiecm/subscription/v3/request/deny`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "subscriptionRequestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Deny Subscription - Callback

**Purpose:** This API is used to send a callback for the denied subscription request.

**URL:** `{callback_url}/api/v3/hiu/subscription/request/on-deny`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "subscriptionRequest": {
        "id": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "DENIED"
    },
    "error": null,
    "response": {
        "requestId": "e1f08798-8949-4a23-a04e-fe0054397cf5"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Edit Subscription

**Purpose:** This API is used to edit a subscription.

**URL:** `/api/hiecm/subscription/v3/edit`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| Authorization | Gateway Session Token | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "subscriptionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "hiTypes": [
        "Prescription",
        "DiagnosticReport",
        "DischargeSummary",
        "ImmunizationRecord",
        "HealthDocumentRecord",
        "WellnessRecord",
        "OPConsultation"
    ],
    "purpose": {
        "code": "CAREMGT",
        "text": "Care Management",
        "refUri": "www.abdm.gov.in"
    },
    "permission": {
        "dateRange": {
            "from": "2023-05-09T08:58:09.738Z",
            "to": "2023-05-10T08:58:09.738Z"
        },
        "frequency": {
            "unit": "DAY",
            "value": 0,
            "repeats": 0
        },
        "accessMode": "VIEW",
        "dataEraseAt": "2124-11-09T00:00:00.000Z"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Edit Subscription - Callback

**Purpose:** This API is used to send a callback for the edited subscription.

**URL:** `{callback_url}/api/v3/hiu/subscription/on-edit`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "subscription": {
        "id": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "EDITED"
    },
    "error": null,
    "response": {
        "requestId": "e1f08798-8949-4a23-a04e-fe0054397cf5"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Subscription HIU - Notify

**Purpose:** This API is used to notify HIU about the subscription.

**URL:** `{callback_url}/api/v3/hiu/subscription/notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "subscription": {
        "id": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "NOTIFIED"
    },
    "error": null,
    "response": {
        "requestId": "e1f08798-8949-4a23-a04e-fe0054397cf5"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Subscription HIU - On-Notify

**Purpose:** This API is used to send a callback for the subscription notification.

**URL:** `{callback_url}/api/v3/hiu/subscription/on-notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-HIU-ID | HIU_ID | Yes | Health information user unique ID. |
| Authorization | Gateway Session Token | Yes | ABDM Gateway Session Token. |

**Request Body:**

```json
{
    "subscription": {
        "id": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
        "status": "NOTIFIED"
    },
    "error": null,
    "response": {
        "requestId": "e1f08798-8949-4a23-a04e-fe0054397cf5"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

## API Listing

This section lists all the APIs available in the ABDM system. The detailed documentation for each API can be found in the respective sections above.

## Error Codes Listing

This section lists all the error codes that can be encountered while using the ABDM APIs. Each error code is associated with a specific error scenario.

| Error Code | Description |
| --- | --- |
| ABDM-1030 | Invalid request ID |
| ABDM-1016 | Invalid Timestamp |
| ABDM-1064 | Request body was missing |
| ABDM-1092 | Duplicate Link token request |
| ABDM-9999 | Invalid ABHA Number, it must be only 14 digit |
| ABDM-9999 | Invalid ABHA Address, it must start with Alphanumeric . and _ in the middle and must be ending with @abdm or @sbx |
| ABDM-1207 | Demographic details was invalid or doesn't exists |
| ABDM-9999 | Invalid Gender, It must be M, F, O, D |
| ABDM-9999 | Invalid Year of birth, must be 4 digit range between 1900 and 2200 |
| ABDM-1063 | HIP Id mismatch with Link token |
| ABDM-1038 | ABHA address mismatch with Link token |
| ABDM-1062 | ABHA number mismatch with Link token |
| ABDM-9999 | careContexts attribute required in the payload |
| ABDM-9999 | Invalid HiType, it must be in PRESCRIPTION, DIAGNOSTIC REPORT, OPCONSULTATION, DISCHARGE SUMMARY, IMMUNIZATIONRECORD, HEALTHDOCUMENTRECORD, WELLNESSRECORD |
| ABDM-1037 | Count and Care context count mismatch |
| ABDM-1066 | Invalid JWT token |
| ABDM-1035 | Invalid HIP ID |
| ABDM-1024 | Dependent service unavailable |
| ABDM-1115 | Invalid patient information. At least one patient information is required. |
| ABDM-1031 | The abha address is deactivated. |
