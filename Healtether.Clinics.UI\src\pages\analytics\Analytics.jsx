import { useEffect, useRef, useState } from "react";
import {
  getGenderRatio,
  getPatientAgeGroup,
  getPatientAppointmentAnalyasis,
} from "../../services/analyatics/analyatics";
import DefaultTextboxClass from "../../utils/Classes";

import dayjs from "dayjs";

import moment from "moment";
import PatientAnalytics from "./PatientAnalytics";
import PaymentAnalytics from "./PaymentAnalytics";
import AppointmentAnalytics from "./AppointmentAnalytics";

const Analytics = () => {
  const [patientAnalysisType, setPatientAnalysisType] = useState("monthly");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

 



  // Appointment Booking Analysis
  const appointmentBooking = {
    labels: [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ],
    datasets: [
      {
        label: "By Whatsapp Assistance",
        backgroundColor: "#44B092",
        fill: false,
        lineTension: 0.5,
        borderColor: "#44B092",
        borderWidth: 2,
        data: [25, 27, 35, 37, 40, 45, 50, 73, 48, 45, 43, 44],
      },
      {
        label: "In The Hospitals",
        backgroundColor: "#85F8D5",
        data: [30, 34, 37, 52, 55, 63, 67, 70, 73, 76, 80, 85],
        fill: false,
        lineTension: 0.5,
        borderColor: "#85F8D5",
        borderWidth: 2,
      },
    ],
  };

  const appointmentBookingOption = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: true,
        text: "Appointment Booking Analysis",
        align: "start",
        padding: { top: 10 },
        color: "black",
        font: { size: "18px", weight: "400" },
      },
    },
    scales: {
      y: {
        grid: {
          drawOnChartArea: false,
        },
        min: 0,
        max: 100,
        ticks: {
          stepSize: 25,
        },
      },
    },
  };

  // Appointment Analysis
  const appointmentAnalysis = {
    labels: ["Completed", "Cancelled", "Reschedule"],
    datasets: [
      {
        backgroundColor: ["#85F8D5", "#44B092", "#205C4C"],
        fill: false,
        lineTension: 0.5,
        borderRadius: 5,
        borderSkipped: false,
        data: [58, 42, 10],
        datalabels: {
          color: "white",
          align: "left", // Center align the data labels
          font: { size: "18px", weight: "400" },
          formatter: (value, context) => {
            // Add percentage sign to data labels
            return value + "%" + "  ";
          },
          labels: {
            title: {
              font: {
                weight: "bold",
              },
            },
          },
        },
      },
    ],
  };

  const appointmentAnalysisOption = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: "Appointment Analysis",
        align: "start",
        font: { size: "18px", weight: "500" },
        padding: { top: 10, bottom: 20 },
        color: "black",
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          padding: 30,
          // display:false
          font: {
            size: 17,
          },
          color: "black",
        },
        border: {
          display: false,
        },
      },
      y: {
        grid: {
          display: false,
        },
        suggestedMin: 0,
        ticks: {
          display: false,
        },
        border: {
          display: false,
        },
      },
    },
  };

  // Mode of Payment
  const paymentMode = {
    labels: ["Cash", "UPI", "Card"],
    datasets: [
      {
        backgroundColor: ["#85F8D5", "#44B092", "#205C4C"],
        fill: false,
        lineTension: 0.5,
        borderRadius: 5,
        borderSkipped: false,
        data: [10, 60, 30],
        categoryPercentage: 1.0,
        datalabels: {
          color: "white",
          align: "left", // Center align the data labels
          font: { size: "18px", weight: "400" },
          formatter: (value, context) => {
            // Add percentage sign to data labels
            return value + "%" + " ";
          },
          labels: {
            title: {
              font: {
                weight: "bold",
              },
            },
          },
        },
      },
    ],
  };

  const paymentModeOption = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: "Mode Of Payments",
        align: "start",
        font: { size: "18px", weight: "400" },
        padding: { bottom: 50, top: 20 },
        color: "black",
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          padding: 30,
          // display:false
          font: {
            size: 17,
          },
          color: "black",
        },
        border: {
          display: false,
        },
      },
      y: {
        grid: {
          display: false,
        },
        suggestedMin: 0,
        ticks: {
          display: false,
        },
        border: {
          display: false,
        },
      },
    },
  };

  // Age Group
  const paymentAnalysis = {
    labels: ["Consultation Fee", "Procedure Fee"],
    datasets: [
      {
        backgroundColor: ["#44B092", "#205C4C"],
        fill: false,
        lineTension: 0.5,
        borderRadius: 5,
        borderSkipped: false,
        data: [90, 10],
        categoryPercentage: 1.0,
        axis: "y",
        datalabels: {
          color: "white",
          labels: {
            title: {
              font: {
                size: "20px",
                weight: "bold",
              },
            },
          },
        },
      },
    ],
  };

  const paymentAnalysisOption = {
    indexAxis: "y",
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: "Payments analysis",
        align: "start",
        font: { size: "20px", weight: "400" },
        padding: { bottom: 50, top: 20 },
        color: "black",
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          // padding:10,
          display: false,
        },
        border: {
          display: false,
        },
      },
      y: {
        grid: {
          display: false,
        },
        suggestedMin: 0,
        ticks: {
          font: {
            size: 15,
          },
          color: "black",
        },
        border: {
          display: false,
        },
      },
    },
  };
  const [analysis, setAnalysis] = useState(1);
  const handleDateChange = (e) => {
    const selectedDate = e.target.value;
    if (selectedDate < endDate) {
      setStartDate(selectedDate);
    } else {
      setEndDate(selectedDate);
    }
  };

  const dateInputRef = useRef(null);
  const handleButtonClick = () => {
    dateInputRef.current.showPicker();
    setPatientAnalysisType("custom");
  };

  const getPatientAppointmentAnalysis = async () => {
          const data = await getPatientAppointmentAnalyasis(
            patientAnalysisType,
            startDate,
            endDate
          );
          const result = data.data;
      
          if (result.length > 0) {
            if (patientAnalysisType == "weekly") {
              setPatientAnalysisLabel(data.data.map((item) => item.weekday));
            } else if (patientAnalysisType == "monthly") {
              setPatientAnalysisLabel(data.data.map((item) => item.month));
            } else if (patientAnalysisType == "custom") {
              setPatientAnalysisLabel(
                data.data.map((item) => moment(item.date).format("MMM DD"))
              );
            }
            setNewPatient(data.data.map((item) => item.newPatients));
            setRepeatedPatient(data.data.map((item) => item.repeatedPatients));
          } else {
            console.error("no filter data available");
          }
        };

  useEffect(() => {
    getPatientAppointmentAnalysis();
  }, [startDate, endDate, patientAnalysisType]);


  return (
    <div className="">

        <nav className="tabs overflow-x-auto space-x-3 p-1 font-primary" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
          <button type="button" className="btn btn-text !border !border-secondary/20 active-tab:bg-primary active-tab:text-white hover:text-primary active hover:bg-primary/20" id="tabs-pill-icon-item-1" data-tab="#tabs-pill-icon-1" aria-controls="tabs-pill-icon-1" role="tab" aria-selected="false">
            <span className="icon-[tabler--user] size-5 shrink-0"></span>
            <span className="hidden sm:inline">Patient Analysis</span>
          </button>
          <button type="button" className="btn btn-text !border !border-secondary/20 active-tab:bg-primary active-tab:text-white hover:text-primary hover:bg-primary/20" id="tabs-pill-icon-item-2" data-tab="#tabs-pill-icon-2" aria-controls="tabs-pill-icon-2" role="tab" aria-selected="false">
            <span className="icon-[tabler--currency-rupee] size-5 shrink-0"></span>
            <span className="hidden sm:inline">Payment Analysis</span>
          </button>
          <button type="button" className="btn btn-text !border !border-secondary/20 active-tab:bg-primary active-tab:text-white hover:text-primary hover:bg-primary/20" id="tabs-pill-icon-item-3" data-tab="#tabs-pill-icon-3" aria-controls="tabs-pill-icon-3" role="tab" aria-selected="false">
            <span className="icon-[tabler--message] size-5 shrink-0"></span>
            <span className="hidden sm:inline">Appointment Analysis</span>
          </button>
        </nav>

            <div className="mt-3">
              <div id="tabs-pill-icon-1" role="tabpanel" aria-labelledby="tabs-pill-icon-item-1">
                <PatientAnalytics/>
              </div>
              <div id="tabs-pill-icon-2" className="hidden" role="tabpanel" aria-labelledby="tabs-pill-icon-item-2">
                <PaymentAnalytics/>
              </div>
              <div id="tabs-pill-icon-3" className="hidden" role="tabpanel" aria-labelledby="tabs-pill-icon-item-3">
                <AppointmentAnalytics/>
              </div>
            </div>

                  {/* <div className="flex h-10 justify-between items-center text-sm lg:text-base">
          <h1 className="text-md text-gray-600">Patient&apos;s Analysis</h1>
          <div className="flex">
            <button
              className={`p-2.5 mx-2  rounded-lg mr-1 ${
                patientAnalysisType != "weekly"
                  ? "bg-[#F9F4FE]"
                  : "text-white bg-[#32856E]"
              }  shadow-[0px_4px_12px_0px_rgba(0,0,0,0.1)]`}
              onClick={() => setPatientAnalysisType("weekly")}
            >
              Weekly
            </button>
            <button
              className={`p-2.5 mx-2  rounded-lg mr-1 ${
                patientAnalysisType != "monthly"
                  ? "bg-[#F9F4FE]"
                  : "text-white bg-[#32856E]"
              } shadow-[0px_4px_12px_0px_rgba(0,0,0,0.1)]`}
              onClick={() => setPatientAnalysisType("monthly")}
            >
              Monthly
            </button>
            <div className="relative">
              <button
                type="button"
                className={`p-2.5 mx-2 rounded-lg mr-1 ${
                  patientAnalysisType !== "custom"
                    ? "bg-[#F9F4FE]"
                    : "text-white bg-[#32856E]"
                } shadow-[0px_4px_12px_0px_rgba(0,0,0,0.1)]`}
                onClick={() => handleButtonClick()}
              >
                Custom
              </button>

              <input
                type="date"
                name="dateRange"
                ref={dateInputRef}
                onChange={(e) => handleDateChange(e)}
                min="2024-01-01"
                placeholder="Select Date Range"
                autoComplete="off"
                style={{  colorScheme: "green" }}
                className={
                  DefaultTextboxClass +
                  " w-full leading-8 disabled:opacity-75 absolute left-[20px] bg-[#fff]  focus:ring-2 focus:ring-[#32856E] text-[#32856E] disabled:cursor-not-allowed collapse"
                }
              />
            </div>
        </div>
        */}

          

      </div>
      );
};
      export default Analytics;
