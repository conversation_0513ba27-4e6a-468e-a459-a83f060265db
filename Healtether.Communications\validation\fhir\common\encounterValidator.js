const encounterValidator = {
  encounter: {
    in: ["body"],
    exists: { errorMessage: "encounter is required" },
    isObject: true,
  },
  "encounter.status": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    isIn: {
      options: [
        [
          "planned",
          "arrived",
          "triaged",
          "in-progress",
          "onleave",
          "finished",
          "cancelled",
        ],
      ],
    },
    errorMessage:
      "status must be one of: planned | arrived | triaged | in-progress | onleave | finished | cancelled",
  },
  "encounter.startTime": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "startTime must be a valid ISO 8601 date string",
  },
  "encounter.endTime": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "endTime must be a valid ISO 8601 date string",
  },
};

export default encounterValidator;
