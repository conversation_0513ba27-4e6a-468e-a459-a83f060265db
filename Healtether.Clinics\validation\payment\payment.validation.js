import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateGetPaymentOverview = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
    page: {
      trim: true,
      escape: true,
      in: ["query"],
      isInt: {
        options: { min: 1 },
        errorMessage: "Page number must be a positive integer",
      },
    },
    size: {
      trim: true,
      escape: true,
      in: ["query"],
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
    },
    keyword: {
      trim: true,
      escape: true,
      in: ["query"],
      optional: true,
      isString: true,
      errorMessage: "Keyword must be a string",
    },
    sortby: {
      trim: true,
      escape: true,
      in: ["query"],
      optional: true,
      isString: true,
      errorMessage: "Sort by field must be a string",
    },
    direction: {
      in: ["query"],
      optional: true,
      isIn: {
        options: [["asc", "desc"]],
        errorMessage: 'Sort direction must be either "asc" or "desc"',
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateAddInvoice = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "ID must be a valid ObjectId",
      },
    },
    data: {
      in: ["body"],
      isObject: true,
      errorMessage: "Data must be an object",
      custom: {
        options: (value) => {
          // You can add additional checks for the shape of `data` if necessary
          return value != null && typeof value === "object";
        },
        errorMessage: "Data must be a valid object",
      },
    },
    "data.treatments.*.treatment": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Treatment must be a string",
    },
    "data.treatments.*.quantity": {
      trim: true,
      escape: true,
      in: ["body"],
      isInt: {
        options: { min: 1 },
        errorMessage: "Treatment Quantity must be a positive integer",
      },
    },
    "data.treatments.*.amount": {
      trim: true,
      escape: true,
      in: ["body"],
      isDecimal: true,
      errorMessage: "Treatment Amount must be a decimal number",
    },
    "data.treatments.*.discRate": {
      trim: true,
      escape: true,
      in: ["body"],
      optional:true,
      isInt: {
        options: { min: 1 ,max:100 },
        errorMessage: "Treatment discount rate must be in 1 to 100 ",
      },
    },
    "data.discount": {
      trim: true,
      escape: true,
      in: ["body"],
      optional:true,
      isInt: {
        options: { min: 1 ,max:100 },
        errorMessage: "Treatment overall discount rate must be in 1 to 100 ",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetInvoiceById = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSendPaymentLink = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validatePayByCash = async (req, res, next) => {
  await checkSchema({
    invoiceId: {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Invoice ID must be a valid ObjectId",
      },
    },
    clientId: {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
    amount: {
      trim: true,
      escape: true,
      in: ["body"],
      isDecimal: true,
      errorMessage: "Amount must be a decimal number",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateAddPayment = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "ID must be a valid ObjectId",
      },
    },
    data: {
      trim: true,
      escape: true,
      in: ["body"],
      isObject: true,
      errorMessage: "Data must be an object",
      custom: {
        options: (value) => {
          // Add checks for `data` shape if necessary
          return value != null && typeof value === "object";
        },
        errorMessage: "Data must be a valid object",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};


export const validateFormAndSendPaymentLink = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "ID must be a valid ObjectId",
      },
    },
    clinicId: {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    }
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};