
const serviceValidator = {
  serviceRequests: {
    in: ["body"],
    isArray: true,
    errorMessage: "serviceRequests must be an array",
  },

  "serviceRequests.*.status": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each serviceRequest must have a non-empty status",
  },

  "serviceRequests.*.intent": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each serviceRequest must have a non-empty intent",
  },

  "serviceRequests.*.categories": {
    in: ["body"],
    isArray: true,
    notEmpty: true,
    errorMessage:
      "Each serviceRequest must have a non-empty array of categories",
  },

  "serviceRequests.*.categories.*": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each category must be a non-empty string",
  },

  "serviceRequests.*.type": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each serviceRequest must have a non-empty type",
  },
}

export default serviceValidator;
