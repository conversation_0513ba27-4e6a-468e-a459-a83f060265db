import { useState } from "react";
import { isToday } from "date-fns";
import { Link, useLoaderData, useNavigate } from "react-router-dom";
import ViewPersonalDetails from "components/view-page/ViewPersonalDetails";
import ViewDocument from "components/view-page/ViewDocument";
import ViewFiles from "./patient-detail/ViewFiles";
import Consultation from "./Consultation";
import { SetStartConsultation } from "../../services/appointment/appointment";
import { GetPatientWithAllMedicalRecords } from "../../services/patient/patient";
export async function PatientWithAppointmentDetailLoader({ params }) {
  var patientData = undefined;
  if (params?.id != undefined) {
    patientData = await GetPatientWithAllMedicalRecords(params.id);
    return { patientData };
  }
  return { patientData };
}
function ViewPatient() {
  const navigate = useNavigate();
  const { patientData } = useLoaderData();

  const [callEndConsultation, SetCallEndConsultation] = useState(false);
  var prescription = [];
  var medicalFile = [];
  var procedure = [];
  var currentAppointment = null;
  //let loadingRecords =false;
  const settingsArray = [
    {
      name: "Details",
      isVisible: true,
      isEnabled: true,
    },
    {
      name: "Consultation",
      isVisible: false,
      isEnabled: false,
    },
    {
      name: "Prescription",
      isVisible: true,
      isEnabled: true,
    },
    {
      name: "Medical-records",
      isVisible: true,
      isEnabled: true,
    },
    {
      name: "Procedure-records",
      isVisible: true,
      isEnabled: true,
    },
    {
      name: "Appointment-history",
      isVisible: false,
      isEnabled: true,
    },
    {
      name: "Payment-history",
      isVisible: false,
      isEnabled: true,
    },
  ];
  const [activeTab, setActiveTab] = useState(1);
  const [patientMenu, setPatientMenu] = useState(settingsArray);
  const [consultationStarted, setConsultationStarted] = useState(false);
  const handleTabClick = (tabNumber) => {
    setActiveTab(tabNumber);
  };
  // const [consultationRecord,
  //     SetConsultationRecord] = useState(null);
  // useEffect(() => {     if (!loadingRecords) {         loadingRecords = true;
  //     const fetch = async() => {             var result = await
  // GetCurrentRecords(patientData?.appointment?._id);             if (result !=
  // null) {                 SetConsultationRecord(result);             } }
  //  fetch();     } }, [loadingRecords]);

  for (let index = 0; index < patientData?.appointments.length; index++) {
    var appointment = patientData?.appointments[index];
    if (appointment?.prescriptionRecords != null) {
      prescription = prescription.concat(appointment?.prescriptionRecords);
    }

    if (appointment?.medicalRecords != null) {
      medicalFile = medicalFile.concat(appointment?.medicalRecords);
    }

    if (appointment?.procedureRecords != null) {
      procedure = procedure.concat(appointment?.procedureRecords);
    }

    const today = isToday(new Date(appointment?.appointmentDate));

    if (today) {
      currentAppointment = appointment;
    }
  }

  const handleConsultation = async (e, id) => {
    if (id != null) {
      if (currentAppointment?.ended?.yes == true) return false;

      let arrayMenu = [...patientMenu];
      arrayMenu[0].isVisible = true;
      arrayMenu[0].isEnabled = true;
      for (let j = 1; j < arrayMenu.length; j++) {
        arrayMenu[j].isEnabled = false;
      }
      if (!(patientData?.appointment?.started?.yes == true)) {
        await SetStartConsultation(id);
      }
      setPatientMenu(arrayMenu);
      setActiveTab(0);
      setConsultationStarted(true);
    } else {
      navigate("/scheduleappointment");
    }
  };
  const endConsultation = () => SetCallEndConsultation(!callEndConsultation);
  return (
    <div className="flex flex-row  h-full space-x-3 overflow font-primary">
      <nav className="w-[24%] tabs flex-col border items-start p-3 text-base font-medium" aria-label="Tabs" role="tablist" data-tabs-vertical="true" aria-orientation="horizontal" >
        <div className="flex flex-row  items-center  p-1  h-[6rem] ">
          <div className="h-[60px] w-[60px] bg-[#E4E0F3] rounded-[50%] object-cover text-2xl flex items-center justify-center">
            <div className="text-center uppercase w-full h-fit">
              {patientData.firstName?.substring(0, 1) +
                patientData.lastName?.substring(0, 1)}
            </div>
          </div>

          <div className="flex flex-col ml-3">
            <div className=" text-accent text-2xl font-medium">
              {patientData.firstName + " " + patientData.lastName}
            </div>
            <div className=" text-secondary text-sm  font-normal ">
              {patientData.mobile}
            </div>
            <div className=" text-secondary text-sm  font-medium ">
              Patient ID&nbsp;:&nbsp;{patientData.patientId}
            </div>
          </div>
        </div>
        <div class="flex justify-between  border-b w-full py-2">
          <Link
            to={"edit"}
            className="btn btn-primary btn-soft btn-md "
          >
            Edit Profile
          </Link>
          <button
            type="button"
            className="btn btn-secondary btn-soft btn-md"
            onClick={(e) => {
              navigate(-1);
            }}
          >
            Cancel
          </button>
          {/* {consultationStarted ? (
            <button
              type="button"
              className="bg-Primary text-white border text-sm w-1/2 h-fit py-4 rounded-md text-center cursor-pointer"
              onClick={(e) => {
                endConsultation(e);
              }}
            >
              End Consultation
            </button>
          ) : (
            <button
              type="button"
              className="bg-Primary text-white border text-sm w-1/2 h-fit py-4 rounded-md text-center cursor-pointer"
              onClick={(e) => {
                handleConsultation(e, currentAppointment?._id);
              }}
            >
              {!(currentAppointment?.ended?.yes == true) &&
              currentAppointment?.started?.yes == true
                ? "Continue Consultation"
                : currentAppointment?.ended?.yes == true
                ? "Ended"
                : "Start Consultation"}
            </button>
          )} */}
        </div>

        {patientMenu.map((data, index) => {
          return (
            data.isVisible ? <button type="button" key={`tab_button-${index}`} className={`mt-3 btn btn-text btn-secondary active-tab:bg-secondary/10 active-tab:text-black hover:text-secondary hover:bg-secondary/10 ${index == 0 ? "active" : ""} w-full px-10 py-7 justify-start`}
              id={`tab-${data.name}`}
              data-tab={`#tab-${data.name}-data`}
              aria-controls={`tab-${data.name}-data`}
              role="tab"
              aria-selected="true" >
              {data.name.toString().replace("-", " ")}
            </button> : <></>)
        })}
      </nav>

      <div className="w-[76%] flex flex-col font-medium">
        <div id="tab-Consultation-data" className=" flex flex-col h-full px-8 py-6 bg-backcolor_detailpage hidden" role="tabpanel" aria-labelledby="tab-Consultation">
          <Consultation
            appointmentId={currentAppointment?._id}
            setEndConsultation={callEndConsultation}
            consultationRecords={currentAppointment}
          />
        </div>
        <div id="tab-Details-data" className=" flex flex-col h-full px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Details">
          <ViewPersonalDetails obj={patientData} type={"patient"} />
          <ViewDocument obj={patientData} />
        </div>
        <div id="tab-Prescription-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Prescription">
          <ViewFiles heading={"Prescription"} files={prescription} />
        </div>
        <div id="tab-Medical-records-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Medical-records">
          <ViewFiles heading={"Medical Records"} files={medicalFile} />
        </div>
        <div id="tab-Procedure-records-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Procedure-records">
          <ViewFiles heading={"Procedure Records"} files={procedure} />
        </div>
        <div id="tab-Appointment-history-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Appointment-history">
        </div>
        <div id="tab-Payment-history-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage" role="tabpanel" aria-labelledby="tab-Payment-history">
        </div>
      </div>
    </div>
  );
}

export default ViewPatient;
