import React, { useEffect, useState } from "react";
import PropTypes from 'prop-types';
import { confirmable } from 'react-confirm';
import { createConfirmation } from 'react-confirm';

const Prompt = ({ show, title, proceed, confirmation }) => {
  var keyString = "modal"+new Date().getTime();
  const closeModal = () => {
    window.HSOverlay.close('#'+keyString);
  };

  const openModal = () => {
    window.HSOverlay.autoInit();
    window.HSOverlay.open('#'+keyString);
  };
  const cancelRef = React.useRef();

  
  useEffect(() => {
    // setTimeout(() => {
    //   if (
    //     window.HSStaticMethods &&
    //     typeof window.HSStaticMethods.autoInit === 'function'
    //   ) {
    //     window.HSStaticMethods.autoInit();
    //   }
    // }, 100);
    if (show) {
      openModal();
    }
  }, [show, title, confirmation, proceed]);
  return (
      <div
        id={keyString}
        className="overlay modal overlay-open:opacity-100 overlay-open:duration-300 modal-middle overlay-backdrop-open:bg-color_dark/30 hidden [--overlay-backdrop:static]"
        role="dialog"
        tabIndex="-1"
        leastDestructiveRef={cancelRef}
        data-overlay-keyboard="false"
      >
        <div className="overlay-animation-target modal-dialog modal-dialog-lg overlay-open:opacity-100 overlay-open:duration-300 transition-all ease-out">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title text-xl font-medium leading-normal text-neutral-600">
                {title}
              </h5>
              <button
                type="button"
                className="btn btn-text btn-circle btn-sm absolute end-3 top-3"
                ref={cancelRef} 
                onClick={() => closeModal()}
              >
                <span className="icon-[tabler--x] size-4"></span>
              </button>
            </div>

            {confirmation instanceof Array ? (
              <div className="modal-body">
                <div className="flex flex-col space-y-2 justify-start pl-4">
                  {confirmation.map((message, i) => (
                    <div className="content-start capitalize" key={i + 1}>
                      {i + 1}.&nbsp;&nbsp;{message}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="modal-body">{confirmation}</div>
            )}

            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-soft btn-secondary"
                ref={cancelRef} 
                onClick={() => closeModal()}
              >
                Close
              </button>
              {proceed !== undefined ? (
                <button
                  type="button"
                  onClick={(e) => {
                    var success = proceed(e);
                    closeModal();
                  }}
                  className="btn btn-soft btn-primary"
                >
                  Confirm
                </button>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </div>
  );
};

Prompt.propTypes = {
  show: PropTypes.bool,            // from confirmable. indicates if the dialog is shown or not.
  proceed: PropTypes.func,
  title: PropTypes.string,     // from confirmable. call to close the dialog with promise resolved.
  confirmation: PropTypes.any, 
  options: PropTypes.object   // arguments of your confirm function
}

export default confirmable(Prompt);
export const confirm = createConfirmation(Prompt);
export const alertBox = createConfirmation(Prompt);
export function confirmWrapper(confirmation, options = {}) {
  return confirm({ confirmation, options });
}

