import { Schema } from 'mongoose';

const telecomSchema = new Schema({
    system: String,
    value: String,
    use: String
});

const licenseSchema = new Schema({
    code: String,
    display: String,
    licNo: String
});

const conditionSchema = new Schema({
    type: String,
    status: String,
    recordedDate: String,
    startDate:String,
    endDate:String
});

const dosageInstructionSchema = new Schema({
    text: String,
    additionalInstruction: [String],
    route: String,
    site:String,
    doseQuantity:{
        value:String,
        unit:String
    },
    repeat: {
        frequency: Number,
        period: Number,
        periodUnit: String
    },
    // method: String
});

const medicationRequestSchema = new Schema({
    status: String,
    intent: String,
    authoredOn: String,
    medication: String,
    forCondition: [String],
    reason:[String],
    dosageInstruction: [dosageInstructionSchema]
});


const signatureSchema = new Schema({
    who: {
        type: { type: String },
        name: String
    },
    sigFormat: String,
    data: String
});

const addressSchema = new Schema({
    use: String,
    type: String,
    text: String,
    // line: [String],
    city: String,
    state: String,
    district: String,
    postalCode: String,
    country: String
});

const allergyIntoleranceSchema = new Schema({
    type: String,
    clinicalStatus: String,
    verificationStatus: String,
    doctor: String,
    notes: [String]
});

const patientSchema = new Schema({
    id:String,
    abhaNumber: String,
    abhaAddress: String,
    name: {
        text:String,
        prefix:[String]
    },
    gender: String,
    dob: String,
    doctors: [String],
    allergyIntolerances: [allergyIntoleranceSchema],
    telecom: [telecomSchema],
    address:[addressSchema]
});

const generalSchema = new Schema({
    artifact: String,
    hipUrl: String,
    hipIds: [String],
    status: String,
    clientId: String
});

const practitionerSchema = new Schema({
    names: [String],
    licenses: [licenseSchema],
    patient: String,
    gender: String,
    birthDate: String,
    address: [addressSchema],
    telecom: [telecomSchema],
});

const encounterSchema = new Schema({
    status: String,
    startTime: String,
    endTime: String
});


const organizationSchema = new Schema({
    name: String,
    telecom: [telecomSchema],
    licenses: [licenseSchema]
});

const binarySchema = new Schema({
    contentType: String,
    data: String
});

const procedureSchema=new Schema({
    status:String,
    type:String,
    performedDateTime:String,
    followUp:String
})

const PrescriptionRecordSchema = new Schema({
    fhirId: {
        type: String,
        required: true,
        index: true
    },
    general: generalSchema,
    patient: patientSchema,
    encounter: encounterSchema,
    practitioners: [practitionerSchema],
    conditions: [conditionSchema],
    organization: organizationSchema,
    medicationRequests: [medicationRequestSchema],
    procedure:procedureSchema,
    binary: binarySchema,
    signature: signatureSchema,
    abhaCareContextLinked:{
        type: Boolean,
        default: false
    }
});

export { PrescriptionRecordSchema };
