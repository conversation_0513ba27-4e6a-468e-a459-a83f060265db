import axios from "services/axios/axios";
import store from "../../store/store";
import { GetJSONHederWithToken } from "utils/CommonMethods";
import { Appointment } from "../appointment/appointment";
export const BookConsultation = async(value, id) => {
    try {
        const {currentClinic} = store.getState();
        var header = GetJSONHederWithToken();
        var data = new Appointment(
          id,
          value.patientId,
          value.doctorId,
          value.mobile,
          value.name,
          value.prefix,
          value.gender,
          value.age,
          value.birthDate,
          value.appointmentDate,
          value.timeSlot,
          value.doctorName,
          value.reason,
          value.virtualConsultation,
          currentClinic?.clinic?._id,
          value.instantAppointment,
          value.clinicPatientId,
          value.abhaNumber,
          value.abhaAddress,
          value.speciality,
          value.address,
          value.pincode,
          value.district,
          value.state,
          value.isFollowUp,
          value.tokenNumber
        );
        const formData = await axios.post('/booked-consultation/bookconsultation', {data},header);
        return formData

    } catch (error) {
        console.log(error)
    }
}

export const BookedConsultationOverview = async (
  page,
  size,
  param,
  filterDate
) => {
  try {
    const { currentClinic } = store.getState();
    const header = GetJSONHederWithToken();

    let url = `/booked-consultation/getbookedconsultationoverview?clinicId=${currentClinic?.clinic?._id}&page=${page}&size=${size}${param}`;

    // Only add filterDate if it's provided
    if (filterDate) {
      url += `&date=${filterDate}`;
    }

    const formData = await axios.get(url, header);
    return formData;
  } catch (error) {
    console.log(error);
  }
};

export const CancelConsultation = async (id) => {
  try {
    var header = GetJSONHederWithToken();
    const formData = await axios.get(
      "/booked-consultation/cancelbookedconsultation?id=" + id,
      header
    );

    if (formData.status == 200) return formData.data;

    return null;
  } catch (error) {
    console.log(error);
  }
};
