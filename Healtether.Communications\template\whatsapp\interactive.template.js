import { WHATSAPP_CHOOSE_CLINIC_FLOWTOKEN } from "../../keys/whatsapp/flowtoken.js";
import { getProperMobile } from "../../utils/common.js";

export const welcomeTemplate = (to) =>
  formTemplateJson(to, process.env.WHATSAPP_TEMPLATE_INTRO, "en", []);

export const chooseLanguageTemplate = (to, code, displayName) => {
  code = code != null ? code : "en";
  var languageButton = [
    {
      type: "reply",
      reply: {
        id: "english_button",
        title: "English",
      },
    },
    {
      type: "reply",
      reply: {
        id: "tamil_button",
        title: "தமிழ்",
      },
    },
    {
      type: "reply",
      reply: {
        id: "hindi_button",
        title: "हिंदी",
      },
    },
  ];
  var headerText = getHeaderText(code);
  var bodyText = getBodyTextForChooseLanguage(code);
  return formInteractiveReplyButtonTemplate(
    to,
    headerText,
    bodyText,
    languageButton
  );
};

export const chooseUserAction = (to, code, displayName, clinicName) => {
  code = code != null ? code : "en";
  var headerText = getHeaderText(code);
  var bodyText = getBodyTextForUserAction(code, displayName, clinicName);
  var userButton = [
    {
      type: "reply",
      reply: {
        id: "bookappointmant_button",
        title: "1. Book Appointment",
      },
    },
    {
      type: "reply",
      reply: {
        id: "livechat_button",
        title: "2. Chat With Clinic",
      },
    },
    {
      type: "reply",
      reply: {
        id: "changelanguage_button",
        title: "3. Change Language",
      },
    },
  ];
  return formInteractiveReplyButtonTemplate(
    to,
    headerText,
    bodyText,
    userButton
  );
};
export const notExistingTemplate = (to, displayName) =>
  formTemplateJson(to, process.env.WHATSAPP_TEMPLATE_NOT_CUSTOMER, "en", [
    {
      type: "text",
      text: displayName, // clientname
    },
  ]);
export const forgotPasswordOTP = (to, otp) =>
  formTemplateJson(to, process.env.WHATSAPP_FORGOT_PASSWORD_OTP, "en", [
    {
      type: "text",
      text: otp,
    },
  ]);
export const sendTemplateTextMessage = (to, clinicName, message) =>
  formTemplateJson(to, process.env.WHATSAPP_TEXT_MESSAGE, "en", [
    {
      type: "text",
      text: clinicName,
    },
    {
      type: "text",
      text: message,
    },
  ]);



const formTemplateJson = (to, name, code, templateParam) => {
  var toMobile = getProperMobile(to);
  var res = {
    messaging_product: "whatsapp",
    recipient_type: "individual",
    to: toMobile,
    type: "template",
    template: {
      name: name,
      language: {
        code: code,
      },
      components: [
        {
          type: "body",
          parameters: templateParam,
        },
      ],
    },
  };

  return res;
};

const formTemplateWithHeaderParamJson = (
  to,
  name,
  code,
  bodyParam,
  headerParam
) => {
  var toMobile = getProperMobile(to);
  var res = {
    messaging_product: "whatsapp",
    recipient_type: "individual",
    to: toMobile,
    type: "template",
    template: {
      name: name,
      language: {
        code: code,
      },
      components: [
        {
          type: "header",
          parameters: headerParam,
        },
        {
          type: "body",
          parameters: bodyParam,
        },
      ],
    },
  };

  return res;
};

export const chooseClinicTemplate = (to, displayName, clinic) => {
  var toMobile = getProperMobile(to);
  var clinicName = [];
  for (let index = 0; index < clinic.length; index++) {
    const name = clinic[index];
    clinicName.push({
      id: name._id,
      title: name.clinicName,
      description: name.clinicName,
    });
  }

  var clinicTemplate = {
    recipient_type: "individual",
    messaging_product: "whatsapp",
    to: toMobile,
    type: "interactive",
    interactive: {
      type: "flow",
      header: {
        type: "text",
        text: "Healtether",
      },
      body: {
        text: "Please choose clinic",
      },
      action: {
        name: "flow",
        parameters: {
          flow_message_version: "3",
          flow_token: WHATSAPP_CHOOSE_CLINIC_FLOWTOKEN,
          flow_id: process.env.WHATSAPP_CHOOSE_CLINIC_FLOWID,
          flow_cta: "☶ Select Clinic",
          flow_action: "navigate",
          flow_action_payload: {
            screen: "HEALTETHER_CHOOSE_CLINIC",
            data: {
              data_clinic: clinicName,
            },
          },
        },
      },
    },
  };
  return clinicTemplate;
};

export const formInteractiveReplyButtonTemplate = (
  to,
  headerText,
  bodyText,
  buttonArray
) => {
  var toMobile = getProperMobile(to);

  var clinicTemplate = {
    recipient_type: "individual",
    messaging_product: "whatsapp",
    to: toMobile,
    type: "interactive",
    interactive: {
      type: "button",
      header: {
        type: "text",
        text: headerText,
      },
      body: {
        text: bodyText,
      },
      action: {
        buttons: buttonArray,
      },
    },
  };
  return clinicTemplate;
};

const getHeaderText = (code) => {
  switch (code) {
    case "en":
      return "Healtether";
    case "ta":
      return "ஹீல்டெதர்";
    case "hi":
      return "हीलटेदर";
    default:
      return "Healtether";
  }
};

const getBodyTextForChooseLanguage = (code) => {
  switch (code) {
    case "en":
      return "Please choose preferred language";
    case "ta":
      return "விருப்பமான மொழியை தேர்வு செய்யவும்";
    case "hi":
      return "कृपया पसंदीदा भाषा चुनें";
    default:
      return "Please choose preferred language";
  }
};

const getBodyTextForUserAction = (code, displayName, clinicName) => {
  switch (code) {
    case "en":
      return `Dear ${displayName},
        Choose option to continue with ${clinicName}`;

    case "ta":
      return `அன்புள்ள ${displayName},
        ${clinicName} தொடர்வதற்கான விருப்பத்தைத் தேர்வுசெய்க`;

    case "hi":
      return `प्रिय ${displayName},
        ${clinicName}  क्लिनिक के साथ जारी रखने का विकल्प चुनें`;

    default:
      return `Dear ${displayName},
        Choose option to continue with ${clinicName}`;
  }
};
