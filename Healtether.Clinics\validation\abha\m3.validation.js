import { body } from 'express-validator';

export const validateInitiateConsentRequest = [
  body('requestdata').isObject().withMessage('Request data must be an object'),
  // Add other validations if needed
];

export const validateCheckConsentStatus = [
  body('requestdata').isObject().withMessage('Request data must be an object'),
  // Add other validations if needed
];

export const validateNotifyConsentRequest = [
  body('consentId').isString().withMessage('Consent ID must be a string'),
  body('type').isString().withMessage('Type must be a string'),
  body('data').isObject().withMessage('Data must be an object'),
  // Add other validations if needed
];

export const validateFetchConsentDetails = [
  body('consentArtefactId').isString().withMessage('Consent Artefact ID must be a string'),
  body('hiuId').isString().withMessage('HIU ID must be a string'),
  // Add other validations if needed
];

export const validateRequestHealthInformation = [
  body('data').isObject().withMessage('Data must be an object'),
  body('hiuId').isString().withMessage('HIU ID must be a string'),
  // Add other validations if needed
];

export const validateNotifyHealthInformation = [
  body('transaction_id').isString().withMessage('Transaction ID must be a string'),
  // Add other validations if needed
];