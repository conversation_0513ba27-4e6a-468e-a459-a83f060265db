import dayjs from "dayjs";
import { useEffect, useState } from "react";
import CustomSelect from "../../../../../components/detail-page/select.jsx";
import InfiniteScroll from "react-infinite-scroll-component";
import {
  EndConsultation,
  GetAppointmentOverview,
} from "../../../../../services/appointment/appointment.js";
import Spinner from "../../../../../components/loader/Spinner.jsx";
import { PatientQueueInfo } from "./PatientInfoInQueue.jsx";
import moment from "moment";
import { DateSelector } from "../../../../../components/detail-page/new appointment/DateSelector.jsx";

export default function QueueDetails({
  doctors,
  setDefaultKeywords,
  setSelectedDoctorId,
  setAppointmentStatus,
  setselectedAppointmentDate,
  defaultKeywords,
  data,
  fetchQueueData,
  queueHasMore,
}) {
  let tabButton = [
    {
      id: 2,
      name: "Upcoming",
      isActive: false,
      default: true,
      setStatus: { status: "Upcoming" },
    },
    {
      id: 5,
      name: "Completed",
      isActive: false,
      setStatus: { status: "Completed" },
    },
  ];

  const endConsultation = async (appointmentId) => {
    let result = await EndConsultation([], [], [], [], appointmentId);
    console.log(result);
  };

  return (
    <div className="h-full">
      <div
        className=" flex justify-between items-center gap-10 mt-2"
        style={{
          height: "7%",
        }}
      >
        <div className="rounded-full border border-[#bac1be] overflow-x-auto bg-backcolor_light hide-scrollbar font-primary font-semibold ">
          <div className="flex flex-nowrap items-center">
            {tabButton?.map((prop, idx) => (
              <div
                key={idx}
                onClick={() => {
                  setAppointmentStatus(prop.setStatus.status);
                  console.log(prop.setStatus.status);
                  setDefaultKeywords({
                    ...defaultKeywords,
                    option: { ...defaultKeywords.option, status: prop.name },
                  });
                }}
                className={`${
                  defaultKeywords.option.status == prop.name
                    ? "text-color_dark bg-primary_light"
                    : " text-color_muted text-color_dark"
                } rounded-full px-5 py-2 flex items-center cursor-pointer duration-200`}
              >
                <div className={`text-sm `}>{prop.name}</div>
              </div>
            ))}
          </div>
        </div>

        <DateSelector
          label="queue"
          onChange={(date) => setselectedAppointmentDate(date)}
        />

        <div className="w-4/12">
          <CustomSelect
            label={"Filter By Attending Doctor"}
            data={[{ firstName: "all", _id: "0" }, ...doctors]}
            handleClick={(e) => {
              console.log("clicked");
              setSelectedDoctorId(e._id);
            }}
            placeholder="Attending Doctor"
          />
        </div>
      </div>
      <div
        className="mt-3 w-full z-0"
        id="QueueLayout"
        style={{
          height: "90%",
          overflowY: "scroll",
        }}
      >
        <InfiniteScroll
          dataLength={data.length}
          next={() => fetchQueueData(false)}
          hasMore={queueHasMore}
          loader={
            <p className="text-center m-5">
              {" "}
              <Spinner show={true} /> &nbsp;loading ...
            </p>
          }
          endMessage={
            <p className="text-center m-5"> end of queue ...!🐰🕕 </p>
          }
          scrollableTarget="QueueLayout"
        >
          {data.length > 0 &&
            data.map((info) => (
              <PatientQueueInfo info={info} endConsultation={endConsultation} />
            ))}
        </InfiniteScroll>
      </div>
    </div>
  );
}
