4079d9033ded17e5b609294f1ae4d26c0d5801fe		branch 'm1-m2-m3-fixes' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
d74d9630ccad29ddd32b61997ce40f5c22e6c55e	not-for-merge	branch 'FHIR-format-mapping' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
08064afe3b286f8c62fabb2ae91569ff829c5dc1	not-for-merge	branch 'abha-consents-and-records-show' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
d3eca4fb49a81ece4c71f41bffcd2dcf05a57397	not-for-merge	branch 'abha-patientUI' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
7d0d7c45488cba5e10c3a6be73a235abb49f8c39	not-for-merge	branch 'abha/m1-getSuggetion' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
ae10f7d15291ada51b22137fc1d19df0848d4b78	not-for-merge	branch 'abha/m1-integration' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
0245f4ec0d1664dd8664396914e468c227225f17	not-for-merge	branch 'azure-pipelines' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
14fe338e0efb83dd830250f6392ab59b1280c371	not-for-merge	branch 'azure-pipelines_n1' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
f2853e3f3b6baf1c7f66c055b0a8d4d425991c95	not-for-merge	branch 'bug/setgooglemeet' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
b40df6114a515a27aa98b98e72ff4a29f58fdc50	not-for-merge	branch 'commented-ui' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
5a31878eede3372c6927784d870683053f4271c8	not-for-merge	branch 'dischargeSummaryUi' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
dc0b9ec4217421daa9e0b866620010a8643f448e	not-for-merge	branch 'feature/m1_ui_rework' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
c4fbc7125878de5c3bf0deb4e1ca0431dc8383e3	not-for-merge	branch 'fix/abha_m3_ui' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
0a12dbabad3d566bdde3b724580d61893e4d800e	not-for-merge	branch 'fix/bug_fix_appointment' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
f84a54314a158344380c096613ec739c6287e9c5	not-for-merge	branch 'fix/m1_changes' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
664ff8cbfeff8b95eb13db59f83620f2f09267fe	not-for-merge	branch 'fix_booked_ui' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
b0a34553ae88095aa794e6c330cada04427507ce	not-for-merge	branch 'followup-filter' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
6d77f2052ee7d8c0f885eab6bf457c1dbae3f939	not-for-merge	branch 'immunization' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
de6781f4297e94f6a4d559cd5f326eb756ab3870	not-for-merge	branch 'izzy-integration' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
601c63ed91cd647e6384ab5529750484f7d98645	not-for-merge	branch 'm2-fixes' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
9f7f27ed6d3235f81bdaddc8ab2d7614d562dd13	not-for-merge	branch 'm2-updates' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
de6781f4297e94f6a4d559cd5f326eb756ab3870	not-for-merge	branch 'main' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
99b4d3b8c1810a9f530f9f0f8b66d537dc8462ef	not-for-merge	branch 'medication-search' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
b93e53198a6ec2ffe93fcb71c0549d04a305dd3a	not-for-merge	branch 'merge/m3-ui' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
669198938e4981844aed62f49c6e20fa8d813da7	not-for-merge	branch 'merging-abha-consent-and-version/2.3' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
076ad2374417b61d918df61d5943c23d1f8c1a7a	not-for-merge	branch 'monika/Fixes/25-11-2024' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
4208d4952e0f42ca27373f74f3db9ac80e1530d7	not-for-merge	branch 'monika/M1-Abha-UI' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
7e26c01872cf3a34cf8d321096ff94670c591c9c	not-for-merge	branch 'monika/changes/09-11-2024' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
407506aed585181e180955732f4d60360d709065	not-for-merge	branch 'monika/changes/14-11-2024' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
ec397e549bedf18347326ded687b997494c750af	not-for-merge	branch 'monika/changes/17-12-2024' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
17da9902f1fdcd2595ae6e3cfba2ea7814da8a07	not-for-merge	branch 'monika/medicalReport/25-11-2024' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
****************************************	not-for-merge	branch 'monika/prescription-settings' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
bd1cf94a27c26ebb1dd12a9e9d090f1cca25eed4	not-for-merge	branch 'release/1.0' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
875567eaf4dca088ac761fab01b4ef3e73f34329	not-for-merge	branch 'release/1.1' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
de6781f4297e94f6a4d559cd5f326eb756ab3870	not-for-merge	branch 'release/1.2' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
9823d2f6dcdf5b25eded360c1eee196d13f57848	not-for-merge	branch 'revert-2-monika/changes/09-11-2024' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
6cf8e3ea39dab0598405fc213c491e831cd45540	not-for-merge	branch 'version/2.2' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
d8eacab624aeff42391dc225d065bcff71a95b68	not-for-merge	branch 'version/2.3(abha_m1)' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
9b00dfe1e30d4235b7d45c689227d79cdab12f72	not-for-merge	branch 'version/2.4(abha_all)' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
3eff6b58cdca2da2a484d3930adb1fd28df3d807	not-for-merge	branch 'version/abha_m1' of https://dev.azure.com/healtether/HealtetherApps/_git/Healtether.Clinics.UI
