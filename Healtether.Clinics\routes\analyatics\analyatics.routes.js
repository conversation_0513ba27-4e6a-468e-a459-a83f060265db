import { Router } from "express";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";
import {
  getPatientGenderRatio,
  getAgeRatio,
  getPatientAnalysis,
} from "../../controllers/analytics/analytic.controller.js";

const analytic = Router();
analytic.post("/getGenderRatio", 
  authorizationCheck,
  async (req, res, next) => {
    try {
        return await getPatientGenderRatio(req, res);
    }
    catch (e) {
        next(e)
    }
  }  
   );

analytic.post("/getAgeGroupRatio", 
  authorizationCheck,
  async (req, res, next) => {
    try {
        return await getAgeRatio(req, res);
    }
    catch (e) {
        next(e)
    }
  }  
   );
analytic.post("/getPatientAnalysis",
   authorizationCheck,
   async (req, res, next) => {
    try {
        return await getPatientAnalysis(req, res);
    }
    catch (e) {
        next(e)
    }
  }  
  );
export default analytic;
