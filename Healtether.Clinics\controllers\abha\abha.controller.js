import {
  abhaNumberAddressotp,
  createAbhaCard,
  enrolledByAadhar,
  enrolledbyMobile,
  enrollUsingAbhaddress,
  getUserAbhaAddressProfile,
  getUserProfile,
  indexRequestOtp,
  loginUsingAadhar,
  searchAbhaByMobile,
  suggestions,
  UserAbhaAddressCard,
  UserAbhaCard,
  verifyAbhaNumberAddressotp,
  verifyEnrolledMobile,
  verifyOtp,
} from "../../helpers/externalApi/appointment/abha.api.js";
export const searchAbha = async (req, res) => {
  const mobile = req.body.mobile;
  var result = await searchAbhaByMobile(mobile);
  res.status(200).json(result);
};
export const requestIndexMobileOtp = async (req, res) => {
  const index = req.body.index;
  const txnId = req.body.txnId;
  var result = await indexRequestOtp(index, txnId);
  res.status(200).json(result);
};

export const verifyMobileOtp = async (req, res) => {
  const otp = req.body.otp;
  const txnId = req.body.txnId;
  var result = await verifyOtp(otp, txnId);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(400).json(result);
  }
};

export const userProfile = async (req, res) => {
  const xToken = req.headers["x-token"];
  var result = await getUserProfile(xToken);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(500).json(result);
  }
};
export const userAbhaAddressProfile = async (req, res) => {
  const xToken = req.headers["x-token"];
  var result = await getUserAbhaAddressProfile(xToken);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(500).json(result);
  }
};

export const getUserAbhaCard = async (req, res) => {
  const xToken = req.headers["x-token"];
  var result = await UserAbhaCard(xToken);
  res.setHeader("Content-Type", "image/png");
  res.setHeader("Content-Disposition", 'inline; filename="abha-card.png"');
  res.status(200).send(result);
};
export const getUserAbhaAddressCard = async (req, res) => {
  const xToken = req.headers["x-token"];
  var result = await UserAbhaAddressCard(xToken);
  res.setHeader("Content-Type", "image/png");
  res.setHeader("Content-Disposition", 'inline; filename="abha-card.png"');
  res.status(200).send(result);
};

export const enrollAbha = async (req, res) => {
  const { aadhaarNumber } = req.body;
  var result = await createAbhaCard(aadhaarNumber);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(500).json(result);
  }
};

export const enrollByAadhaar = async (req, res) => {
  const { mobileNumber, otp, txnId } = req.body;
  var result = await enrolledByAadhar(mobileNumber, otp, txnId);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(500).json(result);
  }
};

export const enrollbyMobile = async (req, res) => {
  const { mobile, txnId } = req.body;
  var result = await enrolledbyMobile(mobile, txnId);
  res.status(200).json(result);
};

export const enrollMobileOTP = async (req, res) => {
  const { mobileNumber, otp, txnId } = req.body;
  var result = await verifyEnrolledMobile(mobileNumber, otp, txnId);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(500).json(result);
  }
};
export const getSuggestions = async (req, res) => {
  const txnId = req.headers.transaction_id;
  var result = await suggestions(txnId);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(500).json(result);
  }
};
export const enrollByAbhaddress = async (req, res) => {
  const { txnId, abhaAddress, preferred } = req.body;
  var result = await enrollUsingAbhaddress(txnId, abhaAddress, preferred);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(500).json(result);
  }
};

export const aadhaarLogin = async (req, res) => {
  const { aadhaarNumber } = req.body;
  var result = await loginUsingAadhar(aadhaarNumber);
  if (result.isSuccess) {
    res.status(200).json(result);
  } else {
    res.status(500).json(result);
  }
};

//   abha address/number
export const handleAbhaNumberAddressOtpRequest = async (req, res) => {
  const { type, abhaNumberAddress } = req.body;
  const result = await abhaNumberAddressotp(type, abhaNumberAddress);
  res.status(result.isSuccess ? 200 : 500).json(result);
};

export const handleVerifyAbhaNumberAddressOtp = async (req, res) => {
  const { type, otp, txnId } = req.body;
  const result = await verifyAbhaNumberAddressotp(type, otp, txnId);
  res.status(result.isSuccess ? 200 : 500).json(result);
};
