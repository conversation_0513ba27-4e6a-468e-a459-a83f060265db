import { useEffect, useRef, useState } from "react";
import { Tooltip } from "react-tooltip";
import { useNavigate, useParams } from "react-router-dom";
import PropTypes from "prop-types";
import VitalDetail from "components/detail-page/vitals/VitalDetail.jsx";
import VitalsSidebar from "components/detail-page/write-prescription/SiderBar/VitalsSidebar.jsx";
import PastHistorySidebar from "components/detail-page/write-prescription/SiderBar/PastHistorySidebar.jsx";
import MedicalReportSidebar from "components/detail-page/write-prescription/SiderBar/MedicalReportSidebar.jsx";
/*import {GetAppointmentConsultation} from "../../services/appointment/appointment";*/

function PrescriptionSidebar({
  isLoading,
  vitals,
  medicalHistory,
  medicalRecords,
}) {
    const navigate = useNavigate();
    const { appointmentId } = useParams();

    const handleConsent = () => {
        // Navigate to consultation page with state information for tab and upload popup
        navigate(`/appointment/${appointmentId}/consultation_v2`, {
          state: {
            openAbhaConsentTab: true,
            openConsentDialog: true
          }
        });
      };
  return (
    <>
      <div
        className="flex-col lg:flex md:hidden xs:hidden md:peer-click:flex gap-2 mt-1 "
        id="sideNav"
      >
        <VitalsSidebar loading={isLoading} vitalsObj={vitals} />
        <PastHistorySidebar
          allergies={medicalHistory?.allergies}
          medication={medicalHistory?.medication}
          pastHistory={medicalHistory?.pastHistory}
          procedure={medicalHistory?.pastProcedureHistory}
        />
        <MedicalReportSidebar medicalRecords={medicalRecords} />
        <div className="d-flex justify-content-between align-items-center my-3">
          <span className="text-md text-center ">Abha Consent</span>
          <span className="float-end">
            <button className="btn btn-Primary" onClick={handleConsent}>
             Request Consent
            </button>
          </span>
        </div>
      </div>
    </>
  );
}

export default PrescriptionSidebar;

PrescriptionSidebar.propTypes = {
  isLoading: PropTypes.bool,
  vitals: PropTypes.object,
  medicalHistory: PropTypes.object,
};
