import mongoose from "mongoose";
import { UserNotification } from "../../model/clinics.model.js";
import { getExpiryByMinutes } from "../../utils/common.utils.js";

export const overview = async (pg, size, keyword, sortby, direction,clinicId,userId,isAdmin) => {
  const regex = new RegExp(keyword, "i");

  // const findObj =
  //   keyword != null ? { $or: [{ UserNotification: { $regex: regex } }], clinic:clinicId ,user:userId} : { clinic:clinicId ,user:userId};

  const findObj =
    isAdmin ? { clinic:clinicId } : { clinic:clinicId ,user:userId};
  const sortObj =
    sortby != null
      ? { sortby: direction == "desc" ? -1 : 1 }
      : { showTime: -1 };

  const notificationCollection = await UserNotification.find(findObj)
    .sort(sortObj)
    .skip((pg - 1) * size)
    .limit(size)
    .select({
      _id: 1,
      notificationMessage: 1,
      showTime: 1,
      header: 1,
      seen: 1
    })
    .exec();

  const notificationCount = await UserNotification.find(findObj).count();
  return { data: notificationCollection, totalCount: notificationCount };
};

export const upsertUserNotification = async (header,message,userId,clinicId) => {
  const UserNotificationCollection = new UserNotification({
    notificationMessage: message,
    showTime: new Date().toISOString(),
    header: header,
    seen:false,
    clinic:new mongoose.Types.ObjectId(clinicId),
    user:new mongoose.Types.ObjectId(userId),
    expiresAt: getExpiryByMinutes(10080), // 7days in minutes
  });
  await UserNotificationCollection.save();

  return UserNotificationCollection;
};
