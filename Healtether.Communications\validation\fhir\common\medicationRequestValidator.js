const medicationRequestValidator = {
  medicationRequests: {
    in: ["body"],
    isArray: true,
    errorMessage: "medicationRequests must be an array",
  },

  "medicationRequests.*.status": {
    in: ["body"],
    isString: true,
    notEmpty: { errorMessage: "status is required" },
  },

  "medicationRequests.*.intent": {
    in: ["body"],
    isString: true,
    notEmpty: { errorMessage: "intent is required" },
  },

  "medicationRequests.*.authoredOn": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "authoredOn must be a valid ISO 8601 datetime",
  },

  "medicationRequests.*.medication": {
    in: ["body"],
    isString: true,
    notEmpty: { errorMessage: "medication is required" },
  },

  "medicationRequests.*.forCondition": {
    in: ["body"],
    isArray: true,
    errorMessage: "forCondition must be an array of strings",
  },

  "medicationRequests.*.forCondition.*": {
    in: ["body"],
    isString: true,
    notEmpty: { errorMessage: "Each forCondition must be a non-empty string" },
  },

  "medicationRequests.*.reason": {
    in: ["body"],
    isArray: true,
    errorMessage: "reason must be an array of strings",
  },

  "medicationRequests.*.reason.*": {
    in: ["body"],
    isString: true,
    notEmpty: { errorMessage: "Each reason must be a non-empty string" },
  },

  "medicationRequests.*.dosageInstruction": {
    in: ["body"],
    optional: true,
    isArray: true,
    errorMessage: "dosageInstruction must be an array",
  },

  "medicationRequests.*.dosageInstruction.*.text": {
    in: ["body"],
    optional: true,
    isString: true,
  },

  "medicationRequests.*.dosageInstruction.*.repeat.frequency": {
    in: ["body"],
    optional: true,
    errorMessage: "repeat.frequency is required",
  },

  "medicationRequests.*.dosageInstruction.*.repeat.period": {
    in: ["body"],
    isInt: true,
    optional: true,
    errorMessage: "repeat.period must be an integer",
  },

  "medicationRequests.*.dosageInstruction.*.repeat.periodUnit": {
    in: ["body"],
    isString: true,
    optional: true,
    notEmpty: { errorMessage: "repeat.periodUnit is required" },
  },

  "medicationRequests.*.dosageInstruction.*.route": {
    in: ["body"],
    isString: true,
    optional: true,
    notEmpty: { errorMessage: "route is required" },
  },

  "medicationRequests.*.dosageInstruction.*.doseQuantity.value": {
    in: ["body"],
    optional: true,
    errorMessage: "doseQuantity.value must be an integer",
  },

  "medicationRequests.*.dosageInstruction.*.doseQuantity.unit": {
    in: ["body"],
    isString: true,
    optional: true,
    notEmpty: { errorMessage: "doseQuantity.unit is required" },
  },

  "medicationRequests.*.dosageInstruction.*.site": {
    in: ["body"],
    isString: true,
    optional: true,
    notEmpty: { errorMessage: "site is required" },
  },

  "medicationRequests.*.dosageInstruction.*.additionalInstruction": {
    in: ["body"],
    optional: true,
    isString: true,
  },
};

export default medicationRequestValidator;
