import mongoose from "mongoose";
import { CLIENT_COLLECTION, USER_COLLECTION } from "../mongodb.collection.name.js";
const staffSchema = new mongoose.Schema({
    staffId:{
        type: String,
        maxLength: 255,
        required:true
    },
    prefix:{
        type: String,
        enum: ['Mr.', 'Mrs.', 'Miss', 'Dr.', 'Prof.', 'Rev.', 'Hon.', 'Other'],
        required: true,
        maxLength: 50
    },
    firstName: {
        type: String,
        maxLength: 255,
        required:true
    },
    lastName: {
        type: String,
        maxLength: 255,
        required:true
    },
    hprId:{
        type: String,
        maxLength: 50,
    },
    specialization: {
        type: String,
        maxLength: 50
    },
    isDoctor: {
        type: Boolean,
        default:false
    },
    age: {
        type: Number,
        min: 1,
        max: 100
    },
    birthday: {
        type: Date
    },
    gender: {
        type: String,
        maxLength: 10
    },
    mobile: {
        type: String,
        maxLength: 15,
        required:true
    },
    email:  {
        type: String,
        maxLength: 100
    },
    address:  {
        house:{
            type: String,
            maxLength: 255
        },
        street:{
            type: String,
            maxLength: 1000 
        },
        landmarks:{
            type: String,
            maxLength: 1000
        },
        city:{
            type: String,
            maxLength: 500
        },
        pincode:{
            type: String,
            maxLength: 50
        }
    },
    documentType:  {
        type: String,
        maxLength: 100
    },
    documentNumber:  {
        type: String,
        maxLength: 100
    },
    upiId:  {
        type: String,   
        maxLength: 100
    },
    bankName:  {
        type: String,
        maxLength: 100
    },
    accountName:  {
        type: String,
        maxLength: 255
    },
    accountNo:  {
        type: String,
        maxLength: 100
    },
    ifsc: {
        type: String,
        maxLength: 50
    },
    created: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            },
        }
    },
    modified: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            },
        }
    },
    profilePic: String,
    documents: [{
        fileName:{
            type: String,
            maxLength: 255
        },
        blobName:{
            type: String,
            maxLength: 255
        },
        uploadedOn:Date
    }],
    deleted: {
        type: Boolean,
        default:false
    },
    availableTimeSlot:[
        {
            weekDay:[{
                type: String,
                maxLength: 5,
            }],
            timeSlot:[
                {
                    start:{
                        type: String,
                        maxLength: 15,
                    },
                    end:{
                        type: String,
                        maxLength: 15,
                    }
                }
            ],
            slotDuration:{
                type:Number
            }
        }
    ],
    userId: { type: mongoose.Schema.Types.ObjectId, ref: USER_COLLECTION },
    clinic:{
        type: mongoose.Schema.Types.ObjectId,
        ref: CLIENT_COLLECTION,
    }
},
{versionKey:'1.8'})

staffSchema.index({ mobile: 1, clinic: 1}, { unique: true });
staffSchema.index({ firstName: 1, lastName:1, clinic: 1}, { unique: true });
staffSchema.index({ staffId:1, clinic: 1}, { unique: true });
// Create the user model
//const Staff = new mongoose.model("Staff", staffSchema);
export {staffSchema};
