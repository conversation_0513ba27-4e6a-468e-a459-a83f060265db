import {
  getAllClients,
  upsertClient,
  deleteClient,
  getClientById,
  getClientSetting,
  overview,
  updateSettingClient,
} from "../../helpers/clinic/client.helper.js";
import {
  getInvoiceNumber,
  getPatientAutoId,
  getStaffAutoId,
} from "../../helpers/clinicautoidentifier/clinicautoid.helper.js";
import { BlobHelper } from "../../helpers/storage/blob.helper.js";

//creating client
export const clientUpsert = async (req, res) => {
    const data = req.body.value;
    const id = req.body.id;
    await upsertClient(data, id);
    res.status(200).json({ success: true });
};
export const clientSetting = async (req, res) => {
    const data = req.body.value;
    const id = req.body.id;
    await updateSettingClient(data, id, req.user);
    res.status(200).json({ success: true });
};

//get  clients
export const getClient = async (query, res) => {
    const data = query; 
    var overviewData = await getClientById(data.query.id);
    res.json(overviewData).status(200);
};

//get client overview
export const getClientOverview = async (query, res) => {
    const data = query.query;
    var overviewData = await overview(data.page, data.size,data.keyword);
    res.json(overviewData).status(200);
};

//delete clients
export const clientDelete = async (query, res) => {
    const data = query;
    await deleteClient(data.query.id);
   return res.status(200).json({ success: true });
};

export const updateDoc = async (req, res) => {
    const data = req.body;
    var blobObj = new BlobHelper(process.env.AZURE_CONNECTIONSTRING, "common");
    if (req.files.Logo != undefined)
      blobObj.UploadBlob(req.files.Logo[0], "clinic/", data.logoName);
      res.status(200).json({ success: true });
};

export const getAllClinics = async (req, res) => {
    var allClients = await getAllClients();
    res.json(allClients).status(200);
};
export const generatePatientId = async (req, res) => {
    var data = req;
    var clinic = await getPatientAutoId(data.query.id);
    if (clinic != null) {
      var data = {
        patientId: {
          prefix: clinic.clinic?.patientId?.prefix,
          suffix: clinic.clinic?.patientId?.suffix,
          currentPatientId: clinic?.currentPatientId,
        },
      };
      res.json(data).status(200);
    } else {
      res.status(404).json("");
    }
};
export const generateStaffId = async (req, res) => {
    var data = req;
    var clinic = await getStaffAutoId(data.query.id);
    if (clinic != null) {
      var data = {
        staffId: {
          prefix: clinic.clinic?.staffId?.prefix,
          suffix: clinic.clinic?.staffId?.suffix,
          currentStaffId: clinic?.currentStaffId,
        },
      };
      res.json(data).status(200);
    } else {
      res.json("").status(404);
    }
};
export const generateInvoiceId = async (req, res) => {
    var data = req;
    var invoiceNo = await getInvoiceNumber(data.query.id);
    if (invoiceNo != null) {
      var data = {
        invoiceNumber: invoiceNo.currentInvoiceId,
      };
      res.json(data).status(200);
    } else {
      res.json("").status(404);
    }
};
export const getClinicTimeSlots = async (req, res) => {
    const data = req;
    var clinic = await getClientSetting(data.query.id);
    console.log(clinic.timeSlots);
    return res.status(200).json(clinic);
};
// update client export const UpdateClient = async (req, res) => {   try { const
// id = req.query.id;     const dataToUpdate = req.body.data;     await
// updateClient(id, dataToUpdate);     res.json({ success: true }).status(200);
// } catch (error) {     console.log(error);   } };
