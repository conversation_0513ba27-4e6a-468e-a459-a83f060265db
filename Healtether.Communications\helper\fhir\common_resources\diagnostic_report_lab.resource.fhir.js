import {
  diagnosticReportDiv,
  diagnosticReportMetaData,
  getSnomedCtCode,
} from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";
import { v4 as uuidv4 } from "uuid";
export const generateDiagnosticReportResource = async (
  diagnosticReport,
  patientResource,
  practitionerResources,
  organizationResource,
  categories,
  type,
  currentTime,
  encounterResource
) => {
  const id = uuidv4();

  const categoryData = await Promise.all(
    categories.map(async (category) => {
      const trimmedCategory = category.trim();
      const snomedCategory = await generateSnomedCtCode(trimmedCategory);
      return getSnomedCtCode(snomedCategory.conceptId, trimmedCategory);
    })
  );

  const getSnomedDataType = await generateSnomedCtCode(type);
  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "DiagnosticReport",
      id,
      meta: diagnosticReportMetaData(currentTime),
      status: diagnosticReport.status,
      category: categoryData,
      code: {
        coding : [
          {
            system : "http://loinc.org",
            code : "24331-1",
            display: "Lipid 1996 panel - Serum or Plasma"
          }
        ],
        text: "Lipid 1996 panel"
      },
      subject: { reference: patientResource.fullUrl, display: "Patient" },
      encounter: encounterResource
        ? {
            reference: `urn:uuid:${encounterResource.resource.id}`,
            display: encounterResource.resource.resourceType,
          }
        : undefined,
      issued: diagnosticReport.issued,
      performer: organizationResource
        ? [
            {
              reference: organizationResource.fullUrl,
              display: organizationResource.resource.resourceType,
            },
          ]
        : undefined,
      resultsInterpreter: practitionerResources.map((practitioner) => ({
        reference: practitioner.fullUrl,
        display: practitioner.resource.resourceType,
      })),
      result: diagnosticReport.result.map((observation) => ({
        reference: `urn:uuid:${observation.id}`,
        display: observation.display,
      })),
      conclusion: diagnosticReport.conclusion,
      text: diagnosticReportDiv(),
      presentedForm: diagnosticReport.presentedForm.map(({ attachment }) => ({
        contentType: attachment.contentType,
        language: "en",
        data: attachment.data,
        title: toTitleCase(attachment.title.trim()),
      })),
    },
  };
};
