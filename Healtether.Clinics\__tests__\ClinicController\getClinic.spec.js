import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
mockClientHelper();
const { getClient } = await import("../../controllers/clinic/client.controller.js");
const { getClientById } = await import("../../helpers/clinic/client.helper.js");

// Mock client data for testing
const mockClientData = {
  _id: '62a000000000000000000000',
  clinicName: 'Test Clinic',
  adminUserId: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    mobile: '1234567890',
  }
};

// Mock request and response objects
let query = {
  query: {
    id: '62a000000000000000000000',
  },
};

const res = {
  json: jest.fn().mockReturnThis(),
  status: jest.fn().mockReturnThis(),
};

describe('getClient', () => {
  beforeEach(() => {
    jest.clearAllMocks(); // Clear mocks before each test
  });

  it('should return client data successfully', async () => {

    getClientById.mockResolvedValue(mockClientData);

    await getClient(query, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(mockClientData);
  });


  

});
