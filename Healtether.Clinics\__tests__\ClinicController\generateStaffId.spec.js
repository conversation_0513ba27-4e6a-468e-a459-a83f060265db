import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
const { mockAutoIdHelper } = await import("../mocks/clinicAutoId.helper.js");
mockClientHelper();
mockAutoIdHelper();
const { generateStaffId } = await import("../../controllers/clinic/client.controller.js");
const { getStaffAutoId } = await import("../../helpers/clinicautoidentifier/clinicautoid.helper.js");

import mongoose from "mongoose";
describe("generateStaffId", () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: { id: new mongoose.Types.ObjectId().toString() },
        };
        res = {
            json: jest.fn().mockReturnThis(),
            status: jest.fn().mockReturnThis(),
        };
        jest.clearAllMocks();
    });

    it("should return the current staff ID with status 200 if clinic data is found", async () => {
        const mockClinic = {
            clinic: {
                staffId: {
                    prefix: "STAFF",
                    suffix: "2024",
                },
            },
            currentStaffId: "12345",
        };

        getStaffAutoId.mockResolvedValueOnce(mockClinic);

        await generateStaffId(req, res);

        expect(getStaffAutoId).toHaveBeenCalledWith(req.query.id);
        expect(res.json).toHaveBeenCalledWith({
            staffId: {
                prefix: mockClinic.clinic.staffId.prefix,
                suffix: mockClinic.clinic.staffId.suffix,
                currentStaffId: mockClinic.currentStaffId,
            },
        });
        expect(res.status).toHaveBeenCalledWith(200);
    });

    it("should return an empty string with status 404 if clinic data is not found", async () => {
        getStaffAutoId.mockResolvedValueOnce(null);
        await generateStaffId(req, res);
        expect(getStaffAutoId).toHaveBeenCalledWith(req.query.id);
        expect(res.json).toHaveBeenCalledWith("");
        expect(res.status).toHaveBeenCalledWith(404);
    });


});
