import { v4 as uuidv4 } from "uuid";
import { organizationIdentifiers, organizationMetadata, organizationDiv } from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";

export const generateOrganizationResource = async (organization) => {
    const id = uuidv4();

    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'Organization',
            id,
            meta: organizationMetadata(),
            identifier: organizationIdentifiers(organization.licenses),
            name: toTitleCase(organization.name),
            telecom: organization.telecom.map(telecom => ({
                system: telecom.system,
                value: telecom.value,
                use: telecom.use
            }))
            
            // text: organizationDiv()
        }
    }
}