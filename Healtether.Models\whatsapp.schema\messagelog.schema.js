import mongoose from "mongoose";

export const MessagelogSchema = new mongoose.Schema(
  {
    mobile: {
      type: String,
      required: true,
      index: true,
      maxLength: 15,
    },
    message: {
      type: String,
      required: [true, "message required"],
    },
    createdOn: {
      type: Date,
      default: Date.now,
      index: true,
    },
    isDelivered: {
      type: Boolean,
    },
    clinicId: {
      type: String,
      maxLength: 45,
      index: true,
    },
    isReceived: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
    },
    expireAt: {
      type: Date,
      expires: 2628000,
    },
  },
  { versionKey: "1.1" }
);

// const MessageLogModel = mongoose.model("WhatsappMessageLog",
// messagelogSchema);

