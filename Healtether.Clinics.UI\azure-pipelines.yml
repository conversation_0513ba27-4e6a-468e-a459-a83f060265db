# Node.js React Web App to Linux on Azure
# Build a Node.js React app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  branches:
   include:
    - version/*
    - release/*

variables:
  - group: pipeline-variable

  - name: 'azureSubscription'
    value: 'c4a628c3-52e8-435c-9f92-a2cbce88789c'

  - name: 'webAppName'
    value: 'app-uhi'

  - name: 'environmentName'
    value: 'app-uhi'

  - name: 'vmImageName'
    value: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)

    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '18.x'
      displayName: 'Install Node.js'

    - script: |
        npm config set registry https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/
        npm config set //pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/:_authToken $personal_Token
        npm install
        npm run build --if-present
      displayName: 'npm install, build'
      env:
        personal_Token: $(package_token)
        
    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)/dist'
        includeRootFolder: false
        archiveType: zip
        archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
        replaceExistingArchive: true

    - upload: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
      artifact: drop