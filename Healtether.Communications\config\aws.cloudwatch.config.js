//import AWS from 'aws-sdk';
import dotenv from 'dotenv';

let env=dotenv.config();
if (process.env.ENV !== "dev") {
  // Configure AWS credentials and region
  // AWS.config.update({
  //   accessKeyId: process.env.CLOUD_WATCH_ACCESSKEY,
  //   secretAccessKey: process.env.CLOUD_WATCH_SECRET,
  //   region: process.env.CLOUD_WATCH_REGION
  // });
}

// Create a CloudWatch Logs client
//const cloudWatchLogs = new AWS.CloudWatchLogs();

// Define the log group and stream names
//   const logGroupName = '<YOUR_LOG_GROUP_NAME>';
//   const logStreamName = '<YOUR_LOG_STREAM_NAME>';

// Function to write a log message to CloudWatch Logs
// const logMessage = (message) => {
//   const params = {
//     logGroupName: process.env.CLOUD_WATCH_LOG_GROUP,
//     logStreamName: process.env.CLOUD_WATCH_LOG_STREAM,
//     logEvents: [
//       {
//         message: message,
//         timestamp: Date.now()
//       }
//     ]
//   };

//   cloudWatchLogs.putLogEvents(params, (error, data) => {
//     if (error) {
//       console.log('Error writing log:', error);
//     } else {
//       console.log('Log written successfully:', data);
//     }
//   });
// };

// export const LogExceptionInCloudWatch = (exception) => {
//   if (process.env.ENV === "dev") {
//     console.error(`Error in Application ${exception}`);
//   }
//   else {
//     logMessage(`Error: ${exception.message}\nStack: ${exception.stack}`);
//   }
// }
