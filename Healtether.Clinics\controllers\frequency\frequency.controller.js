import {
  getSymptom,
  getDiagnosis,
  getLabTest,
  getDrugs,
  upsertDiagnosis,
  upsertDrugs,
  upsertLabtest,
  upsertSymptoms,
} from "../../helpers/frequency/frequency.helper.js";
export const getFrequentTextForPrescription = async (req, res) => {
  const clinicId = req.query.clinicId;
  let frequency = {};
  frequency.symptoms = await getSymptom(clinicId);
  frequency.diagnosis = await getDiagnosis(clinicId);
  frequency.labtest = await getLabTest(clinicId);
  frequency.drugs = await getDrugs(clinicId);
  res.json(frequency).status(200);
};

export const upsertFrequentTextForPrescription = async (data, clinicId) => {
  await upsertDiagnosis(data.diagnosis, clinicId);
  await upsertDrugs(data.drugs, clinicId);
  await upsertLabtest(data.labTests, clinicId);
  await upsertSymptoms(data.symptoms, clinicId);
};
