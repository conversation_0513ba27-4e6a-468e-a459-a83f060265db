import { useState, useEffect } from "react";
import { useLoaderData, useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

// Components
import ViewPersonalDetails from "components/view-page/ViewPersonalDetails";
import ViewDocument from "components/view-page/ViewDocument";
import MedicalRecords from "../../../../components/detail-page/medical-records/MedicalRecords";
import AbhaConsents from "../../../../components/detail-page/abha-consents/AbhaConsents";

// ABHA components
import AddAbhaUsingMobile from "../../../../components/detail-page/add-abha/AddAbhaUsingMobile";
import AddAbhaUsingAadharNum from "../../../../components/detail-page/add-abha/AddAbhaUsingAadhaar";
import AbhaCreation from "../../../../components/detail-page/add-abha/AbhaCreation";
import AddAbhaUsingAddress from "../../../../components/detail-page/add-abha/AddAbhaUsingAddress";

// Services
import {
  GetAppointmentConsultation,
  SetStartConsultation,
} from "../../../../services/appointment/appointment";

// Custom hook for dropdown functionality
import dropdown from "../../../../components/detail-page/dropdown";

// Import the MedicalReportSidebar component
// import MedicalReportSidebar from "./MedicalReportSidebar";

export async function EditAppointmentConsultationLoader({ params }) {
  var consultation = null;
  if (params?.id != undefined) {
    consultation = await GetAppointmentConsultation(params.id);
    return { consultation };
  }
  return { consultation };
}

function ViewAppointmentAndPatient_V2() {
  const { isOpen, setIsOpen, DropdownMenu, DropdownMenuItem, DropdownMenuContent } = dropdown();
  const { user } = useSelector((state) => state.user);
  const [patientMobile, setpatientMobile] = useState("");
  const [abhaNumber, setAbhaNumber] = useState("");
  const [address, setAddress] = useState("");
  const { consultation } = useLoaderData();
  const patientData = consultation != null ? consultation?.patientId : {};
  const isEnded = consultation?.ended?.yes;
  
  // State for tabs and UI
  const [activeTab, setActiveTab] = useState(0);
  const [consultationStarted, setConsultationStarted] = useState(false);
  const [callEndConsultation, SetCallEndConsultation] = useState(false);
  const [consultationRecord, SetConsultationRecord] = useState(consultation);

  // ABHA related states
  const [addAbhaUsing, setAddAbhaUsing] = useState("");
  const [isCreatingAddress, setIsCreatingAddress] = useState(false);
  const [grantedConsentAbhaRecords, setGrantedConsentAbhaRecords] = useState(
    []
  );
  const [followUpDates, setFollowUpDates] = useState({
    current: "None",
    list: [
      "None",
      "After 3 days",
      "After 5 days",
      "After a week",
      "After 2 weeks",
      "After a month",
    ],
  });

  // Tab state
  const [tab, setTab] = useState({
    current: "Details",
    list: ["Details", "Medical records", "ABHA records", "ABHA consents"],
  });

  // Menu state
  const settingsArray = [
    { name: "Details", isVisible: true, isEnabled: true },
    { name: "Medical records", isVisible: true, isEnabled: true },
    { name: "ABHA records", isVisible: true, isEnabled: true },
    { name: "ABHA consents", isVisible: true, isEnabled: true },
  ];
  const [patientMenu, setPatientMenu] = useState(settingsArray);

  const navigate = useNavigate();

  // Handle tab changes
  useEffect(() => {
    // Find the tab button and programmatically click it
    const tabId = `tab-${tab.list[activeTab].toLowerCase().replace(/\s+/g, '')}`;
    const tabButton = document.getElementById(tabId);
    if (tabButton) {
      tabButton.click();
    }
  }, [activeTab, tab]);

  const handleConsultation = async (e, id) => {
    console.log(e, id);
    if (isEnded) return false;

    let arrayMenu = [...patientMenu];
    arrayMenu[0].isVisible = true;
    arrayMenu[0].isEnabled = true;
    for (let j = 1; j < arrayMenu.length; j++) {
      arrayMenu[j].isEnabled = false;
    }
    
    if (!(consultation?.started?.yes == true)) {
      await SetStartConsultation(consultation?._id);
    }
    
    setPatientMenu(arrayMenu);
    setActiveTab(0);
    setConsultationStarted(true);
  };

  const endConsultation = () => SetCallEndConsultation(!callEndConsultation);

  const { mobile = "", patientId = "" } = patientData;

  // Props for components
  const props = {
    addAbhaUsing,
    setAddAbhaUsing,
    tab,
    setpatientMobile,
    patientMobile,
    setAbhaNumber,
    abhaNumber,
    setAddress,
    address,
    patientData,
    setActiveTab,
    consultation,
  };

  const state= useLocation().state;
  useEffect(()=>{
    state?.openMedicalRecordsTab&&setActiveTab(1);
    state?.openAbhaConsentTab&&setActiveTab(2);
  },[])

  const handleTabClick = (index) => {
    setActiveTab(index);
    setTab({ ...tab, current: tab.list[index] });
  };
  return (
    <div className="h-full">
      {/*-------------------- Modals & more  --------------------*/}

      <AddAbhaUsingMobile
        addAbhaUsing={addAbhaUsing}
        setAddAbhaUsing={setAddAbhaUsing}
        patientMobile={patientMobile}
        setpatientMobile={setpatientMobile}
        OnSelectPatient={() => {}}
        setAddress={setAddress}
        address={address}
      />
      <AddAbhaUsingAadharNum
        addAbhaUsing={addAbhaUsing}
        setAddAbhaUsing={setAddAbhaUsing}
        setpatientMobile={setpatientMobile}
        OnSelectPatient={() => {}}
      />
      <AbhaCreation
        addAbhaUsing={addAbhaUsing}
        setAddAbhaUsing={setAddAbhaUsing}
        setIsCreatingAddress={setIsCreatingAddress}
        isCreatingAddress={isCreatingAddress}
        setAddress={setAddress}
        address={address}
        OnSelectPatient={() => {}}
        setpatientMobile={setpatientMobile}
        patientMobile={patientMobile}
        user={user}
      />
      <AddAbhaUsingAddress
        addAbhaUsing={addAbhaUsing}
        setAddAbhaUsing={setAddAbhaUsing}
        setpatientMobile={setpatientMobile}
        OnSelectPatient={() => {}}
      />
      {/*-------------------- End of modals & more  --------------------*/}
      <nav className="navbar rounded-box justify-between border shadow-sm h-[10%] ">
        <div className="navbar-start">
          <section className="flex items-center gap-2">
            <div className="avatar avatar-placeholder">
              <div className="bg-color_muted/20 text-neutral-content w-10 rounded-full">
                <span className="text-lg uppercase text-black font-primary">
                  {" "}
                  {patientData?.firstName?.substring(0, 1) +
                    patientData?.lastName?.substring(0, 1)}
                </span>
              </div>
            </div>

            <div className="text-xs text-dark">
              <div className="flex items-center gap-1.5">
                <div className="text-base text-blue font-medium font-primary text-accent">
                  {patientData?.firstName + " " + patientData?.lastName}
                </div>
                <div>{patientData?.gender},</div>
                <div>{patientData?.age}yrs</div>
              </div>
              <div className="tracking-wide -mt-0.5">
                +91 {mobile?.substring?.(0, 4)} {mobile?.slice?.(5)}
              </div>
            </div>
          </section>
        </div>

        <div className="navbar-center flex items-center">
          <section className="">
            <div className="text-md text-dark font-normal font-primary">
              Patient ID : {patientId}
            </div>
            {patientData.abhaAddress ? (
              <div className="flex items-center gap-1 text-color_muted font-normal text-md">
                <span className="icon-[ph--flower-lotus-light] dropdown-open:rotate-180 size-4"></span>
                ABHA: {patientData.abhaAddress}
              </div>
            ) : (
              <div className="dropdown relative text-md inline-flex max-sm:[--placement:bottom-end]">
                <button
                  id="dropdown-input"
                  type="button"
                  className="dropdown-toggle btn btn-soft btn-primary join-item "
                  aria-haspopup="menu"
                  aria-expanded="false"
                  aria-label="Dropdown"
                >
                  <span className="icon-[ph--flower-lotus-light] dropdown-open:rotate-180 size-4"></span>
                  <span className="hidden sm:block">Add ABHA</span>
                  <span className="icon-[tabler--chevron-down] dropdown-open:rotate-180 size-4"></span>
                </button>
                <ul
                  className="dropdown-menu dropdown-open:opacity-100 hidden  cursor-pointer"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="dropdown-input"
                >
                  <li>
                    <div
                      className="dropdown-item"
                      onClick={() => setAddAbhaUsing("mobile number")}
                    >
                      Add using mobile number
                    </div>
                  </li>
                  <li>
                    <div
                      className="dropdown-item"
                      onClick={() => setAddAbhaUsing("Aadhar number")}
                    >
                      {" "}
                      Add using Aadhar number
                    </div>
                  </li>
                  <li>
                    <div
                      className="dropdown-item"
                      onClick={() => setAddAbhaUsing("address")}
                    >
                      {" "}
                      Add using ABHA Address
                    </div>
                  </li>
                  <li>
                    <div
                      className="dropdown-item"
                      onClick={() => setAddAbhaUsing("abha creation")}
                    >
                      ABHA Creation
                    </div>
                  </li>
                </ul>
              </div>
            )}
          </section>
        </div>

        <div className="navbar-end">
          <button
            className="btn btn-primary waves waves-light text-md font-normal"
            type="button"
            onClick={() =>
              navigate(
                "/patient/viewpatient/" + consultation?.patientId?._id + "/edit"
              )
            }
          >
            Edit Profile
          </button>
        </div>
      </nav>

      <div className="flex mt-2 h-[88%] ">
        <nav
          className="tabs flex-col items-start space-y-1 font-primary text-base font-medium gap-3 w-2/12"
          aria-label="Tabs"
          role="tablist"
          data-tabs-vertical="true"
          aria-orientation="horizontal"
        >
          <button
            type="button"
            className={`btn btn-text btn-secondary ${
              activeTab === 0 ? "bg-secondary/10 text-black" : ""
            } hover:text-secondary hover:bg-secondary/10 active w-full px-10 py-7 justify-start`}
            id="tab-patientdetails"
            onClick={() => handleTabClick(0)}
          >
            Details
          </button>
          <button
            type="button"
            className={`btn btn-text btn-secondary ${
              activeTab === 1 ? "bg-secondary/10 text-black" : ""
            } hover:text-secondary hover:bg-secondary/10 w-full px-10 py-7 justify-start`}
            id="tab-medicalrecords"
            onClick={() => handleTabClick(1)}
          >
            Medical Records
          </button>
          <button
            type="button"
            className={`btn btn-text btn-secondary ${
              activeTab === 2 ? "bg-secondary/10 text-black" : ""
            } hover:text-secondary hover:bg-secondary/10 w-full px-10 py-7 justify-start`}
            id="tab-abharecords"
            onClick={() => handleTabClick(2)}
          >
            ABHA Records
          </button>
          <button
            type="button"
            className={`btn btn-text btn-secondary ${
              activeTab === 3 ? "bg-secondary/10 text-black" : ""
            } hover:text-secondary hover:bg-secondary/10 w-full px-10 py-7 justify-start`}
            id="tabs-abhaconsents"
            onClick={() => handleTabClick(3)}
          >
            ABHA Consents
          </button>
        </nav>

        <div className="w-10/12 ml-2 h-full">
          {activeTab == 0 && (
            <div
              id="tab-patientdetails-data"
              className=" flex flex-col h-full px-8 pt-2 bg-backcolor_detailpage"
              role="tabpanel"
              aria-labelledby="tab-patientdetails"
            >
              <ViewPersonalDetails obj={patientData} type={"patient"} />
              <ViewDocument obj={patientData} />
            </div>
          )}
          {activeTab == 1 && (
            <div
              id="tab-medicalrecords-data"
              className="h-full"
              role="tabpanel"
              aria-labelledby="tab-medicalrecords"
            >
              <MedicalRecords {...props} type="medical record" />
            </div>
          )}
          {activeTab == 2 && (
            <div
              id="tab-abharecords-data"
              className="h-full"
              role="tabpanel"
              aria-labelledby="tab-abharecords"
            >
              <MedicalRecords
                {...props}
                patientData={patientData}
                consultation={consultation}
                type="abha record"
                grantedConsentAbhaRecords={grantedConsentAbhaRecords}
              />
            </div>
          )}
          {activeTab == 3 && (
            <div
              id="tabs-abhaconsents-data"
              className="h-full"
              role="tabpanel"
              aria-labelledby="tabs-abhaconsents"
            >
              <AbhaConsents
                {...props}
                patientData={patientData}
                setGrantedConsentAbhaRecords={setGrantedConsentAbhaRecords}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ViewAppointmentAndPatient_V2;