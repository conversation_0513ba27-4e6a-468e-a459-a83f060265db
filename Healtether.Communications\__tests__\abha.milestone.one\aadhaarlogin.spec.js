import { jest, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';

// Mock the module before import
await jest.unstable_mockModule("../../helper/abha/milestone.one.helper.js", () => ({
  loginUsingAadhar: jest.fn(),
  abhaCardCreation: jest.fn(),
  abhaNumberAddressotp: jest.fn(),
  enrollMobileNoOTP: jest.fn(),
  enrollUsingAadhaar: jest.fn(),
  enrollUsingAbhaddress: jest.fn(),
  enrollUsingMobile: jest.fn(),
  getAbhaAddressCard: jest.fn(),
  getAbhaAddressSuggestion: jest.fn(),
  getAbhaCard: jest.fn(),
  loginUsingIndexMobile: jest.fn(),
  patientProfileOnShare: jest.fn(),
  qrCode: jest.fn(),
  searchLogin: jest.fn(),
  userAbhaAddressProfile: jest.fn(),
  userProfile: jest.fn(),
  verifyAbhanumberAddressOtp: jest.fn(),
  verifyMobileNoOtp: jest.fn(),
}));

// Import modules after mocking
const { loginUsingAadhar, getAbhaAddressCard,abhaNumberAddressotp,abhaCardCreation,enrollUsingAadhaar,enrollUsingAbhaddress,enrollUsingMobile,enrollMobileNoOTP,userAbhaAddressProfile,getAbhaCard,userProfile,loginUsingIndexMobile, getAbhaAddressSuggestion,verifyAbhanumberAddressOtp,verifyMobileNoOtp,searchLogin} = await import('../../helper/abha/milestone.one.helper.js');
const { LogDBCloseConnection } = await import('../../config/whatsapp.collections.config.js');
const { CloseConnection } = await import('../../config/clinics.collections.config.js');
const { app, server } = await import('../../index.js');

afterAll(async () => {
  await CloseConnection();
  await LogDBCloseConnection();
  server.close();
});

describe('POST /api/abha/aadhaarlogin', () => {
  test('should return 200 and OTP sent message when aadhaarNumber is valid', async () => {
    loginUsingAadhar.mockResolvedValueOnce({
      isSuccess: true,
      response: {
        txnId: '10b63d04-6c97-462e-9cd0-cf2eebd8fe90',
        message: 'OTP sent to Aadhaar registered mobile number ending with ******2837',
      },
    });

    const response = await request(app).post('/api/abha/aadhaarlogin').send({ aadhaarNumber: '************' });

    expect(response.status).toBe(200);
    expect(response.body?.response?.message).toBe(
      'OTP sent to Aadhaar registered mobile number ending with ******2837'
    );
  });

  it('should return 500 and an error message when loginUsingAadhar fails', async () => {
    loginUsingAadhar.mockResolvedValueOnce({
      isSuccess: false,
      response: null,
    });

    const response = await request(app).post('/api/abha/aadhaarlogin').send({ aadhaarNumber: 'invalid-aadhaar' });

    expect(response.status).toBe(500);
    expect(response.body.isSuccess).toBe(false);
    expect(response.body.response).toBeNull();
    expect(loginUsingAadhar).toHaveBeenCalledWith('invalid-aadhaar');
  });
});

describe('GET /abhaaddresscard', () => {
  it('should return an image when getAbhaAddressCard succeeds', async () => {
    const mockImageBuffer = Buffer.from('mockImageData');

    getAbhaAddressCard.mockResolvedValueOnce({
      isSuccess: true,
      response: mockImageBuffer,
    });

    const response = await request(app).get('/api/abha/abhaaddresscard').set('x-token', 'valid-token');

    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toBe('image/png');
    expect(response.headers['content-disposition']).toBe('inline; filename="abha-card.png"');

    // Compare the response as a buffer
    expect(Buffer.isBuffer(response.body)).toBe(true);
    expect(getAbhaAddressCard).toHaveBeenCalledWith('valid-token');
  });
});


describe('POST /api/abha/abhanumberaddressotp', () => {
  it('should return 200 and success response for valid input', async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        txnId: "1b68b381-a479-4f39-8d67-246528ba5117",
        message: "OTP is sent to Mobile number ending with ******3133",
      },
    };

    abhaNumberAddressotp.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/abhanumberaddressotp')
      .send({
        abhaNumberAddress: "testm_233@sbx",
        type: "abhaAddressMobileOtp",
      });

    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
    expect(abhaNumberAddressotp).toHaveBeenCalledWith({
      type: "abhaAddressMobileOtp",
      abhaNumberAddress: "testm_233@sbx",
    });
  });

  it('should return 500 and invalid response for invalid input', async () => {
    const mockResponse = {
      isSuccess: false,
      response: {
        loginId: "Invalid LoginId",
        timestamp: "2025-01-14 00:08:24",
      },
    };

    abhaNumberAddressotp.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/abhanumberaddressotp')
      .send({
        abhaNumberAddress: "929",
        type: "abhaNumberMobileLogin",
      });

    expect(res.status).toBe(500);
    expect(res.body).toEqual(mockResponse);
    expect(abhaNumberAddressotp).toHaveBeenCalledWith({
      type: "abhaNumberMobileLogin",
      abhaNumberAddress: "929",
    });
  });

});


describe('POST /createabhacard', () => {
  it('should return a success response when abhaCardCreation succeeds', async () => {
    abhaCardCreation.mockResolvedValueOnce({
      isSuccess: true,
      response: {
        txnId: '4c11bf9c-cac0-41f8-9608-ecf5537aa738',
        message: 'OTP sent to Aadhaar registered mobile number ending with ******2837',
      },
    });

    const response = await request(app)
      .post('/api/abha/createabhacard')
      .send({ aadhaarNumber: '************' });

    expect(response.status).toBe(200);
    expect(response.body.isSuccess).toBe(true);
    expect(response.body.response.txnId).toBe('4c11bf9c-cac0-41f8-9608-ecf5537aa738');
    expect(response.body.response.message).toBe('OTP sent to Aadhaar registered mobile number ending with ******2837');
    expect(abhaCardCreation).toHaveBeenCalledWith('************');
  });

  it('should return 500 if abhaCardCreation fails', async () => {
    abhaCardCreation.mockResolvedValueOnce({
      isSuccess: false,
      response: null,
    });

    const response = await request(app)
      .post('/api/abha/createabhacard')
      .send({ aadhaarNumber: '************' });

    expect(response.status).toBe(500);
    expect(response.body.isSuccess).toBe(false);
    expect(abhaCardCreation).toHaveBeenCalledWith('************');
  });
});

describe('POST /api/abha/enrollbyaadhaar', () => {
    it('should return 200 and success response when a new account is created', async () => {
        const mockResponse = {
          isSuccess: true,
          response: {
            message: "Account created successfully",
            txnId: "ceeadebe-ef35-4f0f-a4df-f764f3b8928e",
            tokens: {
              token: "new-test-token",
              expiresIn: 1800,
              refreshToken: "new-refresh-token",
              refreshExpiresIn: 1296000,
            },
            ABHAProfile: {
              firstName: "Mohit",
              middleName: "",
              lastName: "Kushwah",
              dob: "24-07-2006",
              gender: "M",
            },
            isNew: true,
          },
        };
    
        enrollUsingAadhaar.mockResolvedValue(mockResponse);
    
        const res = await request(app)
          .post('/api/abha/enrollbyaadhaar')
          .send({ mobileNumber: '**********', otp: '123456', txnId: 'ceeadebe-ef35-4f0f-a4df-f764f3b8928e' });
    
        expect(res.status).toBe(200);
        expect(res.body).toEqual(mockResponse);
        expect(enrollUsingAadhaar).toHaveBeenCalledWith('**********', '123456', 'ceeadebe-ef35-4f0f-a4df-f764f3b8928e');
      });
  it('should return 200 and success response when enrollUsingAadhaar is successful', async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        message: "This account already exist",
        txnId: "4c11bf9c-cac0-41f8-9608-ecf5537aa738",
        tokens: {
          token: "test-token",
          expiresIn: 1800,
          refreshToken: "test-refresh-token",
          refreshExpiresIn: 1296000,
        },
        ABHAProfile: {
          firstName: "Sobran",
          lastName: "Singh",
          dob: "01-01-1975",
          gender: "M",
        },
      },
    };

    enrollUsingAadhaar.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/enrollbyaadhaar')
      .send({ mobileNumber: '**********', otp: '123456', txnId: '4c11bf9c-cac0-41f8-9608-ecf5537aa738' });

    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
    expect(enrollUsingAadhaar).toHaveBeenCalledWith('**********', '123456', '4c11bf9c-cac0-41f8-9608-ecf5537aa738');
  });

  it('should return 500 and error response when enrollUsingAadhaar fails', async () => {
    const mockResponse = {
      isSuccess: false,
      response: {
        message: "Enrollment failed",
      },
    };

    enrollUsingAadhaar.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/enrollbyaadhaar')
      .send({ mobileNumber: '**********', otp: '654321', txnId: '4c11bf9c-cac0-41f8-9608-ecf5537aa738' });

    expect(res.status).toBe(500);
    expect(res.body).toEqual(mockResponse);
    expect(enrollUsingAadhaar).toHaveBeenCalledWith('**********', '654321', '4c11bf9c-cac0-41f8-9608-ecf5537aa738');
  });
});


describe('POST /api/abha/enrollbyabhaaddress', () => {
  it('should return 500 and invalid ABHA address response for invalid input', async () => {
    const mockResponse = {
      isSuccess: false,
      response: {
        abhaAddress: "Invalid ABHA Address",
        timestamp: "2025-01-14 00:00:48",
      },
    };

    enrollUsingAbhaddress.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/enrollbyabhaaddress')
      .send({
        txnId: "0c19ba43-c723-4d70-b1a9-b6f5e2eff8fc",
        abhaAddress: "test",
        preferred: 1,
      });

    expect(res.status).toBe(500);
    expect(res.body).toEqual(mockResponse);
    expect(enrollUsingAbhaddress).toHaveBeenCalledWith(
      "0c19ba43-c723-4d70-b1a9-b6f5e2eff8fc",
      "test",
      1
    );
  });

  it('should return 200 and success response for valid input', async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        txnId: "0c19ba43-c723-4d70-b1a9-b6f5e2eff8fc",
        healthIdNumber: "91-5557-2174-1018",
        preferredAbhaAddress: "sobran_75@sbx",
      },
    };

    enrollUsingAbhaddress.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/enrollbyabhaaddress')
      .send({
        txnId: "0c19ba43-c723-4d70-b1a9-b6f5e2eff8fc",
        abhaAddress: "sobran_75",
        preferred: 1,
      });

    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
    expect(enrollUsingAbhaddress).toHaveBeenCalledWith(
      "0c19ba43-c723-4d70-b1a9-b6f5e2eff8fc",
      "sobran_75",
      1
    );
  });

});


describe('POST /api/abha/enrollbymobile', () => {
  it('should return 200 and success response when enrollUsingMobile is successful', async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        txnId: "4c11bf9c-cac0-41f8-9608-ecf5537aa738",
        message: "OTP sent to  mobile number ending with ******2837",
      },
    };

    enrollUsingMobile.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/enrollbymobile')
      .send({ mobile: '**********', txnId: '4c11bf9c-cac0-41f8-9608-ecf5537aa738' });

    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
    expect(enrollUsingMobile).toHaveBeenCalledWith('**********', '4c11bf9c-cac0-41f8-9608-ecf5537aa738');
  });

  it('should return 500 and error response when enrollUsingMobile fails', async () => {
    const mockResponse = {
      isSuccess: false,
      response: {
        message: "Enrollment failed",
      },
    };

    enrollUsingMobile.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/enrollbymobile')
      .send({ mobile: '**********', txnId: '4c11bf9c-cac0-41f8-9608-ecf5537aa738' });

    expect(res.status).toBe(500);
    expect(res.body).toEqual(mockResponse);
    expect(enrollUsingMobile).toHaveBeenCalledWith('**********', '4c11bf9c-cac0-41f8-9608-ecf5537aa738');
  });
});

describe('POST /api/abha/enrollmobileotp', () => {
  it('should return 200 and success response when enrollMobileNoOTP succeeds', async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        message: "OTP verified and account created successfully",
        txnId: "12345-test-txn-id",
      },
    };

    enrollMobileNoOTP.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/enrollmobileotp')
      .send({
        mobileNumber: '**********',
        otp: '123456',
        txnId: '12345-test-txn-id',
      });

    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
    expect(enrollMobileNoOTP).toHaveBeenCalledWith('**********', '123456', '12345-test-txn-id');
  });

  it('should return 500 and error response when enrollMobileNoOTP fails', async () => {
    const mockResponse = {
      isSuccess: false,
      response: {
        message: "OTP verification failed",
      },
    };

    enrollMobileNoOTP.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/enrollmobileotp')
      .send({
        mobileNumber: '**********',
        otp: '000000',
        txnId: '12345-test-txn-id',
      });

    expect(res.status).toBe(500);
    expect(res.body).toEqual(mockResponse);
    expect(enrollMobileNoOTP).toHaveBeenCalledWith('**********', '000000', '12345-test-txn-id');
  });


});

describe('GET /api/abha/getuserabhaaddressprofile', () => {
  it('should return the user Abha address profile when x-token is valid', async () => {

    userAbhaAddressProfile.mockResolvedValueOnce({
      isSuccess: true,
      response: {
        abhaAddress: 'testm_233@sbx',
        fullName: 'Monika Kushwah',
        abhaNumber: '91-1248-5708-0632',
        stateName:"MADHYA PRADESH"
      },
    });

    const response = await request(app)
      .get('/api/abha/getuserabhaaddressprofile')
      .set('x-token', 'valid-token'); 

    expect(response.status).toBe(200);
    expect(response.body.isSuccess).toBe(true);
    expect(response.body.response.abhaAddress).toBe('testm_233@sbx');
    expect(response.body.response.fullName).toBe('Monika Kushwah');
    expect(response.body.response.stateName).toBe('MADHYA PRADESH');
    expect(userAbhaAddressProfile).toHaveBeenCalledWith('valid-token');
  });

  it('should return 500 if userAbhaAddressProfile fails', async () => {
    userAbhaAddressProfile.mockResolvedValueOnce({
      isSuccess: false,
      response: null,
    });

    const response = await request(app)
      .get('/api/abha/getuserabhaaddressprofile')
      .set('x-token', 'invalid-token'); // Mock an invalid x-token

    expect(response.status).toBe(500);
    expect(response.body.isSuccess).toBe(false);
    expect(userAbhaAddressProfile).toHaveBeenCalledWith('invalid-token');
  });


});

describe('GET /api/abha/abhacard', () => {
  it('should return an image when getAbhaCard succeeds', async () => {
    const mockImageBuffer = Buffer.from('mockImageData');

    getAbhaCard.mockResolvedValueOnce({
      isSuccess: true,
      response: mockImageBuffer,
    });

    const response = await request(app)
      .get('/api/abha/abhacard')
      .set('x-token', 'valid-token');

    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toBe('image/png');
    expect(response.headers['content-disposition']).toBe('inline; filename="abha-card.png"');
    expect(response.body).toEqual(expect.any(Buffer));
    expect(getAbhaCard).toHaveBeenCalledWith('valid-token');
  });

//   it('should return 500 if getAbhaCard fails', async () => {
//     getAbhaCard.mockResolvedValueOnce({
//       isSuccess: false,
//       response: null,
//     });

//     const response = await request(app)
//       .get('/api/abhacard')
//       .set('x-token', 'invalid-token');

//     expect(response.status).toBe(500);
//     expect(getAbhaCard).toHaveBeenCalledWith('invalid-token');
//   });

});

describe('GET /api/abha/getuserprofile', () => {
  it('should call getUserProfile and return a successful response', async () => {
    // Mock the service response
    const mockResponse = {
      isSuccess: true,
      response: {
        ABHANumber: '91-7214-2788-0670',
        preferredAbhaAddress: 'senthamaraip1003@sbx',
        mobile: '9329473133',
      },
    };
    userProfile.mockResolvedValue(mockResponse);

    const res = await request(app)
      .get('/api/abha/getuserprofile')
      .set('x-token', 'test-token');

    expect(userProfile).toHaveBeenCalledWith('test-token');
    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
  });
});


describe('POST /api/abha/indexlogin', () => {
  it('should return 200 and a valid response structure when loginUsingIndexMobile succeeds', async () => {
    const mockResult = {
      isSuccess: true,
      response: {
        txnId: '17fb99ec-5aa4-433c-bf2c-4655cd721b12',
        message: 'OTP sent to mobile number ending with ******3133',
      },
    };

    loginUsingIndexMobile.mockResolvedValue(mockResult);

    const response = await request(app)
      .post('/api/abha/indexlogin')
      .send({ index: 1, txnId: '17fb99ec-5aa4-433c-bf2c-4655cd721b12' });

    // Assertions
    expect(response.status).toBe(200);
    expect(response.body.isSuccess).toBe(true);
    expect(response.body.response).toBeDefined();
    expect(response.body.response).toHaveProperty('txnId', '17fb99ec-5aa4-433c-bf2c-4655cd721b12');
    expect(response.body.response).toHaveProperty('message', 'OTP sent to mobile number ending with ******3133');
  });

  it('should return 500 and an error message when loginUsingIndexMobile fails', async () => {
    const mockResult = {
      isSuccess: false,
      error: 'Invalid transaction ID or index',
    };

    loginUsingIndexMobile.mockResolvedValue(mockResult);

    const response = await request(app)
      .post('/api/abha/indexlogin')
      .send({ index: 1, txnId: 'invalid-txn-id' });

    // Assertions
    expect(response.status).toBe(500);
    expect(response.body.isSuccess).toBe(false);
    expect(response.body).toHaveProperty('error', 'Invalid transaction ID or index');
  });
});


describe('POST /api/abha/searchabha', () => {
  it('should return 200 and a valid response structure when searchLogin succeeds', async () => {
    const mockResult = {
      isSuccess: true,
      response: [
        {
          txnId: '6fee6bd3-b1e1-4699-96a8-27d985f01261',
          ABHA: [
            {
              index: 1,
              ABHANumber: 'xx-xxxx-xxxx-0670',
              name: 'Senthamarai P',
              gender: 'F',
            },
            {
              index: 2,
              ABHANumber: 'xx-xxxx-xxxx-0632',
              name: 'Monika Kushwah',
              gender: 'F',
            },
          ],
        },
      ],
    };
    searchLogin.mockResolvedValue(mockResult);

    const response = await request(app)
      .post('/api/abha/searchabha')
      .send({ mobile: '9329473133' });

    // Assertions
    expect(response.status).toBe(200);
    expect(response.body.isSuccess).toBe(true);
    expect(response.body.response).toBeInstanceOf(Array);
    expect(response.body.response[0]).toHaveProperty('txnId', '6fee6bd3-b1e1-4699-96a8-27d985f01261');
    expect(response.body.response[0].ABHA).toBeInstanceOf(Array);
    expect(response.body.response[0].ABHA).toHaveLength(2);
    expect(response.body.response[0].ABHA[0]).toEqual({
      index: 1,
      ABHANumber: 'xx-xxxx-xxxx-0670',
      name: 'Senthamarai P',
      gender: 'F',
    });
    expect(response.body.response[0].ABHA[1]).toEqual({
      index: 2,
      ABHANumber: 'xx-xxxx-xxxx-0632',
      name: 'Monika Kushwah',
      gender: 'F',
    });
  });

  it('should return 500 and an error message when searchLogin fails', async () => {
    const mockResult = {
      isSuccess: false,
      error: 'Unable to process the request',
    };
    searchLogin.mockResolvedValue(mockResult);

    const response = await request(app)
      .post('/api/abha/searchabha')
      .send({ mobile: '9329473133' });
    expect(response.status).toBe(500);
    expect(response.body.isSuccess).toBe(false);
    expect(response.body).toHaveProperty('error', 'Unable to process the request');
  });


  it('should return 404 and a "User not found" error when searchLogin returns user not found error (ABDM-1114)', async () => {
    const mockResult = {
      isSuccess: false,
      error: {
        code: 'ABDM-1114',
        message: 'User not found.',
      },
    };
    searchLogin.mockResolvedValue(mockResult);

    const response = await request(app)
      .post('/api/abha/searchabha')
      .send({ mobile: '9399895119' });

    // Assertions
    expect(response.status).toBe(500); // User not found is a 404 error
    expect(response.body.isSuccess).toBe(false);
    expect(response.body.error).toEqual({
      code: 'ABDM-1114',
      message: 'User not found.',
    });
  });
  it('should return 400 and an "Invalid Mobile Number" error when the mobile number is invalid', async () => {
    const mockResult = {
      isSuccess: false,
      response: {
        mobile: 'Invalid Mobile Number',
        timestamp: '2025-01-13 20:25:47',
      },
    };
    searchLogin.mockResolvedValue(mockResult);

    const response = await request(app)
      .post('/api/abha/searchabha')
      .send({ mobile: 'invalidMobile' });

    // Assertions
    expect(response.status).toBe(500); 
    expect(response.body.isSuccess).toBe(false);
    expect(response.body).toHaveProperty('response');
    expect(response.body.response).toEqual({
      mobile: 'Invalid Mobile Number',
      timestamp: '2025-01-13 20:25:47',
    });
  });
});


describe('POST /api/abha/suggestion', () => {
  it('should return 200 and success response when getAbhaAddressSuggestion is successful', async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        txnId: "65a48d6e-b73b-4e82-9757-27dba7eabe62",
        abhaAddressList: [
          "sobransingh197501",
          "sobransingh1975",
          "sobransingh011975",
          "sobransingh01",
          "sobran_s.75",
          "sobran_s.19",
          "sobran_75",
          "sobran_1975197501",
          "sobran_1975011975",
          "sobran_1975",
        ],
      },
    };

    getAbhaAddressSuggestion.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/suggestion')
      .send({ txnId: "65a48d6e-b73b-4e82-9757-27dba7eabe62" });

    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
    expect(getAbhaAddressSuggestion).toHaveBeenCalledWith("65a48d6e-b73b-4e82-9757-27dba7eabe62");
  });

  it('should return 500 and error response when getAbhaAddressSuggestion fails', async () => {
    const mockResponse = {
      isSuccess: false,
      response: {
        message: "Failed to fetch suggestions",
      },
    };

    getAbhaAddressSuggestion.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/suggestion')
      .send({ txnId: "65a48d6e-b73b-4e82-9757-27dba7eabe62" });

    expect(res.status).toBe(500);
    expect(res.body).toEqual(mockResponse);
    expect(getAbhaAddressSuggestion).toHaveBeenCalledWith("65a48d6e-b73b-4e82-9757-27dba7eabe62");
  });
});

describe('POST /api/abha/verifyabhanumberaddressotp', () => {
  it('should return 200 and failure response for incorrect OTP', async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        txnId: "4f24ac5b-7162-4fc6-9ab9-48652de125f8",
        message: "Entered OTP is incorrect. Kindly re-enter valid OTP.",
        authResult: "failed",
        users: []
      },
    };

    verifyAbhanumberAddressOtp.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/verifyabhanumberaddressotp')
      .send({
        otp: "343532",
        txnId: "4f24ac5b-7162-4fc6-9ab9-48652de125f8",
        type: "verifyAbhaMobileOtp",
      });

    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
    expect(verifyAbhanumberAddressOtp).toHaveBeenCalledWith("verifyAbhaMobileOtp", "343532", "4f24ac5b-7162-4fc6-9ab9-48652de125f8");
  });

  it('should return 200 and success response for correct OTP', async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        message: "OTP verified successfully",
        authResult: "success",
        users: [
          {
            abhaAddress: "testm_233@sbx",
            fullName: "Monika Kushwah",
            abhaNumber: "91-1248-5708-0632",
            status: "ACTIVE",
            kycStatus: "VERIFIED",
          },
        ],
        tokens: {
          token: "someValidToken",
          expiresIn: 1800,
          refreshToken: "someValidRefreshToken",
          refreshExpiresIn: 1296000,
          switchProfileEnabled: false,
        },
      },
    };

    verifyAbhanumberAddressOtp.mockResolvedValue(mockResponse);

    const res = await request(app)
      .post('/api/abha/verifyabhanumberaddressotp')
      .send({
        otp: "206877",
        txnId: "4f24ac5b-7162-4fc6-9ab9-48652de125f8",
        type: "verifyAbhaMobileOtp",
      });

    expect(res.status).toBe(200);
    expect(res.body).toEqual(mockResponse);
    expect(verifyAbhanumberAddressOtp).toHaveBeenCalledWith("verifyAbhaMobileOtp", "206877", "4f24ac5b-7162-4fc6-9ab9-48652de125f8");
  });

});


describe("POST /api/abha/verifymobileotp", () => {
  it("should return 200 and success response when OTP verification is successful", async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        txnId: "102f86bd-bef4-4ad7-9661-59096e1bb600",
        authResult: "success",
        accounts: [
          {
            ABHANumber: "91-7214-2788-0670",
            preferredAbhaAddress: "senthamaraip1003@sbx",
            name: "Senthamarai P",
            mobileVerified: false,
            profilePhoto: "/9j/4AAQSkZJRgABAgAAAQABAAD/2wBDA...",
          },
        ],
      },
    };

    verifyMobileNoOtp.mockResolvedValue(mockResponse);

    const response = await request(app)
      .post("/api/abha/verifymobileotp")
      .send({ otp: "123456", txnId: "sample-txn-id" });

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockResponse);
    expect(verifyMobileNoOtp).toHaveBeenCalledWith("123456", "sample-txn-id");
  });

  it("should return 400 when OTP verification fails", async () => {
    const mockResponse = {
      isSuccess: false,
      message: "Invalid OTP or transaction ID",
    };

    verifyMobileNoOtp.mockResolvedValue(mockResponse);

    const response = await request(app)
      .post("/api/abha/verifymobileotp")
      .send({ otp: "000000", txnId: "invalid-txn-id" });

    expect(response.status).toBe(400);
    expect(response.body).toEqual(mockResponse);
    expect(verifyMobileNoOtp).toHaveBeenCalledWith("000000", "invalid-txn-id");
  });

  it("should return 400 when OTP verification fails due to expired or incorrect OTP", async () => {
    const mockResponse = {
      isSuccess: true,
      response: {
        txnId: "8bfb605e-df5c-4053-965a-b445ced83db4",
        authResult: "failed",
        accounts: [],
      },
      authResult: "failed",
      message: "Please enter a valid OTP. Entered OTP is either expired or incorrect.",
      txnId: "8bfb605e-df5c-4053-965a-b445ced83db4",
    };

    verifyMobileNoOtp.mockResolvedValue(mockResponse);

    const response = await request(app)
      .post("/api/abha/verifymobileotp")
      .send({ otp: "incorrectOtp", txnId: "8bfb605e-df5c-4053-965a-b445ced83db4" });

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockResponse);
    expect(verifyMobileNoOtp).toHaveBeenCalledWith("incorrectOtp", "8bfb605e-df5c-4053-965a-b445ced83db4");
  });
});
