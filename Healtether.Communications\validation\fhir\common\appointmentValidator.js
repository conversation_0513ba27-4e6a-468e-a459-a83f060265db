const appointmentValidator = {
  appointment: {
    in: ["body"],
    exists: { errorMessage: "appointment is required" },
    isObject: true,
  },
  "appointment.status": {
    in: ["body"],
    isString: true,
    isIn: {
      options: [
        [
          "proposed",
          "pending",
          "booked",
          "arrived",
          "fulfilled",
          "cancelled",
          "noshow",
        ],
      ],
    },
    errorMessage:
      "status must be one of: proposed, pending, booked, arrived, fulfilled, cancelled, noshow",
  },
  "appointment.serviceCategories": {
    in: ["body"],
    isArray: true,
    errorMessage: "serviceCategories must be an array",
  },
  "appointment.serviceCategories.*": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each serviceCategory must be a non-empty string",
  },
  "appointment.serviceTypes": {
    in: ["body"],
    isArray: true,
    errorMessage: "serviceTypes must be an array",
  },
  "appointment.serviceTypes.*": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each serviceType must be a non-empty string",
  },
  "appointment.specialty": {
    in: ["body"],
    isArray: true,
    errorMessage: "specialty must be an array",
  },
  "appointment.specialty.*": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each specialty must be a non-empty string",
  },
  "appointment.appointmentType": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "appointmentType is required and must be a non-empty string",
  },
  "appointment.description": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "description is required and must be a non-empty string",
  },
  "appointment.start": {
    in: ["body"],
    isString: true,
    errorMessage: "start must be a valid date string",
  },
  "appointment.end": {
    in: ["body"],
    isString: true,
    errorMessage: "end must be a valid date string",
  },
  "appointment.created": {
    in: ["body"],
    isString: true,
    errorMessage: "created must be a valid date string",
  },
  "appointment.reasonReference": {
    in: ["body"],
    isArray: true,
    errorMessage: "reasonReference must be an array",
  },
  "appointment.reasonReference.*": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each reasonReference must be a non-empty string",
  },
  "appointment.basedOnServices": {
    in: ["body"],
    isArray: true,
    errorMessage: "basedOnServices must be an array",
  },
  "appointment.basedOnServices.*": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "Each basedOnService must be a non-empty string",
  },
};

export default appointmentValidator;
