import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
mockCommonUtils();
mockFirebaseMethod();
mockPatientHelper();


const { deletePatient } = await import('../../controllers/patients/patients.controller.js'); 

const { removePatient }= await import('../../helpers/patient/patient.helper.js'); 
const { sendNotificationViaToken }= await import('../../config/firebase.admin.js'); 
const { buildNotificationText }= await import('../../utils/common.utils.js'); 

const mockPatientData = {
  _id:"patientid",
  firstName: "test",
  lastName: "test",
  patientId: "SD_173",
  age: "10",
  height: "120",
  weight: "30",
  birthday: "2014-07-17",
  gender: "Male",
  mobile: "**********",
  email: "",
  address: {
      house: "",
      street: "",
      landmarks: "",
      city: "",
      pincode: ""
  },
  documentType: "",
  documentNumber: "",
  documents: [],
  createdOn: "08/09/2024, 10:48:15",
  modifiedOn: "08/09/2024, 10:48:15",
  clientId: "662ca0a41a2431e16c41ebaa"
}

const query = {
    query: { id: mockPatientData._id },
    user: { id: 'user123', name: 'Test User' },
    Notificationkey: 'notification-key',
  };
const res = {
  json: jest.fn(),
  status: jest.fn().mockReturnThis(),
 
};

describe('deletePatient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should delete a patient and send a notification', async () => {

    removePatient.mockResolvedValueOnce(mockPatientData);
    buildNotificationText.mockReturnValueOnce('Patient test test has been deleted');
    sendNotificationViaToken.mockResolvedValueOnce();
    await deletePatient(query, res);
    expect(removePatient).toHaveBeenCalledWith(query.query.id);

    expect(buildNotificationText).toHaveBeenCalledWith(
      'Patient',
      'test test',
      'has been deleted',
      query.user
    );

    expect(sendNotificationViaToken).toHaveBeenCalledWith(
      query.Notificationkey,
      'Patient test test has been deleted',
      'Patient Detail',
      true,
      mockPatientData.clinic,
      query.user.id
    );

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });

  
});
