import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { getPatientDetail } from '../../../helpers/patient/patient.helper.js';
import { Patient } from '../../../model/clinics.model.js'; 
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('getPatientDetail function', () => {
  let patientId;

  beforeEach(async () => {
    await Patient.deleteMany({}); 
    const patient = new Patient({
      firstName: 'John',
      lastName: 'Doe',
      mobile: '**********',
      patientId: 'PAT001',
      age: 30,
      prefix:"Mr.",
      birthday: new Date('1993-01-01'),
      gender: 'Male',
      email: '<EMAIL>',
      address: '123 Main St',
      height: 180,
      weight: 75,
      documentType: 'ID',
      documentNumber: 'ID123456',
      deleted: false,
    });
    
    patientId = (await patient.save())._id;
  });

  afterEach(async () => {
    await Patient.deleteMany({});
  });

  it('should return patient details for a valid ID', async () => {
    const result = await getPatientDetail(patientId);
    
    expect(result).toHaveProperty('firstName', 'John'); 
    expect(result).toHaveProperty('lastName', 'Doe'); 
    expect(result).toHaveProperty('age', 30); 
    expect(result).toHaveProperty('prefix', 'Mr.');
    expect(result).toHaveProperty('gender', 'Male'); 
    expect(result).toHaveProperty('mobile', '**********'); 
    expect(result).toHaveProperty('email', '<EMAIL>'); 
    expect(result).toHaveProperty('height', 180); 
    expect(result).toHaveProperty('weight', 75); 
    expect(result).toHaveProperty('documentType', 'ID');
    expect(result).toHaveProperty('documentNumber', 'ID123456');
  });

  it('should return null when no patient is found', async () => {
    const result = await getPatientDetail(new mongoose.Types.ObjectId());
    
    expect(result).toBeNull(); 
  });

 
});
