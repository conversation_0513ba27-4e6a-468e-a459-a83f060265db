import dayjs from "dayjs";
import { whatsapplogDb } from "../../config/whatsapp.collections.config.js";
import { fetchAbdmSession } from "./abdm.helper.js";
import utc from "dayjs/plugin/utc.js";
dayjs.extend(utc);

const AbhaAccessTokenModel = whatsapplogDb.model("AbdmAccessToken");

export const AbdmAccessToken = async () => {
  let tokenData = await AbhaAccessTokenModel.findOne();
  let response;
  let refreshTokenExpiresAt = tokenData?.refreshtoken_expiresAt;
  let refreshIsValid =
    refreshTokenExpiresAt &&
    dayjs().utc().isBefore(dayjs(refreshTokenExpiresAt).utc());
  let accesTokenIsValid =
    tokenData?.accesstoken_expiresAt &&
    dayjs().utc().isBefore(dayjs(tokenData?.accesstoken_expiresAt).utc());

  if (tokenData && accesTokenIsValid) {
    return tokenData.accesstoken;
  }

  if (tokenData == null || (!refreshIsValid && !accesTokenIsValid)) {
    response = await GetAbdmAccessToken();
    const refreshExpiresIn = response?.refreshExpiresIn || 1800;
    refreshTokenExpiresAt = dayjs()
      .add(refreshExpiresIn, "second")
      .utc()
      .format();
  } else if (refreshIsValid) {
    response = await GetAbdmAccessTokenByRefreshToken(tokenData?.refreshtoken);
  }

  const newToken = response?.accessToken?.toString();
  const expiresIn = response?.expiresIn || 1200;
  const refreshToken = response?.refreshToken?.toString();
  let accessTokenExpiresAt = dayjs().add(expiresIn, "second").utc().format();

  if (tokenData) {
    // Update existing token entry
    await AbhaAccessTokenModel.findByIdAndUpdate(tokenData._id, {
      accesstoken: newToken,
      accesstoken_expiresAt: accessTokenExpiresAt,
      refreshtoken: refreshToken,
      refreshtoken_expiresAt: refreshTokenExpiresAt,
    });
  } else {
    // Create new token entry if not found
    await AbhaAccessTokenModel.create({
      accesstoken: newToken,
      accesstoken_expiresAt: accessTokenExpiresAt,
      refreshtoken: refreshToken,
      refreshtoken_expiresAt: refreshTokenExpiresAt,
    });
  }

  return newToken;
};

const GetAbdmAccessToken = async () => {
  const data = {
    clientId: process.env.CLIENT_ID,
    clientSecret: process.env.CLIENT_SECRET,
    grantType: "client_credentials",
  };

  return await fetchAbdmSession(data);
};

const GetAbdmAccessTokenByRefreshToken = async (token) => {
  const data = {
    clientId: process.env.CLIENT_ID,
    clientSecret: process.env.CLIENT_SECRET,
    grantType: "refresh_token",
    refreshToken: token,
  };

  return await fetchAbdmSession(data);
};
