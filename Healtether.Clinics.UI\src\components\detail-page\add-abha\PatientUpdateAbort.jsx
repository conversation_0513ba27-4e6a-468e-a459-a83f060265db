import React, { useEffect } from "react";
// import { Buttons } from "../appointment/button";
import { Buttons } from "../appointment/button";
import moment from "moment";

export default function PatientUpdateAbort({
  handlePatientAutoFill,
  onComplete,
  patientData,
  profileData,
  handleBack,
  address,
  patientMobile
}) {
  return (
    <>
      <article className=" ">
        <section className="mt-3 flex flex-col gap-1">
          <div className="flex justify-between text-sm">
            <span className="font-bold ">Updated ABHA Profile Details</span>   <span>Patient ID: {patientData?.patientId}</span> 
          </div>
          <p>
            <span className="text-gray-400 font-bold">First Name:</span>{" "}
            {profileData?.firstName || "N/A"}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Last Name:</span>{" "}
            {profileData?.lastName || "N/A"}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Gender:</span>{" "}
            {profileData?.gender=="F"?"Female":"Male" || "N/A"}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Date Of Birth:</span>{" "}
            {profileData?.dob ||
              profileData?.dateOfBirth ||
              `${profileData.dayOfBirth}-${profileData.monthOfBirth}-${profileData.yearOfBirth}`}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Mobile Number:</span>{" "}
            {profileData?.mobile || "N/A"}
          </p>
          <p>
            <span className="text-gray-400 font-bold">ABHA Address:</span>{" "}
            {profileData?.preferredAbhaAddress ||
              profileData?.abhaAddress ||
              address ||
              profileData?.phrAddress?.[0] ||
              profileData?.phrAddress ||
              ""}
          </p>
          <p>
            <span className="text-gray-400 font-bold">ABHA Number:</span>{" "}
            {profileData?.ABHANumber || profileData?.abhaNumber || "N/A"}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Address:</span>
            {profileData?.address ? `${profileData.address || ""}` : "N/A"}
          </p>
        </section>

        <hr />

        <section className="mt-3 flex flex-col gap-2">
          <h2 className="text-lg font-bold  text-sm">
            Patient Profile Details Present With Us
          </h2>
          <p>
            <span className="text-gray-400 font-bold">First Name:</span>{" "}
            {patientData?.firstName}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Last Name:</span>{" "}
            {patientData?.lastName}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Gender:</span>{" "}
            {patientData?.gender}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Date Of Birth:</span>{" "}
            {moment(patientData?.birthday).format("DD-MM-YYYY")}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Mobile Number:</span>{" "}
            {patientMobile||patientData?.mobile}
          </p>
          <p>
            <span className="text-gray-400 font-bold">ABHA Address:</span>{" "}
            {patientData?.abhaAddress}
          </p>
          <p>
            <span className="text-gray-400 font-bold">ABHA Number:</span>{" "}
            {patientData?.abhaNumber}
          </p>
          <p>
            <span className="text-gray-400 font-bold">Address:</span>
            {patientData?.address.house}
          </p>
        </section>

        <footer className="flex justify-between gap-3 mt-3">

          <Buttons.secondary
            title={"Back"}
            classname=""
            onClick={handleBack}
          />
          <div>
          <Buttons.secondary
            title={"Abort"}
            classname=""
            onClick={() => handlePatientAutoFill(patientData)}
          />
          <Buttons.primary title={"Update"} classname="" onClick={onComplete} />
          </div>
          
        </footer>
      </article>
    </>
  );
}
