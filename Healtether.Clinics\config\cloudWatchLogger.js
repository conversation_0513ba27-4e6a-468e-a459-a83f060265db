// import AWS from 'aws-sdk';

// if(process.env.ENV!=="dev")
// {

// AWS.config.update({
//     accessKeyId: process.env.CLOUD_WATCH_ACCESSKEY,
//     secretAccessKey: process.env.CLOUD_WATCH_SECRET,
//     region: process.env.CLOUD_WATCH_REGION
//   });
// }
  

 // const cloudWatchLogs = new AWS.CloudWatchLogs();
  
  const logMessage = (message) => {
    const params = {
      logGroupName: process.env.CLOUD_WATCH_LOG_GROUP,
      logStreamName: process.env.CLOUD_WATCH_LOG_STREAM,
      logEvents: [
        {
          message: message,
          timestamp: Date.now()
        }
      ]
    };
  
    // cloudWatchLogs.putLogEvents(params, (error, data) => {
    //   if (error) {
    //     console.log('Error writing log:', error);
    //   } else {
    //     console.log('Log written successfully:', data);
    //   }
    // });
  };

export const LogExceptionInCloudWatch=(exception)=>
{
    if(process.env.ENV==="dev")
    {
        console.error(`Error in Application ${exception}\nStack: ${exception.stack}`);
    }
    else{
        logMessage(`Error in Application : ${exception?.message}\nStack: ${exception?.stack}`);
    }
}
export const LogUnHandledExceptionInCloudWatch=(exception)=>
  {
      if(process.env.ENV==="dev")
      {
          console.error(`Error Unhandled in Application ${exception}\nStack: ${exception.stack}`);
      }
      else{
          logMessage(`Error Unhandled : ${exception?.message}\nStack: ${exception?.stack}`);
      }
  }
export const LogMessageInCloudWatch=(message)=>
  {
      if(process.env.ENV==="dev")
      {
          console.error(`Log in Application ${message}`);
      }
      else{
          logMessage(`Log: ${message}`);
      }
  }
    