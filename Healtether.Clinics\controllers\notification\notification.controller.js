import {overview, upsertUserNotification} from "./../../helpers/notification/notification.helper.js";

export const notificationUpsert = async(req, res) => {
        const data = req.body;
        let userId=req.user.id;
        //   let id = req.Token;   const userId = new mongoose.Types.ObjectId(id);
        await upsertUserNotification(data.header,data.message,userId,data.clinicId);
        res
            .json({success: true})
            .status(200);
};

export const getNotificationOverview = async(req, res) => {
        const data = req.query;
        var isAdmin = false;
        //   let id = req.Token;   const userId = new mongoose.Types.ObjectId(id);
        req
            .linkedClinics
            .forEach(element => {
                if (element.clinic == data.clinicId) {
                    isAdmin = element.isAdmin;
                    return;
                }
            });

        var overviewData = await overview(data.page, data.size, data.keyword, data.sortby, data.direction, data.clinicId, req.user.id, isAdmin);
        res
            .json(overviewData)
            .status(200);
};
