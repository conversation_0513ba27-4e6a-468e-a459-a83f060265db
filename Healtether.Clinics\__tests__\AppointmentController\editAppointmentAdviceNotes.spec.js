import { jest } from "@jest/globals";
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");

mockApointmentHelper();
mockCommonUtils();
mockFirebaseMethod();

const { updateAdviceNotes } = await import("../../helpers/appointment/appointment.helper.js"); 
const { editAppointmentAviceNotes } = await import("../../controllers/appointments/appointment.controller.js"); 

describe('editAppointmentAviceNotes', () => {
    let req, res;

    beforeEach(() => {
        req = {
            params: {
                id: 'appointmentId'
            },
            body: {
                notes: ' notes ',
                advice:" advice",
            },
            user: {
                id: 'userId'
            }
        };

        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should successfully update the appointment advice notes', async () => {
        // Mock data
        const mockAppointment = {
            _id: 'appointmentId',
            notes: 'Updated advice notes',
            advice: 'Updated advice notes'
        };
        updateAdviceNotes.mockResolvedValue(mockAppointment);
        await editAppointmentAviceNotes(req, res);
        expect(updateAdviceNotes).toHaveBeenCalledWith(req.body, req.params.id, req.user);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ message: "Appointment updated successfully", appointment: mockAppointment });
    });
});
