import React, { useState, useRef } from "react";

import  DefaultTextboxClass, {DefaultSelectboxClass} from "../../utils/Classes"

import { Icons } from "./icons";

export const Input = {
  text: (props) => {
    const { width, error, className } = props;

    return (
      <section className={`${width} relative bg-gray z-10 ${className}`}>
        <input
          {...props}
          type="text"
          className={`w-full h-12  ${DefaultTextboxClass} ${className} `}
        />
        <div className="absolute top-full left-3 mt-0.5 text-error font-roboto font-normal text-xs">
          {error}
        </div>
      </section>
    );
  },
  number: (props) => {
    const { width, onChange, value } = props;
  
    const handleChange = (e) => {
      const inputValue = e.target.value;
      const regex = /^[0-9]*$/;
      if (regex.test(inputValue)) {
        onChange(inputValue); 
      }
    };
  
    return (
      <section className={`${width} relative z-10 bg-gray`}>
        <input
          {...props}
          value={value} 
          onChange={handleChange}
          className={`w-full h-12 ${DefaultSelectboxClass}`}
        />
      </section>
    );
  },
  
  date: (props) => {
    const { width, placeholder } = props;
    return (
      <section
        className={`${width} text-base text-dark/70 font-normal font-roboto relative `}
      >
        <input
          type="date"
          {...props}
          className={`w-full h-12  cursor-pointer ${DefaultTextboxClass} `}
        />
        {props?.value ? null : (
          <div
            className={`${placeholder}  absolute top-0 px-3 flex items-center w-max h-full z-10  border border-color_muted/20 border-r-0 rounded-md truncate ${DefaultTextboxClass} shadow-none read-only:opacity-100 `}
            
          >
            {placeholder}
          </div>
        )}
      </section>
    );
  },
};
