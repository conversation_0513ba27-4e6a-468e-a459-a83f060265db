import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateGetFrequentTextForPrescription = async (req, res, next) => {
    await checkSchema({
        clinicId: {
            in: ["query"],
            custom: {
              options: (value) => mongoose.Types.ObjectId.isValid(value),
              errorMessage: "Clinic ID must be a valid ObjectId",
            },
          },
        
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };