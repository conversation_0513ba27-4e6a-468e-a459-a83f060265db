import {
  initiateConsentRequest,
  checkConsentStatus,
  notifyconsentrequest,
  fetchConsentDetails,
  requestHealthInformation,
  notifyHealthInformation,
  consentList
} from "../../helpers/externalApi/appointment/abha.m3.api.js";

export const initiateConsentRequestController = async (req, res, next) => {
    const result = await initiateConsentRequest(req.body);
      if (result?.data?.isSuccess) {
        res.status(200).json(result);
      } else {
        res.status(400).json(result);
      }
};

export const checkConsentStatusController = async (req, res, next) => {
  try {
    const requestdata = req.body;
    const result = await checkConsentStatus(requestdata);
    if (result.ok) {
      res.status(200).json(result.json());
    } else {
      res.status(result.status).json({ error: result.statusText });
    }
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
    next(error);
  }
};

export const notifyConsentRequestController = async (req, res, next) => {
  try {
    const { consentId, type, data } = req.body;
    const headers = req.headers;
    const result = await notifyconsentrequest(consentId, type, data, headers);
    if (result.ok) {
      res.status(200).json(result.json());
    } else {
      res.status(result.status).json({ error: result.statusText });
    }
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
    next(error);
  }
};

export const fetchConsentDetailsController = async (req, res, next) => {
  try {
    const { consentArtefactId, hiuId } = req.body;
    const result = await fetchConsentDetails(consentArtefactId, hiuId);
    if (result.ok) {
      res.status(200).json(result.json());
    } else {
      res.status(result.status).json({ error: result.statusText });
    }
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
    next(error);
  }
};

export const requestHealthInformationController = async (req, res, next) => {
  try {
    const { data, hiuId } = req.body;
    const result = await requestHealthInformation(data, hiuId);
    if (result.ok) {
      res.status(200).json(result.json());
    } else {
      res.status(result.status).json({ error: result.statusText });
    }
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
    next(error);
  }
};

export const notifyHealthInformationController = async (req, res, next) => {
  try {
    const { transaction_id } = req.body;
    const result = await notifyHealthInformation(transaction_id);
    if (result.ok) {
      res.status(200).json(result.json());
    } else {
      res.status(result.status).json({ error: result.statusText });
    }
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
    next(error);
  }
};

export const patientConsentList= async (req, res, next)=>{
    const { patientId} = req.query;
    const result = await consentList(patientId);
    return res.status(200).json(result);
  
}