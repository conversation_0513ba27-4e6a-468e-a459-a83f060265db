import mongoose from "mongoose";
import {whatsapplogDb} from "../../config/whatsapp.collections.config.js";
import { addRecentChat } from "./recentchat.helper.js";
import { getProperMobile } from "../../utils/common.js";
export const saveChatMessage = async (
  message,
  clinicId,
  mobile,
  isReceived = true,
  isDelivered = false
) => {
  var toMobile = getProperMobile(mobile);
  var messageModel = whatsapplogDb.model("WhatsappMessageLog");
  var today = new Date();
  const messageLog = new messageModel({
    mobile: toMobile,
    isReceived: isReceived,
    isDelivered: false,
    message: message,
    type: "text",
    createdOn: new Date().toISOString(),
    expireAt: today,
    clinicId: clinicId,
  });
  await messageLog.save();
  if (isReceived) {
    await addRecentChat(toMobile, clinicId);
  }
  return messageLog;
};

export const getChatMessage = async (mobile, clinicId, pg, pgSize) => {
  var toMobile = getProperMobile(mobile);
  var messageModel = whatsapplogDb.model("WhatsappMessageLog");

  var messageLogs = await messageModel
    .find({ clinicId: clinicId, mobile: toMobile })
    .sort({ createdOn: -1 })
    .limit(pgSize)
    .skip((pg - 1) * pgSize)
    .select({
      message: 1,
      createdOn: 1,
      type: 1,
      isReceived: 1,
      isDelivered: 1,
    })
    .exec();
  return messageLogs;
};
