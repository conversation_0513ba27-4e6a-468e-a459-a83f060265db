import mongoose from "mongoose";
import mongoosePaginate from "mongoose-paginate-v2";
import { CLIENT_COLLECTION, PATIENT_COLLECTION } from "../../mongodb.collection.name.js";

const masterAllergiesSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
            unique: true,
        },
        description: {
            type: String,
        },
        is_deleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true }
);

masterAllergiesSchema.plugin(mongoosePaginate);

const userAllergiesSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
        },
        duration: {
            value:Number,
            unit:String,
        },
        notes: {
            type: String,
        },
        // appointment: {
        //     type: mongoose.Schema.Types.ObjectId,
        //     ref:"Appointment"
        // },
        patient: {
            type: mongoose.Schema.Types.ObjectId,
            ref: PATIENT_COLLECTION
        },
        clinic: {
            type: mongoose.Schema.Types.ObjectId,
            ref: CLIENT_COLLECTION
        },
        created: {
            on: {
                type: Date,
                default: Date.Now
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255
                },
            }
        }
    },
    {
        versionKey: '1.0',   timestamps: true,
    }
);

userAllergiesSchema.index({name:1, clinic: 1, patient: 1});

export { userAllergiesSchema, masterAllergiesSchema };
