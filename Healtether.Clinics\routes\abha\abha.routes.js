import { Router } from "express";
import { a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, enroll<PERSON><PERSON>, enroll<PERSON>y<PERSON><PERSON><PERSON><PERSON>, enrollByAbhaddress, enrollbyMobile, enrollMobileOTP, getSuggestions, getUserAbhaAddressCard, getUserAbhaCard, handleAbhaNumberAddressOtpRequest, handleVerifyAbhaNumberAddressOtp, requestIndexMobileOtp, searchAbha, userAbhaAddressProfile, userProfile, verifyMobileOtp } from "../../controllers/abha/abha.controller.js";
import { validateAbhaNumberAddressOtp, validateEnrollAbha, validateEnrollByAadhaarOtp, validateEnrollByAbhaAddress, validateEnrollByMobile, validateIndexMobileOtp, validateSearchAbha, validateSuggestion, validateUserProfile, validateVerifyAbhaNumberAddressOtp, validateVerifyMobileOtp } from "../../validation/abha/m1.validation.js";
import { json } from "stream/consumers";
const abha = Router();


/**
 * @swagger
 * /abha/searchabha:
 *   post:
 *     summary: Search ABHA by Mobile Number
 *     description: Searches for ABHA details using a mobile number.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mobile:
 *                 type: string
 *                 example: "9876543210"
 *     responses:
 *       200:
 *         description: Successfully retrieved ABHA details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 response:
 *                   - txnId: "f565a278-0025-4e3a-82e3-c107375a0947"
 *                     ABHA:
 *                       - index: 1
 *                         ABHANumber: "xx-xxxx-xxxx-3479"
 *                         name: "Arun Raguram K V"
 *                         gender: "M"
 *                       - index: 2
 *                         ABHANumber: "xx-xxxx-xxxx-7410"
 *                         name: "Mohit Kushwah"
 *                         gender: "M"
 *                       - index: 3
 *                         ABHANumber: "xx-xxxx-xxxx-0670"
 *                         name: "Senthamarai P"
 *                         gender: "F"
 *                       - index: 4
 *                         ABHANumber: "xx-xxxx-xxxx-0632"
 *                         name: "Monika Kushwah"
 *                         gender: "F"
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */


abha.post(
  "/searchabha",
  validateSearchAbha,
  async (req, res, next) => {
    try {
        return await searchAbha(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /abha/requestindexmobileotp:
 *   post:
 *     summary: Request OTP for Index Mobile
 *     description: Sends an OTP request for index-based mobile authentication.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               index:
 *                 type: string
 *                 example: "123456"
 *               txnId:
 *                 type: string
 *                 example: "abcde12345"
 *     responses:
 *       200:
 *         description: Successfully requested OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 response:
 *                   txnId: "1d460ff9-8ca9-4cc1-9f84-0c50088264d9"
 *                   message: "OTP sent to mobile number ending with ******3133"
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */

abha.post(
  "/requestindexmobileotp",
  validateIndexMobileOtp,
  async (req, res, next) => {
    try {
        return await requestIndexMobileOtp(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /abha/verifymobileotp:
 *   post:
 *     summary: Verify Mobile OTP
 *     description: Verifies the OTP sent to the user's mobile number.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               otp:
 *                 type: string
 *                 example: "123456"
 *               txnId:
 *                 type: string
 *                 example: "abcde12345"
 *     responses:
 *       200:
 *         description: OTP verification successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isSuccess:
 *                   type: boolean
 *                   example: true
 *                 response:
 *                   type: object
 *                   properties:
 *                     txnId:
 *                       type: string
 *                       example: "1d460ff9-8ca9-4cc1-9f84-0c50088264d9"
 *                     authResult:
 *                       type: string
 *                       example: "success"
 *                     message:
 *                       type: string
 *                       example: "OTP verified successfully"
 *                     token:
 *                       type: string
 *                       example: "eyJhbGciOiJSUzUxMiJ9"
 *                     expiresIn:
 *                       type: integer
 *                       example: 1800
 *                     refreshToken:
 *                       type: string
 *                       example: "eyJhbGciOiJSUzUxMiJ9."
 *                     refreshExpiresIn:
 *                       type: integer
 *                       example: 1296000
 *                     accounts:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           ABHANumber:
 *                             type: string
 *                             example: "91-4136-3424-7410"
 *                           preferredAbhaAddress:
 *                             type: string
 *                             example: "**************@sbx"
 *                           name:
 *                             type: string
 *                             example: "Mohit Kushwah"
 *                           status:
 *                             type: string
 *                             example: "ACTIVE"
 *                           profilePhoto:
 *                             type: string
 *                             example: "/9j/4AAQSkZJRgABAgAAAQABAA..."
 */
abha.post(
    "/verifymobileotp",
    validateVerifyMobileOtp,
    async (req, res, next) => {
      try {
          return await verifyMobileOtp(req, res);
      }
      catch (e) {
          next(e)
      }
  });

/**
 * @swagger
 * /abha/userprofile:
 *   get:
 *     summary: Get User Profile
 *     description: Retrieves the user profile information based on the provided x-token.
 *     tags:
 *       - ABHA
 *     parameters:
 *       - in: header
 *         name: x-token
 *         required: true
 *         schema:
 *           type: string
 *         description: Authentication token for the user
 *     responses:
 *       200:
 *         description: Successfully retrieved user profile
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 data:
 *                   userId: "12345"
 *                   name: "John Doe"
 *                   email: "<EMAIL>"
 *       400:
 *         description: Bad Request - Missing or invalid token
 *       500:
 *         description: Internal Server Error
 */

abha.get("/userprofile",validateUserProfile, async (req, res, next) => {
    try {
        return await userProfile(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /abha/userabhaaddressprofile:
 *   get:
 *     summary: Get User ABHA Address Profile
 *     description: Retrieves the user's ABHA address profile based on the provided x-token.
 *     tags:
 *       - ABHA
 *     parameters:
 *       - in: header
 *         name: x-token
 *         required: true
 *         schema:
 *           type: string
 *         description: Authentication token for the user
 *     responses:
 *       200:
 *         description: Successfully retrieved user ABHA address profile
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 data:
 *                   address:
 *                     house: "123"
 *                     street: "Main St"
 *                     city: "Gwalior"
 *                     pincode: "474001"
 *       400:
 *         description: Bad Request - Missing or invalid token
 *       500:
 *         description: Internal Server Error
 */

abha.get("/userabhaaddressprofile",validateUserProfile, async (req, res, next) => {
    try {
        return await userAbhaAddressProfile(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /abha/abhacard:
 *   get:
 *     summary: Get User ABHA Card
 *     description: Retrieves the user's ABHA card as a PNG image based on the provided x-token.
 *     tags:
 *       - ABHA
 *     parameters:
 *       - in: header
 *         name: x-token
 *         required: true
 *         schema:
 *           type: string
 *         description: Authentication token for the user
 *     responses:
 *       200:
 *         description: Successfully retrieved user ABHA card
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Bad Request - Missing or invalid token
 *       500:
 *         description: Internal Server Error
 */

abha.get("/abhacard",validateUserProfile,async (req, res, next) => {
    try {
        return await getUserAbhaCard(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /abha/abhaaddresscard:
 *   get:
 *     summary: Get User ABHA Address Card
 *     description: Retrieves the user's ABHA address card as a PNG image based on the provided x-token.
 *     tags:
 *       - ABHA
 *     parameters:
 *       - in: header
 *         name: x-token
 *         required: true
 *         schema:
 *           type: string
 *         description: Authentication token for the user
 *     responses:
 *       200:
 *         description: Successfully retrieved user ABHA address card
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Bad Request - Missing or invalid token
 *       500:
 *         description: Internal Server Error
 */

abha.get("/abhaaddresscard",validateUserProfile,async (req, res, next) => {
    try {
        return await getUserAbhaAddressCard(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /abha/createabhacard:
 *   post:
 *     summary: Create ABHA Card
 *     description: Enrolls a user and creates an ABHA card based on the provided Aadhaar number.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               aadhaarNumber:
 *                 type: string
 *                 example: "123412341234"
 *     responses:
 *       200:
 *         description: Successfully created ABHA card
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "ABHA card created successfully"
 *       400:
 *         description: Bad Request - Invalid Aadhaar number or missing fields
 *       500:
 *         description: Internal Server Error
 */

abha.post("/createabhacard",validateEnrollAbha, async (req, res, next) => {
    try {
        return await enrollAbha(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /abha/enrollbyaadhaar:
 *   post:
 *     summary: Enroll by Aadhaar OTP
 *     description: Enrolls a user using the mobile number, OTP, and transaction ID for Aadhaar-based enrollment.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mobileNumber:
 *                 type: string
 *                 example: "9876543210"
 *               otp:
 *                 type: string
 *                 example: "123456"
 *               txnId:
 *                 type: string
 *                 example: "txn_123456789"
 *     responses:
 *       200:
 *         description: Successfully enrolled by Aadhaar OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "Enrollment successful"
 *       400:
 *         description: Bad Request - Invalid input or OTP mismatch
 *       500:
 *         description: Internal Server Error
 */

abha.post("/enrollbyaadhaar",validateEnrollByAadhaarOtp, async (req, res, next) => {
    try {
        return await enrollByAadhaar(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /abha/enrollbymobile:
 *   post:
 *     summary: Enroll by Mobile Number
 *     description: Enrolls a user by providing their mobile number and transaction ID.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mobile:
 *                 type: string
 *                 example: "9876543210"
 *               txnId:
 *                 type: string
 *                 example: "txn_123456789"
 *     responses:
 *       200:
 *         description: Successfully enrolled by mobile number
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "Enrollment successful"
 *       400:
 *         description: Bad Request - Invalid mobile number or missing fields
 *       500:
 *         description: Internal Server Error
 */

abha.post("/enrollbymobile",validateEnrollByMobile, async (req, res, next) => {
    try {
        return await enrollbyMobile(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /abha/enrollmobileotp:
 *   post:
 *     summary: Enroll by Mobile OTP
 *     description: Enrolls a user by verifying the OTP sent to their mobile number along with the transaction ID.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mobileNumber:
 *                 type: string
 *                 example: "9876543210"
 *               otp:
 *                 type: string
 *                 example: "123456"
 *               txnId:
 *                 type: string
 *                 example: "txn_123456789"
 *     responses:
 *       200:
 *         description: Successfully verified mobile OTP and enrolled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "Mobile OTP verified and enrollment successful"
 *       400:
 *         description: Bad Request - Invalid OTP or missing fields
 *       500:
 *         description: Internal Server Error
 */

abha.post( "/enrollmobileotp",validateEnrollByAadhaarOtp, async (req, res, next) => {
    try {
        return await enrollMobileOTP(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /abha/suggestion:
 *   get:
 *     summary: Get Suggestions
 *     description: Retrieves suggestions based on the provided transaction ID.
 *     tags:
 *       - ABHA
 *     parameters:
 *       - in: header
 *         name: transaction_id
 *         required: true
 *         description: The transaction ID used to fetch suggestions.
 *         schema:
 *           type: string
 *           example: "txn_123456789"
 *     responses:
 *       200:
 *         description: Successfully retrieved suggestions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "Suggestions retrieved successfully"
 *                 suggestions: ["Suggestion 1", "Suggestion 2"]
 *       400:
 *         description: Bad Request - Missing or invalid transaction ID
 *       500:
 *         description: Internal Server Error
 */

abha.get("/suggestion",validateSuggestion, async (req, res, next) => {
    try {
        return await getSuggestions(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /abha/enrollbyabhaaddress:
 *   post:
 *     summary: Enroll using ABHA Address
 *     description: Enrolls a user by providing the ABHA address and a transaction ID.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               txnId:
 *                 type: string
 *                 example: "txn_123456789"
 *               abhaAddress:
 *                 type: string
 *                 example: "abc@abha"
 *               preferred:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Successfully enrolled using ABHA address
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "Enrollment successful using ABHA address"
 *       400:
 *         description: Bad Request - Invalid or missing fields
 *       500:
 *         description: Internal Server Error
 */


abha.post("/enrollbyabhaaddress",validateEnrollByAbhaAddress, async (req, res, next) => {
    try {
        return await enrollByAbhaddress(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /abha/aadhaarlogin:
 *   post:
 *     summary: Login using Aadhaar
 *     description: Allows users to log in by providing their Aadhaar number.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               aadhaarNumber:
 *                 type: string
 *                 example: "123456789012"
 *     responses:
 *       200:
 *         description: Successfully logged in using Aadhaar
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "Login successful using Aadhaar"
 *       400:
 *         description: Bad Request - Invalid or missing Aadhaar number
 *       500:
 *         description: Internal Server Error
 */


abha.post("/aadhaarlogin",validateEnrollAbha, async (req, res, next) => {
    try {
        return await aadhaarLogin(req, res);
    }
    catch (e) {
        next(e)
    }
});


//   abha address/number 

/**
 * @swagger
 * /abha/abhanumberaddressotp:
 *   post:
 *     summary: Handle ABHA Number Address OTP
 *     description: Sends an OTP for a given ABHA number address and type.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 example: "mobile"
 *               abhaNumberAddress:
 *                 type: string
 *                 example: "xyz@abha"
 *     responses:
 *       200:
 *         description: OTP successfully sent for ABHA number address
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "OTP sent successfully for ABHA number address"
 *       400:
 *         description: Bad Request - Invalid or missing fields
 *       500:
 *         description: Internal Server Error
 */

  abha.post("/abhanumberaddressotp", validateAbhaNumberAddressOtp,
    async(req,res,next)=>{
        try{
            return await handleAbhaNumberAddressOtpRequest(req,res)

        }catch(e){
         next(e)
        }
    }
)

/**
 * @swagger
 * /abha/verifyAbhaNumberAddressOtp:
 *   post:
 *     summary: Verify ABHA Number Address OTP
 *     description: Verifies the OTP for the given ABHA number address and type.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 example: "mobile"
 *               otp:
 *                 type: string
 *                 example: "123456"
 *               txnId:
 *                 type: string
 *                 example: "txn_12345"
 *     responses:
 *       200:
 *         description: OTP successfully verified for ABHA number address
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 isSuccess: true
 *                 message: "OTP verified successfully for ABHA number address"
 *       400:
 *         description: Bad Request - Invalid or missing fields
 *       500:
 *         description: Internal Server Error
 */


abha.post("/verifyAbhaNumberAddressOtp",validateVerifyAbhaNumberAddressOtp,
    async(req,res,next)=>{
        try{
            return await handleVerifyAbhaNumberAddressOtp(req,res)
        }catch(e){
         next(e)
        }
    }
)

abha.post("/listener",
    async(req,res,next)=>{
        try{
            console.log(JSON.stringify(req))
            res.status(200).json("success");
        }catch(e){
         next(e)
        }
    }
)
export default abha;