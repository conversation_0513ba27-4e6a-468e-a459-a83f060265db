import { Router } from "express";
import {
  checkConsentStatusHIP,
  fetchConsentDetailsHIP,
  initiateConsentRequestHIP,
  notifyHealthInformationHIP,
  requestHealthInformationHIP,
  storeWebhookResponseForHIUAndHIP,
} from "../controllers/abha.milestone.three.controller.js";
import { validateConsentInit } from "../validation/abha/abha.validation.js";

const abhaM3Router = Router();
/**
 * @swagger
 * /initiateconsentrequesthip:
 *   post:
 *     summary: Initiate Consent Request HIP
 *     description: Initiates a consent request for a given patient using ABHA API.
 *     tags:
 *       - ABHA M3
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   hiu:
 *                     type: string
 *                     example: "IN2410000949"
 *                   hiTypes:
 *                     type: array
 *                     items:
 *                       type: string
 *                     example: ["OPConsultation", "DiagnosticReport"]
 *                   abhaAddress:
 *                     type: string
 *                     example: "testm_233@sbx"
 *                   purposeCode:
 *                     type: string
 *                     example: "CAREMGT"
 *                   purposeText:
 *                     type: string
 *                     example: "Care Management"
 *                   requesterName:
 *                     type: string
 *                     example: "Dr. <PERSON> Doe"
 *                   dateRange:
 *                     type: object
 *                     properties:
 *                       startDate:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-01T00:00:00Z"
 *                       endDate:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-12-31T23:59:59Z"
 *                   expiryDate:
 *                     type: string
 *                     format: date-time
 *                     example: "2025-01-01T00:00:00Z"
 *     responses:
 *       202:
 *         description: Consent Request Initiated Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Consent Request Initiated"
 *                 data:
 *                   type: object
 *                   example: { "requestId": "**********" }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Error initiating consent request"
 */

abhaM3Router
  .route("/initiateconsentrequesthip")
  .post(validateConsentInit,async (req, res, next) => {
    try {
      return await initiateConsentRequestHIP(req, res);
    } catch (e) {
      next(e);
    }
  });

/**
 * @swagger
 * /checkconsentstatuship:
 *   post:
 *     summary: Check Consent Status HIP
 *     description: Checks the status of a consent request using ABHA API.
 *     tags:
 *       - Consent
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               consentRequest:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     example: "**********"
 *     parameters:
 *       - in: header
 *         name: X-HIU-ID
 *         required: true
 *         schema:
 *           type: string
 *         example: "IN2410000949"
 *     responses:
 *       202:
 *         description: Request Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Request Success"
 *                 data:
 *                   type: object
 *                   example: { "isSuccess": true }
 *       500:
 *         description: Request Failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Request Failed"
 */

abhaM3Router.route("/checkconsentstatuship").post(async (req, res, next) => {
  try {
    return await checkConsentStatusHIP(req, res);
  } catch (e) {
    next(e);
  }
});
// abhaM3Router.route("/notifyconsentrequesthip").post(async (req, res, next) => {
//   try {
//     return await storeWebhookResponseForHIUAndHIP(req, res);
//   } catch (e) {
//     next(e);
//   }
// });

/**
 * @swagger
 * /fetchconsentdetailship:
 *   post:
 *     summary: Fetch Consent Details HIP
 *     description: Retrieves consent details for a given consent ID and HIU ID.
 *     tags:
 *       - Consent
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 example: "**********"
 *               hiuId:
 *                 type: string
 *                 example: "IN2410000949"
 *     responses:
 *       200:
 *         description: Successfully retrieved consent details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 response:
 *                   type: object
 *                   example: { "consentDetails": { "id": "**********", "status": "active" } }
 *       400:
 *         description: Bad Request - Invalid input
 *       500:
 *         description: Internal Server Error
 */

abhaM3Router.route("/fetchconsentdetailship").post( async (req, res, next) => {
    try {
        return await fetchConsentDetailsHIP(req, res);
    }
    catch (e) {
        next(e)
    }
});

abhaM3Router.route("/requesthealthinformationhip").post( async (req, res, next) => {
    try {
        return await requestHealthInformationHIP(req, res);
    }
    catch (e) {
        next(e)
    }
});

abhaM3Router.route("/notifyhealthinformationhip").post( async (req, res, next) => {
    try {
        return await notifyHealthInformationHIP(req, res);
    }
    catch (e) {
        next(e)
    }
});

export default abhaM3Router;
