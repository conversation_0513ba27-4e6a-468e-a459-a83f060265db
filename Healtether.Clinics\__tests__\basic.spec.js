import mongoose from 'mongoose';
import { jest } from "@jest/globals";

const { setup, teardown } = await import("../setup.js");
// import { deleteTestDatabases } from '../testdb-cleanup'; 
jest.setTimeout(30000); 

beforeAll(async () => {
  await setup(); // Ensure the DB is connected before running tests
});

afterAll(async () => {
  await teardown(); 
});

describe('Basic Test', () => {
  it('should connect to the database successfully', async () => {
    const state = mongoose.connection.readyState;
    console.log('Connection State:', state);
    expect(state).toBe(1); 
  });
});
