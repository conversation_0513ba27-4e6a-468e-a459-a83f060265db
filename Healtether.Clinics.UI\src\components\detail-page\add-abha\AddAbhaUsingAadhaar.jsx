import React, { useState, useEffect } from "react";
import Mo<PERSON> from "../modal";
import { AadhaarInput } from "../appointment/aadhar-input";
import { Buttons } from "../appointment/button";
import { OtpInputs } from "./otp-inputs";
import <PERSON><PERSON><PERSON><PERSON> from "./create-abha";
import AB<PERSON><PERSON><PERSON> from "./ABHACard";
import {
  fetchAbhaCard,
  getUserProfile,
  loginUsingAadhar,
  verifyAbhaNumberAddressOtp,
} from "../../../services/appointment/abha-m1";
import {
  CalculateAge,
  formatDateToUTC,
  startTimer,
  updateResendAttempts,
} from "../../../utils/CommonMethods";
import { PatientProfileWithAbhaDetails } from "../../../services/patient/patient";
import Spinner from "../..//loader/Spinner";
import PatientUpdateAbort from "./PatientUpdateAbort";

// Define constants for flow steps
const Steps = {
  AADHAAR_INPUT: "aadhaarInput",
  VERIFY_NUMBER: "verifyNumber",
  ABHA_PRESENT: "abhaPresnt",
  ABHA_NOT_PRESENT: "abhaNotPresent",
  AADHAR_AUTHENTICATION: "aadharAuthentication",
  AADHAR_MOBILE_VERIFY: "aadharMobileVerify",
  NUMBER_REVIEW: "numberReview",
  NUMBER_NOT_SAME: "numberNotSAME",
  AADHAR_MOBILE_AUTHENTICATION: "aadharMobileAuthentication",
  CREATE_ABHA: "createAbha",
  VIEW_ABHA_CARD: "viewAbhaCard",
  NEW_ADDRESS: "newAddress",
  PATIENT_EXIST: "patientexist",
};

export default function AddAbhaUsingAadharNum({
  addAbhaUsing,
  setAddAbhaUsing,
  patientMobile,
  OnSelectPatient,
  user,
}) {
  const [busy, setBusy] = useState(false);
  const [currentStep, setCurrentStep] = useState(Steps.AADHAAR_INPUT);
  const [otp, setOtp] = useState(Array(6).fill(""));
  const [error, setError] = useState(false);
  const [isAadhaarvalid, setIsAadhaarValid] = useState(false);

  const [aadhaar, setAadhaar] = useState(Array(3).fill(""));
  const [address, setAddress] = useState("");
  const [abhaCard, setAbhaCard] = useState(null);
  const [mobileEnding, setMobileEnding] = useState("");
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [patientData, setPatientData] = useState(null);
  const [timer, setTimer] = useState(60);
  const [resendAttemptsMap, setResendAttemptsMap] = useState(new Map());
  const [patientName, setPatientName] = useState("");

  const [checkboxes, setCheckboxes] = useState({
    consent1: false,
    consent2: false,
    consent3: false,
    consent4: false,
    consent5: false,
    consent6: false,
  });

  const areAllChecked = Object.values(checkboxes).every(Boolean);

  const handleCheckboxChange = (key) => {
    setCheckboxes((prev) => ({ ...prev, [key]: !prev[key] }));
    handleError(message);
  };

  const resetState = () => {
    setCurrentStep(Steps.AADHAAR_INPUT);
    setOtp(Array(6).fill(""));
    setAadhaar(Array(3).fill(""));
    setAddress("");
    setAddAbhaUsing("");
  };

  const props = {
    otp,
    setOtp,
    aadhaar,
    setAadhaar,
    address,
    error,
    busy,
    setAddress,
    nextStep: setCurrentStep,
  };
  const handleAadharLogin = async (isResend = false) => {
    if (!areAllChecked) {
      handleError("Please accept all terms to proceed and enter patient name.");
      return;
    }
    const { canResend, updatedMap, message } = updateResendAttempts(
      aadhaar.join(""),
      isResend,
      resendAttemptsMap
    );

    try {
      if (!canResend) {
        handleError(message);
        return;
      }
      setBusy(true);
      let result = await loginUsingAadhar(aadhaar);
      console.log("Aadhar Login Success", result);
      if (result.isSuccess) {
        const message = result.response.message;
        const match = message.match(/ending (.+)$/);
        if (match) {
          setMobileEnding(match[1]);
        }
        setCurrentStep(Steps.VERIFY_NUMBER);
        setResendAttemptsMap(updatedMap);
        setCheckboxes({
          consent1: false,
          consent2: false,
          consent3: false,
          consent4: false,
          consent5: false,
          consent6: false,
        });
        startTimer(60, setTimer, setIsResendDisabled);
        setBusy(false);
      } else {
        setBusy(false);
        handleError(result.response.message);
      }
    } catch (error) {
      setBusy(false);
      console.log("Aadhar catch", error);
      if (
        error.response.data.response &&
        error.response.data.response.error?.code === "ABDM-1114"
      ) {
        setCurrentStep(Steps.ABHA_NOT_PRESENT);
      } else if (!error.response.data.response.isSuccess) {
        handleError(error.response.data.response?.response?.message);
      } else {
        handleError(error.message);
      }
    }
  };
  const aadharLogin = () => handleAadharLogin(false); // Initial Aadhar login
  const resendAadharLogin = () => handleAadharLogin(true);
  const verifyAadharOtp = async () => {
    try {
      setBusy(true);
      let result = await verifyAbhaNumberAddressOtp(otp, "", "aadhar", true);
      if (result?.isSuccess) {
        if (result?.response?.token) {
          localStorage.setItem("verifyUserToken", result.response.token);
        }
        if (result?.response?.authResult === "success") {
          let profile = await getUserProfile();
          if (profile.isSuccess) {
            setProfileData(profile.response);
          }
          setCurrentStep(Steps.CREATE_ABHA);
        } else if (
          result?.response?.error &&
          result?.response?.error?.code === "ABDM-1114"
        ) {
          setCurrentStep(Steps.ABHA_NOT_PRESENT);
        } else {
          setOtp(Array(6).fill(""));
          handleError(result.response.message);
        }
        setBusy(false);
      }
    } catch (error) {
      setBusy(false);
      handleError(error?.message);
      if (error.response.data.response.error.code === "ABDM-1114") {
        setCurrentStep(Steps.ABHA_NOT_PRESENT);
        setError("");
      }
    }
  };

  const AbhaCard = async () => {
    try {
      setBusy(true);
      let result = await fetchAbhaCard();
      setAbhaCard(result);
      setCurrentStep(Steps.VIEW_ABHA_CARD);
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };

  const handleAddPatientProfile = async (update) => {
    let data;
    try {
      let birthDay;
      if (profileData.dob) {
        birthDay = formatDateToUTC(profileData.dob);
      } else if (profileData.dateOfBirth) {
        birthDay = new Date(Date.UTC(profileData.dateOfBirth));
      } else if (
        profileData.yearOfBirth &&
        profileData.monthOfBirth &&
        profileData.dayOfBirth
      ) {
        birthDay = new Date(
          Date.UTC(
            parseInt(profileData.yearOfBirth),
            parseInt(profileData.monthOfBirth) - 1, // Month is zero-based
            parseInt(profileData.dayOfBirth)
          )
        );
      } else {
        throw new Error("Insufficient data to calculate birth date.");
      }
      let addresshouse = {
        house: profileData.address,
        city: profileData.districtName,
        district: profileData.districtName,
        state: profileData.stateName,
        pincode: profileData.pinCode || profileData.pincode,
        landmarks: profileData.stateName,
      };
      data = {
        firstName:
          `${profileData.firstName} ${profileData.middleName || ""}`.trim() ||
          profileData.name?.split(" ")[0],

        lastName: profileData.lastName || profileData.name?.split(" ")[1],
        abhaAddress:
          address ||
          profileData?.preferredAbhaAddress ||
          profileData?.phrAddress[0],
        gender: profileData.gender?.toUpperCase() === "M" ? "Male" : "Female",

        birthday: birthDay,
        address: addresshouse,
        age: CalculateAge(birthDay),
        profilePic: `data:image/jpeg;base64,${
          profileData?.photo ||
          profileData.profilePic ||
          profileData.profilePhoto
        }`,
        abhaNumber: profileData.ABHANumber,
        mobile: profileData.mobile || patientMobile,
      };
      setBusy(true);
      let result = await PatientProfileWithAbhaDetails(data, update);
      if (result.success) {
        if (result.isExist && !update) {
          setCurrentStep(Steps.PATIENT_EXIST);
          setPatientData(result.data);
        } else {
          OnSelectPatient(result.data);
          resetState();
        }
      }
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };
  const handleError = (message) => {
    setError(message);
    setTimeout(() => setError(""), 10000);
  };

  const renderStep = () => {
    switch (currentStep) {
      case Steps.AADHAAR_INPUT:
        return (
          <div>
            <p className="py-2 text-sm font-semibold text-dark">
              Aadhaar Number
            </p>
            <AadhaarInput
              aadhaar={aadhaar}
              setAadhaar={setAadhaar}
              setError={setIsAadhaarValid}
              error={isAadhaarvalid}
            />
            {isAadhaarvalid && (
              <p className="text-red-600 font-semibold">
                Invalid Aadhaar Number
              </p>
            )}

            {error && <p className="text-red-600 font-semibold">{error}</p>}
            {[
              "I authorize NHA to use my Aadhaar number for performing Aadhaar based authentication with UIDAI for the sole purpose of creation of ABHA number.",
              "I consent to usage of my ABHA address and ABHA number for linking of my legacy (past) health records and those which will be generated during this encounter.",
              "I authorize the sharing of all my health records with healthcare provider(s) for the purpose of providing healthcare services to me during this encounter.",
              "I consent to the anonymization and subsequent use of my health records for public health purposes.",
            ].map((text, index) => (
              <label key={index} className="text-sm text-dark flex items-start">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={checkboxes[`consent${index + 1}`]}
                  onChange={() => handleCheckboxChange(`consent${index + 1}`)}
                />
                {text}
              </label>
            ))}

            {/* Sub-points under the 4th point with left margin */}
            <div className="ml-8">
              {/* 5th point - healthcare provider confirmation */}
              <label className="text-sm text-dark flex items-start">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={checkboxes[`consent5`]}
                  onChange={() => handleCheckboxChange(`consent5`)}
                />
                I, {user?.firstName} {user?.lastName} confirm that I have duly
                informed and explained the beneficiary of the contents of
                consent for aforementioned purposes.
              </label>

              {/* 6th point - patient confirmation with input field */}
              <label className="text-sm text-dark flex items-start">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={checkboxes[`consent6`]}
                  onChange={() => handleCheckboxChange(`consent6`)}
                />
                <span>
                  I,
                  <input
                    type="text"
                    className="border border-gray-300 rounded-sm p-2 mx-1 text-sm"
                    placeholder="Enter Patient Name"
                    value={patientName}
                    onChange={(e) => setPatientName(e.target.value)}
                  />
                  have been explained about the consent as stated above and
                  hereby provide my consent for the aforementioned purposes.
                </span>
              </label>
            </div>

            <button
              onClick={aadharLogin}
              className="mt-7 mx-auto h-10 w-20 flex items-center justify-center text-white bg-Primary rounded-lg"
            >
              {busy ? <Spinner show={true} /> : "Submit"}
            </button>
          </div>
        );
      case Steps.VERIFY_NUMBER:
        return (
          <div>
            <p className="text-sm text-dark font-semibold">
              Please enter 6 -digit OTP sent to Aadhaar linked mobile number{" "}
              {mobileEnding}
            </p>
            <OtpInputs
              otp={otp}
              setOtp={setOtp}
              verify={(e) => console.log(e)}
              isResendDisabled={isResendDisabled}
              timer={timer}
              onResendOtp={resendAadharLogin}
               resendAttemptsCount={resendAttemptsMap.get(aadhaar.join(""))}
            />
            {error && (
              <div className="mt-2 text-sm font-semibold text-red-600">
                {error}
              </div>
            )}
            <button
              onClick={verifyAadharOtp}
              className="mt-7 mx-auto h-10 w-20 flex items-center justify-center text-white  bg-Primary rounded-lg"
            >
              {busy ? <Spinner show={true} /> : "Submit"}
            </button>
          </div>
        );

      case Steps.ABHA_NOT_PRESENT:
        return (
          <div className=" flex flex-col  p-2">
            <p className="text-sm font-semibold text-dark mt-3">
              Couldn’t find ABHA addresses for this mobile number
            </p>

            <footer className=" flex justify-end items-end  gap-2 mt-5">
              <Buttons.light
                onClick={() => setCurrentStep(Steps.AADHAAR_INPUT)}
                title="Back"
                classname="w-fit font-semibold"
              />
              <Buttons.primary
                onClick={() => setAddAbhaUsing("abha creation")}
                title="Create ABHA"
                classname="w-fit font-semibold"
              />
            </footer>
          </div>
        );
      case Steps.CREATE_ABHA:
        return (
          <CreateAbha
            {...props}
            handleChancel={resetState}
            onComplete={() => handleAddPatientProfile(false)}
            profileData={profileData}
            onViewAbhaCard={AbhaCard}
          />
        );
      case Steps.VIEW_ABHA_CARD:
        return (
          <ABHACard
            {...props}
            abhaCard={abhaCard}
            onCancel={() => setCurrentStep(Steps.CREATE_ABHA)}
          />
        );
      case Steps.PATIENT_EXIST:
        return (
          <PatientUpdateAbort
            handlePatientAutoFill={(value) => {
              OnSelectPatient(value);
              resetState();
            }}
            handleBack={() => setCurrentStep(Steps.CREATE_ABHA)}
            address={address}
            onComplete={() => handleAddPatientProfile(true)}
            patientData={patientData}
            profileData={profileData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      isOpen={addAbhaUsing === "Aadhar number"}
      setIsOpen={resetState}
      isCard={currentStep == Steps.VIEW_ABHA_CARD}
      classname={`font-primary ${
        currentStep == Steps.VIEW_ABHA_CARD
          ? "w-[400px] "
          : "min-w-[350px]! min-h-96!"
      }`}
    >
      {renderStep()}
    </Modal>
  );
}
