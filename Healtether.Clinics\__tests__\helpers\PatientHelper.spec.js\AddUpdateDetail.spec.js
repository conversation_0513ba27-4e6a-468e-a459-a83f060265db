import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { addUpdateDetails } from '../../../helpers/patient/patient.helper.js'; 
import { Patient } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown(); 
});

describe('addUpdateDetails function', () => {
  beforeEach(async () => {
    await Patient.deleteMany({}); 
  });

  afterEach(async () => {
    await Patient.deleteMany({}); 
  });

  it('should add a new patient when id is null', async () => {
    const data = {
      patientId: 'PAT002',
      firstName: 'Jane',
      lastName: 'Doe',
      prefix: 'Mr.', // Added required prefix field
      age: 28,
      height: 170,
      weight: 65,
      birthday: new Date('1995-05-05'),
      gender: 'Female',
      mobile: '**********',
      email: '<EMAIL>',
      address: {
        house: "",
        street: "",
        landmarks: "",
        city: "gwalior",
        pincode: "474001"
      },
      documentType: 'Passport',
      documentNumber: 'PAS123456',
      clientId: new mongoose.Types.ObjectId(), // Changed to ObjectId
      documents: []
    };
    const user = { id: 'user1', name: 'User One' };
  
    const result = await addUpdateDetails(data, null, user);
  
    const patient = await Patient.findOne({ patientId: 'PAT002' }); 
    
    expect(patient).toBeDefined(); 
    expect(patient.firstName).toBe("Jane"); 
    expect(patient.lastName).toBe("Doe"); 
    expect(patient.prefix).toBe("Mr."); // Verify prefix
    expect(patient.gender).toBe("Female"); 
    expect(patient.mobile).toBe("**********"); 
    expect(patient.email).toBe("<EMAIL>"); 
  });

  it('should update an existing patient when id is provided', async () => {
    // Create initial patient with all required fields
    const initialData = {
      patientId: 'PAT003',
      firstName: 'Alice',
      lastName: 'Smith',
      prefix: 'Mr.', // Added required prefix field
      age: 30,
      height: 165,
      weight: 60,
      birthday: new Date('1993-01-01'),
      gender: 'Female',
      mobile: '**********',
      email: '<EMAIL>',
      address: {
        house: "",
        street: "",
        landmarks: "",
        city: "gwalior",
        pincode: "474001"
      },
      documentType: 'ID',
      documentNumber: 'ID987654',
      clientId: new mongoose.Types.ObjectId(), // Changed to ObjectId
      documents: []
    };

    const patient = await Patient.create(initialData);
    const patientId = patient._id;

    const updateData = {
      firstName: 'Alice Updated',
      lastName: 'Smith Updated',
      prefix: 'Mr.', // Include prefix in update
      age: 31,
      height: 170,
      weight: 65,

      birthday: new Date('1992-01-01'),
      gender: 'Female',
      mobile: '**********',
      email: '<EMAIL>',
      address: {
        house: "",
        street: "",
        landmarks: "",
        city: "tamil nadu",
        pincode: "394387"
      },
      documentType: 'ID',
      documentNumber: 'ID987654',
      documents: []
    };

    const user = { id: 'user2', name: 'User Two' };
    await addUpdateDetails(updateData, patientId, user);

    const updatedPatient = await Patient.findById(patientId);
    // expect(updatedPatient.firstName).toBe(updateData.firstName); 
    expect(updatedPatient.lastName).toBe(updateData.lastName); 
    expect(updatedPatient.prefix).toBe(updateData.prefix); // Verify prefix update
    expect(updatedPatient.age).toBe(updateData.age); 
    expect(updatedPatient.height).toBe(updateData.height); 
    expect(updatedPatient.weight).toBe(updateData.weight); 
    expect(updatedPatient.email).toBe(updateData.email); 
    expect(updatedPatient.modified.on).toBeDefined(); 
  });
});