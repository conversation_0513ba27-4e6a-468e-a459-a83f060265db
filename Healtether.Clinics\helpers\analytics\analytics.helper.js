import mongoose from "mongoose";
import { Appointment } from "../../model/clinics.model.js";
import {generateDateRange} from "../../utils/array.utils.js"
import moment from "moment"
import 'moment-timezone';

export const getPatientsAppointmentsGenderRatio = async () => {
  const startOfYear = moment().startOf('year').toDate();
  const endOfYear = moment().endOf('year').toDate();
  const appointments = await Appointment.aggregate([
    {
      $match: {
        appointmentDate: { $gte: startOfYear, $lte: endOfYear },
      },
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
      },
    },
    {
      $lookup: {
        from: "appointments",
        pipeline: [
          {
            $group: {
              _id: "$gender",
              count: { $sum: 1 },
            },
          },
        ],
        as: "genders",
      },
    },
    {
      $unwind: "$genders",
    },
    {
      $project: {
        gender: "$genders._id",
        total: 1,
        genderCount: "$genders.count",
        percentage: {
          $multiply: [{ $divide: ["$genders.count", "$total"] }, 100],
        },
      },
    },
  ]).exec();
  return appointments;
};
export const getWeeklyPatientsAppointmentsGenderRatio = async () => {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - dayOfWeek); // Set to the start of the week (Sunday)
  startOfWeek.setHours(0, 0, 0, 0); // Set time to the beginning of the day

  const endOfWeek = new Date(today);
  endOfWeek.setHours(23, 59, 59, 999); // Set time to the end of the day
  const appointments = await Appointment.aggregate([
    {
      $match: {
        appointmentDate: {
          $gte: startOfWeek,
          $lte: endOfWeek,
        },
      },
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
      },
    },
    {
      $lookup: {
        from: "appointments",
        pipeline: [
          {
            $group: {
              _id: "$gender",
              count: { $sum: 1 },
            },
          },
        ],
        as: "genders",
      },
    },
    {
      $unwind: "$genders",
    },
    {
      $project: {
        gender: "$genders._id",
        total: 1,
        genderCount: "$genders.count",
        percentage: {
          $multiply: [{ $divide: ["$genders.count", "$total"] }, 100],
        },
      },
    },
  ]).exec();

  const defaultGenders = ["Male", "Female", "Other"];
  const mergedResults = defaultGenders.map(gender => {
    const found = appointments.find(result => result.gender === gender);
    return found || {
      gender,
      total: appointments.length ? appointments[0].total : 0,
      genderCount: 0,
      percentage: 0
    };
  });
 
  return mergedResults;
};
export const getPatientsAppointmentsCustomGenderRatio = async (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Step 1: Calculate gender counts
  const genderCounts = await Appointment.aggregate([
    {
      $match: {
        appointmentDate: {
          $gte: start,
          $lte: end
        }      
      },
    },
    {
      $group: {
        _id: "$gender",
        count: { $sum: 1 },
      },
    },
  ]);

  // Step 2: Calculate total appointments
  const totalAppointments = genderCounts.reduce((acc, curr) => acc + curr.count, 0);

  // Step 3: Calculate percentage and merge results
  const defaultGenders = ["Male", "Female", "Other"];

  const mergedResults = defaultGenders.map(gender => {
    const found = genderCounts.find(result => result._id === gender);
    return {
      gender,
      genderCount: found ? found.count : 0,
      percentage: totalAppointments > 0 ? (found ? (found.count / totalAppointments) * 100 : 0) : 0
    };
  });

  console.log("appointments", mergedResults);

  return mergedResults;
};


export const getAgeGroupAnalyasis = async () => {
  const startOfYear = moment().startOf('year').toDate();
  const endOfYear = moment().endOf('year').toDate();
  const appointments = await Appointment.aggregate([
    {
      $match: {
        appointmentDate: { $gte: startOfYear, $lte: endOfYear },
      },
    },
    {
      $bucket: {
        groupBy: "$age",
        boundaries: [0, 15, 40, 60, 150],
        default: "Other",
        output: {
          count: { $sum: 1 },
        },
      },
    },
    {
      $project: {
        _id: 0,
        ageGroup: {
          $switch: {
            branches: [
              { case: { $eq: ["$_id", 0] }, then: "0-15 yrs" },
              { case: { $eq: ["$_id", 15] }, then: "16-40 yrs" },
              { case: { $eq: ["$_id", 40] }, then: "41-60 yrs" },
              { case: { $eq: ["$_id", 60] }, then: "61+ yrs" },
            ],
            default: "Unknown",
          },
        },
        count: 1,
      },
    },
  ]).exec();
  return appointments;
};


export const getAgeGroupWeeklyAnalysis = async () => {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - dayOfWeek); // Set to the start of the week (Sunday)
  startOfWeek.setHours(0, 0, 0, 0); // Set time to the beginning of the day

  const endOfWeek = new Date(today);
  endOfWeek.setHours(23, 59, 59, 999); // Set time to the end of the day

  const ageGroup = await Appointment.aggregate([
    {
      $match: {
        appointmentDate: {
          $gte: startOfWeek,
          $lte: endOfWeek,
        },
      },
    },
    {
      $bucket: {
        groupBy: "$age",
        boundaries: [0, 15, 40, 60, 150],
        default: "Other",
        output: {
          count: { $sum: 1 },
        },
      },
    },
    {
      $project: {
        _id: 0,
        ageGroup: {
          $switch: {
            branches: [
              { case: { $eq: ["$_id", 0] }, then: "0-15 yrs" },
              { case: { $eq: ["$_id", 15] }, then: "16-40 yrs" },
              { case: { $eq: ["$_id", 40] }, then: "41-60 yrs" },
              { case: { $eq: ["$_id", 60] }, then: "61+ yrs" },
            ],
            default: "Unknown",
          },
        },
        count: 1,
      },
    },
  ]).exec();;

  const defaultAgeGroups = [
    { ageGroup: "0-15 yrs", count: 0 },
    { ageGroup: "16-40 yrs", count: 0 },
    { ageGroup: "41-60 yrs", count: 0 },
    { ageGroup: "61+ yrs", count: 0 },
  ];

  // Merge default age groups with actual results
  const mergedResults = defaultAgeGroups.map(group => {
    const found = ageGroup.find(result => result.ageGroup === group.ageGroup);
    return found || group;
  });

  return mergedResults;
};


export const getCustomAgeGroupAnalysis = async (startDate, endDate) => {
  const start =  new Date(startDate);
  const end = new Date(endDate);

  
  const appointments = await Appointment.aggregate([
    {
      $match: {
        appointmentDate: {
          $gte: start,
          $lte: end,
        },
      },
    },
    {
      $bucket: {
        groupBy: "$age",
        boundaries: [0, 15, 40, 60, 150],
        default: "Other",
        output: {
          count: { $sum: 1 },
        },
      },
    },
    {
      $project: {
        _id: 0,
        ageGroup: {
          $switch: {
            branches: [
              { case: { $eq: ["$_id", 0] }, then: "0-15 yrs" },
              { case: { $eq: ["$_id", 15] }, then: "16-40 yrs" },
              { case: { $eq: ["$_id", 40] }, then: "41-60 yrs" },
              { case: { $eq: ["$_id", 60] }, then: "61+ yrs" },
            ],
            default: "Unknown",
          },
        },
        count: 1,
      },
    },
  ]).exec();
  return appointments;
};

export const patientAnalyasis = async () => {
  const patientRatio = await Appointment.aggregate([
    {
      $group: {
        _id: {
          month: { $month: "$appointmentDate" },
          year: { $year: "$appointmentDate" },
          patientId: "$patientId",
        },
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: {
          month: "$_id.month",
          year: "$_id.year",
        },
        newPatients: {
          $sum: {
            $cond: [{ $eq: ["$count", 1] }, 1, 0],
          },
        },
        repeatedPatients: {
          $sum: {
            $cond: [{ $gt: ["$count", 1] }, 1, 0],
          },
        },
      },
    },
    {
      $project: {
        month: "$_id.month",
        year: "$_id.year",
        newPatients: 1,
        repeatedPatients: 1,
        _id: 0,
      },
    },
    { $sort: { year: 1, month: 1 } },
  ]);

  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1; // getMonth() returns 0-based index, so add 1

  const months = Array.from({ length: currentMonth }, (_, i) => i + 1);
  const result = months.map((month) => {
    const data = patientRatio.find(
      (item) => item.month === month && item.year === currentYear
    );
    return {
      month: monthNames[month - 1],
      year: currentYear,
      newPatients: data ? data.newPatients : 0,
      repeatedPatients: data ? data.repeatedPatients : 0,
    };
  });

  return result;
};

export const patientWeeklyAnalysis = async () => {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - dayOfWeek); // Set to the start of the week (Sunday)
  startOfWeek.setHours(0, 0, 0, 0); // Set time to the beginning of the day

  const endOfWeek = new Date(today);
  endOfWeek.setHours(23, 59, 59, 999); // Set time to the end of the day

  const patientRatio = await Appointment.aggregate([
    {
      $match: {
        appointmentDate: {
          $gte: startOfWeek,
          $lte: endOfWeek,
        },
      },
    },
    {
      $group: {
        _id: {
          dayOfWeek: { $dayOfWeek: "$appointmentDate" }, 
          patientId: "$patientId",
        },
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: "$_id.dayOfWeek",
        newPatients: {
          $sum: {
            $cond: [{ $eq: ["$count", 1] }, 1, 0],
          },
        },
        repeatedPatients: {
          $sum: {
            $cond: [{ $gt: ["$count", 1] }, 1, 0],
          },
        },
      },
    },
    {
      $project: {
        dayOfWeek: "$_id",
        newPatients: 1,
        repeatedPatients: 1,
        _id: 0,
      },
    },
    { $sort: { dayOfWeek: 1 } },
  ]);

  // Create an array with all days from Sunday (0) to the current day of the week
  const allDays = Array.from({ length: dayOfWeek + 1 }, (_, i) => ({
    dayOfWeek: i + 1,
    newPatients: 0,
    repeatedPatients: 0,
  }));


  const mergedResult = allDays.map(day => {
    const found = patientRatio.find(item => item.dayOfWeek === day.dayOfWeek);
    return found || day;
  });

  const weekdays = ["Sun", "Mon", "Tues", "Wed", "Thur", "Fri", "Sat"];

  const result = mergedResult.map(item => ({
    weekday: weekdays[item.dayOfWeek - 1],
    newPatients: item.newPatients,
    repeatedPatients: item.repeatedPatients,
  }));

  return result;
};



export const patientCustomAnalysis = async (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const allDates = generateDateRange(startDate, endDate);

  console.log("start",start,"end",end);

  const patientRatio = await Appointment.aggregate([
    {
      $match: {
        appointmentDate: {
          $gte: start,
          $lte: end,
        },
      },
    },
    {
      $group: {
        _id: {
          date:  "$appointmentDate" ,
          patientId: "$patientId",
        },
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: "$_id.date",
        newPatients: {
          $sum: {
            $cond: [{ $eq: ["$count", 1] }, 1, 0],
          },
        },
        repeatedPatients: {
          $sum: {
            $cond: [{ $gt: ["$count", 1] }, 1, 0],
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        date: "$_id",
        newPatients: 1,
        repeatedPatients: 1,
      },
    },
    { $sort: { date: 1 } },
  ]);

  console.log("patientRatio",allDates);


  const AllDates = allDates.map(date => ({
    newPatients: 0,
    repeatedPatients: 0,
    date: moment(date).format('YYYY-MM-DD'), // Ensure the date format matches your data
  }));

  // Merge the results
  const mergedResult = AllDates.map(day => {
    const found = patientRatio.find(item => moment(item.date).isSame(day.date, 'day'));
    return found || day;
  });

  return mergedResult;
};
