import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { deleteClient } from '../../../helpers/clinic/client.helper.js';
import { Client } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js"; 
jest.setTimeout(30000); 

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('deleteClient function', () => {
  let clinicId;

  beforeEach(async () => {

    await Client.deleteMany({});

    clinicId = new mongoose.Types.ObjectId(); 
    await Client.create({
      _id: clinicId,
      clinicName: 'Test Clinic',
      address: '123 Test Street',
      created: { on: new Date('2024-04-27T06:52:20.110Z') },
      isdeleted: false,
      logo: '',
      modified: { on: new Date('2024-09-17T09:15:18.594Z') },
    });
  });

  afterEach(async () => {
    await Client.deleteMany({});
  });

  it('should mark client as deleted when client exists', async () => {

    const result = await deleteClient(clinicId);

    expect(result.isdeleted).toBe(true); 
    const updatedClient = await Client.findById(clinicId).exec();

    expect(updatedClient.isdeleted).toBe(true); 
  });

  it('should return "Client not found" if client does not exist', async () => {
    const invalidClientId = new mongoose.Types.ObjectId(); 
    const result = await deleteClient(invalidClientId);

    expect(result).toBe("Client not found");
  });
});
