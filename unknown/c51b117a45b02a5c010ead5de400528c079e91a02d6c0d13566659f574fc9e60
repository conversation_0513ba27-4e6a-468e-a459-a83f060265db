import { jest } from "@jest/globals";
const { mockStaffHelper } = await import("../mocks/mock.staff.helper");
mockStaffHelper();
const { getDoctorsWithAvailableTime } = await import('../../controllers/staffs/staffs.controller.js'); 
const { getDoctorsWithTimeSlots }= await import('../../helpers/staff/staff.helper.js');

describe('getDoctorsWithAvailableTime', () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: {}
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should return doctors with available time slots successfully', async () => {
        const mockClinicId = 'clinic1';
        const mockDoctors = [{ id: 1, name: 'Dr<PERSON> <PERSON>', availableSlots: [] }];
        
        getDoctorsWithTimeSlots.mockResolvedValue(mockDoctors);

        req.query = { clinicId: mockClinicId };

        await getDoctorsWithAvailableTime(req, res);

        expect(getDoctorsWithTimeSlots).toHaveBeenCalledWith(mockClinicId);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockDoctors);
    });
});
