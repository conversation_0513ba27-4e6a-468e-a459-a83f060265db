import { jest } from "@jest/globals";
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
mockApointmentHelper();
const { getPatientAppointmentById } = await import("../../controllers/appointments/appointment.controller.js"); 
const { getAppointmentWithPatient } = await import("../../helpers/appointment/appointment.helper.js");
describe('getPatientAppointmentById', () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: {
                id: 'patientId123'
            }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should return 200 with the patient appointment data', async () => {
        const mockAppointments = [
            {
                _id: 'appointmentId123',
                patientId: 'patientId123',
                appointmentDate: '2024-09-02',
                details: 'Test Appointment'
            }
        ];

        getAppointmentWithPatient.mockResolvedValue(mockAppointments);

        await getPatientAppointmentById(req, res);

        expect(getAppointmentWithPatient).toHaveBeenCalledWith(req.query.id);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockAppointments);
    });

    
});
