import { jest } from "@jest/globals";
const { mockPatientHelper } = await import("../mocks/mock.patient.helper.js");
const { mockBlobHelper } = await import("../mocks/mock.blob.helper.js");
mockBlobHelper()
mockPatientHelper();

const { updateMedicalRec } = await import('../../controllers/patients/patients.controller.js'); 
const { BlobHelper } = await import("../../helpers/storage/blob.helper.js"); 

const req = {
  body: {
    clientId: 'testClientId', // Add this to match the BlobHelper constructor
    medicalRecordName: 'record1,record2',
    profileName: 'profilePic.jpg',
  },
  files: {
    profile: [{ 
      originalname: 'profilePic.jpg', 
      buffer: Buffer.from('test'),
      fieldname: 'profile',
      encoding: '7bit',
      mimetype: 'image/jpeg'
    }],
    medicalRecords: [
      { 
        originalname: 'record1.jpg', 
        buffer: Buffer.from('test1'),
        fieldname: 'medicalRecords',
        encoding: '7bit',
        mimetype: 'image/jpeg'
      },
      { 
        originalname: 'record2.jpg', 
        buffer: Buffer.from('test2'),
        fieldname: 'medicalRecords',
        encoding: '7bit',
        mimetype: 'image/jpeg'
      },
    ],
  },
};

const res = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn(),
};

describe("updateMedicalRec", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock process.env variables
    process.env.AZURE_CONNECTIONSTRING = 'test-connection-string';
    process.env.CLINICBLOB_CONTAINER_PREFIX = 'test-prefix-';
    process.env.PATIENT_BLOB_FOLDER = 'patient/';
  });

  it('should upload profile and medical records and return success', async () => {
    // Create a mock for UploadBlob method
    const uploadBlobMock = jest.fn();
    
    // Mock BlobHelper constructor to return an object with the mocked UploadBlob method
    BlobHelper.mockImplementation(() => ({
      UploadBlob: uploadBlobMock
    }));

    await updateMedicalRec(req, res);

    // Verify profile upload
    expect(uploadBlobMock).toHaveBeenCalledWith(
      req.files.profile[0],
      process.env.PATIENT_BLOB_FOLDER,
      req.body.profileName
    );

    // Verify medical records uploads
    expect(uploadBlobMock).toHaveBeenCalledWith(
      req.files.medicalRecords[0],
      process.env.PATIENT_BLOB_FOLDER,
      'record1'
    );
    expect(uploadBlobMock).toHaveBeenCalledWith(
      req.files.medicalRecords[1],
      process.env.PATIENT_BLOB_FOLDER,
      'record2'
    );

    // Expect a successful response
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ success: true });
  });

  it("should handle errors and log them", async () => {
    const uploadBlobMock = jest.fn().mockImplementation(() => {
      throw new Error("Upload error");
    });
    
    BlobHelper.mockImplementation(() => ({
      UploadBlob: uploadBlobMock
    }));

    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

    await expect(updateMedicalRec(req, res)).rejects.toThrow("Upload error");

    // Verify that error handling occurs
    expect(consoleSpy).toHaveBeenCalled();

    consoleSpy.mockRestore();
  });
});