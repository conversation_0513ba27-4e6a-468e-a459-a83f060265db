import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { getClientSetting } from '../../../helpers/clinic/client.helper.js';
import { Client } from '../../../model/clinics.model.js';

import { setup, teardown } from '../../../setup.js';

jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe('getClientSetting Function Test', () => {
  let testClientId;

  beforeEach(async () => {
    // Create a test client
    const client = new Client({
    
      clinicName: 'Test Clinic',
      patientId: {
        prefix: "SD"
      },
      phonepeSetting: {
        merchantId: "marchant id",
        saltKey: "salt key",
        saltIndex: ""
      },
      staffId: {
        prefix: "CHENNAI"
      },
      timeSlots: [
        {
          startTime: {
            hours: 3,
            min: 0,
            tt: "PM"
          },
          endTime: {
            hours: 5,
            min: 59,
            tt: "PM"
          },
        }
    ]
    
    });
    await client.save();
    testClientId = client._id;
  });

  it('should retrieve a client by id', async () => {
    const client = await getClientSetting(testClientId);
    expect(client).toBeDefined();
    expect(client.clinicName).toBe('Test Clinic');
    expect(client.phonepeSetting.merchantId).toStrictEqual("marchant id");
    expect(client.phonepeSetting.saltKey).toStrictEqual("salt key");

  });

  it('should return an error if client is not found', async () => {
    const clientId = new mongoose.Types.ObjectId();
    const client = await getClientSetting(clientId);
    expect(client).toBeNull();
  });


});