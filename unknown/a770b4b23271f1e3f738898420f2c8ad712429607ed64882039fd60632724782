import { jest } from "@jest/globals";
const { mockStaffHelper } = await import("../mocks/mock.staff.helper");
mockStaffHelper();
const { checkStaffMobileExists } = await import('../../controllers/staffs/staffs.controller.js'); 
const { checkMobileNumberPresent }= await import('../../helpers/staff/staff.helper.js');

describe('checkStaffMobileExists', () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: {}
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should return true if the mobile number exists', async () => {
        const mockMobile = '1234567890';
        const mockId = '12345';
        checkMobileNumberPresent.mockResolvedValue(true);
        req.query = { mobile: '1234567890', id: '12345' };
        await checkStaffMobileExists(req, res);
        expect(checkMobileNumberPresent).toHaveBeenCalledWith(mockMobile, mockId);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ exists: true });
    });

    it('should return false if the mobile number does not exist', async () => {
        const mockMobile = '1234567890';
        const mockId = '12345';
        checkMobileNumberPresent.mockResolvedValue(false);
        req.query = { mobile: mockMobile, id: mockId };
        await checkStaffMobileExists(req, res);
        expect(checkMobileNumberPresent).toHaveBeenCalledWith(mockMobile, mockId);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ exists: false });
    });

});
