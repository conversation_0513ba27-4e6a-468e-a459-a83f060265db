import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { overview } from '../../../helpers/appointment/appointment.helper.js';
import { Appointment } from '../../../model/clinics.model.js';
import { setup, teardown } from '../../../setup.js'; 

jest.setTimeout(30000); 

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe('overview function', () => {
  let clientId,appointmentId;
  beforeEach(async () => {
    await Appointment.deleteMany({});
    
    clientId = new mongoose.Types.ObjectId();
    appointmentId = new mongoose.Types.ObjectId();

  
    await Appointment.create({
      _id:appointmentId,
      name: 'Test',
      mobile: '**********',
      virtualConsultation: true,
      appointmentDate: new Date(),
      timeSlot: '10:00 AM - 11:00 AM',
      doctor<PERSON>ame: '<PERSON><PERSON> <PERSON>',
      patientId: new mongoose.Types.ObjectId(),
      gender: 'Male',
      age: 30,
      clinic: clientId,
      isDeleted: false,
      isCanceled: false,  
      paymentStatus: false, 
    });
  });
  

  it('should return appointments with default sorting and no keyword', async () => {
    const result = await overview(clientId, 1, 10, null, null, null, 'Upcoming');
 
    expect(result.data).toHaveLength(1);
    expect(result.data[0].name).toBe('Test'); 
    expect(result.data[0].mobile).toBe('**********'); 
    expect(result.data[0].doctorName).toBe('Dr. Smith'); 
    expect(result.totalCount).toBe(1);
  });
  

  it('should return appointments with default sorting and no keyword', async () => {
    const result = await overview(clientId, 1, 10, null, null, null, 'Upcoming');
    expect(result.data).toHaveLength(1);
    expect(result.data[0].name).toBe('Test');
    expect(result.data[0].mobile).toBe('**********'); 
    expect(result.data[0].doctorName).toBe('Dr. Smith'); 
    expect(result.totalCount).toBe(1);
  });

  it('should handle cases with different status filters (Cancelled)', async () => {
    await Appointment.updateOne({_id:appointmentId, clinic: clientId }, { $set: { isCanceled: true } });
    const result = await overview(clientId, 1, 10, null, null, null, 'Cancelled');
    expect(result.data).toHaveLength(1);
    expect(result.data[0].name).toBe('Test');
    expect(result.data[0].mobile).toBe('**********'); 
    expect(result.data[0].doctorName).toBe('Dr. Smith'); 
    expect(result.totalCount).toBe(1);
  });

  it('should return an empty array if no appointments are found', async () => {
    await Appointment.deleteMany({ clinic: clientId });

    const result = await overview(clientId, 1, 10, null, null, null, 'Upcoming');

    expect(result.data).toHaveLength(0);
    expect(result.totalCount).toBe(0);
  });
});
