import { jest } from "@jest/globals";
import mongoose from 'mongoose';
import { setStarted } from '../../../helpers/appointment/appointment.helper.js'; 
import { Appointment } from '../../../model/clinics.model.js';
import { setup, teardown } from '../../../setup.js';

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('setStarted', () => {
  let appointmentId;
  let user;

  beforeEach(async () => {
    await Appointment.deleteMany({}); 

    appointmentId = new mongoose.Types.ObjectId();
    user = { id: 'user123', name: 'Test User' };

    await Appointment.create({
      _id: appointmentId,
      name: '<PERSON>',
      mobile: '**********',
      gender: 'Male',
      age: 30,
      birthDate: '1993-05-15',
      doctorId: new mongoose.Types.ObjectId(),
      started: { yes: false }, 
      patientId: new mongoose.Types.ObjectId(),
      appointmentDate: '2024-09-30T10:00:00Z',
      timeSlot: '9:00 AM - 10:00 AM',
      rescheduled: {},
      modified: {},
      isDeleted: false,
      isCanceled: false,
      medicalRecords: [
        {
          fileName: "diabetes_report.pdf",
          blobName: "45678_diabetes_report"
        }
      ],
      procedureRecords: [
        {
          fileName: "procedurefile.pdf",
          blobName: "45678_diabetes_report1"
        }
      ],
      prescriptionRecords: [
        {
          fileName: "prescriptionRecords.pdf",
          blobName: "45678_diabetes_report2"
        }
      ],
    });
  });

  it('should set the appointment as started and update the modified field', async () => {
    const result = await setStarted(appointmentId, user); 

    expect(result.started.yes).toBe(true);
  });

  it('should throw an error if the appointment is not found', async () => {
    const nonexistentAppointmentId = new mongoose.Types.ObjectId(); 

    await expect(setStarted(nonexistentAppointmentId, user)).rejects.toThrow('appointment not found');
  });
});
