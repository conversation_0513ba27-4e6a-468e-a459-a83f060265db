import moment from "moment";
export function findIndexInArray(myArray,searchTerm){
    let index = -1;
    for(var i = 0, len = myArray.length; i < len; i++) {
        if (myArray[i].clinicId === searchTerm) {
            index = i;
            return index;
        }
    }
}
export const generateDateRange = (startDate, endDate) => {
    const dates = [];
    let currentDate = new Date(startDate);
    const end = new Date(endDate);
  
    while (currentDate <= end) {
      dates.push(moment(currentDate).format('YYYY-MM-DD'));
      currentDate.setDate(currentDate.getDate() + 1);
    }
  
    return dates;
  };