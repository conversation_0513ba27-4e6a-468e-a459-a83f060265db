import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { addUpdateVitals } from "../../../helpers/appointment/write-prescription/vitals.helper.js";
import { Vitals } from "../../../model/clinics.model.js";
import { setup, teardown } from "../../../setup.js";
import { Patient } from "../../../model/clinics.model.js";
import { Appointment } from "../../../model/clinics.model.js";
import { Client } from "../../../model/clinics.model.js";

jest.setTimeout(30000);

describe("addUpdateVitals function", () => {
  let patientId;
  let appointmentId;
  let clinicId;

  beforeAll(async () => {
    await setup();
    
    // Create test data once before all tests
    const patient = new Patient({
      firstName: "John",
      lastName: "Doe",
      mobile: "**********",
      patientId: "PAT001",
      prefix: "Mr.",
      deleted: false,
    });
    patientId = (await patient.save())._id;

    const clinic = await Client.create({
      clinicName: "TEST",
      address: "18 veera alagamman koil street",
      created: { on: new Date("2024-04-27T06:52:20.110Z") },
      isDeleted: false,
      logo: "",
      modified: { on: new Date("2024-09-17T09:15:18.594Z") },
      patientId: { prefix: "SD", suffix: "" },
    });
    clinicId = clinic._id;

    const appointment = new Appointment({
      mobile: "**********",
      name: "First Patient",
      gender: "Male",
      age: 33,
      patientId: patientId,
      clinic: clinicId,
      doctorName: "Dr. Smith",
      appointmentDate: new Date(),
      timeSlot: "10:00 AM",
      reason: "Checkup",
      paymentStatus: true,
      virtualConsultation: false,
      isCanceled: false,
    });
    appointmentId = (await appointment.save())._id;
  });

  afterAll(async () => {
    await teardown();
  });

  beforeEach(async () => {
    await Vitals.deleteMany({});
  });

  it("should add new vitals when no existing record is found", async () => {
    const data = {
      vitals: {
        bloodPressure: {
          systolic: "120",
          diastolic: "80"
        },
        spo2: "98",
        temperature: "37",
        height: "170",
        weight: "65",
        pulseRate: "75",
        rbs: "90",
        heartRate: "72",
        respiratoryRate: "18"
      }
    };

    const user = { id: "user1", name: "User One" };

    const result = await addUpdateVitals(
      data,
      user,
      patientId,
      clinicId,
      appointmentId
    );

    expect(result).toBeDefined();
    expect(result.bloodPressure.diastolic).toBe(80);
    expect(result.bloodPressure.systolic).toBe(120);
    expect(result.rbs).toBe(90);
    expect(result.temperature).toBe(37);
    expect(result.weight).toBe(65);
  });

  it("should update existing vitals when a record is found", async () => {
    // Create an existing vitals record first
    const existingVitals = await Vitals.create({
      bloodPressure: { systolic: 110, diastolic: 70 },
      spo2: 97,
      temperature: 36.5,
      height: 168,
      weight: 60,
      pulseRate: 70,
      rbs: 85,
      heartRate: 70,
      respiratoryRate: 16,
      clinic: clinicId,
      patient: patientId,
      appointment: appointmentId
    });

    const newData = {
      vitals: {
        bloodPressure: { systolic: "120", diastolic: "80" },
        spo2: "98",
        temperature: "37",
        height: "170",
        weight: "65",
        pulseRate: "75",
        rbs: "90",
        heartRate: "72",
        respiratoryRate: "18",
      },
    };

    const user = { id: "user1", name: "User One" };

    // Debug: Log the existing vitals before update
    console.log("Existing vitals before update:", existingVitals);

    const result = await addUpdateVitals(
      newData, 
      user, 
      patientId, 
      clinicId, 
      appointmentId
    );

    // Debug: Log the result from addUpdateVitals
    console.log("Result from addUpdateVitals:", result);

    // Verify the update by fetching fresh from database
    const updatedVitals = await Vitals.findById(existingVitals._id);
    
    // Debug: Log what was actually stored in database
    console.log("Updated vitals from database:", updatedVitals);

    expect(updatedVitals).toBeDefined();
    expect(updatedVitals.bloodPressure.diastolic).toBe(80);
    expect(updatedVitals.bloodPressure.systolic).toBe(120);
    expect(updatedVitals.spo2).toBe(98);
    expect(updatedVitals.temperature).toBe(37);
    expect(updatedVitals.height).toBe(170);
    expect(updatedVitals.weight).toBe(65);
    expect(updatedVitals.pulseRate).toBe(75);
    expect(updatedVitals.rbs).toBe(90);
    expect(updatedVitals.respiratoryRate).toBe(18);
  });
});