// import {   checkVerificationToken,   sendVerificationToken,
// resendVerificationToken, } from "../../config/twilio.js"; import {
// StartChatBotHelper } from "../../helper/user/chatBotHelper.js";
import {forgotPasswordSendOTP, userVerfication} from "../../helpers/user/userVerify.js";
import {createToken} from "../../utils/token.utils.js";
import bcrypt from "bcrypt";
import {User} from "../../model/clinics.model.js";
import jwt from "jsonwebtoken";
import {verifyUserOTPByEmail, verifyUserOTPByMobile} from "../../helpers/user/userotp.helper.js";
import { setNewPassword } from "../../helpers/user/user.helper.js";

export const loginVerify = async(req, res) => {
        let {emailOrPhone, password} = req.body;
        const userDetail = await userVerfication(emailOrPhone);
        if (userDetail) {
            const encryptPassword = userDetail.password;
            const matchPassword = await bcrypt.compare(password, encryptPassword);
            if (userDetail.active === true && matchPassword) {
                const token = createToken(userDetail._id);
                res
                    .status(200)
                    .json({
                        success: true,
                        token,
                        user: {
                            id: userDetail._id,
                            firstName: userDetail.firstName,
                            lastName: userDetail.lastName,
                            email: userDetail.email,
                            isAdmin: userDetail.isAdmin,
                            isSuperAdmin: userDetail.isSuperAdmin,
                            linkedClinics: userDetail
                                ?.linkedClinics,
                            profilePic: userDetail
                                ?.staffDetail
                                    ?.profilePic,
                            isDoctor:userDetail.staffDetail.isDoctor,
                            specialization:userDetail.staffDetail.specialization

                        },
                        message: "logged in"
                    });
            } else {
                res
                    .status(200)
                    .json({success: false, message: "incorrect password"});
            }
        } else {
            console.log("invalid user");
            res
                .status(200)
                .json({success: false, message: "incorrect user"});
        }
};

export const tokenVerificationApi = async(req, res) => {
        const token = req.body.token;

        if (!token) {
            res.json({user: false});
        } else {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            const userId = decoded.id;
            const user = await User.findById(userId);
            if (user) {
                res
                    .status(200)
                    .json({user: true});
            } else {
                res.json({user: false});
            }
        }
};

export const checkOTP = async(req, res) => {
    const data = req.body;
    let emailOrPhone=data.mobile;
    let isVerified = false;
    if (emailOrPhone != null && emailOrPhone.indexOf("@") > 0) {
        isVerified = await verifyUserOTPByEmail(emailOrPhone,data?.password);
    } else {
        isVerified = await verifyUserOTPByMobile(emailOrPhone,data?.password);
    }
    if (isVerified) {
        res
            .status(200)
            .json({success: true, message: "OTP Verified"});
    } else {
        res
            .status(200)
            .json({success: false, message: "incorrect OTP"});
    }
}
export const resendOTP = async(req, res) => {
    const data = req.body;
    var [forgototp,id] = await forgotPasswordSendOTP(data.emailOrPhone);
    if (forgototp) {
        res
            .status(200)
            .json({success: true,token:id, message: "OTP sent"});
    } else {
        res
            .status(400)
            .json({success: false, message: "incorrect user"});
    }
}
export const forgotPassword = async(req, res) => {
    const data = req.body;
    var [forgototp,id] = await forgotPasswordSendOTP(data.emailOrPhone.mobile);
    if (forgototp) {
        res
            .status(200)
            .json({success: true,token:id, message: "OTP sent"});
    } else {
        res
            .status(200)
            .json({success: false, message: "incorrect user"});
    }
}
export const setPassword = async(req, res) => {
    const data = req.body;
    var result = await setNewPassword(data.token,data.password);
    if (result) {
        res
            .status(200)
            .json({success: true, message: "Password set"});
    } else {
        res
            .status(200)
            .json({success: false, message: "incorrect user"});
    }
}