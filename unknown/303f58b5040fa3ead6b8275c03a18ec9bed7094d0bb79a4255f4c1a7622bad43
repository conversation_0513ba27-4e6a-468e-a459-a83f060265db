import { Router } from "express";
import {
  getNotificationOverview,
  notificationUpsert,
} from "./../../controllers/notification/notification.controller.js";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";
import { validateGetNotificationOverview, validateNotificationUpsert } from "../../validation/notification/notification.validation.js";

const notification = Router();
notification.post("/addnotification", authorizationCheck, validateNotificationUpsert, async (req, res, next) => {
  try {
    return await notificationUpsert(req, res);
  }
  catch (e) {
    next(e)
  }
});


/**
 * @swagger
 * /notification/getnotification:
 *   get:
 *     tags:
 *       - notification
 *     summary: Retrieve notification overview for a user
 *     description: Fetches a paginated list of notifications for a user based on the specified filters and sorting options.
 *     parameters:
 *       - name: page
 *         in: query
 *         required: true
 *         description: The page number to retrieve (1-based index).
 *         schema:
 *           type: integer
 *           example: 1
 *       - name: size
 *         in: query
 *         required: true
 *         description: The number of notifications per page.
 *         schema:
 *           type: integer
 *           example: 10
 *       - name: keyword
 *         in: query
 *         required: false
 *         description: A keyword to filter notifications based on the notification message.
 *         schema:
 *           type: string
 *           example: "appointment"
 *       - name: sortby
 *         in: query
 *         required: false
 *         description: The field to sort notifications by.
 *         schema:
 *           type: string
 *           example: "showTime"
 *       - name: direction
 *         in: query
 *         required: false
 *         description: The direction of the sort, either "asc" for ascending or "desc" for descending.
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           example: "desc"
 *       - name: clinicId
 *         in: query
 *         required: true
 *         description: The ID of the clinic for which notifications are being fetched.
 *         schema:
 *           type: string
 *           example: "60d0fe4f5311236168a109ca"
 *     responses:
 *       200:
 *         description: Successfully retrieved the notification overview.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       notificationMessage:
 *                         type: string
 *                       showTime:
 *                         type: string
 *                         format: date-time
 *                       header:
 *                         type: string
 *                       seen:
 *                         type: boolean
 *                 totalCount:
 *                   type: integer
 *                   example: 50
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
notification.get(
  "/getnotification",
  authorizationCheck,
  validateGetNotificationOverview,
  async (req, res, next) => {
    try {
      return await getNotificationOverview(req, res);
    }
    catch (e) {
      next(e)
    }
  }

);

export default notification;
