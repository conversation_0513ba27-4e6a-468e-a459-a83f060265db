import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";


export const ValidateLoginVerify = async (req, res, next) => {
    await checkSchema({
        emailOrPhone: {
           trim: true,
           escape: true,
            in: ["body"],
            isString: true,
            errorMessage: "Email/Phone is required",
          },
          password: {
            trim: true,
            escape: true,
            in: ["body"],
            isString: true,
            errorMessage: "Password is required",
          },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };



  export const ValidateTokenVerificationApi = async (req, res, next) => {
    await checkSchema({
        token: {
          trim: true,
          escape: true,
            in: ["body"],
            isString: true,
            errorMessage: "token is required",
          },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const ValidateForgotPassword = async (req, res, next) => {
    await checkSchema({
        "emailOrPhone.mobile": {
          trim: true,
          escape: true,
            in: ["body"],
            isString: true,
            errorMessage: "Email/Mobile is required",
          },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const ValidateResendOTP = async (req, res, next) => {
    await checkSchema({
        "emailOrPhone": {
          trim: true,
          escape: true,
            in: ["body"],
            isString: true,
            errorMessage: "Email/Mobile is required",
          },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const ValidateCheckOTP= async (req, res, next) => {
    await checkSchema({
        "mobile": {
          trim: true,
          escape: true,
            in: ["body"],
            isString: true,
            errorMessage: "Email/Mobile is required",
          },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };

  export const ValidateSetPassword= async (req, res, next) => {
    await checkSchema({
        "token": {
          trim: true,
          escape: true,
            in: ["body"],
            isString: true,
            errorMessage: "token is required",
          },
          "password": {
            trim: true,
          escape: true,
            in: ["body"],
            isString: true,
            errorMessage: "password is required",
          },
    }).run(req);
  
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  };
  