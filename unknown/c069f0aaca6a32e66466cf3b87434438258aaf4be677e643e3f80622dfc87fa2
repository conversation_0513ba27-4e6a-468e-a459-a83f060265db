import { getClientDelegation } from "../../config/googleAuthConection.js";
import { v4 as uuidv4 } from 'uuid';
import { Client } from "../../model/clinics.model.js";

export const scheduleMeeting = async (doctorEmail, doctorName, patientEmail, meetingDescription, scheduleTime, scheduleDuration = 30) => {
        const calendar = await getClientDelegation();
        const requestId = uuidv4();
        const event = {
            summary: meetingDescription,
            location: 'Online',
            description: meetingDescription,
            start: {
                dateTime: scheduleTime,
                timeZone: 'Asia/Kolkata'
            },
            end: {
                dateTime: new Date(new Date(scheduleTime).getTime() + scheduleDuration * 60000).toISOString(),
                timeZone: 'Asia/Kolkata'
            },
            attendees: [
                { email: doctorEmail, role:"OWNER", organizer : true },
            ],
            conferenceData: {
                createRequest: {
                    requestId: requestId,
                    conferenceSolutionKey: {
                        type: 'hangoutsMeet',
                    },
                },
            },
            conferenceDataVersion: 1,
            reminders: {
                useDefault: false,
                overrides: [
                    { method: 'popup', minutes: 60 },
                    { method: 'popup', minutes: 10 },
                ],
            },
            organizer: {
                displayName: doctorName,
                email: doctorEmail
              },
              creator: {
                email: doctorEmail,
                displayName: doctorName
            },
        };

        if (patientEmail) {
            event.attendees.push({ email: patientEmail });
        }

        const response =await calendar.events.insert({
            calendarId: "primary",
            resource: event,
            conferenceDataVersion: 1,
            sendUpdates: 'all',
            supportsAttachments: true,
            sendNotifications: true
        });

        console.log(response.data.hangoutLink);
        const hangoutLink = response.data.hangoutLink;
        const eventId = response.data.id;
        return { success: true, link: hangoutLink, id : eventId };
   
};



export async function createGoogleMeetLink(appointment, isVirtual) {
    let clinic = await Client.findOne({ _id: appointment.clinic }).select("googleMeetEmail")
    if (!isVirtual)
        return;
    if (!clinic?.googleMeetEmail) {
        return;
    }
    const doctorEmail = clinic.googleMeetEmail;
    const doctorName = appointment.doctorName;
    const patientEmail = "";
    const description = "Virtual Medical Checkup";
    const scheduledTime = appointment.appointmentDate;

    const res = await scheduleMeeting(doctorEmail, doctorName, patientEmail, description, scheduledTime);

    if (res.success)
        return {
            isSuccess: true,
            data: {
                link: res.link,
                id: res.id
            }
        };

    else
        return {
            isSuccess: false,
            data: res.error
        };

}

export const cancelMeeting = async (eventId) => {
        const calendar = await getClientDelegation();
        try {
            await calendar.events.delete({
                calendarId: "primary",
                eventId: eventId
            });
        } catch (error) {
            console.log(`Cancel Event in Google Meet error: ${error}`)
        }
       
        return { success: true };
    
};