import { sendNotificationViaToken } from "../../config/firebase.admin.js";
import {
  removeStaff,
  staffById,
  upsertStaff,
  staffOverview,
  searchStaff,
  getDoctor,
  checkMobileNumberPresent,
  getDoctorsWithTimeSlots,
  getDoctorTimeSlotsById,
} from "../../helpers/staff/staff.helper.js";
import { BlobHelper } from "../../helpers/storage/blob.helper.js";
import {
  buildNotificationText,
  resultObject,
} from "../../utils/common.utils.js";

export const staffUpsert = async (req, res) => {
    const data = req.body.data;
    const user = req.user;
    var result = await upsertStaff(data, data.id, user);
    console.log("===>",result);
    if (result.code === 400) {
      return res.status(400).json(result);
  }
   
    if (data != null) {
      var message = buildNotificationText(
        data.firstName,
        data.lastName,
        " has been added",
        req.user
      );
      if (req.Notificationkey) {
      await sendNotificationViaToken(
        req.Notificationkey,
        message,
        "Staff Detail",
        true,
        data.clientId,
        req.user.id
      );
      }}


    res.status(200).json(result);
  }

export const updateDoc = async (req, res) => {
    const data = req.body;
    var blobObj = new BlobHelper(process.env.AZURE_CONNECTIONSTRING, process.env.CLINICBLOB_CONTAINER_PREFIX + data.clientId);
    if (req.files.profile != undefined)
      blobObj.UploadBlob(req.files.profile[0], "staff/", data.profileName);
    var docName = JSON.parse(data.documentName);
    //.split(",");
    for (let index = 0; index < req.files.documents?.length; index++) {
      const element = req.files.documents[index];
      var blobName = "";
      for (let j = 0; j < docName.length; j++) {
        blobName =
          docName[j].fileName == element.originalname
            ? docName[j].blobName
            : blobName;
      }
      blobObj.UploadBlob(element, "staff/", blobName);
    }
    res.status(200).json({ success: true });
};
export const getStaffOverview = async (req, res) => {
    const data = req.query;
    var overviewData = await staffOverview(
      data.clientId,
      data.page,
      data.size,
      data.keyword,
      data.sortby,
      data.direction,
      data.status
    );
    res.status(200).json(overviewData);
};

export const getStaff = async (query, res) => {
    const data = query;
    var overviewData = await staffById(data.query.id);
    res.status(200).json(overviewData);
};

export const checkStaffMobileExists = async (query, res) => {
    const data = query;
    var result = await checkMobileNumberPresent(
      data.query.mobile,
      data.query.id
    );
    res.status(200).json({ exists: result });
};
export const getDoctorsByClinic = async (query, res) => {
    const data = query;
    var doctors = await getDoctor(data.query.clinicId);
   return res.status(200).json(doctors);
};
export const getDoctorsWithAvailableTime = async (query, res) => {
    const data = query;
    let doctors = await getDoctorsWithTimeSlots(data.query.clinicId);
   return res.status(200).json(doctors);
};
export const getDoctorTimeSlot = async (query, res) => {
    const data = query;
    let doctors = await getDoctorTimeSlotsById(data.query.id);
   return res.status(200).json(doctors);
};

export const deleteStaff = async (query, res) => {
    const data = query;
    var staff = await removeStaff(data.query.id);
    if (staff != null) {
      var message = buildNotificationText(
        staff.firstName,
        staff.lastName,
        " has been deleted",
        query.user
      );
      await sendNotificationViaToken(
        data.Notificationkey,
        message,
        "Staff Detail",
        true,
        staff.clinic,
        query.user.id
      );
    }
    var result = resultObject(200, null, true, {});
   return res.status(200).json(result);
};
export const searchStaffName = async (req, res) => {
    const data = req.query;
    var overviewData = await searchStaff(data.clientId,data.name, data.size);
    return res.status(200).json({
      data: overviewData.staffCollection,
    });
};
