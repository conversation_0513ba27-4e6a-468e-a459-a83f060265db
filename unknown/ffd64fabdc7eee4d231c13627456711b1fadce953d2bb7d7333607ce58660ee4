import { jest } from "@jest/globals";
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
mockApointmentHelper();

const { setAppointmentStarted } = await import('../../controllers/appointments/appointment.controller.js'); 
const { setStarted }= await import('../../helpers/appointment/appointment.helper.js'); 
describe('setAppointmentStarted', () => {
    let req, res;

    beforeEach(() => {
        req = {
            body: {
                data: {
                    id: 'appointmentId123'
                }
            },
            user: {
                id: 'userId123'
            }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should return 200 with the updated appointment data', async () => {
        const mockAppointments = {
            _id: 'appointmentId123',
            status: 'Started'
        };

        setStarted.mockResolvedValue(mockAppointments);

        await setAppointmentStarted(req, res);

        expect(setStarted).toHaveBeenCalledWith(req.body.data.id, req.user);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockAppointments);
    });

});
