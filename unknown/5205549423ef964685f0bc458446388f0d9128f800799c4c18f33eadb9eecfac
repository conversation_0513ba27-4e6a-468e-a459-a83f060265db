import { bookedAppointmentSchema } from "healtether.models/clinics.schema/appointment/booked-appointment.schema.js";
import { queuedAppointmentSchema } from "healtether.models/clinics.schema/appointment/queued-appointment.schema.js";
import { clientAutoIdSchema } from "healtether.models/clinics.schema/autoIdentifier.schema.js";
import { clinicSchema } from "healtether.models/clinics.schema/client.schema.js";
import { clinicGroupSchema } from "healtether.models/clinics.schema/clinicgroup.schema.js";
import { invoiceSchema } from "healtether.models/clinics.schema/invoice.schema.js";
import { userNotificationSchema } from "healtether.models/clinics.schema/notification.schema.js";
import { patientSchema } from "healtether.models/clinics.schema/patient.schema.js";
import { paymentSchema } from "healtether.models/clinics.schema/payment.schema.js";
import { masterAllergiesSchema, userAllergiesSchema } from "healtether.models/clinics.schema/prescription/allergie.schema.js";
import { familyHistorySchema } from "healtether.models/clinics.schema/prescription/family-history.schema.js";
import { masterMedicationSchema, pastMedicationSchema } from "healtether.models/clinics.schema/prescription/medication-history.schema.js";
import { pastDiseaseHistorySchema } from "healtether.models/clinics.schema/prescription/past-history.schema.js";
import { procedureSchema } from "healtether.models/clinics.schema/prescription/past-procedure.schema.js";
import { personalHistorySchema } from "healtether.models/clinics.schema/prescription/personal-history.schema.js";
import { frequentDiagnosisSchema, frequentDrugsSchema, frequentLabtestSchema, frequentSymptomsSchema, prescriptionSchema } from "healtether.models/clinics.schema/prescription/prescription.schema.js";
import { vitalsSchema } from "healtether.models/clinics.schema/prescription/vitals.schema.js";
import { staffSchema } from "healtether.models/clinics.schema/staff.schema.js";
import { mapClinicUserSchema, userSchema } from "healtether.models/clinics.schema/user.schema.js";
import { userOTPSchema } from "healtether.models/clinics.schema/userotp.schema.js";
import mongoose from "mongoose";
import { BOOKED_APPOINTMENT_COLLECTION, CLIENT_AUTOID_COLLECTION, CLIENT_COLLECTION, CLINIC_GROUP_COLLECTION, FREQUENT_DIAGNOSIS_COLLECTION, FREQUENT_DRUGS_COLLECTION, FREQUENT_LABTEST_COLLECTION, FREQUENT_SYMPTOM_COLLECTION, HISTORY_ALLERGIES_COLLECTION, 
    HISTORY_FAMILY_DISEASES_COLLECTION, HISTORY_MEDICATIONS_COLLECTION, HISTORY_PAST_DISEASES_COLLECTION, HISTORY_PERSONAL_DISEASES_COLLECTION, INVOICE_COLLECTION, MAP_CLINIC_USER_COLLECTION, 
    MASTER_ALLERGIES_COLLECTION, MASTER_MEDICATION_COLLECTION, PAST_PROCEDURES_COLLECTION, PATIENT_COLLECTION, PAYMENT_COLLECTION, PRESCRIPTIONS_COLLECTION, QUEUED_APPOINTMENT_COLLECTION, STAFF_COLLECTION, USER_COLLECTION, 
    USER_NOTIFICATION_COLLECTION, 
    USER_OTP_COLLECTION, 
    VITALS_COLLECTION} from "healtether.models/mongodb.collection.name.js";


export const ClientAutoIdentifier = new mongoose.model(CLIENT_AUTOID_COLLECTION, clientAutoIdSchema);
export const Client = new mongoose.model(CLIENT_COLLECTION, clinicSchema);
export const ClinicGroup = new mongoose.model(CLINIC_GROUP_COLLECTION, clinicGroupSchema);

export const Invoice = new mongoose.model(INVOICE_COLLECTION, invoiceSchema);
export const Payment = new mongoose.model(PAYMENT_COLLECTION, paymentSchema);

export const UserNotification = new mongoose.model(USER_NOTIFICATION_COLLECTION, userNotificationSchema);
export const Patient = new mongoose.model(PATIENT_COLLECTION, patientSchema);

export const Staff = new mongoose.model(STAFF_COLLECTION, staffSchema);
export const User = new mongoose.model(USER_COLLECTION, userSchema);
export const UserOTPModel = new mongoose.model(USER_OTP_COLLECTION, userOTPSchema);
export const ClientUser = new mongoose.model(MAP_CLINIC_USER_COLLECTION, mapClinicUserSchema);

export const BookConsultation = new mongoose.model(BOOKED_APPOINTMENT_COLLECTION, bookedAppointmentSchema);
export const Appointment = new mongoose.model(QUEUED_APPOINTMENT_COLLECTION, queuedAppointmentSchema);

export const Allergies = mongoose.model(HISTORY_ALLERGIES_COLLECTION, userAllergiesSchema);
export const masterAllergiesModel = mongoose.model(MASTER_ALLERGIES_COLLECTION, masterAllergiesSchema);
export const FamilyHistories = mongoose.model(HISTORY_FAMILY_DISEASES_COLLECTION, familyHistorySchema);
export const masterMedicationModel = mongoose.model(MASTER_MEDICATION_COLLECTION, masterMedicationSchema);
export const MedicationHistories = mongoose.model(HISTORY_MEDICATIONS_COLLECTION, pastMedicationSchema);
export const PastHistories = mongoose.model(HISTORY_PAST_DISEASES_COLLECTION, pastDiseaseHistorySchema);
export const PastProcedure = mongoose.model(PAST_PROCEDURES_COLLECTION, procedureSchema);
export const PersonalHistories = mongoose.model(HISTORY_PERSONAL_DISEASES_COLLECTION, personalHistorySchema);
export const Vitals = mongoose.model(VITALS_COLLECTION, vitalsSchema);
export const FrequentDrugs = mongoose.model(FREQUENT_DRUGS_COLLECTION, frequentDrugsSchema);
export const FrequentDiagnosis = mongoose.model(FREQUENT_DIAGNOSIS_COLLECTION, frequentDiagnosisSchema);
export const FrequentSymptoms = mongoose.model(FREQUENT_SYMPTOM_COLLECTION, frequentSymptomsSchema);
export const PatientPrescriptions = mongoose.model(PRESCRIPTIONS_COLLECTION, prescriptionSchema);
export const FrequentLabtest = mongoose.model(FREQUENT_LABTEST_COLLECTION, frequentLabtestSchema);