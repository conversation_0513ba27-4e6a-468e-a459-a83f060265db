import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateAppointmentUpsert = async (req, res, next) => {
  await checkSchema({
    "data.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: true,
      errorMessage: "Name is required and must be a valid string",
    },
    "data.prefix": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: true,
      errorMessage: "prefix is required and must be a valid string",
    },
    "data.mobile": {
      trim:true,
      escape:true,
      in: ["body"],
      isMobilePhone: true,
      errorMessage: "Mobile must be a valid phone number",
    },
    "data.age": {
      trim:true,
      escape:true,
      in: ["body"],
      isInt: {
        options: { min: 0 },
        errorMessage: "Age must be a positive integer",
      },
      optional: true,
    },
    "data.birthDate": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isISO8601: true,
      toDate: true,
      errorMessage: "Birth date must be a valid date",
    },
    "data.gender": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: true,
      errorMessage: "Gender must be a string",
      isLength: {
        options: { max: 10 },
        errorMessage: "Gender must be at most 10 characters long",
      },
    },
    "data.appointmentDate": {
      in: ["body"],
      isISO8601: true,
      optional:true,
      toDate: true,
      errorMessage: "Appointment date must be a valid date",
    },
    "data.doctorId": {
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Doctor ID must be a valid ObjectId",
      },
    },
    "data.doctorName": {
      trim:true,
      escape:true,
      in: ["body"],
      optional:true,
      isString: true,
      notEmpty: true,
      errorMessage: "Doctor name is required and must be a valid string",
    },
    "data.reason": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Reason must be a valid string",
    },
    // "data.timeSlot": {
    //   trim:true,
    //   escape:true,
    //   in: ["body"],
    //   optional: true,
    //   isString: true,
    //   notEmpty: true,
    //   errorMessage: "Time slot must be a valid string",
    // },
    "data.patientId": {
      in: ["body"],
      optional:true,
      custom: {
        options: (value) =>{
          if(value?.trim()!=="")
        { return mongoose.Types.ObjectId.isValid(value)}
          return true;
          },
        errorMessage: "Patient ID must be a valid ObjectId",
      },
    },
    "data.virtualConsultation": {
      trim:true,
      escape:true,
      in: ["body"],
      isBoolean: true,
      toBoolean: true,
      errorMessage: "Virtual consultation flag must be a boolean",
    },
    "data.isFollowUp": {
      trim:true,
      escape:true,
      in: ["body"],
      isBoolean: true,
      toBoolean: true,
      optional: true,
      errorMessage: "Follow-up flag must be a boolean",
    },
    "data.created": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => {
          if (!value.on || !value.by) {
            throw new Error(
              "Created field must have both 'on' and 'by' properties"
            );
          }
          if (isNaN(Date.parse(value.on))) {
            throw new Error("'on' property must be a valid date");
          }
          return true;
        },
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateScheduleGoogleMeet = async (req, res, next) => {
  await checkSchema({
    "data.doctorEmail": {
      trim: true,
      escape: true,
      in: ["body"],
      isEmail: true,
      errorMessage: "Doctor's email must be a valid email address",
    },
    "data.patientEmail": {
      trim: true,
      escape: true,
      in: ["body"],
      optional: true,
      isEmail: true,
      errorMessage: "Patient's email must be a valid email address",
    },
    "data.doctorName": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      errorMessage: "Doctor's name must be a valid string",
    },
    "data.meetingDescription": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      errorMessage: "Meeting description must be a valid string",
    },
    "data.scheduleTime": {
      trim:true,
      escape:true,
      in: ["body"],
      isISO8601: true,
      toDate: true,
      errorMessage: "Schedule time must be a valid date",
    },
    "data.scheduleDuration": {
      trim:true,
      escape:true,
      in: ["body"],
      isInt: {
        options: { min: 1 },
        errorMessage: "Schedule duration must be a positive integer",
      },
    },
    "data.appointmentID": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateWithdrawGoogleMeet = async (req, res, next) => {
  await checkSchema({
    "data.eventID": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      errorMessage: "Event ID must be a valid string",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetAppointmentOverview = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    page: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 0 },
        errorMessage: "Page number must be a non-negative integer",
      },
    },
    size: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
    },
    keyword: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isString: true,
      errorMessage: "Keyword must be a string",
    },
    sortby: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isString: true,
      errorMessage: "Sort by field must be a string",
    },
    direction: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isIn: {
        options: [["asc", "desc"]],
        errorMessage: 'Sort direction must be either "asc" or "desc"',
      },
    },
    status: {
      trim:true,
      escape:true,
      in: ["query"],
      optional: true,
      isIn: {
        options: [["Upcoming", "Cancelled", "Completed", "FollowUp","Reschedule","All"]],
        errorMessage:
          "Status must be one of 'Upcoming', 'Cancelled', 'Completed', or 'FollowUp' or 'Reschedule'",
      },
    }, 
    doctor: {
      in: ["query"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "doctor ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetAppointmentById = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSetAppointmentStarted = async (req, res, next) => {
  await checkSchema({
    "data.id": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSetAppointmentEnded = async (req, res, next) => {
  await checkSchema({
    "data.id": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
    "data.removeRecords": {
      trim:true,
      escape:true,
      in: ["body"],
      isArray: true,
      optional: true,
      errorMessage: "Remove records must be an array",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateDraftRecord = async (req, res, next) => {
  await checkSchema({
    data: {
      in: ["body"],
      exists: true,
      errorMessage: "Data object is required",
    },
    "data.id": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
    "data.medicalRecords": {
      in: ["body"],
      isArray: true,
      optional: true,
      errorMessage: "Medical records must be an array",
    },
    "data.procedureRecords": {
      in: ["body"],
      isArray: true,
      optional: true,
      errorMessage: "Procedure records must be an array",
    },
    "data.prescriptionRecords": {
      in: ["body"],
      isArray: true,
      optional: true,
      errorMessage: "Prescription records must be an array",
    },
    "data.removeRecords": {
      trim:true,
      escape:true,
      in: ["body"],
      isArray: true,
      optional: true,
      errorMessage: "Remove records must be an array",
    },
    "data.clientId": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateUpdateDoc = async (req, res, next) => {
  await checkSchema({
    "clientId": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
    // Add more validations as required
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetCurrentMedicalRecord = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    patientId: {
      in: ["query"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateEditAppointmentAdviceNotes = async (req, res, next) => {
  await checkSchema({
    id: {
      
      in: ["params"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
    "data.adviceNotes": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Advice notes must be a string",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateReScheduleAppointment = async (req, res, next) => {
  await checkSchema({
    "data.id": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
    "data.appointmentDate": {
      trim:true,
      escape:true,
      in: ["body"],
      isISO8601: true,
      toDate: true,
      errorMessage: "New date must be a valid date",
    },
    "data.timeSlot": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      errorMessage: "New time slot must be a valid string",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateCancelAppointment = async (req, res, next) => {
  await checkSchema({
    "data.id": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
    "data.reason": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Reason for cancellation must be a string",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSetFollowUpAppointment = async (req, res, next) => {
  await checkSchema({
    "data.id": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
    "data.appointmentDate": {
      trim:true,
      escape:true,
      in: ["body"],
      isISO8601: true,
      toDate: true,
      errorMessage: "Follow-up date must be a valid date",
    },
    "data.followUpNotes": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Follow-up notes must be a string",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetPatientAppointmentById = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetTodayAppointment = async (req, res, next) => {
  await checkSchema({
    clinicId: {
      in: ["query"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    // Add any other query parameters you need to validate
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const ValidateMarkReceipt= async (req, res, next) => {
  await checkSchema({
    "data.appointmentId": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment ID must be a valid ObjectId",
      },
    },
    "data.clinicId": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
