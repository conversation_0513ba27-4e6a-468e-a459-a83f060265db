# NDHM FHIR R4 Implementation Guide for ABDM - Comprehensive Overview

## Table of Contents
1. [Introduction](#introduction)
2. [Background & Vision](#background--vision)
3. [Key Actors](#key-actors)
4. [Architecture Overview](#architecture-overview)
5. [Profile Categories](#profile-categories)
6. [Implementation Standards](#implementation-standards)
7. [Quick Reference](#quick-reference)

## Introduction

The FHIR Implementation Guide for ABDM (Ayushman Bharat Digital Mission) v6.5.0 is a comprehensive specification based on FHIR R4 that defines the minimum conformance requirements for health data interchange in India's digital health ecosystem.

**Official URL**: https://nrces.in/ndhm/fhir/r4/ImplementationGuide/ndhm.in  
**Version**: 6.5.0  
**Base Standard**: FHIR R4.0.1  
**Authority**: National Resource Center for EHR Standards (NRCeS) under National Health Authority (NHA)

## Background & Vision

### National Health Policy 2017
- **Vision**: "Health and wellbeing for all at all ages"
- **Goal**: Create a framework for National Digital Health Eco-system (NDHE)
- **Objective**: Support 'Continuum of care' for individuals

### <PERSON><PERSON>shman Bharat Digital Mission (ABDM)
- **Established by**: Ministry of Health and Family Welfare, Government of India
- **Implementation**: National Health Authority (NHA)
- **Vision**: Create a national digital health ecosystem supporting universal health coverage
- **Principles**: Efficient, accessible, inclusive, affordable, timely, and safe healthcare

### Key Compliance Standards
- National Digital Health Blueprint (NDHB)
- EHR Standards for India (2016)
- Medical Council of India (MCI) guidelines
- Pharmacy Council of India (PCI) guidelines
- Health Claim Exchange Platform (NHCX) standards

## Key Actors

### Health Information Provider (HIP)
- **Definition**: Any entity that creates health information pertaining to a user
- **Capability**: Ready to share health data digitally with users
- **Requirement**: Must adopt compliant software
- **Examples**: Hospitals, clinics, diagnostic centers, pharmacies

### Health Information User (HIU)
- **Definition**: Any entity that intends to view health records of an individual
- **Requirement**: Must obtain informed consent using compliant software
- **Examples**: Healthcare providers, insurance companies, research institutions

## Architecture Overview

### Core Components
1. **FHIR R4 Base**: Built on HL7 FHIR R4.0.1 standard
2. **Indian Context**: Specialized for Indian healthcare needs and regulations
3. **Interoperability**: Enables seamless data exchange across ABDM ecosystem
4. **Consent Framework**: Integrated with ABDM consent management
5. **Security**: Built-in security and privacy protections

### Data Exchange Flow
```
Patient → Consent → HIP → ABDM Gateway → HIU → Healthcare Service
```

## Profile Categories

### 1. Clinical Artifacts (Composition-based)
All clinical artifacts are based on the FHIR Composition resource:

| Profile | Purpose | Use Case |
|---------|---------|----------|
| DiagnosticReportRecord | Radiology and Laboratory reports | Sharing diagnostic results |
| DischargeSummaryRecord | Hospital discharge summaries | Continuity of care |
| HealthDocumentRecord | Unstructured historical health records | Patient uploads via Health Locker |
| ImmunizationRecord | Vaccination records and certificates | Immunization tracking |
| OPConsultRecord | Outpatient consultation notes | Consultation documentation |
| PrescriptionRecord | Medication prescriptions (PCI compliant) | Prescription management |
| WellnessRecord | Regular wellness data from PHR apps | Wellness tracking |

### 2. Billing Artifacts
| Profile | Purpose | Use Case |
|---------|---------|----------|
| InvoiceRecord | Pharmacy invoices, consultation invoices | Healthcare billing |

### 3. Core Resource Profiles (40+ profiles)

#### Patient Management
- **Patient**: Demographics and administrative information
- **Practitioner**: Healthcare provider information
- **PractitionerRole**: Provider roles and specializations
- **Organization**: Healthcare organizations

#### Clinical Data
- **Condition**: Medical conditions and diagnoses
- **Procedure**: Medical procedures performed
- **AllergyIntolerance**: Allergies and adverse reactions
- **FamilyMemberHistory**: Family medical history

#### Observations (Indian Context Specialized)
- **ObservationVitalSigns**: Blood pressure, heart rate, temperature
- **ObservationBodyMeasurement**: Height, weight, BMI
- **ObservationWomenHealth**: Menstrual cycle, pregnancy data
- **ObservationLifestyle**: Diet, smoking, alcohol consumption
- **ObservationPhysicalActivity**: Exercise, sleep patterns
- **ObservationGeneralAssessment**: Overall health assessments

#### Diagnostics
- **DiagnosticReportLab**: Laboratory test results
- **DiagnosticReportImaging**: Radiology and imaging reports
- **ImagingStudy**: DICOM imaging studies
- **Specimen**: Sample collection information

#### Medications
- **Medication**: Drug information (SNOMED CT + Indian codes)
- **MedicationRequest**: Prescription orders
- **MedicationStatement**: Medication history

#### Care Management
- **CarePlan**: Treatment plans
- **Encounter**: Healthcare encounters
- **Appointment**: Appointment scheduling
- **ServiceRequest**: Service orders

#### Documents & Media
- **DocumentReference**: Clinical documents
- **Binary**: Raw digital content
- **Media**: Photos, videos, audio recordings

#### Billing
- **Invoice**: Healthcare invoices
- **ChargeItem**: Individual billing items

## Implementation Standards

### MUST Support Elements
- **HIU (receiver)**: MUST have capacity to read elements
- **HIP (sender)**: May optionally populate elements
- **Principle**: Ensures interoperability while allowing flexibility

### DocumentBundle Profile
- **Purpose**: Packages all resources into exchangeable clinical documents
- **Benefit**: Maintains containing context for complete clinical records
- **Usage**: Primary mechanism for data exchange

### Indian Healthcare Context Features
- **Multilingual Support**: Supports Indian languages
- **ABDM Integration**: Native integration with ABDM ecosystem
- **Regulatory Compliance**: Aligns with Indian medical councils
- **Local Terminologies**: Includes Indian-specific code systems

## Quick Reference

### Key URLs
- **Implementation Guide**: https://nrces.in/ndhm/fhir/r4/
- **ABDM Sandbox**: https://sandbox.abdm.gov.in/
- **NRCeS**: https://nrces.in/

### Important Code Systems
- **Medicine Codes**: SNOMED CT + Common Drug Codes for India
- **Diagnostic Codes**: ICD-10
- **Billing Codes**: NDHM billing code system
- **Price Components**: NDHM price component system

### Currency
- **Standard**: INR (Indian Rupee)
- **Format**: FHIR Money datatype

### Versioning
- **Current Version**: 6.5.0
- **Release Status**: Active
- **Update Frequency**: Regular updates with community feedback

---

**Next Documents in this Series:**
- [Clinical Profiles Detailed Guide](./NDHM-Clinical-Profiles.md)
- [Billing and Invoice Implementation](./NDHM-Billing-Implementation.md)
- [Terminology and Code Systems](./NDHM-Terminology.md)
- [Implementation Examples](./NDHM-Implementation-Examples.md)
