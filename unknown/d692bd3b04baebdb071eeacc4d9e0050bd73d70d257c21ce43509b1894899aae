import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
mockClientHelper();
const { getClinicTimeSlots } = await import("../../controllers/clinic/client.controller.js");
const { getClientSetting } = await import("../../helpers/clinic/client.helper.js");



describe("getClinicTimeSlots", () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: { id: "clinicId123" },
        };
        res = {
            json: jest.fn().mockReturnThis(),
            status: jest.fn().mockReturnThis(),
        };
        jest.clearAllMocks();
    });

    it("should return the clinic time slots with status 200", async () => {
        const mockClinic = {
            clinicName: "Test Clinic",
            timeSlots: ["9:00 AM - 12:00 PM", "2:00 PM - 5:00 PM"],
        };

        getClientSetting.mockResolvedValueOnce(mockClinic);

        await getClinicTimeSlots(req, res);

        expect(getClientSetting).toHaveBeenCalledWith("clinicId123");
        expect(res.json).toHaveBeenCalledWith(mockClinic);
        expect(res.status).toHaveBeenCalledWith(200);
    });

});
