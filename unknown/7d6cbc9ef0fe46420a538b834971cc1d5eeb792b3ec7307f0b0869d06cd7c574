import { jest } from "@jest/globals";
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockgooglemeetHelper } = await import("../mocks/mock.googlemeet.helper.js");
mockApointmentHelper();
mockgooglemeetHelper();
const { withDrawGoogleMeet } = await import("../../controllers/appointments/appointment.controller.js"); 
const { cancelMeeting } = await import("../../helpers/googleMeet/googleMeet.js");
describe('withDrawGoogleMeet', () => {
    let req, res;

    beforeEach(() => {
        req = {
            body: {
                data: {
                    eventID: 'event123'
                }
            }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });


    it('should return 200 if meeting cancellation is successful', async () => {
        const mockResponse = { success: true };

        cancelMeeting.mockResolvedValue(mockResponse);

        await withDrawGoogleMeet(req, res);

        expect(cancelMeeting).toHaveBeenCalledWith(req.body.data.eventID);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });


    it('should return 400 if meeting cancellation fails', async () => {
        const mockErrorResponse = { success: false, error: 'Failed to cancel meeting' };

        cancelMeeting.mockResolvedValue(mockErrorResponse);

        await withDrawGoogleMeet(req, res);

        expect(cancelMeeting).toHaveBeenCalledWith(req.body.data.eventID);
        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({
            success: false,
            error: mockErrorResponse.error
        });
    });

});
