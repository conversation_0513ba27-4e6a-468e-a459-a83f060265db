import mongoose from 'mongoose';
import { mongodb } from './config/mongodb.config'; // Ensure correct path
import { v4 as uuidv4 } from 'uuid';

export const setup = async () => {
    const testDbUri = `mongodb://127.0.0.1:27017/test_${uuidv4()}`;
    await mongodb(testDbUri); // Ensure the test database connection is established
};

export const teardown = async () => {
    if (mongoose.connection.readyState === 1) {
        // await mongoose.connection.dropDatabase();
        await mongoose.connection.close();
    }
};
