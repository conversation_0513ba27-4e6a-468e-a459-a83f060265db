import mongoose from "mongoose";
import { sendNotificationViaToken } from "../../config/firebase.admin.js";
import {
  addUpdateDetails,
  generatePatientAutoId,
  getPatientCurrentAppointment,
  getPatientDetail,
  getPatientWithAllAppointmentDetails,
  getPatientWithAllMedicalDetails,
  overview,
  removePatient,
  searchByMobile,
} from "../../helpers/patient/patient.helper.js";
import { Blob<PERSON>elper } from "../../helpers/storage/blob.helper.js";
import { Patient } from "../../model/clinics.model.js";
import { buildNotificationText } from "../../utils/common.utils.js";
import { generateAbhaLinkToken } from "../../utils/fhir.data.js";
const apiUrl = process.env.WHATSAPP_API_URL;
export const deletePatient = async (query, res) => {
  const data = query;
  var patient = await removePatient(data.query.id);
  var message = buildNotificationText(
    "Patient",
    patient.firstName + " " + patient.lastName,
    "has been deleted",
    query.user
  );
  await sendNotificationViaToken(
    query.Notificationkey,
    message,
    "Patient Detail",
    true,
    patient.clinic,
    query.user.id
  );
  res.status(200).json({ success: true });
};
//add patient
export const addPatientDetails = async (req, res) => {
  const data = req.body.patientData;
  await addUpdateDetails(data, null, req.user);
  var message = buildNotificationText(
    "Patient",
    data.firstName + " " + data.lastName,
    "has been added successfully",
    req.user
  );
  await sendNotificationViaToken(
    req.Notificationkey,
    message,
    "Patient Detail",
    true,
    data.clientId,
    req.user.id
  );
  return res.status(200).json({ success: true });
};
export const updatePatientDetails = async (req, res) => {
  const data = req.body.patientData;
  await addUpdateDetails(data, data.id, req.user);
  var message = buildNotificationText(
    "Patient",
    data.firstName + " " + data.lastName,
    "has been updated successfully",
    req.user
  );
  await sendNotificationViaToken(
    req.Notificationkey,
    message,
    "Patient Detail",
    true,
    data.clientId,
    req.user.id
  );

  res.status(200).json({ success: true });
};


export const addPatientProfileWithAbhaDetails = async (req, res) => {
  const data = req.body.patientData;
  console.log(req.body.patientData);
  let obj = {
    firstName: { $regex: new RegExp(data.firstName, "i") },
    clinic: new mongoose.Types.ObjectId(data.clientId),
    mobile: data.mobile?.toString(),
  }
  data.gender && (obj.gender = data.gender);
  data.age && ({ $gte: data.age - 2, $lte: data.age + 2 });
  if (data.abhaNumber) {
    obj.abhaNumber = data.abhaNumber;
  }
  let Exist = await Patient.findOne(obj);
  if (Exist) {
    if (req.body.isUpdate) {
      if (Exist.abhaAddress !== data.abhaAddress) {
        await generateAbhaLinkToken(data, apiUrl);
      }
      Object.assign(Exist, data);
      let result = await Exist.save();
      var message = buildNotificationText(
        "Patient",
        data.firstName + " " + data.lastName,
        "Profile has been updated successfully",
        req.user
      );
      await sendNotificationViaToken(
        req.Notificationkey,
        message,
        "Patient Detail",
        true,
        data.clientId,
        req.user.id
      );
    }

    return res.status(200).json({ success: true, isExist: true, data: Exist });
  }
  let patientId = await generatePatientAutoId(data.clientId)
  data.patientId = patientId;
  let patient = await addUpdateDetails(data, null, req.user);
  var message = buildNotificationText(
    "Patient",
    data.firstName + " " + data.lastName,
    "has been added successfully",
    req.user
  );
  await sendNotificationViaToken(
    req.Notificationkey,
    message,
    "Patient Detail",
    true,
    data.clientId,
    req.user.id
  );
  res.status(200).json({ success: true, isExist: false, data: patient });
}
export const updateMedicalDetails = async (req, res) => {
  const data = req.body.patientData;
  await addUpdateDetails(data, data.id);
  res.status(200).json({ success: true });
};
export const updateDoc = async (req, res) => {
  console.log("################ document ################");
  const data = req.body;
  var blobObj = new BlobHelper(
    process.env.AZURE_CONNECTIONSTRING,
    process.env.CLINICBLOB_CONTAINER_PREFIX + data.clientId
  );
  if (req.files.profile != undefined)
    blobObj.UploadBlob(req.files.profile[0], "patient/", data.profileName);
  var docs = JSON.parse(data.documentName);
  for (let index = 0; index < req.files.documents?.length; index++) {
    const element = req.files.documents[index];
    blobObj.UploadBlob(
      element,
      process.env.PATIENT_BLOB_FOLDER,
      docs[index].blobName
    );
  }

  //   let id = req.Token;   const userId = new mongoose.Types.ObjectId(id); const
  // blobName = req.file.originalname; const data = req.file.buffer; const
  // blockBlobClient = containerClient.getBlockBlobClient(blobName); await
  // blockBlobClient.upload(data, data.length);  await
  // modelSubmission(data,data.id);
  res.status(200).json({ success: true });
};

export const updateMedicalRec = async (req, res) => {
  console.log("################ medical Record ################");
  const data = req.body;
  var blobObj = new BlobHelper(
    process.env.AZURE_CONNECTIONSTRING,
    process.env.CLINICBLOB_CONTAINER_PREFIX + data.clientId
  );
  if (req.files.profile != undefined)
    blobObj.UploadBlob(
      req.files.profile[0],
      process.env.PATIENT_BLOB_FOLDER,
      data.profileName
    );
  var docName = data.medicalRecordName.split(",");
  for (let index = 0; index < req.files.medicalRecords?.length; index++) {
    const element = req.files.medicalRecords[index];
    blobObj.UploadBlob(
      element,
      process.env.PATIENT_BLOB_FOLDER,
      docName[index]
    );
  }

  //   let id = req.Token;   const userId = new mongoose.Types.ObjectId(id); const
  // blobName = req.file.originalname; const data = req.file.buffer; const
  // blockBlobClient = containerClient.getBlockBlobClient(blobName); await
  // blockBlobClient.upload(data, data.length);  await
  // modelSubmission(data,data.id);
  res.status(200).json({ success: true });
};

export const updateProcedureRec = async (req, res) => {
  console.log("################ procedure record ################");
  const data = req.body;
  var blobObj = new BlobHelper(
    process.env.AZURE_CONNECTIONSTRING,
    process.env.CLINICBLOB_CONTAINER_PREFIX + data.clientId
  );
  if (req.files.profile != undefined)
    blobObj.UploadBlob(
      req.files.profile[0],
      process.env.PATIENT_BLOB_FOLDER,
      data.profileName
    );
  var docName = data.procedureRecordName.split(",");
  for (let index = 0; index < req.files.procedureRecords?.length; index++) {
    const element = req.files.procedureRecords[index];
    blobObj.UploadBlob(
      element,
      process.env.PATIENT_BLOB_FOLDER,
      docName[index]
    );
  }

  //   let id = req.Token;   const userId = new mongoose.Types.ObjectId(id); const
  // blobName = req.file.originalname; const data = req.file.buffer; const
  // blockBlobClient = containerClient.getBlockBlobClient(blobName); await
  // blockBlobClient.upload(data, data.length);  await
  // modelSubmission(data,data.id);
  res.status(200).json({ success: true });
};

export const updatePrescriptionRec = async (req, res) => {
  console.log("################ prescription record ################");
  const data = req.body;
  var blobObj = new BlobHelper(
    process.env.AZURE_CONNECTIONSTRING,
    process.env.CLINICBLOB_CONTAINER_PREFIX + data.clientId
  );
  if (req.files.profile != undefined)
    blobObj.UploadBlob(
      req.files.profile[0],
      process.env.PATIENT_BLOB_FOLDER,
      data.profileName
    );
  var docName = data.prescriptionRecordName.split(",");
  for (let index = 0; index < req.files.prescriptionRecords?.length; index++) {
    const element = req.files.prescriptionRecords[index];
    blobObj.UploadBlob(
      element,
      process.env.PATIENT_BLOB_FOLDER,
      docName[index]
    );
  }

  //   let id = req.Token;   const userId = new mongoose.Types.ObjectId(id); const
  // blobName = req.file.originalname; const data = req.file.buffer; const
  // blockBlobClient = containerClient.getBlockBlobClient(blobName); await
  // blockBlobClient.upload(data, data.length);  await
  // modelSubmission(data,data.id);
  res.status(200).json({ success: true });
};
export const searchPatientMobile = async (req, res) => {
  const data = req.query;
  var overviewData = await searchByMobile(
    data.mobile,
    data.clinicId,
    data.size
  );
  res.status(200).json({ data: overviewData.patientCollection });
};

export const getPatientOverview = async (req, res) => {
  const data = req.query;
  var overviewData = await overview(
    data.clientId,
    data.page,
    data.size,
    data.keyword,
    data.sortby,
    data.direction
  );
  res.json(overviewData).status(200);
};

export const getPatient = async (query, res) => {
  const data = query;
  var overviewData = await getPatientDetail(data.query.id);
  return res.status(200).json(overviewData);
};
export const getPatientWithAllAppointment = async (query, res) => {
  const data = query;
  var patientDetail = await getPatientWithAllAppointmentDetails(data.query.id);
  res.json(patientDetail).status(200);
};

export const getPatientWithAllMedical = async (query, res) => {
  const data = query;
  var patientDetail = await getPatientWithAllMedicalDetails(data.query.id);
  res.json(patientDetail).status(200);
};
export const getPatientLatestAppointment = async (query, res) => {
  const data = query;
  var patientAppointment = await getPatientCurrentAppointment(data.query.id);

  var result = {
    firstName: patientAppointment.firstName,
    lastName: patientAppointment.lastName,
    age: patientAppointment.age,
    birthday: patientAppointment.birthday,
    gender: patientAppointment.gender,
    mobile: patientAppointment.mobile,
    email: patientAppointment.email,
    patientId: patientAppointment.patientId,
    address: patientAppointment.address,
    height: patientAppointment.height,
    weight: patientAppointment.weight,
    abhaAddress: patientAppointment.abhaAddress,
    abhaNumber: patientAppointment.abhaNumber,
    documentType: patientAppointment.documentType,
    documentNumber: patientAppointment.documentNumber,
    documents: patientAppointment.documents,
    appointment:
      patientAppointment.appointments.length > 0
        ? {
          _id: patientAppointment.appointments[0]._id,
          started: patientAppointment.appointments[0].started,
          ended: patientAppointment.appointments[0].ended,
          tokenNumber: patientAppointment.appointments[0].tokenNumber,
          timeSlot: patientAppointment.appointments[0].timeSlot,
        }
        : null,
  };
  res.json(result).status(200);
};
