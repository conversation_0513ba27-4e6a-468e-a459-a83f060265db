import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
mockClientHelper();
const { clientSetting } = await import("../../controllers/clinic/client.controller.js");
const { updateSettingClient } = await import("../../helpers/clinic/client.helper.js");
describe("clientSetting", () => {
    let req, res;

    beforeEach(() => {
        req = {
            body: {
                value: {
                    PatientId_Prefix: "P",
                    PatientId_Suffix: "01",
                    StaffId_Prefix: "S",
                    StaffId_Suffix: "02",
                    Phonepe_MerchantId: "merchant123",
                    Phonepe_SaltKey: "saltKey",
                    Phonepe_SaltIndex: "1",
                    TimeSlots: JSON.stringify(["09:00-10:00", "10:00-11:00"]),
                },
                id: "clientId123",
            },
            user: { id: "user123", name: "Test User" },
        };
        res = {
            json: jest.fn().mockReturnThis(),
            status: jest.fn().mockReturnThis(),
        };

        jest.clearAllMocks();
    });

    it("should update client settings and return success response", async () => {
        updateSettingClient.mockResolvedValueOnce({});

        await clientSetting(req, res);

        expect(updateSettingClient).toHaveBeenCalledWith(req.body.value, req.body.id, req.user);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });


    it("should call updateSettingClient with null id if id is missing", async () => {
        req.body.id = null;
    
        updateSettingClient.mockResolvedValueOnce({});
    
        await clientSetting(req, res);
    
        expect(updateSettingClient).toHaveBeenCalledWith(req.body.value, null, req.user);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ success: true });
    });
    
    
});
