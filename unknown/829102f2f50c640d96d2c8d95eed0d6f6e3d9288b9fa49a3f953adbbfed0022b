import { jest } from "@jest/globals";
import mongoose from 'mongoose';
import { getAppointmentWithPatient } from '../../../helpers/appointment/appointment.helper.js';
import { Appointment } from "../../../model/clinics.model.js";
import { Patient } from '../../../model/clinics.model.js'; 
import { setup, teardown } from '../../../setup.js'; 

jest.setTimeout(30000); 

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('getAppointmentWithPatient', () => {
  beforeEach(async () => {
    await Appointment.deleteMany({}); 
    await Patient.deleteMany({});
  });

  it('should return the appointment along with patient details', async () => {
    const appointmentId = new mongoose.Types.ObjectId();
    const patientId = new mongoose.Types.ObjectId();
    
    const patientData = {
      _id: patientId,
      patientId: "SD_162",
      firstName: 'John',
      lastName: 'Doe',
      age: 30,
      birthday: '1993-05-15',
      gender: 'Male',
      mobile: '**********',
      email: '<EMAIL>',
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '12345',
      },
      height: 180,
      weight: 75,
      prefix:"Mr.",
      documentType: 'ID',
      documentNumber: 'A123456',
      documents: [],
      appointments: [],
    };

    await Patient.create(patientData);

    const appointmentData = {
      _id: appointmentId,
      name: 'John Doe',
      mobile: '**********',
      gender: 'Male',
      age: 30,
      birthDate: '1993-05-15',
      appointmentDate: new Date(),
      timeSlot: '10:00 AM',
      reason: 'Checkup',
      virtualConsultation: false,
      doctorName: 'Dr. Smith',
      doctorId: new mongoose.Types.ObjectId(),
      patientId: patientId, // Reference to patient ID
      medicalRecords: [],
      procedureRecords: [],
      prescriptionRecords: [],
      started: false,
      ended: false,
    };

    // Insert the appointment into the database
    await Appointment.create(appointmentData);

 
    const result = await getAppointmentWithPatient(appointmentId);

    const appointmentQuery = await Appointment.findById(appointmentId)
      .populate({
        path: "patientId",
        perDocumentLimit: 1,
        select: {
          firstName: 1,
          lastName: 1,
          age: 1,
          birthday: 1,
          gender: 1,
          mobile: 1,
          email: 1,
          patientId: 1,
          address: 1,
          height: 1,
          weight: 1,
          documentType: 1,
          prefixt:1,
          documentNumber: 1,
          documents: 1,
        },
      })
      .exec();

    expect(result._id.toString()).toBe(appointmentId.toString());
    expect(appointmentQuery.name).toBe(appointmentData.name);
    expect(appointmentQuery.mobile).toBe(appointmentData.mobile);
    expect(appointmentQuery.gender).toBe(appointmentData.gender);
    expect(appointmentQuery.age).toBe(appointmentData.age);
    expect(appointmentQuery.timeSlot).toBe(appointmentData.timeSlot);
    expect(appointmentQuery.reason).toBe(appointmentData.reason);
    expect(appointmentQuery.patientId._id).toStrictEqual(patientId);
    expect(appointmentQuery.patientId.firstName).toStrictEqual(patientData.firstName);
    expect(appointmentQuery.patientId.lastName).toStrictEqual(patientData.lastName);

  });

  it('should return null if no appointment is found', async () => {
    const appointmentId = new mongoose.Types.ObjectId();

    const result = await getAppointmentWithPatient(appointmentId);

    expect(result).toBeNull();
  });
});
