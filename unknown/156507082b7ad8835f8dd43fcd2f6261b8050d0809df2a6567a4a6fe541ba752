import {ClientAutoIdentifier,Client} from "../../model/clinics.model.js";

export const setInitialClientAutoId = async(clinicId) => {

    if (clinicId != null) {
        let clientObj = null;
        clientObj = await ClientAutoIdentifier
            .findOne({clinic: clinicId})
            .exec();

        if (clientObj ==null) {
            clientObj = new ClientAutoIdentifier({currentPatientId: 1, currentStaffId: 2, currentInvoiceId: 1, clinic: clinicId});
            clientObj.save();
        }
    }
};

export const getPatientAutoId= async(clinicId) => {

    if (clinicId != null) {
        let clientObj = null;
        clientObj= await ClientAutoIdentifier
            .findOne({clinic: clinicId})
            .populate({
                path: 'clinic',
                perDocumentLimit: 1,
                select: {
                    patientId: 1,
                    staffId: 1
                }
            })
            .select({currentPatientId: 1, clinic: 1})
            .exec();

        var updateId=parseInt(clientObj?.currentPatientId)+1;
        await ClientAutoIdentifier
        .findByIdAndUpdate(clientObj?._id,{currentPatientId:updateId});
       
        return clientObj;
    }
};

export const getStaffAutoId= async(clinicId) => {

    if (clinicId != null) {
        let clientObj = null;
        clientObj= await ClientAutoIdentifier
            .findOne({clinic: clinicId})
            .populate({
                path: 'clinic',
                perDocumentLimit: 1,
                select: {
                    staffId: 1
                }
            })
            .select({currentStaffId: 1, clinic: 1})
            .exec();

        
        var updateId=parseInt(clientObj?.currentStaffId)+1;
        await ClientAutoIdentifier
        .findByIdAndUpdate(clientObj._id,{currentStaffId:updateId});
       
        return clientObj;
    }
};
export const getInvoiceNumber= async(clinicId) => {

    if (clinicId != null) {
        let clientObj = null;
        clientObj= await ClientAutoIdentifier
            .findOne({clinic: clinicId})
            .select({currentInvoiceId: 1, clinic: 1})
            .exec();

        
        var updateId=parseInt(clientObj?.currentInvoiceId)+1;
        await ClientAutoIdentifier
        .findByIdAndUpdate(clientObj._id,{currentInvoiceId:updateId});
       
        return clientObj;
    }
};

// export const UpdateCurrentPatientId = async(clinicId, txtpatientId) => {
//     var patientId = "0";
//     var patientId_array = txtpatientId
//         ?.split("_");
//     if (patientId_array.length == 3 || patientId_array.length == 2) {
//         patientId = patientId_array[1];
//     } else if (patientId_array.length == 1) {
//         patientId = patientId_array[0];
//     }
//     if (clinicId != null && patientId != null) {
//         let clientObj = null;
//         clientObj = await ClientAutoIdentifier
//             .find({clinic: clinicId})
//             .exec();
//         if (clientObj != null && parseInt(patientId) > parseInt(clientObj[0].currentPatientId)) {
//             var response = await ClientAutoIdentifier.findOneAndUpdate({
//                 clinic: clinicId
//             }, {currentPatientId: patientId});
//         }
//         return clientObj;
//     }
// };

// export const UpdateCurrentStaffId = async(clinicId, staffId) => {

//     if (clinicId != null && staffId != null) {
//         let clientObj = null;
//         clientObj = await ClientAutoIdentifier
//             .find({clinic: clinicId})
//             .exec();
//         if (parseInt(staffId) > parseInt(clientObj.currentStaffId)) {
//             var response = await ClientAutoIdentifier.findOneAndUpdate({
//                 clinic: clinicId
//             }, {currentStaffId: staffId});
//         }
//         return clientObj;
//     }
// };
export const updateCurrentInvoiceId = async(clinicId, invoiceId) => {

    if (clinicId != null && invoiceId != null) {
        let clientObj = null;
        clientObj = await ClientAutoIdentifier
            .find({clinic: clinicId})
            .exec();
        if (parseInt(invoiceId) > parseInt(clientObj.currentInvoiceId)) {
            var response = await ClientAutoIdentifier.findOneAndUpdate({
                clinic: clinicId
            }, {currentInvoiceId: invoiceId});
        }
        return clientObj;
    }
};