import { jest } from "@jest/globals";
const { mockStaffHelper } = await import("../mocks/mock.staff.helper");
mockStaffHelper();
const { searchStaffName } = await import('../../controllers/staffs/staffs.controller.js'); 
const { searchStaff }= await import('../../helpers/staff/staff.helper.js');
describe('searchStaffName', () => {
  let req, res;

  beforeEach(() => {

    req = {
      query: {}
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
  });

  it('should return staff data successfully', async () => {

    const mockStaffCollection = [
      { name: '<PERSON>', position: 'Doctor' },
      { name: '<PERSON>', position: 'Nurse' }
    ];
    const mockResult = { staffCollection: mockStaffCollection };

    searchStaff.mockResolvedValue(mockResult);

    req.query = {
      clientId: '605c72efc761bc5b184334b2',
      name: '<PERSON>',
      size: 10
    };

    await searchStaffName(req, res);

 
    expect(searchStaff).toHaveBeenCalledWith('605c72efc761bc5b184334b2', 'John', 10);
    
 
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      data: mockStaffCollection
    });
  });



});
