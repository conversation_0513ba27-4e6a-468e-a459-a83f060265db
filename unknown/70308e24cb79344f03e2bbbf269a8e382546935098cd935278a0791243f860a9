import { Router } from "express";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";
import { bookConsultation, BookedConsultationCheckIn, cancelBookedConsultation, getBookedConsultationOverview } from "../../controllers/bookedConsultation/booked-consultation.controller.js";
import { validateScheduleAppointment, validateScheduleAppointmentOverview } from "../../validation/bookedConsultation/bookedConsultation.validation.js";
const bookedConsultation = Router();

/**
 * @swagger
 * /booked-consultation/bookconsultation:
 *   post:
 *     summary: Book a consultation appointment
 *     description: Books a consultation appointment for a patient and sends a notification to the user.
 *     tags:
 *       - Booked Consultation
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 description: The consultation details for booking.
 *                 properties:
 *                   name:
 *                     type: string
 *                     example: "John <PERSON>"
 *                     description: The name of the patient.
 *                   mobile:
 *                     type: string
 *                     example: "**********"
 *                     description: The patient's mobile number.
 *                   age:
 *                     type: integer
 *                     example: 30
 *                     description: The patient's age.
 *                   birthDate:
 *                     type: string
 *                     format: date
 *                     example: "1995-01-01"
 *                     description: The patient's birth date.
 *                   gender:
 *                     type: string
 *                     example: "Male"
 *                     description: The patient's gender.
 *                   appointmentDate:
 *                     type: string
 *                     format: date-time
 *                     example: "2025-02-01T10:00:00Z"
 *                     description: The scheduled date and time of the appointment.
 *                   timeSlot:
 *                     type: string
 *                     example: "10:00 AM - 11:00 AM"
 *                     description: The time slot for the appointment.
 *                   doctorId:
 *                     type: string
 *                     example: "abc123"
 *                     description: The doctor's ID.
 *                   doctorName:
 *                     type: string
 *                     example: "Dr. Smith"
 *                     description: The name of the doctor.
 *                   reason:
 *                     type: string
 *                     example: "Routine Checkup"
 *                     description: The reason for the consultation.
 *                   address:
 *                     type: string
 *                     example: "123 Main St, City"
 *                     description: The patient's address.
 *                   pincode:
 *                     type: string
 *                     example: "123456"
 *                     description: The patient's pincode.
 *                   abhaAddress:
 *                     type: string
 *                     example: "abha@1234"
 *                     description: The patient's ABHA address.
 *                   abhaNumber:
 *                     type: string
 *                     example: "**********12"
 *                     description: The patient's ABHA number.
 *                   speciality:
 *                     type: string
 *                     example: "Cardiology"
 *                     description: The doctor's speciality.
 *                   type:
 *                     type: string
 *                     example: "New Patient"
 *                     description: The type of consultation (e.g., new or follow-up).
 *                   clinic:
 *                     type: string
 *                     example: "clinic123"
 *                     description: The clinic ID where the consultation is booked.
 *                   patientId:
 *                     type: string
 *                     example: "patient123"
 *                     description: The patient ID.
 *                   virtualConsultation:
 *                     type: boolean
 *                     example: true
 *                     description: Indicates whether the consultation is virtual.
 *                   clinicPatientId:
 *                     type: string
 *                     example: "clinicPatient123"
 *                     description: The patient's clinic-specific ID.
 *     responses:
 *       200:
 *         description: Consultation appointment successfully booked.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "abc123"
 *                       description: The ID of the booked consultation.
 *       400:
 *         description: Bad request. Unable to book consultation appointment.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Appointment could not be scheduled due to missing information."
 *       500:
 *         description: Internal server error while processing the request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "An unexpected error occurred."
 */

bookedConsultation.post(
  "/bookconsultation",
  authorizationCheck,
  validateScheduleAppointment,
  async (req, res, next) => {
    try {
        return await bookConsultation(req, res);
    }
    catch (e) {
      next(e)
    }
  }
);
bookedConsultation.post(
  "/bookconsultationusingscanandshare",
  validateScheduleAppointment,
  async (req, res, next) => {
    try {
      console.log("req.body", req.body)
        return await bookConsultation(req, res);
    }
    catch (e) {
      next(e)
    }
  }
);



/**
 * @swagger
 * /booked-consultation/getbookedconsultationoverview:
 *   get:
 *     summary: Retrieve an overview of booked consultations
 *     description: Fetches an overview of booked consultations with pagination and filtering options.
 *     tags:
 *       - Booked Consultation
 *     parameters:
 *       - name: clinicId
 *         in: query
 *         required: true
 *         description: The ID of the clinic.
 *         schema:
 *           type: string
 *           example: "clinic123"
 *       - name: page
 *         in: query
 *         required: true
 *         description: The page number for pagination.
 *         schema:
 *           type: integer
 *           example: 1
 *       - name: size
 *         in: query
 *         required: true
 *         description: The number of items per page for pagination.
 *         schema:
 *           type: integer
 *           example: 10
 *       - name: keyword
 *         in: query
 *         description: A keyword to filter the consultations (optional).
 *         schema:
 *           type: string
 *           example: "John"
 *       - name: sortby
 *         in: query
 *         required: true
 *         description: The field by which to sort the consultations (e.g., `appointmentDate`).
 *         schema:
 *           type: string
 *           example: "appointmentDate"
 *       - name: direction
 *         in: query
 *         required: true
 *         description: The sort direction, either "asc" or "desc".
 *         schema:
 *           type: string
 *           example: "desc"
 *       - name: status
 *         in: query
 *         description: The status of the consultations to filter by (optional).
 *         schema:
 *           type: string
 *           example: "scheduled"
 *     responses:
 *       200:
 *         description: Successfully fetched the consultation overview.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalCount:
 *                   type: integer
 *                   example: 100
 *                   description: Total number of consultations matching the filters.
 *                 consultations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: "abc123"
 *                         description: The ID of the consultation.
 *                       patientName:
 *                         type: string
 *                         example: "John Doe"
 *                         description: The name of the patient.
 *                       appointmentDate:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-02-01T10:00:00Z"
 *                         description: The date and time of the appointment.
 *                       doctorName:
 *                         type: string
 *                         example: "Dr. Smith"
 *                         description: The name of the doctor.
 *                       status:
 *                         type: string
 *                         example: "scheduled"
 *                         description: The status of the consultation.
 *       400:
 *         description: Bad request, some required parameters might be missing or incorrect.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid parameters."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "An unexpected error occurred."
 */



bookedConsultation.get(
  "/getbookedconsultationoverview",
  authorizationCheck,
  validateScheduleAppointmentOverview,
  async (req, res, next) => {
    try {
        return await getBookedConsultationOverview(req, res);
    }
    catch (e) {
      next(e)
    }
  }
);




/**
 * @swagger
 * /booked-consultation/cancelbookedconsultation:
 *   get:
 *     summary: Cancel a booked consultation
 *     description: Cancels a specific booked consultation by its ID and sends a notification about the cancellation.
 *     tags:
 *       - Booked Consultation
 *     parameters:
 *       - name: id
 *         in: query
 *         required: true
 *         description: The ID of the consultation to be cancelled.
 *         schema:
 *           type: string
 *           example: "abc123"
 *     responses:
 *       200:
 *         description: Successfully canceled the booked consultation and sent the notification.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                   description: Whether the cancellation was successful.
 *                 message:
 *                   type: string
 *                   example: "Appointment for John Doe on 2025-02-01 has been cancelled."
 *                 data:
 *                   type: object
 *                   description: Consultation details that were cancelled.
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "abc123"
 *                       description: The ID of the cancelled consultation.
 *                     patientName:
 *                       type: string
 *                       example: "John Doe"
 *                       description: The name of the patient whose consultation was cancelled.
 *                     appointmentDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-02-01T10:00:00Z"
 *                       description: The date and time of the cancelled appointment.
 *                     doctorName:
 *                       type: string
 *                       example: "Dr. Smith"
 *                       description: The name of the doctor.
 *       400:
 *         description: Bad request, the consultation ID is missing or invalid.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid consultation ID."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "An unexpected error occurred while canceling the consultation."
 */


bookedConsultation.get(
  "/cancelbookedconsultation",
  authorizationCheck,
  async (req, res, next) => {
    try {
        return await cancelBookedConsultation(req, res);
    }
    catch (e) {
      next(e)
    }
  }
);



/**
 * @swagger
 * /booked-consultation/consultationcheckin:
 *   get:
 *     summary: Check in for a booked consultation
 *     description: Marks a consultation as checked in by its ID.
 *     tags:
 *       - Booked Consultation
 *     parameters:
 *       - name: id
 *         in: query
 *         required: true
 *         description: The ID of the consultation to check in.
 *         schema:
 *           type: string
 *           example: "abc123"
 *     responses:
 *       200:
 *         description: Successfully checked in for the consultation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                   description: Whether the check-in was successful.
 *                 message:
 *                   type: string
 *                   example: "Consultation has been successfully checked in."
 *                 data:
 *                   type: object
 *                   description: Consultation details after check-in.
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "abc123"
 *                       description: The ID of the consultation that was checked in.
 *                     patientName:
 *                       type: string
 *                       example: "John Doe"
 *                       description: The name of the patient.
 *                     appointmentDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-02-01T10:00:00Z"
 *                       description: The date and time of the consultation.
 *                     status:
 *                       type: string
 *                       example: "Checked In"
 *                       description: The current status of the consultation.
 *       400:
 *         description: Bad request, the consultation ID is missing or invalid.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid consultation ID."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "An unexpected error occurred while checking in."
 */

bookedConsultation.get(
  "/consultationcheckin",
  authorizationCheck,
  async (req, res, next) => {
    try {
        return await BookedConsultationCheckIn(req, res);
    }
    catch (e) {
      next(e)
    }
  }
);
export default bookedConsultation;