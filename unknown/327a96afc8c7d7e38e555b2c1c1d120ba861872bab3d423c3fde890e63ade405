# Node.js Express Web App to Linux on Azure
# Build a Node.js Express app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  branches:
    include:
    - version/*
    - release/*

variables:
  - group: pipeline-variable

  - name: 'azureSubscription'
    value: '56397bb2-6d4f-4521-aeac-0c8a15a4ee8c'

  - name: 'webAppName'
    value: 'api-uhi'

  - name: 'environmentName'
    value: 'api-uhi'

  - name: 'vmImageName'
    value: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    services:
      mongodb:
       image: mongo:latest
       ports:
        - 27017:27017

    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '20.x'
      displayName: 'Install Node.js'

    - script: |
        npm config set registry https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/
        npm config set //pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/:_authToken $personal_Token
        npm install
      displayName: 'npm config and install'
      env:
        personal_Token: $(package_token)

    # - script: |
    #     export NODE_OPTIONS="--max-old-space-size=4096"
    #     npm test
    #   displayName: 'Run tests'

    - task: CopyFiles@2
      displayName: "Copy files"
      inputs:
        SourceFolder: "$(System.DefaultWorkingDirectory)"
        Contents: |
          **/*
          !jest.config.js
          !*.md
          !*.yml
          !.git/**
          !__tests__/**
          !*.gitignore
        TargetFolder: "$(System.DefaultWorkingDirectory)/tmp"

    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)/tmp'
        includeRootFolder: false
        archiveType: zip
        archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
        replaceExistingArchive: true

    - publish: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
      artifact: drop
