import { jest } from "@jest/globals";
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
mockApointmentHelper();
const { getAppointmentOverview } = await import("../../controllers/appointments/appointment.controller.js"); 
const { overview } = await import("../../helpers/appointment/appointment.helper.js");

describe("getAppointmentOverview", () => {
  let req, res;

  beforeEach(() => {
    req = {
      query: {
        clinicId: "clinic123",
        page: 1,
        size: 10,
        keyword: "checkup",
        date:"2025-03-01",
        sortby:"date",
        direction: "asc",
        status: "scheduled",
        doctor:"662ca0ad1a2431e16c41ebb1",
        paymentStatus:""
      },
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  });

  it("should return 200 with appointment overview data on success", async () => {
    const mockOverviewData = {
      appointments: [{ id: "appointment1", details: "details1" }],
      total: 1,
    };

    overview.mockResolvedValue(mockOverviewData);

    await getAppointmentOverview(req, res);

    expect(overview).toHaveBeenCalledWith(
      req.query.clinicId,
      req.query.page,
      req.query.size,
      req.query.keyword,
      req.query.date,
      req.query.sortby,
      req.query.direction,
      req.query.status,
      req.query.doctor,
      req.query.paymentStatus,
    );
    expect(res.json).toHaveBeenCalledWith(mockOverviewData);
    expect(res.status).toHaveBeenCalledWith(200);
  });

  
});
