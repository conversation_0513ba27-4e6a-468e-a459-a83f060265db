import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { getAllClients } from '../../../helpers/clinic/client.helper.js';
import { Client } from '../../../model/clinics.model.js';
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000); 

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('getAllClients function', () => {
  beforeEach(async () => {
    await Client.deleteMany({});

    await Client.create([
      {
        clinicName: 'Clinic 1',
        isdeleted: false,
        address: 'Address 1',
        created: { on: new Date('2024-01-01') },
        modified: { on: new Date('2024-09-01') }
      },
      {
        clinicName: 'Clinic 2',
        isdeleted: false,
        address: 'Address 2',
        created: { on: new Date('2024-01-01') },
        modified: { on: new Date('2024-09-01') }
      },
      {
        clinicName: 'Deleted Clinic',
        isdeleted: true,
        address: 'Address 3',
        created: { on: new Date('2024-01-01') },
        modified: { on: new Date('2024-09-01') }
      }
    ]);
  });

  afterEach(async () => {
    // Clean up clients after each test
    await Client.deleteMany({});
  });

  it('should return all clients that are not marked as deleted', async () => {
    const result = await getAllClients();
    expect(result).toHaveLength(2); 
  });
});
