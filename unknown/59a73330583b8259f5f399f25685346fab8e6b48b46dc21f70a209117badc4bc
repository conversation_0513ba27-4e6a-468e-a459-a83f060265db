import { jest } from "@jest/globals";
const { mockCommonUtils } = await import("../mocks/mock.common.utils.js");
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
const { mockFirebaseMethod } = await import("../mocks/mock.firebase.admin.js");
mockApointmentHelper();
mockCommonUtils();
mockFirebaseMethod();
const { setFollowUpAppointment } = await import('../../controllers/appointments/appointment.controller.js'); 
const { setFollowUp }= await import('../../helpers/appointment/appointment.helper.js');
const { buildNotificationText, resultObject, formatTodayDate } = await import("../../utils/common.utils.js");
const { sendNotificationViaToken } = await import("../../config/firebase.admin.js");

describe('setFollowUpAppointment', () => {
    let req, res;

    beforeEach(() => {
        req = {
            body: {
                data: {
                    id: 'appointmentId'
                }
            },
            Notificationkey: 'mockNotificationKey',
            user: {
                id: 'userId'
            }
        };

        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };

        formatTodayDate.mockReturnValue('2024-09-02');
    });

    it('should successfully set follow-up appointment and send a notification', async () => {
        // Mock data
        const mockAppointment = {
            _id: 'appointmentId',
            name: 'Test Appointment',
            appointmentDate: '2024-09-02',
            clinic: 'clinicId'
        };

        setFollowUp.mockResolvedValue(mockAppointment);
        buildNotificationText.mockReturnValue('Notification message');
        sendNotificationViaToken.mockResolvedValue();
        resultObject.mockReturnValue({ statusCode: 200, success: true, data: { id: mockAppointment._id } });

        await setFollowUpAppointment(req, res);

        expect(setFollowUp).toHaveBeenCalledWith(req.body.data, req.body.data.id, req.user);

        expect(buildNotificationText).toHaveBeenCalledWith(
            "Follow-up Appointment for",
            mockAppointment.name,
            " on 2024-09-02 has been scheduled.",
            req.user
        );

        expect(sendNotificationViaToken).toHaveBeenCalledWith(
            req.Notificationkey,
            'Notification message',
            "Appointment",
            true,
            mockAppointment.clinic,
            req.user.id
        );

        expect(resultObject).toHaveBeenCalledWith(200, null, true, { id: mockAppointment._id });

        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({ statusCode: 200, success: true, data: { id: mockAppointment._id } });
    });

    it('should return "Already in Started" if appointment is not null but already started', async () => {
        const mockAppointment = "Already in Started";
        setFollowUp.mockResolvedValue(mockAppointment);
        await setFollowUpAppointment(req, res);
        expect(setFollowUp).toHaveBeenCalledWith(req.body.data, req.body.data.id, req.user);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockAppointment);
    });
});
