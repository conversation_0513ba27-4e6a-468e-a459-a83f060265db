import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

// Validation for Upsert Client
export const validateClientUpsert = async (req, res, next) => {
  await checkSchema({
    "value.ClinicName": {
      in: ["body"],
      isString: true,
      errorMessage: "Clinic Name must be a string",
    },
    "value.Address": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Address must be a string",
    },
    "value.PatientId_Prefix": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Patient ID Prefix must be a string",
    },
    "value.PatientId_Suffix": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Patient ID Suffix must be a string",
    },
    "value.StaffId_Prefix": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Staff ID Prefix must be a string",
    },
    "value.StaffId_Suffix": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Staff ID Suffix must be a string",
    },
    "value.Phonepe_MerchantId": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "PhonePe Merchant ID must be a string",
    },
    "value.Phonepe_SaltKey": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "PhonePe Salt Key must be a string",
    },
    "value.Phonepe_SaltIndex": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "PhonePe Salt Index must be a string",
    },
    "value.TimeSlots": {
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => {
          try {
            JSON.parse(value);
            return true;
          } catch (e) {
            return false;
          }
        },
        errorMessage: "Time Slots must be a valid JSON string",
      },
    },
    "value.LogoName": {
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Logo Name must be a string",
    },
    "id": {
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Validation for Client Setting
export const validateClientSetting = async (req, res, next) => {
  await checkSchema({
    "body.value.PatientId_Prefix": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Patient ID Prefix must be a string",
    },
    "body.value.PatientId_Suffix": {
      trim: true,
      escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Patient ID Suffix must be a string",
    },
    "body.value.StaffId_Prefix": {
      trim: true,
        escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Staff ID Prefix must be a string",
    },
    "body.value.StaffId_Suffix": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Staff ID Suffix must be a string",
    },
    "body.value.Phonepe_MerchantId": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "PhonePe Merchant ID must be a string",
    },
    "body.value.Phonepe_SaltKey": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "PhonePe Salt Key must be a string",
    },
    "body.value.Phonepe_SaltIndex": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "PhonePe Salt Index must be a string",
    },
    "body.value.TimeSlots": {
      trim: true,
          escape: true,
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => {
          try {
            JSON.parse(value);
            return true;
          } catch (e) {
            return false;
          }
        },
        errorMessage: "Time Slots must be a valid JSON string",
      },
    },
    "body.id": {
      in: ["body"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Validation for Get Client Overview
export const validateGetClientOverview = async (req, res, next) => {
  await checkSchema({
    page: {
      trim: true,
          escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 0 },
        errorMessage: "Page number must be a non-negative integer",
      },
    },
    size: {
      trim: true,
          escape: true,
      in: ["query"],
      optional:true,
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Validation for Get Client by ID
export const validateGetClient = async (req, res, next) => {
  await checkSchema({
    "id": {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Validation for Generate Patient ID
export const validateGeneratePatientId = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Validation for Generate Staff ID
export const validateGenerateStaffId = async (req, res, next) => {
  await checkSchema({
    id: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Validation for Generate Invoice ID
export const validateGenerateInvoiceId = async (req, res, next) => {
  await checkSchema({
    "id": {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Validation for Get Clinic Time Slots
export const validateGetClinicTimeSlots = async (req, res, next) => {
  await checkSchema({
    "id": {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Client ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateCreateClinicGroup = async (req, res, next) => {
  await checkSchema({
    "groupName": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      errorMessage: "Clinic Group Name must be a string",
    },
    "Description": {
      trim: true,
          escape: true,
      in: ["body"],
      isString: true,
      optional: true,
      errorMessage: "Description must be a string",
    },
    "Clinics": {
      trim: true,
          escape: true,
      in: ["body"],
      isArray: true,
      optional: true,
      errorMessage: "Clinics must be an array",
    },
    "Clinics.*": {
      trim: true,
          escape: true,
      in: ["body"],
      optional:true,
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Each Clinic ID must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetAllClinicGroup = async (req, res, next) => {
  await checkSchema({
    pg: {
      trim: true,
          escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 0 },
        errorMessage: "Page number must be a non-negative integer",
      },
    },
    size: {
      trim: true,
          escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetAllClinics = async (req, res, next) => {
  await checkSchema({
    pg: {
      trim: true,
          escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 0 },
        errorMessage: "Page number must be a non-negative integer",
      },
    },
    size: {
      trim: true,
          escape: true,
      in: ["query"],
      optional: true,
      isInt: {
        options: { min: 1 },
        errorMessage: "Size must be a positive integer",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
