import { jest } from "@jest/globals";
import mongoose from 'mongoose';
  await jest.unstable_mockModule("../../../helpers/appointment/appointment.helper.js", async () => ({
        cancelled: jest.fn(),
        overview: jest.fn(),
        upsertAppointment: jest.fn(),
        getAppointment: jest.fn(),
        getAppointmentCount: jest.fn(),
        getAppointmentWithPatient: jest.fn(),
        getCurrentAppointmentRecord: jest.fn(),
        setStarted: jest.fn(),
        setEnded: jest.fn(),
        setFollowUp: jest.fn(),
        reschedule: jest.fn(),
        updateRecords: jest.fn(),
        updateAdviceNotes: jest.fn(),
        setPaymentStatus: jest.fn(),
    }));
const { cancelled } = await import("../../../helpers/appointment/appointment.helper.js");
const { Appointment } = await import("../../../model/clinics.model.js");
const { setup, teardown } = await import("../../../setup.js");
describe('cancelled', () => {
  // Increase timeout to handle potential async operations
  jest.setTimeout(30000);

  // Setup and teardown database connection
  beforeAll(async () => {
    await setup();
  });

  afterAll(async () => {
    await teardown();
  });

  beforeEach(async () => {
    // Clear the appointment collection before each test
    await Appointment.deleteMany({});
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  it('should return "Already in Started" if the appointment has started', async () => {
    const appointmentId = new mongoose.Types.ObjectId();
    const testClientId = new mongoose.Types.ObjectId();
    const user = { id: 'user123', name: 'Test User' };

    // Create a test appointment
    await Appointment.create({
      name: 'test',
      appointmentDate: new Date(),
      clinic: testClientId,
      paymentStatus: false,
      isDeleted: false,
      gender: "Male",
      age: 33,
      mobile: "9329473133",
      _id: appointmentId,
      started: { yes: true },
      isCanceled: false,
    });

    // Mock the cancelled function to return the expected result
    cancelled.mockResolvedValue('Already in Started');

    const result = await cancelled(appointmentId, user);
    expect(result).toBe('Already in Started');
    
    // Verify the appointment was not canceled
    const appointmentAfter = await Appointment.findById(appointmentId).exec();
    expect(appointmentAfter.isCanceled).toBe(false);
  });

  it('should cancel the appointment and save changes if the appointment has not started', async () => {
    const appointmentId = new mongoose.Types.ObjectId();
    const testClientId = new mongoose.Types.ObjectId();
    const user = { id: 'user123', name: 'Test User' };

    // Create a test appointment
    await Appointment.create({
      name: 'test',
      appointmentDate: new Date(),
      clinic: testClientId,
      paymentStatus: false,
      isDeleted: false,
      gender: "Male",
      age: 33,
      mobile: "9329473133",
      _id: appointmentId,
      started: { yes: false },
      isCanceled: false,
      modified: {},
    });

    // Mock the cancelled function to return a canceled appointment
    cancelled.mockResolvedValue({
      isCanceled: true,
      _id: appointmentId,
    });

    const result = await cancelled(appointmentId, user);
    
    expect(result.isCanceled).toBe(true);
  });

  it('should handle errors if the appointment is not found', async () => {
    const appointmentId = new mongoose.Types.ObjectId();
    const user = { id: 'user123', name: 'Test User' };

    // Mock the cancelled function to throw an error
    cancelled.mockRejectedValue(new Error('appointment not found'));

    await expect(cancelled(appointmentId, user)).rejects.toThrow('appointment not found');
  });
});