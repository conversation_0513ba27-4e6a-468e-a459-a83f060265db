import fetch from "node-fetch";
import { AbhaConsentsModel } from "../../../schema/consent.schema.js";

export const initiateConsentRequest = async (data) => {
  // const body = { requestdata };
  console.log(data);
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/m3/initiateconsentrequesthip`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );
  return response.json();
};

export const checkConsentStatus = async (requestdata) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/m3/checkconsentstatuship`,
    {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestdata),
    }
  );
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    json: async () => response.json(),
  };
};

export const notifyconsentrequest = async (consentId, type, data, headers) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/m3/notifyconsentrequesthip`,
    {
      method: "POST",
      headers: headers,
      body: JSON.stringify({ consentId, type, data }),
    }
  );
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    json: async () => response.json(),
  };
};

export const fetchConsentDetails = async (consentArtefactId, hiuId) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/m3/fetchconsentdetailship`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-hiu-id": hiuId,
      },
      body: JSON.stringify({ consentArtefactId }),
    }
  );
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    json: async () => response.json(),
  };
};

export const requestHealthInformation = async (data, hiuId) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/m3/requesthealthinformationhip`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-hiu-id": hiuId,
      },
      body: JSON.stringify(data),
    }
  );
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    json: async () => response.json(),
  };
};

export const notifyHealthInformation = async (transaction_id) => {
  const response = await fetch(
    `${process.env.WHATSAPP_API_URL}/m3/notifyhealthinformationhip`,
    {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ transaction_id }),
    }
  );
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    json: async () => response.json(),
  };
};

export const consentList = async (patientId) => {
  try {
    const response = await AbhaConsentsModel.find({ patientId });
    const updatedConsents = updateConsentExpiryStatus(response);

    return updatedConsents;
    // // console.log(response)
    // return response;
  } catch (error) {
    return {
      ok: false,
      status: 500,
      statusText: "Internal Server Error",
      json: async () => ({ error: error.message }),
    };
  }
};

const isConsentExpired = (consent) => {
  const currentDate = new Date();

  if (consent.consentStatus === "EXPIRED") {
    return true;
  }

  if (
    consent.permission?.dataEraseAt &&
    new Date(consent.permission.dataEraseAt) < currentDate
  ) {
    return true;
  }

  return false;
};

const updateConsentExpiryStatus = (consents) => {
  return consents.map((consent) => {
    // Check if consent is expired but not marked as expired yet
    if (isConsentExpired(consent) && consent.consentStatus !== "EXPIRED") {
      // Update the consent status to EXPIRED
      consent.consentStatus = "EXPIRED";
      consent.consentStatusUpdatedOn = new Date();

      consent
        .save()
        .catch((err) =>
          console.error("Error updating consent expiry status:", err)
        );
    }
    return consent;
  });
};
