import { jest } from "@jest/globals";
import mongoose from 'mongoose';
import { Appointment } from "../../../model/clinics.model.js";
import { getAppointmentCount } from "../../../helpers/appointment/appointment.helper.js";
import { setup, teardown } from "../../../setup.js";

describe('getAppointmentCount', () => {
  jest.setTimeout(30000); 

  beforeAll(async () => {
    await setup(); 
  });

  afterAll(async () => {
    await teardown(); 
  });

  beforeEach(async () => {
    await Appointment.deleteMany({}); 
  });

  it('should return appointments for a given client and filterDate', async () => {
    const clientId = new mongoose.Types.ObjectId();
    const filterDate = new Date('2024-08-21T18:30:00.000Z'); 

    await Appointment.insertMany([
      {
        name: 'First Patient',
        mobile: '**********',
        virtualConsultation: false,
        appointmentDate: filterDate,
        timeSlot: '08:30 pm - 08:50 pm',
        doctorName: 'Venkat<PERSON>',
        patientId: new mongoose.Types.ObjectId(),
        clinic: clientId,
        isDeleted: false,
        isCanceled: true,
        paymentStatus: false,
        isFollowUp: false,
        created: { on: filterDate }
      },
      {
        name: 'Second Patient',
        mobile: '**********',
        virtualConsultation: false,
        appointmentDate: filterDate,
        timeSlot: '08:30 pm - 08:50 pm',
        doctorName: 'Venkatesh Raja',
        patientId: new mongoose.Types.ObjectId(),
        clinic: clientId,
        isDeleted: false,
        isCanceled: true,
        paymentStatus: false,
        isFollowUp: false,
        created: { on: filterDate }
      },
    ]);

    const result = await getAppointmentCount(clientId, filterDate);

    expect(result.length).toBe(2);
    expect(result[0]).toHaveProperty('name', 'First Patient');
    expect(result[1]).toHaveProperty('name', 'Second Patient');
  });

  it('should return an empty array if no appointments match the criteria', async () => {
    const clientId = new mongoose.Types.ObjectId();
    const filterDate = new Date();

    const result = await getAppointmentCount(clientId, filterDate);

    expect(result).toEqual([]);
  });
});