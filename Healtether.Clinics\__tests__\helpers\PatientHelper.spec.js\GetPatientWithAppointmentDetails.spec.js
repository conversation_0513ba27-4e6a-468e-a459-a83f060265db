import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { getPatientWithAllAppointmentDetails } from '../../../helpers/patient/patient.helper.js';
import { Patient } from '../../../model/clinics.model.js'; 
import { Appointment } from '../../../model/clinics.model.js'; 
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); // Set up MongoDB connection
});

afterAll(async () => {
  await teardown(); // Tear down MongoDB connection
});

describe('getPatientWithAllAppointmentDetails function', () => {
  let patientId;
  let appointmentId;

  beforeEach(async () => {
    await Patient.deleteMany({}); 
    await Appointment.deleteMany({});
    const patient = new Patient({
          firstName: 'John',
          lastName: 'Doe',
          mobile: '**********',
          patientId: 'PAT001',
          age: 30,
          prefix:"Mr.",
          birthday: new Date('1993-01-01'),
          gender: 'Male',
          email: '<EMAIL>',
          address: '123 Main St',
          height: 180,
          weight: 75,
          documentType: 'ID',
          documentNumber: 'ID123456',
          deleted: false,
        });
    
    patientId = (await patient.save())._id;

    const appointment = new Appointment({
        mobile: "**********",
        name: "First Patient",
        gender: "Male",
        age: 33,
      patientId: patientId,
      doctorName: 'Dr. Smith',
      appointmentDate: new Date(),
      timeSlot: '10:00 AM',
      reason: 'Checkup',
      paymentStatus: true,
      virtualConsultation: false,
      isCanceled: false,
    });

    appointmentId = (await appointment.save())._id;

 
    patient.appointments = [appointmentId];
    await patient.save();
  });

  afterEach(async () => {
    await Patient.deleteMany({}); 
    await Appointment.deleteMany({}); 
  });

  it('should return patient with all appointment details', async () => {
    const result = await getPatientWithAllAppointmentDetails(patientId);
    
    expect(result).toHaveProperty('firstName', 'John');
    expect(result).toHaveProperty('appointments'); 
    expect(result.appointments).toHaveLength(1); 
    expect(result.appointments[0]).toHaveProperty('doctorName', 'Dr. Smith'); 
  });

  it('should return null when no patient is found', async () => {
    const result = await getPatientWithAllAppointmentDetails(new mongoose.Types.ObjectId());
    
    expect(result).toBeNull(); 
  });


});
