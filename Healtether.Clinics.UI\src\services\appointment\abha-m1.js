import axios from "services/axios/axios";
export const searchAbha = async (mobile) => {
      const response = await axios.post("/abha/searchabha", {
        mobile: mobile,
      });
      console.log("OTP Request Successful:", response);
      return response.data;
};

export const abhaIndexRequestotp=async (index) => {
  const txnId = localStorage.getItem("txnId");
  try {
    const response = await axios.post(`/abha/requestindexmobileotp`, {
      index,
      txnId,
    });
    return response.data
  } catch (err) {
    console.error("OTP Verification Error:", err);
  }
}
export const verifyIndexAbhaOtp = async (otp) => {
  const txnId = localStorage.getItem("verifyIndexOtp");

  try {
    const response = await axios.post(`/abha/verifymobileotp`, {
      otp,
      txnId,
    });
    return response.data
  } catch (err) {
    console.error("OTP Verification Error:", err);
  }
};
// export const verifyUser = async (ABHANumber) => {
//   const txnId = localStorage.getItem("txnId");
//   const token = localStorage.getItem("authToken");
//   try {
//     const response = await axios.post(`/abha/verifyuser`, {
//       ABHANumber,
//       txnId,
//     },{
//       headers: {
//        "t-token": `Bearer ${token}`,
//       },
//     });
//     console.log("User Verification Successful1234:", response.data);
//     localStorage.setItem("verifyUserToken", response.data.response.token);
//     return response.data
//   } catch (err) {
//     console.error("user Verification Error:", err);
//   }
// };

export const getUserProfile=async()=>{
  const token = localStorage.getItem("verifyUserToken");
  try {
    const response = await axios.get(`/abha/userprofile`,{
      headers: {
        "x-token": `Bearer ${token}`,
      },
    });
    console.log("User profile data:", response.data);
    // localStorage.setItem("verifyUserToken", response.data.response.token);
    return response.data
  } catch (err) {
    console.error("user Verification Error:", err);
  }
}



export const fetchAbhaCard = async () => {
  const token = localStorage.getItem("verifyUserToken");
  console.log("Token stored in localStorage:", localStorage.getItem("verifyUserToken"));
  try {
    const response = await axios.get(`/abha/abhacard`, {
      headers: {
        "x-token": `Bearer ${token}`,
        Accept: "image/png", // Request an image response
    },
    responseType: 'blob' 
    });
    return response.data; // Blob object containing the image data
  } catch (err) {
    console.error("User Verification Error:", err);
    throw new Error("Failed to fetch ABHA card");
  }
};


export const createAbha= async(aadhaarNumber)=>{
    const response = await axios.post(`/abha/createabhacard`, {
      aadhaarNumber,
    });
    console.log("User  createAbha:", response);
    localStorage.setItem("txnId", response.data.response.txnId);
    return response.data
}

export const enrollByAadhar=async(mobileNumber,otp)=>{
  const otpValue = otp.join("");
  const txnId = localStorage.getItem("txnId");
    const response = await axios.post(`/abha/enrollbyaadhaar`, {
      mobileNumber,
      otp:otpValue,
      txnId
    });
    
    console.log("User  enrollByAadhar:", response.data);
    localStorage.setItem("txnId", response.data.response.txnId);
    return response.data
}
export const enrolledByMobile=async(mobile)=>{
  const txnId = localStorage.getItem("txnId");
    const response = await axios.post(`/abha/enrollbymobile`, {
      mobile,
      txnId
    });
    
    console.log("User  enrollbymobile:", response.data);
    localStorage.setItem("txnId", response.data.response.txnId);
    return response.data
}

export const verifyEnrolledMobileOtp=async(otp)=>{
  const otpValue = otp.join("");
  const txnId = localStorage.getItem("txnId");
    const response = await axios.post(`/abha/enrollmobileotp`, {
      txnId,
      otp:otpValue
    });
    
    console.log("User  enrollmobileotp:", response.data);
    localStorage.setItem("txnId", response.data.response.txnId);
    return response.data
}

export const enrollAbhaAddress=async(address)=>{
  const txnId = localStorage.getItem("txnId");
    const response = await axios.post(`/abha/enrollbyabhaaddress`, {
      txnId,
      abhaAddress: address,
      preferred: 1,
    });
    console.log("===================>",response);
    console.log("User  enrollbyabhaaddress:", response.data);
    localStorage.setItem("txnId", response.data.response.txnId);
    return response.data
  }
  
export const loginUsingAadhar=async(adhaarNumber)=>{
  const aadhaarNumber = adhaarNumber.join("");
    const response = await axios.post(`/abha/aadhaarlogin`, {
      aadhaarNumber
    });
    console.log("User  aadhaarlogin:", response.data);
    localStorage.setItem("txnId", response.data.response.txnId);
    return response.data
}

//   abha address && abha number
export const getOtpForAbha = async (address, otpType) => {
  let abhaOtpApiType;
  const isAbhaAddress = address.includes("@sbx");
  const isAbhaNumber = /^[0-9-]+$/.test(address);
  isAbhaAddress
    ? otpType === "mobile"
      ? (abhaOtpApiType = "abhaAddressMobileOtp")
      : (abhaOtpApiType = "abhaaddressAadharOtp")
    : isAbhaNumber
    ? otpType === "mobile"
      ? (abhaOtpApiType = "abhaNumberMobileLogin")
      : (abhaOtpApiType = "abhaNumberAadhaarLogin")
    : null;

  console.log("in getOtpForAbha", address, otpType, abhaOtpApiType);
    const response = await axios.post(`/abha/abhanumberaddressotp`, {
      abhaNumberAddress: address,
      type: abhaOtpApiType,
    });
    console.log("User  abhanumberaddressotp:", response.data);
    localStorage.setItem("txnId", response?.data?.response.txnId);
    return response.data;

};

export const verifyAbhaNumberAddressOtp = async (otp,address, otpType , aadharVerification) => {
    let abhaOtpApiType;
    const otpValue = otp.join("");
    const txnId = localStorage.getItem("txnId");
    const isAbhaNumber = /^[0-9-]+$/.test(address);
   isAbhaNumber||aadharVerification
      ? otpType === "mobile"
      ? (abhaOtpApiType = "verifyAbhaNumberMobileOtp")
      : (abhaOtpApiType = "verifyAbhaNumberAadhaarOtp")
      : otpType === "mobile"
      ? (abhaOtpApiType = "verifyAbhaMobileOtp")
        : (abhaOtpApiType = "verifyAbhaAadhaarOtp")
      const response = await axios.post(`/abha/verifyAbhaNumberAddressOtp`, {
        otp: otpValue,
        txnId:txnId,
        type: abhaOtpApiType,
      });
      return response.data;
    
  };
  export const getSuggestion = async () => {
    const txnId = localStorage.getItem("txnId");
    // const token = localStorage.getItem("authToken");
    try {
      const response = await axios.get(`/abha/suggestion`, {
        headers: {
         "Transaction_Id": txnId,
        },
      });
      // console.log("suggestions", response.data);
      // localStorage.setItem("verifyUserToken", response.data.response.token);
      return response.data
    } catch (err) {
      console.error("user Verification Error:", err);
    }
  };

  
  export const getUserAbhaProfile=async(address)=>{
    const token = localStorage.getItem("verifyUserToken");
    const isAbhaNumber = /^[0-9-]+$/.test(address);

    const apiUrl = isAbhaNumber
    ? `/abha/userprofile` // For ABHA number
    : `/abha/userabhaaddressprofile`; // For ABHA address
    try {
      const response = await axios.get(apiUrl,{
        headers: {
          "x-token": `Bearer ${token}`,
        },
      });
      console.log("User profile data:", response.data);
      // localStorage.setItem("verifyUserToken", response.data.response.token);
      return response.data
    } catch (err) {
      console.error("user Verification Error:", err);
    }
  }

  export const fetchAbhaAddressCard = async (address) => {
    const token = localStorage.getItem("verifyUserToken");
    const isAbhaNumber = /^[0-9-]+$/.test(address);

    const apiUrl = isAbhaNumber
    ? `/abha/abhacard` // For ABHA number
    : `/abha/abhaaddresscard`; // For ABHA address

    try {
      const response = await axios.get(apiUrl, {
        headers: {
          "x-token": `Bearer ${token}`,
          Accept: "image/png", // Request an image response
      },
      responseType: 'blob' 
      });
      return response.data; // Blob object containing the image data
    } catch (err) {
      console.error("User Verification Error:", err);
      throw new Error("Failed to fetch ABHA card");
    }
  };