// socket-io.js
import { Server } from "socket.io";
import { createAdapter } from "@socket.io/mongo-adapter";
import { socketCollection } from "./whatsapp.collections.config.js";

let io;

async function createMongoAdapter() {
        await socketCollection.createIndex(
            { createdOn: 1 },
            { expireAfterSeconds: 3600, background: true }
          );
        
        return createAdapter(socketCollection, {
            addCreatedAtField: true,
        });
   
}

export const SocketConnection = async (server) => {
        if(process.env.ENV=="jest"){
            return;
        }

        const mongoAdapter = await createMongoAdapter();

        io = new Server(server, {
            cors: {
                origin: process.env.CORS_URL
            },
            adapter: mongoAdapter
        });

        io.on("connection", (socket) => {
            console.info(`Client connected [id=${socket.id}]`);
            
            socket.on("connectRoom", (arg) => {
                console.log(arg);
            });

            socket.on('disconnect', () => {
                console.info(`Client disconnected [id=${socket.id}]`);
            });

            socket.on("ConnectRoom", (arg) => {
                console.log("connected room " + arg);
                socket.join(arg);
            });

            socket.on("DisconnectRoom", (arg) => {
                console.log("disconnect room" + arg);
                socket.leave(arg);
            });
        });

        console.log("Socket.IO initialized with MongoDB adapter");
        return io;
};

export const sendMessage = (roomId, key, message) => {
    return io.to(roomId).emit(key, message);
};

export const getRooms = () => {
    return io.sockets.adapter.rooms;
};