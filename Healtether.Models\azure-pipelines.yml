# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:

trigger:
  branches:
    include:
      - main

variables:
  - group: pipeline-variable

pool:
  vmImage: 'ubuntu-latest'

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '20.x'
      checkLatest: true

  - script: |
        npm config set registry https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/
        npm config set //pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/:_authToken $personal_Token
        npm install
    displayName: 'Install dependencies'
    env:
      personal_Token: $(package_token)

  - script: |
      git config --global user.email "<EMAIL>"
      git config --global user.name "healtether"
      git add .
      git commit -m "Committing changes before version bump"
      npm version patch
    displayName: 'Bump version'

  - task: npmAuthenticate@0
    inputs:
      workingFile: .npmrc

  - script: |
      npm publish
    env:
      NODE_AUTH_TOKEN: $(NPM_TOKEN)
    displayName: 'Publish to npm'

