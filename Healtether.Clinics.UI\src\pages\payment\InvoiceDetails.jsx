import PropTypes from "prop-types";

import {
  ParseDecimal,
  CalculateAmountAfterDisc,
} from "../../utils/CommonMethods";
import IndianRupeeSymbol from "../../components/detail-page/IndianRupeeSymbol";
import ContainerHeading from "components/detail-page/ContainerHeading";
import { useEffect, useRef, useState } from "react";
import { format, set } from "date-fns";
import { useReactToPrint } from "react-to-print";
import Spinner from "../../components/loader/Spinner";
import {
  SendPaymentLink,
  SetCashPayment,
} from "../../services/payment/payment";
import store from "../../store/store";
import DefaultTextboxClass from "../../utils/Classes";
import { toast } from "react-toastify";
import { useNavigate, useParams } from "react-router-dom";
const PaymentModes = [
  { id: 1, name: "Cash" },
  { id: 2, name: "Credit Card" },
  { id: 3, name: "Debit Card" },
  { id: 4, name: "Paytm" },
  { id: 5, name: "GPay" },
  { id: 6, name: "UPI" },
  { id: 7, name: "Other" },
];

export default function InvoiceDetails({
  invoice,
  onClickPayByCash,
  showAddItem,
  showPayByCash,
  handleMakeReceipt,
}) {
  const contentToPrint = useRef(null);
  const [sendLinkBusy, SetSendLinkBusy] = useState(false);
  const { currentClinic } = store.getState();
  const [amountReceived, SetAmountReceived] = useState(invoice.paidAmount);
  const [paymentMode, setPaymentMode] = useState("");
  const params = useParams();
  const navigation = useNavigate();
  // const [selectedPaymentMode, setSelectedPaymentMode] = useState("Cash");

  const [totalOne, setTotalOne] = useState([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalCgst, setTotalCgst] = useState(0);
  const [totalSgst, setTotalSgst] = useState(0);
  const [overallDiscountRate, setOverallDiscountRate] = useState(
    invoice.discountRate
  );
  const [totalCost, setTotalCost] = useState(
    invoice.totalCost + invoice.discount
  );
  const [grandTotal, setGrandTotal] = useState(invoice.totalCost);
  const [balanceAmount, setBalanceAmount] = useState(0);
  const [treatments, setTreatments] = useState(
    invoice?.treatments != []
      ? invoice.treatments
      : [
          {
            treatment: "",
            quantity: 1,
            amount: 0,
            discRate: 0,
            sgst: 0,
            cgst: 0,
            taxAmount: 0,
            total: 0,
          },
        ]
  );

  async function MakeCashPayment() {
    var id = params.id;
    const { currentClinic } = store.getState();
    console.log("amountt", amountReceived, paymentMode);
    var result = await SetCashPayment({
      invoiceId: id,
      clientId: currentClinic?.clinic?._id,
      amount: amountReceived,
      paymentMode: paymentMode,
    });
    if (result.status === 200) {
      return navigation(`/payments/` + params.id + `/manage`);
    }
    return true;
  }

  // Handle input changes
  const handleFieldChange = (index, field, value) => {
    const updated = [...treatments];
    updated[index][field] =
      field === "treatment"
        ? value
        : value.$numberDecimal
        ? ParseDecimal(value)
        : value;
    setTreatments(updated);
  };

  // Add new treatment row
  const addTreatmentRow = () => {
    setTreatments([
      ...treatments,
      {
        treatment: "",
        quantity: 1,
        amount: 0,
        discRate: 0,
        sgst: 0,
        cgst: 0,
        taxAmount: 0,
        total: 0,
      },
    ]);
  };
  // Delete treatment row
  const removeTreatmentRow = (index) => {
    const updated = [...treatments];
    updated.splice(index, 1);
    setTreatments(updated);
    const updatedTotalOne = [...totalOne];
    updatedTotalOne.splice(index, 1);
    setTotalOne(updatedTotalOne);
  };

  useEffect(() => {
    handleSaveTreatments();
    initialRender();
  }, []);

  // Save treatments
  const handleSaveTreatments = () => {
    try {
      treatments.map((item, index) => {
        setTotalOne((prevTotalOne) => {
          const newTotalOne = [...prevTotalOne];
          newTotalOne[index] = calculateLineTotal(item);
          return newTotalOne;
        });
      });
    } catch (err) {
      console.error("Error saving treatments", err);
      toast.error("Something went wrong while saving.");
    }
  };

  const initialRender = () => {
    try {
      setTotalAmount(
        treatments.reduce(
          (acc, item) =>
            acc + parseFloat(item.quantity) * parseFloat(item.amount),
          0
        )
      );
      setTotalCgst(
        treatments.reduce(
          (acc, item) =>
            acc +
            parseFloat(
              CalculateAmountAfterDisc(
                item.quantity,
                item.amount,
                item.discRate
              )
            ) *
              (parseFloat(item.cgst) / 100),
          0
        )
      );
      setTotalSgst(
        treatments.reduce(
          (acc, item) =>
            acc +
            (parseFloat(
              CalculateAmountAfterDisc(
                item.quantity,
                item.amount,
                item.discRate
              )
            ) *
              parseFloat(item.sgst)) /
              100,
          0
        )
      );
      setBalanceAmount(grandTotal - amountReceived);
    } catch (error) {
      console.error("Error saving treatments", error);
      toast.error("Something went wrong while saving.");
    }
  };

  const handleSaveTotal = () => {
    try {
      setTotalAmount(
        treatments.reduce(
          (acc, item) =>
            acc + parseFloat(item.quantity) * parseFloat(item.amount),
          0
        )
      );
      setTotalCgst(
        treatments.reduce(
          (acc, item) =>
            acc +
            parseFloat(
              CalculateAmountAfterDisc(
                item.quantity,
                item.amount,
                item.discRate
              )
            ) *
              (parseFloat(item.cgst) / 100),
          0
        )
      );
      setTotalSgst(
        treatments.reduce(
          (acc, item) =>
            acc +
            (parseFloat(
              CalculateAmountAfterDisc(
                item.quantity,
                item.amount,
                item.discRate
              )
            ) *
              parseFloat(item.sgst)) /
              100,
          0
        )
      );
      setTotalCost(totalOne.reduce((acc, item) => acc + item * 1, 0));
      setGrandTotal(
        CalculateAmountAfterDisc(1, (totalCost-totalCgst-totalSgst), overallDiscountRate)+totalCgst+totalSgst
      );
    } catch (err) {
      console.error("Error saving treatments", err);
      toast.error("Something went wrong while saving.");
    }
  };

  // Calculate total with tax/discount
  const calculateLineTotal = (item) => {
    const base = CalculateAmountAfterDisc(
      item.quantity,
      item.amount,
      item.discRate
    );
    const taxPercent = parseFloat(item.sgst) + parseFloat(item.cgst);
    const taxAmount = (base * taxPercent) / 100;
    return calculateTotal(base + taxAmount);
  };

  const handlePrint = useReactToPrint({
    documentTitle: "Print This Document",
    onBeforePrint: () => console.log("before printing..."),
    onAfterPrint: () => console.log("after printing..."),
    removeAfterPrint: true,
  });

  // Helper function to safely calculate total with NaN values
  const calculateTotal = (value) => {
    return isNaN(ParseDecimal(value)) ? 0 : ParseDecimal(value);
  };

  // Calculate actual grand total in case totalCost is NaN
  const getGrandTotal = () => {
    if (!isNaN(ParseDecimal(grandTotal))) {
      return ParseDecimal(grandTotal);
    }

    // Calculate manually if totalCost is NaN
    const totalAmount = calculateTotal(invoice.totalAmount);
    const totalTax = calculateTotal(invoice.totalTax);
    const discount = calculateTotal(invoice.discount);

    return totalAmount + totalTax - discount;
  };

  // Calculate remaining balance
  const getRemainingBalance = () => {
    const grandTotal = getGrandTotal();
    const paidAmount = calculateTotal(invoice.paidAmount);
    return grandTotal - paidAmount;
  };

  async function CreatePaymentLink() {
    SetSendLinkBusy(true);
    var result = await SendPaymentLink({
      id: invoice?._id,
      clinicId: currentClinic?.clinic?._id,
    });

    if (result?.data?.success) {
      SetSendLinkBusy(false);
    }
  }

  return (
    <div className="flex flex-col w-[100%] bg-backcolor_detailpage text-md  rounded-xl text-text_primary p-4 shadow-md overflow-y-scroll h-full">
      <div className="flex flex-row justify-between align-center">
        <ContainerHeading heading={"RECEIPT"} />
        <div
          className="text-Secondary cursor-pointer"
          title="Print Receipt"
          onClick={() => handlePrint(null, () => contentToPrint.current)}
        >
          <span className="icon-[streamline--printer-solid] text-xl"></span>
        </div>
      </div>
      <div className="m-6" ref={contentToPrint} data-invoice-content="true">
        <div className="flex justify-between">
          <div>
            Invoice :<span className=""> #{invoice?.invoiceNumber}</span>
          </div>
          <div>{format(new Date(invoice?.created?.on), "dd MMMM, yy")}</div>
        </div>
        <div className=" mt-2 mb-2">
          Patient :{" "}
          <span className="text-Secondary">
            {invoice?.patient?.firstName} {invoice?.patient?.lastName} (#
            {invoice?.patient?.patientId})
          </span>
        </div>
        <div className="border border-t-px border-t-text_bg_primary mb-2"></div>
        <div className="grid grid-cols-8 gap-2 text-md font-normal">
          <div className="text-left font-semibold col-span-2">Treatments</div>
          <div className="text-center font-semibold">Qty</div>
          <div className="text-center font-semibold">Amt.</div>
          <div className="text-center font-semibold">Disc(%)</div>
          <div className="text-center font-semibold">SGST(%)</div>
          <div className="text-center font-semibold">CGST(%)</div>
          <div className="text-right font-semibold">Total</div>
          {invoice.treatments != undefined ? (
            treatments.map((item, index) => (
              <div key={index} className="contents">
                <div className="text-left col-span-2 flex items-center gap-2 ">
                  <div style={{ display: "none" }} className="print-text">
                    {index + 1 + "."}
                  </div>
                  <button
                    onClick={() => removeTreatmentRow(index)}
                    className="text-red-500 text-lg cursor-pointer"
                    title="Remove"
                  >
                    ×
                  </button>
                  <div style={{ display: "none" }} className="print-text">
                    {item.treatment}
                  </div>
                  <input
                    type="text"
                    placeholder="Treatment"
                    name={"treatment_" + index + "_treatment"}
                    className={DefaultTextboxClass + " w-3/4"}
                    value={item.treatment}
                    onChange={(e) =>
                      handleFieldChange(index, "treatment", e.target.value)
                    }
                    onInvalid={(e) =>
                      e.target.setCustomValidity(
                        "Please enter a valid treatment."
                      )
                    }
                    minLength="3"
                    maxLength="255"
                    required
                  />
                </div>
                {["quantity", "amount", "discRate", "sgst", "cgst"].map(
                  (field) => (
                    <div className="text-center" key={field}>
                      <div style={{ display: "none" }} className="print-text">
                        {item[field]}
                      </div>
                      <input
                        type="number"
                        min={
                          field === "quantity" || field === "amount" ? "1" : "0"
                        }
                        placeholder={field === "quantity" ? "" : "00.00"}
                        name={"treatment_" + index + "_" + field}
                        className={DefaultTextboxClass + " w-3/4 text-right"}
                        defaultValue={item[field]}
                        value={item[field]}
                        onChange={(e) => {
                          handleFieldChange(index, field, e.target.value);
                          handleSaveTreatments();
                        }}
                        onInvalid={(e) =>
                          e.target.setCustomValidity(
                            "Please enter a valid quantity."
                          )
                        }
                        required
                      />
                    </div>
                  )
                )}
                <div className="text-right">
                  {totalOne[index]}
                  <IndianRupeeSymbol />
                </div>
              </div>
            ))
          ) : (
            <></>
          )}
        </div>
        <div className="mt-4 text-Secondary font-medium text-md flex justify-between">
          <button
            type="button"
            onClick={addTreatmentRow}
            className="text-Primary border border-Primary rounded px-4 py-1 hover:bg-Primary hover:text-white cursor-pointer"
          >
            + Add Item
          </button>
          {treatments?.length > 0 ? (
            <button
              type="button"
              onClick={handleSaveTotal}
              className="text-Primary border border-Primary rounded px-4 py-1 hover:bg-Primary hover:text-white cursor-pointer"
            >
              Save Items
            </button>
          ) : (
            <></>
          )}
        </div>

        <div className="grid grid-cols-3 gap-2 text-sm border-b  border-t my-2 py-2 ">
          <div className="col text-start">Total Amount</div>
          <div className="col text-center">-</div>
          <div className="col text-end">
            {ParseDecimal(totalAmount)}
            {/* {totalAmount} */}
            &nbsp; <IndianRupeeSymbol />
          </div>
          <div className="col text-start">Total Sgst</div>
          <div className="col text-center">-</div>
          <div className="col text-end">
            {/* {totalSgst} */}
            {ParseDecimal(totalSgst)}
            &nbsp; <IndianRupeeSymbol />
          </div>
          <div className="col text-start">Total Cgst</div>
          <div className="col text-center">-</div>
          <div className="col text-end">
            {/* {totalCgst} */}
            {ParseDecimal(totalCgst)}
            &nbsp; <IndianRupeeSymbol />
          </div>
          <div className="col text-start ">Total Cost</div>
          <div className="col text-center">-</div>
          <div className="col text-end">
            {/* {isNaN(ParseDecimal(invoice.totalCost))
              ? ParseDecimal(invoice.totalAmount) +
                ParseDecimal(invoice.totalTax)
              : ParseDecimal(invoice.totalCost)} */}
            {ParseDecimal(totalCost)}
            &nbsp;
            <IndianRupeeSymbol />
          </div>
          <div className="col text-start mt-2.5">
            Overall Discount (
            {isNaN(overallDiscountRate) ? "0" : overallDiscountRate}%)
          </div>
          <div className="col text-center">-</div>
          <div className="col text-end">
            <div style={{ display: "none" }} className="print-text mt-2.5">
              {overallDiscountRate}
            </div>
            <input
              type="number"
              min="0"
              name="discount"
              className={DefaultTextboxClass + " w-1/4 text-right"}
              defaultValue={overallDiscountRate}
              // value={overallDiscountRate}
              placeholder="0.0"
              onChange={(e) => {
                setOverallDiscountRate(e.target.value);
                setGrandTotal(           
                  CalculateAmountAfterDisc(1, (totalCost-totalCgst-totalSgst), e.target.value)+totalCgst+totalSgst
                );
              }}
              onInvalid={(e) =>
                e.target.setCustomValidity("Please enter a valid quantity.")
              }
              required
            />
            {/* {isNaN(ParseDecimal(invoice.discount))
              ? "0"
              : ParseDecimal(invoice.discount)} */}
            {/* {overallDiscount} */}
            &nbsp; <IndianRupeeSymbol />
          </div>

          <div className="border border-t-px border-t-text_bg_primary col-span-3"></div>
          <div className="text-start text-Secondary font-medium text-md">
            Grand Total
          </div>
          <div className="text-center text-Secondary">-</div>
          <div className="text-end text-Secondary font-medium text-md">
            {getGrandTotal()}
            {/* {grandTotal} */}
            &nbsp;
            <IndianRupeeSymbol />
          </div>
          {/* {ParseDecimal(invoice.paidAmount) > 0 ? ( */}
          <>
            <div className="col text-start mt-4">Amount Received</div>
            <div className="col text-center">-</div>
            <div className="col text-end">
              {/* {ParseDecimal(invoice.paidAmount)} */}
              <div style={{ display: "none" }} className="print-text mt-4">
                {invoice.paidAmount}
              </div>
              <input
                type="number"
                name="amount"
                placeholder="0.0 INR"
                value={amountReceived}
                className={DefaultTextboxClass + " w-1/4 text-right"}
                onChange={(e) => {
                  e.target.setCustomValidity("");
                  SetAmountReceived(e.target.value);
                  setBalanceAmount(
                    grandTotal - invoice.paidAmount - e.target.value
                  );
                }}
                onInvalid={(e) =>
                  e.target.setCustomValidity("Please enter a valid quantity.")
                }
                required
              />
              &nbsp;
              <IndianRupeeSymbol />
            </div>
            <div className="col text-start mt-4">Payment Mode</div>
            <div className="col text-center">-</div>
            <div className="col text-end">
              <div style={{ display: "none" }} className="print-text mt-4">
                {paymentMode || "No payment mode selected"}
              </div>
              <select
                className={`${DefaultTextboxClass} w-1/3 bg-TextBgPrimary  border-transparent text-md focus:border-transparent focus:ring-Primary caret-Primary hover:border-transparent hover:ring-Primary read-only:opacity-75 read-only:cursor-pointer shadow rounded text-left leading-8 mr-[3px]`}
                name="paymentMode"
                value={paymentMode}
                onChange={(e) => setPaymentMode(e.target.value)}
                required
              >
                <option disabled className=" font-normal" value="">
                  Select Payment Mode
                </option>
                {PaymentModes?.map((item) => (
                  <option key={item.id} value={item.name}>
                    {item.name}
                  </option>
                ))}
              </select>
              &nbsp;
            </div>

            <div className="col text-start text-Secondary font-medium text-md">
              Balance Amount
            </div>
            <div className="col text-center">-</div>
            <div className="col text-end text-Secondary font-medium text-md">
              {/* {getRemainingBalance()} */}
              {ParseDecimal(balanceAmount)}
              &nbsp;
              <IndianRupeeSymbol />
            </div>
          </>
        </div>
      </div>

      <div className=" flex mt-4">
        <button
          type="button"
          className="bg-Primary text-white font-medium w-1/2 ml-[2%] h-10 rounded-md shadow-md"
          onClick={(e) => {
            // onClickPayByCash();
            handleMakeReceipt(e);
            MakeCashPayment();
          }}
        >
          Pay
        </button>
        <button
          type="button"
          disabled={sendLinkBusy}
          className="bg-Primary text-white font-medium w-1/2 ml-[2%] h-10 rounded-md shadow-md"
          onClick={CreatePaymentLink}
        >
          <span className="flex items-center justify-center ">
            {sendLinkBusy ? <Spinner show={true} /> : <></>} &nbsp; Send Payment
            Link &nbsp;
            <span className="icon-[ic--twotone-whatsapp] text-lg"></span>
          </span>
        </button>
      </div>
    </div>
  );
}

InvoiceDetails.propTypes = {
  invoice: PropTypes.shape({
    invoiceNumber: PropTypes.string,
    created: PropTypes.shape({
      on: PropTypes.string,
      by: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
    }),
    modified: PropTypes.shape({
      on: PropTypes.string,
      by: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
    }),
    patient: PropTypes.shape({
      firstName: PropTypes.string,
      lastName: PropTypes.string,
      patientId: PropTypes.string,
    }),
    treatments: PropTypes.arrayOf(
      PropTypes.shape({
        treatment: PropTypes.string,
        quantity: PropTypes.number,
        amount: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
        discRate: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
        sgst: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
        cgst: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
        taxAmount: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
        _id: PropTypes.object,
      })
    ),
    totalAmount: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
    totalTax: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
    discountRate: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    discount: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
    totalCost: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
    paidAmount: PropTypes.oneOfType([PropTypes.number, PropTypes.object]),
    _id: PropTypes.object,
    appointmentId: PropTypes.object,
    clinic: PropTypes.object,
  }),
  onClickPayByCash: PropTypes.func,
  showAddItem: PropTypes.bool,
  showPayByCash: PropTypes.bool,
};
