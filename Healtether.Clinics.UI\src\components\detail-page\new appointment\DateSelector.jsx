import moment from "moment";
import dayjs from "dayjs";
import { useRef, useState } from "react";

export const DateSelector = ({ label, onChange }) => {
    const dateInputRef = useRef(null);
    const [date, setDate] = useState(
        moment().format("YYYY-MM-DD")
    );

    const handleDateChange = (direction) => {
       var selected= moment(date, "YYYY-MM-DD")
                .add(direction === "prev" ? -1 : 1, "days")
                .format("YYYY-MM-DD");
        setDate(selected);
        onChange(selected);
    };

    const handleButtonClick = () => {
        dateInputRef.current.showPicker();
    };

    return (
        <div className="relative font-primary font-semibold text-sm flex items-center justify-center border border-[#bac1be] rounded-full py-2  bg-text_bg_primary">
            <button type="button"
                name="date-selector-prev"
                id="date-selector-prev"
                onClick={() => handleDateChange("prev")}
                className="flex items-center justify-start border-r z-10 px-2 cursor-pointer"
            >
                <i className="icon-[mdi--chevron-left] font-bold text-xl bg-Primary "></i>
            </button>
            <span
                className="font-base text-center w-24 z-20 cursor-pointer"
                onClick={handleButtonClick}
            >
                {date && moment(date).format("ddd, D MMM")}
            </span>
            <input
                type="date"
                name={`dateRange-${label}`}
                ref={dateInputRef}
                value={date}
                onChange={(e) => setDate(moment(e.target.value).format("YYYY-MM-DD"))}
                min="2024-01-01"
                autoComplete="off"
                className="absolute opacity-0 cursor-pointer"
            />
            <button type="button"
                name="date-selector-next"
                id="date-selector-next"
                onClick={() => handleDateChange("next")}
                className="flex items-center justify-end border-l px-2 cursor-pointer"
            >
                <i className="icon-[mdi--chevron-right] font-bold text-xl bg-Primary "></i>
            </button>
        </div>
    );
};