import React, { useEffect, useState } from "react";
import { Icons } from "../icons";
import AddRecord from "./add-record";
import FilterModal from "./filter-modal";
import { Buttons } from "../appointment/button";
import RequestAbha from "../abha-consents/request-abha";

export const Records = ({
  records,
  type,
  setActiveTab,
  patientData,
  consultation,
  grantedConsentAbhaRecords,
  setSelectedRecord,
}) => {
  const [filter, setFilter] = useState([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isAddRecord, setIsAddRecord] = useState({
    state: false,
    success: false,
    failed: false,
  });
  const [allRecords, setAllRecords] = useState([]);
  const [allFhirRecords, setAllFhirRecords] = useState([]);

  const [consent, setConsent] = useState({
    request: false,
    successfull: false,
    failed: false,
    data: Array(5).fill(""),
  });

  const reset = () =>
    setFilter([
      {
        state: false,
        type: "",
        time_range: "",
      },
    ]);

  const apply = (addedfilter, timeRange) => {
    setFilter([...addedfilter]);
    setSelectedTimeRange(timeRange);
    setIsOpen(false);
  };

  const props = {
    isAddRecord,
    setIsAddRecord,
    filter,
    setFilter,
    reset,
    apply,
    consent,
    setConsent,
    setActiveTab,
    isOpen,
    consultation,
  };

  const removeRecordType = (type) => {
    const previousFilter = [...filter];
    const alreadyPresent = previousFilter.find(
      (o) => o.type !== "" && o.type === type
    );
    let index = previousFilter.indexOf(alreadyPresent);
    if (index > -1) {
      previousFilter.splice(index, 1);
      setFilter(previousFilter);
    }
  };

  // Format date string
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getRecordIcon = (recordType) => {
    switch (recordType) {
      case "medical":
        return "icon-[medical-icon--i-administration]";
      case "prescription":
        return "icon-[medical-icon--i-administration]";
      case "procedure":
        return "icon-[medical-icon--i-administration]";
      default:
        return "icon-[medical-icon--i-administration]";
    }
  };

  // Filter records based on selected filters
  const filteredRecords = allRecords.filter((record) => {
    // If no filters are applied, show all records
    if (filter.length === 0) return true;

    // Check if record type matches any of the selected filters
    return filter.some((f) => f.type && record.type.includes(f.type));
  });

  const hasAnyRecords = filteredRecords.length > 0;

  const [savedConsultation, setSavedConsultation] = useState(null);

  useEffect(() => {
    if (consultation) {
      setSavedConsultation(consultation);
      const consultationToUse = consultation || savedConsultation;
      if (!consultationToUse) return;

      const medicalRecords =
        consultationToUse?.medicalRecords?.map((record) => ({
          ...record,
          type: "Medical Record",
          recordType: "medical",
        })) || [];

      const prescriptionRecords =
        consultationToUse?.prescriptionRecords?.map((record) => ({
          ...record,
          type: "Prescription Record",
          recordType: "prescription",
        })) || [];

      const procedureRecords =
        consultationToUse?.procedureRecords?.map((record) => ({
          ...record,
          type: "Procedure Record",
          recordType: "procedure",
        })) || [];

      // Combine all records into a single array and update state
      setAllRecords([
        ...medicalRecords,
        ...prescriptionRecords,
        ...procedureRecords,
      ]);
    }
  }, [consultation]);

    // Initialize FHIR records when component mounts
    useEffect(() => {
      if (grantedConsentAbhaRecords) {
        setAllFhirRecords(grantedConsentAbhaRecords);
      }
    }, [grantedConsentAbhaRecords]);

  useEffect(() => {
    console.log("hellooo",grantedConsentAbhaRecords)
    if (grantedConsentAbhaRecords) {
      const abhaRecords =
        grantedConsentAbhaRecords?.grantedConsentData
          ?.map((record) => JSON.parse(record))
          .map((record) => ({
            ...record,
            type: "ABHA Record",
            recordType: "abha",
          })) || [];
      
      setAllFhirRecords((prevRecords = []) => {
        if (!Array.isArray(prevRecords)) {
          prevRecords = [];
        }
        const newRecords = abhaRecords.filter(
          (newRecord) =>
            !prevRecords.some(
              (existingRecord) => existingRecord.id === newRecord.id
            )
        );
        return [...prevRecords, ...newRecords];
      });
    }
  }, [grantedConsentAbhaRecords]);

  // Then in your records processing:
  useEffect(() => {
    // debugger;
    console.log("Consultation changed:", consultation);
    const consultationToUse = consultation || savedConsultation;
    if (!consultationToUse) return;

    const medicalRecords =
      consultationToUse?.medicalRecords?.map((record) => ({
        ...record,
        type: "Medical Record",
        recordType: "medical",
      })) || [];

    const prescriptionRecords =
      consultationToUse?.prescriptionRecords?.map((record) => ({
        ...record,
        type: "Prescription Record",
        recordType: "prescription",
      })) || [];

    const procedureRecords =
      consultationToUse?.procedureRecords?.map((record) => ({
        ...record,
        type: "Procedure Record",
        recordType: "procedure",
      })) || [];

    // Combine all records into a single array and update state
    setAllRecords([
      ...medicalRecords,
      ...prescriptionRecords,
      ...procedureRecords,
    ]);

  }, [consultation, savedConsultation]);

  return (
    <section className="p-6 rounded relative flex flex-col gap-3 font-primary  border-gray-200 ">
      <AddRecord {...props} />
      <FilterModal
        isOpen={isOpen}
        onConfirm={apply}
        filters={filter}
        timerange={selectedTimeRange}
      />
      <RequestAbha
        {...props}
        patientData={patientData}
        consultation={consultation}
      />

      <div className="input input-lg flex space-x-2 ring-0 border-none shadow">
        <span className=" text-base-content/80 my-auto size-5 shrink-0"></span>
        <input
          type="search"
          className="grow focus:outline-hidden border-none ring-0"
          placeholder="Search"
          id="recordSearch"
        />
        <label className="sr-only" htmlFor="recordSearch">
          Search
        </label>
        <span
          className="my-auto size-5 shrink-0 cursor-pointer"
          onClick={() => setIsOpen(true)}
        ></span>
      </div>

      {/* Active Filters */}
      <section className="flex flex-wrap items-center gap-1 mt-2">
        {filter.length > 0 ? (
          <div className="text-sm font-medium text-color_muted font-primary">
            Filters:
          </div>
        ) : (
          <></>
        )}
        {filter.map((item, index) => (
          <span
            key={index}
            className="flex items-center gap-1 py-1 px-3 rounded-full bg-white text-xs text-gray-700 font-semibold"
          >
            {item.type}
            <span
              className=" my-auto size-5 shrink-0 cursor-pointer"
              onClick={() => removeRecordType(item.type)}
            ></span>
          </span>
        ))}

        {selectedTimeRange !== "" && (
          <span className="flex items-center gap-1 py-1 px-3 rounded-full bg-white text-xs text-gray-700 font-medium">
            {selectedTimeRange}
            <span
              className=" my-auto size-5 shrink-0 cursor-pointer"
              onClick={() => setSelectedTimeRange("")}
            ></span>
          </span>
        )}
      </section>

      {/* Records Display */}
      <div className="flex-1 overflow-y-auto space-y-4">
        {type === "medical record" && (allRecords.length > 0 ? (
          allRecords.map((record, index) => (
            <section
              key={record.id || index}
              className="py-3 px-4 rounded-lg bg-white shadow-md flex items-center gap-4 cursor-pointer group hover:bg-Primary transition-colors duration-300"
              onClick={() => setSelectedRecord(record)}
            >
              <div className="bg-gray-100 h-10 w-10 rounded-full flex items-center justify-center">
                <span
                  className={`${getRecordIcon(
                    record.recordType
                  )} text-Primary group-hover:text-white`}
                ></span>
              </div>
              <div className="flex flex-col w-full">
                <div className="text-sm font-medium text-gray-800 group-hover:text-white">
                  {record.fileName || record.blobName || "Unknown Document"}
                </div>
                <div className="flex justify-between text-xs text-gray-500 font-semibold group-hover:text-white">
                  <div>{record.type}</div>
                  <div>{formatDate(record.createdAt) || "N/A"}</div>
                </div>
              </div>
            </section>
          ))
        ) : (
          // Show "No records found" message when no records are available
          <div className="m-auto text-center text-color_muted flex flex-col items-center gap-4 font-medium">
            <p>No records found</p>
              <Buttons.primary
                onClick={() => setIsAddRecord({ ...isAddRecord, state: true })}
                title="Add record"
              />
          </div>
        ))}
      </div>

      {/* Floating Upload Button */}
      {type === "medical record" && hasAnyRecords && (
        <div
          className="absolute bottom-6 right-6 bg-Primary rounded-full shadow-md cursor-pointer flex items-center justify-center h-14 w-14 hover:shadow-lg transition-shadow duration-300"
          onClick={() => setIsAddRecord({ ...isAddRecord, state: true })}
        >
          <Icons.upload className="text-white w-6 h-6" />
        </div>
      )}
      {type==="abha record" && (<Buttons.primary
            onClick={() => setConsent({ ...consent, request: true })}
            title="Request ABHA"
          />)}
      {console.log("allFhirRecords", allFhirRecords)}
      {type === "abha record" &&
        (allFhirRecords.length>0 ?
        (allFhirRecords.map((record, index) => (
          <section
            key={record.id || index}
            className="py-3 px-4 rounded-lg bg-white shadow-md flex items-center gap-4 cursor-pointer group hover:bg-Primary transition-colors duration-300"
            onClick={() => {
              // Clear any existing selected record first
              setSelectedRecord(null);
              // Set timeout to ensure state update completes
              setTimeout(() => {
                setSelectedRecord(record);
              }, 0);
            }}
          >
            <div className="bg-gray-100 h-10 w-10 rounded-full flex items-center justify-center">
              <span
                className={`${getRecordIcon(
                  record.recordType
                )} text-Primary group-hover:text-white`}
              ></span>
            </div>
            <div className="flex flex-col w-full">
              <div className="text-sm font-medium text-gray-800 group-hover:text-white">
                {record.entry[0].resource.type.text ||
                  record.entry[0].resource.type.coding[0].display ||
                  "Unknown Document"}
              </div>
              <div className="flex justify-between text-xs text-gray-500 font-semibold group-hover:text-white">
                <div>{record.type}</div>
                <div>{formatDate(record.timestamp) || "N/A"}</div>
              </div>
            </div>
          </section>
        ))):(
          <div className="m-auto text-center text-color_muted flex flex-col items-center gap-4 font-medium">
         <p>No records found</p>
          </div>
        ))}
    </section>
  );
}