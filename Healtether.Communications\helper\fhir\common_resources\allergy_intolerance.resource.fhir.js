import { v4 as uuidv4 } from "uuid";
import {
  allergyDiv,
  allergyIntoleranceClinicalStatus,
  allergyIntoleranceMetadata,
  allergyIntoleranceVerificationStatus,
  getSnomedCtCode,
} from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

export const generateAllergyIntoleranceResource = async (
  type,
  clinicalStatus,
  verificationStatus,
  notes,
  currentTime,
  patientResource,
  practitionerResources,
  doctorName
) => {
  const id = uuidv4();
  const getSnomedData = await generateSnomedCtCode(type);

  const normalize = (str) => str.trim().toLowerCase();
  const normalizedDoctorName = normalize(doctorName);
  const matchingPractitioner = practitionerResources.find((practitioner) =>
    practitioner.resource.name.some(
      (nameObj) => normalize(nameObj.text) === normalizedDoctorName
    )
  );

  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "AllergyIntolerance",
      id,
      meta: allergyIntoleranceMetadata(),
      clinicalStatus: allergyIntoleranceClinicalStatus(
        clinicalStatus,
        clinicalStatus
      ),
      verificationStatus: allergyIntoleranceVerificationStatus(
        verificationStatus,
        verificationStatus
      ),
      code: getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term),
      recordedDate: currentTime,
      patient: {
        reference: `urn:uuid:${patientResource.resource.id}`,
        display: patientResource.resource.resourceType,
      },
      recorder: matchingPractitioner
        ? {
            reference: `urn:uuid:${matchingPractitioner.resource.id}`,
            display: matchingPractitioner.resource.resourceType,
          }
        : null,
      note: notes.map((n) => ({ text: n }))
      // text: allergyDiv(),
    },
  };
};
