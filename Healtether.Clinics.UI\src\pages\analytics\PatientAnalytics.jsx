import { useEffect, useState } from "react";
import {
  getGenderRatio,
  getPatientAgeGroup,
  getPatientAppointmentAnalyasis,
} from "../../services/analyatics/analyatics";
import DefaultTextboxClass from "../../utils/Classes";
import Chart from "react-apexcharts";

import dayjs from "dayjs";
export default function PatientAnalytics() {
  const [genderRatioLabels, setGenderRatioLabels] = useState([]);
  const [genderRatioData, setgenderRatioData] = useState([]);
  const [ageGroupLabelData, setAgeGroupLabelData] = useState([]);
  const [ageGroupLabel, setAgeGroupLabel] = useState([]);
  const [newPatient, setNewPatient] = useState([]);
  const [patientAnalysisType, setPatientAnalysisType] = useState("monthly");
  const [patientAnalysisLabel, setPatientAnalysisLabel] = useState([]);
  const [repeatedPatient, setRepeatedPatient] = useState([]);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const getPatientAppointmentAnalysis = async () => {
    const data = await getPatientAppointmentAnalyasis(
      patientAnalysisType,
      startDate,
      endDate
    );
    const result = data.data;

    if (result.length > 0) {
      if (patientAnalysisType == "weekly") {
        setPatientAnalysisLabel(data.data.map((item) => item.weekday));
      } else if (patientAnalysisType == "monthly") {
        setPatientAnalysisLabel(data.data.map((item) => item.month));
      } else if (patientAnalysisType == "custom") {
        setPatientAnalysisLabel(
          data.data.map((item) => moment(item.date).format("MMM DD"))
        );
      }
      setNewPatient(data.data.map((item) => item.newPatients));
      setRepeatedPatient(data.data.map((item) => item.repeatedPatients));
    } else {
      console.error("no filter data available");
    }
  };
  // gender ratio api
  const GenderRatio = async () => {
    const data = await getGenderRatio(patientAnalysisType, startDate, endDate);
    if (data.data.length > 0) {
      let arr = [0, 0, 0];
      for (let i = 0; i < data.data.length; i++) {
        let perc = data.data[i].percentage.toFixed(2);
        if (data.data[i].gender == "Male") {
          arr[0] = perc;
        } else if (data.data[i].gender == "Female") {
          arr[1] = perc;
        } else {
          arr[2] = perc;
        }
      }
      setgenderRatioData(arr);
      setGenderRatioLabels(["Male", "Female", "Other"]);
    } else {
      console.error("no filter data available");
    }
  };

  // Age Group

  const PatientAgeGroup = async () => {
    const data = await getPatientAgeGroup(
      patientAnalysisType,
      startDate,
      endDate
    );

    const result = data.data;
    if (result.length > 0) {
      let arr = [0, 0, 0];
      for (let i = 0; i < data.data.length; i++) {
        let count = data.data[i].count;
        if (data.data[i].ageGroup == "0-15 yrs") {
          arr[0] = {
            x: "0-15 yrs",
            y: count
          };
        } else if (data.data[i].ageGroup == "16-40 yrs") {
          arr[1] = {
            x: "16-40 yrs",
            y: count
          };
        } else if (data.data[i].ageGroup == "41-60 yrs") {
          arr[2] = {
            x: "41-60 yrs",
            y: count
          };
        } else {
          arr[3] = {
            x: "60+ yrs",
            y: count
          };
        }
      }
      setAgeGroupLabelData(arr);
      setAgeGroupLabel(["0-15 yrs", "16-40 yrs", "41-60 yrs", "61+ yrs"]);
    } else {
      console.error("no filter data available");
    }
  };

  const stdLegend = {
    show: true,
    position: 'top',
    horizontalAlign: 'center',
    fontSize: '14px',
    fontFamily: "Urbanist",
    fontWeight: 400,
    labels: {
      useSeriesColors: true,
    }
  };
  const stdTooltip = {
    style: {
      fontSize: '12px',
      fontFamily: "Urbanist",
    },
  };
  const stdFill = {
    type: 'gradient',
    gradient: {
      shadeIntensity: 1,
      opacityFrom: 0.7,
      gradientToColors: ['var(--color-base-100)'],
      opacityTo: 0.3,
      stops: [0, 90, 100]
    }
  };

  const stdResponsive = [{
    breakpoint: 568,
    options: {
      chart: {
        height: 300
      },
      labels: {
        style: {
          fontSize: '10px',
          colors: 'var(--color-base-content)',
          fontFamily: 'Urbanist'
        },
        offsetX: -2,
        formatter: title => title.slice(0, 3)
      },
      yaxis: {
        labels: {
          align: 'left',
          minWidth: 0,
          maxWidth: 140,
          style: {
            fontSize: '10px',
            colors: 'var(--color-base-content)',
            fontFamily: 'Urbanist'
          },
          formatter: value => (value >= 1000 ? `${value / 1000}k` : value)
        }
      }
    }
  }];

  const areaOption = {
    chart: {
      height: 350,
      type: 'area',
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    legend: stdLegend,
    dataLabels: {
      enabled: false,
    },
    tooltip: stdTooltip,
    markers: {
      size: [5, 5]
    },
    stroke: {
      curve: 'smooth',
      width: 2
    },
    grid: {
      strokeDashArray: 2,
      borderColor: 'color-mix(in oklab, var(--color-base-content) 40%, transparent)'
    },
    colors: ['var(--color-warning)', 'var(--color-accent)'],
    fill: stdFill,
    xaxis: {
      type: 'category',
      tickPlacement: 'on',
      categories: patientAnalysisLabel,
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: true
      },
      tooltip: {
        enabled: false
      },
      labels: {
        style: {
          colors: 'var(--color-base-content)',
          fontSize: '12px',
          fontFamily: "Urbanist",
          fontWeight: 400
        },
        formatter: title => {
          let t = title

          if (t) {
            const newT = t.split(' ')
            t = `${newT[0]} ${newT[1].slice(0, 3)}`
          }

          return t
        }
      }
    },
    yaxis: {
      labels: {
        align: 'left',
        minWidth: 0,
        maxWidth: 140,
        style: {
          colors: 'var(--color-base-content)',
          fontSize: '12px',
          fontFamily: "Urbanist",
          fontWeight: 400
        },
        formatter: value => (value >= 1000 ? `${value / 1000}k` : value)
      }
    },
    responsive: stdResponsive
  }

  const genderOption = {
    chart: {
      height: 350,
      type: 'bar',
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    legend: stdLegend,
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '60px',
        borderRadius: 4,
        borderRadiusApplication: 'end'
      }
    },
    dataLabels: {
      enabled: true,
      formatter: function (val, opts) {
        return val + "%"
      },
    },
    tooltip: {
      enabled: false,
    },
    markers: {
      size: [5, 5]
    },
    stroke: {
      curve: 'smooth',
      width: 2
    },
    grid: {
      strokeDashArray: 2,
      borderColor: 'color-mix(in oklab, var(--color-base-content) 40%, transparent)'
    },
    colors: ['var(--color-accent)'],
    xaxis: {
      categories: genderRatioLabels,
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: true
      },
      tooltip: {
        enabled: false
      },
      labels: {
        style: {
          colors: 'var(--color-base-content)',
          fontSize: '12px',
          fontFamily: "Urbanist",
          fontWeight: 400
        },
      }
    },
    yaxis: {
      labels: {
        align: 'left',
        minWidth: 0,
        maxWidth: 140,
        style: {
          colors: 'var(--color-base-content)',
          fontSize: '12px',
          fontFamily: "Urbanist",
          fontWeight: 400
        },
        formatter: value => (value + "%")
      }
    },
    responsive: stdResponsive
  }
  const ageOption = {
    chart: {
      height: 350,
      type: 'bar',
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    plotOptions: {
      bar: {
        horizontal: true,
        columnWidth: '16px',
        borderRadius: 4,
        borderRadiusApplication: 'end'
      }
    },
    legend: stdLegend,
    dataLabels: {
      enabled: true,
      formatter: function (val, opts) {
        return val + "%"
      }
    },
    tooltip: {
      enabled: false,
    },
    grid: {
      strokeDashArray: 2,
      borderColor: 'color-mix(in oklab, var(--color-base-content) 40%, transparent)'
    },
    colors: ['var(--color-accent)'],
    xaxis: {
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: true
      },
      tooltip: {
        enabled: false
      },
      labels: {
        style: {
          colors: 'var(--color-base-content)',
          fontSize: '12px',
          fontFamily: "Urbanist",
          fontWeight: 400
        },
        formatter: value => (value + "%")
      },

    },
    yaxis: {
      labels: {
        align: 'left',
        minWidth: 0,
        maxWidth: 140,
        style: {
          colors: 'var(--color-base-content)',
          fontSize: '12px',
          fontFamily: "Urbanist",
          fontWeight: 400
        }
      },
    },
    responsive: stdResponsive
  }
  useEffect(() => {
    getPatientAppointmentAnalysis();
    GenderRatio();
    PatientAgeGroup();
  }, []);

  return (<>
    <div className="stats w-full p-3 mx-3">
      <div className="stat overflow-hidden">
        <div className="stat-title font-primary text-lg font-semibold">Patient's Ratio</div>
        <div className="stat-value">
          <div className="app">
            <div className="row">
              <div className="mixed-chart">
                <Chart
                  options={areaOption}
                  series={[
                    {
                      name: 'New Patient',
                      data: newPatient
                    },
                    {
                      name: 'Repeated Patient',
                      data: repeatedPatient
                    }
                  ]}
                  type="area"
                  height="350"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className="flex sm:flex-col md:flex-row gap-4 mt-4 ml-3">
      <div className=" sm:w-full md:w-1/2">
        <div className="stats w-full">
          <div className="stat overflow-hidden">
            <div className="stat-title font-primary text-lg font-semibold">Gender Ratio</div>
            <div className="stat-value">
              <div className="app">
                <div className="row">
                  <div className="mixed-chart">
                    <Chart
                      options={genderOption}
                      series={[
                        {
                          name: 'PatientAgeWise',
                          data: genderRatioData
                        },
                      ]}
                      type="bar"
                      height="350"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className=" sm:w-full md:w-1/2">
        <div className="stats w-full">
          <div className="stat overflow-hidden">
            <div className="stat-title font-primary text-lg font-semibold">Age group Analysis</div>
            <div className="stat-value">
              <div className="app">
                <div className="row">
                  <div className="mixed-chart">
                    <Chart
                      options={ageOption}
                      series={[
                        {
                          data: ageGroupLabelData
                        }
                      ]}
                      type="bar"
                      height="350"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div className="flex h-10 justify-between items-center text-sm lg:text-base">
      <h1 className="text-md text-gray-600">Patient&apos;s Analysis</h1>
      <div className="flex">
        <button
          className={`p-2.5 mx-2  rounded-lg mr-1 ${patientAnalysisType != "weekly"
            ? "bg-[#F9F4FE]"
            : "text-white bg-[#32856E]"
            }  shadow-[0px_4px_12px_0px_rgba(0,0,0,0.1)]`}
        // onClick={() => setPatientAnalysisType("weekly")}
        >
          Weekly
        </button>
        <button
          className={`p-2.5 mx-2  rounded-lg mr-1 ${patientAnalysisType != "monthly"
            ? "bg-[#F9F4FE]"
            : "text-white bg-[#32856E]"
            } shadow-[0px_4px_12px_0px_rgba(0,0,0,0.1)]`}
        // onClick={() => setPatientAnalysisType("monthly")}
        >
          Monthly
        </button>
        <div className="relative">
          <button
            type="button"
            className={`p-2.5 mx-2 rounded-lg mr-1 ${patientAnalysisType !== "custom"
              ? "bg-[#F9F4FE]"
              : "text-white bg-[#32856E]"
              } shadow-[0px_4px_12px_0px_rgba(0,0,0,0.1)]`}
          //  onClick={() => handleButtonClick()}
          >
            Custom
          </button>

          <input
            type="date"
            name="dateRange"
            //ref={dateInputRef}
            // onChange={(e) => handleDateChange(e)}
            min="2024-01-01"
            placeholder="Select Date Range"
            autoComplete="off"
            style={{ colorScheme: "green" }}
            className={
              DefaultTextboxClass +
              " w-full leading-8 disabled:opacity-75 absolute left-[20px] bg-[#fff]  focus:ring-2 focus:ring-[#32856E] text-[#32856E] disabled:cursor-not-allowed collapse"
            }
          />
        </div>
      </div>
    </div>

  </>
  )
}