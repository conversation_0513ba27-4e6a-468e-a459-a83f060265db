import { useEffect, useState } from "react";
import { FhirRecordDisplay } from "./FhirRecordDisplay";
import { extractFhirBundleInfo } from "./extractFhirBundleInfo";
import PropTypes from "prop-types";

export default function AbhaRecord({ selectedRecord }) {

  // State variable to hold the extracted data for the currently viewed record
  const [extractedRecordData, setExtractedRecordData] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (selectedRecord) {
      handleRecordProcessing(selectedRecord);
    } else {
      // Close modal and clear data when no record is selected
      setIsModalOpen(false);
      setExtractedRecordData(null);
    }
  }, [selectedRecord]);

  // Function to process the selected record bundle
  const handleRecordProcessing = (record) => {
    if (
      !record ||
      !record.entry ||
      !record.entry[0] ||
      !record.entry[0].resource
    ) {
      setExtractedRecordData(null);
      setIsModalOpen(false);
      return;
    }

    try {
      // Use the single extractFhirBundleInfo function
      const extractedData = extractFhirBundleInfo(record);
      setExtractedRecordData(extractedData);

      // Open the modal
      setIsModalOpen(true);
    } catch (error) {
      console.error("Error processing FHIR bundle:", error);
      // Handle extraction errors by clearing data and closing modal
      setExtractedRecordData(null);
      setIsModalOpen(false);
    }
  };

  // Function to close the modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div className="w-full h-full flex flex-col bg-white p-6 rounded-lg shadow bg-backcolor_detailpage">
        {isModalOpen && extractedRecordData && (
          <FhirRecordDisplay
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            recordData={extractedRecordData}
          />
        )}
      </div>
    </>
  );
}

AbhaRecord.propTypes = {
  selectedRecord: PropTypes.object,
};
