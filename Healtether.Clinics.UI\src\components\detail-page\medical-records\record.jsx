import React, { useEffect, useState } from "react";
import store from '../../../store/store';

export default function Record({ selectedRecord }) {
  const { currentClinic } = store.getState();
  const [pdfUrl, setPdfUrl] = useState("");

  useEffect(() => {
    if (selectedRecord && selectedRecord.blobName) {
      const url = `${import.meta.env.VITE_BLOB_URL}${
        import.meta.env.VITE_CLINICBLOB_CONTAINER_PREFIX
      }${currentClinic?.clinic?._id}/patient/${selectedRecord.blobName}`;
      
      console.log("PDF URL:", url);
      setPdfUrl(url);
    }
  }, [selectedRecord]);

  return (
    <div className="w-full h-full flex flex-col bg-white p-6 rounded-lg shadow bg-backcolor_detailpage">
      {pdfUrl && (
        <div className="pdf-container w-full flex-grow overflow-hidden">
          <iframe
            src={`${pdfUrl}#view=FitH&scrollbar=1&toolbar=0&navpanes=0&statusbar=0`}
            className="w-full h-full border-0"
            style={{ height: "calc(100vh - 200px)" }}
            title="PDF Viewer"
          />
        </div>
      )}
    </div>
  );
}