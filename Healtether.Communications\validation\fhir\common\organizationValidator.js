
const organizationValidator = {
  organization: {
    in: ["body"],
    exists: { errorMessage: "organization is required" },
    isObject: true,
  },
  "organization.name": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage:
      "organization.name is required and must be a non-empty string",
  },
  "organization.telecom": {
    in: ["body"],
    isArray: true,
    errorMessage: "organization.telecom must be an array",
  },
  "organization.telecom.*.system": {
    in: ["body"],
    isString: true,
    isIn: {
      options: [["phone", "email", "fax"]],
    },
    errorMessage: "telecom.system must be one of: phone, email, fax",
  },
  "organization.telecom.*.value": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "telecom.value is required and must be a non-empty string",
  },
  "organization.telecom.*.use": {
    in: ["body"],
    isString: true,
    isIn: {
      options: [["home", "work", "mobile"]],
    },
    errorMessage: "telecom.use must be one of: home, work, mobile",
  },
  "organization.licenses": {
    in: ["body"],
    isArray: true,
    errorMessage: "organization.licenses must be an array",
  },
  "organization.licenses.*.code": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "license.code is required and must be a non-empty string",
  },
  "organization.licenses.*.display": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "license.display is required and must be a non-empty string",
  },
  "organization.licenses.*.licNo": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "license.licNo is required and must be a non-empty string",
  },
}

export default organizationValidator;
