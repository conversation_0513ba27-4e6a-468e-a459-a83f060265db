import { jest } from "@jest/globals";

export async function mockStaffHelper() {
    await jest.unstable_mockModule("../../helpers/staff/staff.helper.js", async () => ({
        staffOverview: jest.fn(),
        staffById: jest.fn(),
        removeStaff: jest.fn(),
        getDoctorTimeSlotsById: jest.fn(),
        getDoctorsWithTimeSlots: jest.fn(),
        getDoctor: jest.fn(),
        searchStaff: jest.fn(),
        checkMobileNumberPresent: jest.fn(),
        mapWeekDayWithTimeSlot:jest.fn(),
        upsertStaff:jest.fn(),
        upsertUserByNameMobileEmail:jest.fn(),
    }));
}