import {
    assert,
    object,
    optional,
    string,
} from 'superstruct'
import {
    isBoolean,
    isDate,
    isDateSelected,
    isNumber,
    nonEmptyString,
    optionalNonEmptyString,
} from './utility';
import {alertBox} from 'components/dialog/prompt';
import {GenerateValidationText} from '../utils/CommonMethods';

// Define GoogleLinkStruct
const GoogleLinkStruct = object({
    link: string(),
    id: optional(string()),
});

export function ValidateAppointment(data) {
    const AppointmentObject = object({
        prefix:nonEmptyString,
        clinicPatientId: optionalNonEmptyString,
        patientId: optionalNonEmptyString,
        name: nonEmptyString,
        age: isNumber,
        birthDate: isDate,
        mobile: isNumber,
        pincode: isNumber,
        district: optionalNonEmptyString,
        state: optionalNonEmptyString,
        speciality: nonEmptyString,
        address: optionalNonEmptyString,
        gender: nonEmptyString,
        virtualConsultation: isBoolean,
        doctorId: nonEmptyString,
        doctorName: nonEmptyString,
        abhaAddress: optionalNonEmptyString,
        abhaNumber: optionalNonEmptyString,
        isFollowUp: isBoolean,
        appointmentDate: data.instantAppointment == "true" ? optionalNonEmptyString : isDateSelected,
        timeSlot: optionalNonEmptyString,
        googleLink: optional(GoogleLinkStruct),
        reason: optionalNonEmptyString,
        tokenNumber: isNumber,
    });

    try {
        assert(data, AppointmentObject, "Appointment is invalid..");
        return true;
    } catch (e) {
        var error = e.failures();
        let message = GenerateValidationText(error);
        alertBox({ show: true, title: 'Appointment Validation', proceed: undefined, confirmation: message });
        return false;
    }
}

export  function ValidateBookedConsultation(data) {
    console.log(data);
        const AppointmentObject = object({
            prefix:nonEmptyString,
            clinicPatientId: optionalNonEmptyString,
            patientId: optionalNonEmptyString,
            name: nonEmptyString,
            age: isNumber,
            birthDate: isDate,
            mobile: isNumber,
            pincode: isNumber,
            district: optionalNonEmptyString,
            state: optionalNonEmptyString,
            speciality:nonEmptyString,
            address:optionalNonEmptyString,
            gender: nonEmptyString,
            virtualConsultation: isBoolean,
            doctorId: nonEmptyString,
            doctorName: nonEmptyString,
            isFollowUp: isBoolean,
            abhaAddress: nonEmptyString,
            abhaNumber:nonEmptyString,
            appointmentDate:data.instantAppointment=="true"? optionalNonEmptyString :isDateSelected,
            timeSlot:data.instantAppointment=="true"? optionalNonEmptyString : nonEmptyString,
            reason: optionalNonEmptyString,
            prefix:nonEmptyString,
            tokenNumber: isNumber,
        })
    
        try {
            assert(data, AppointmentObject, "Appointment is invalid..");
            return true;
        } catch (e) {
            var error = e.failures();
            let message = GenerateValidationText(error);
            alertBox({show: true, title: 'Appointment Validation', proceed: undefined, confirmation: message})
            return false;
        }
    }

export  function ValidateRescheduleAppointment(scheduledDate,scheduledTimeSlot) {
var data={
    appointmentDate:scheduledDate,
    timeSlot:scheduledTimeSlot
}
    const AppointmentObject = object({appointmentDate: isDateSelected, timeSlot: nonEmptyString})

    try {
        assert(data, AppointmentObject, "Appointment is invalid..");
        return true;
    } catch (e) {
        var error = e.failures();
        let message = GenerateValidationText(error);
        alertBox({show: true, title: 'Appointment Validation', proceed: undefined, confirmation: message})
        return false;
    }
}
