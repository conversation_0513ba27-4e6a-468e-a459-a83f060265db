import { Router } from "express";
import {
  checkOTP,
  forgotPassword,
  loginVerify,
  resendOTP,
  setPassword,
  tokenVerification<PERSON><PERSON>,
} from "../../controllers/user/auth.controller.js";
import {
  ValidateCheckOTP,
  ValidateForgotPassword,
  ValidateLoginVerify,
  ValidateResendOTP,
  ValidateSetPassword,
  ValidateTokenVerificationApi
} from "../../validation/auth/auth.validation.js";

const auth = Router();

/**
 * @swagger
 * /authlogin:
 *   post:
 *     tags:
 *       - auth
 *     summary: User login verification
 *     description: Verifies user credentials and returns a JWT token if successful.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               emailOrPhone:
 *                 type: string
 *                 description: User's email or phone number
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 description: User's password
 *                 example: "password"
 *     responses:
 *       200:
 *         description: Successfully logged in
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 token:
 *                   type: string
 *                   description: JWT token for authentication
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5..."
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "60d5ec49ed1b9a10b4f4176f"
 *                     firstName:
 *                       type: string
 *                       example: "John"
 *                     lastName:
 *                       type: string
 *                       example: "Doe"
 *                     email:
 *                       type: string
 *                       example: "<EMAIL>"
 *                     isAdmin:
 *                       type: boolean
 *                       example: false
 *                     isSuperAdmin:
 *                       type: boolean
 *                       example: false
 *                     linkedClinics:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ["clinicId1", "clinicId2"]
 *                     profilePic:
 *                       type: string
 *                       example: "profile.jpg"
 *                     isDoctor:
 *                       type: boolean
 *                       example: true
 *                 message:
 *                   type: string
 *                   example: "logged in"
 *       400:
 *         description: Invalid credentials
 *       500:
 *         description: Internal server error
 */

auth.post("/authlogin", ValidateLoginVerify,
  async (req, res, next) => {
    try {
      return await loginVerify(req, res);
    }
    catch (e) {
      next(e)
    }
  }
);
auth.post("/verify-token", ValidateTokenVerificationApi,
  async (req, res, next) => {
    try {
      return await tokenVerificationApi(req, res);
    }
    catch (e) {
      next(e)
    }
  });
auth.post("/forgototp", ValidateForgotPassword,
  async (req, res, next) => {
    try {
      return await forgotPassword(req, res);
    }
    catch (e) {
      next(e)
    }
  });
auth.post("/resendforgototp", ValidateResendOTP,
  async (req, res, next) => {
    try {
      return await resendOTP(req, res);
    }
    catch (e) {
      next(e)
    }
  });
auth.post("/verifyotp", ValidateCheckOTP,
  async (req, res, next) => {
    try {
      return await checkOTP(req, res);
    }
    catch (e) {
      next(e)
    }
  });
auth.post("/setpassword", ValidateSetPassword,
  async (req, res, next) => {
    try {
      return await setPassword(req, res);
    }
    catch (e) {
      next(e)
    }
  });


export default auth;
