import { useEffect, useState } from "react";
import { Form, redirect, useLoaderData } from "react-router-dom";
import Sidebar from "components/detail-page/Sidebar";
import Documents from "components/detail-page/Documents";
import BankDetails from "components/detail-page/BankDetails";
import ContactDetails from "components/detail-page/ContactDetails";
import AvailableTimeSlot from "components/detail-page/staff/AvailableTimeSlot.jsx";
import Status from "components/detail-page/Status";
import { StaffSubmitApi } from "services/staff/staff";
import ValidateStaff from "../../validation/Staff.js";
import StaffPersonalDetails from "../../components/detail-page/staff/StaffPersonalDetails.jsx";
import { GetStaffApi } from "services/staff/staff.js";
import { CheckMobileNumberExists } from "../../services/staff/staff.js";
import { alertBox, confirm } from "../../components/dialog/prompt.jsx";
import { GetErrorMessageForStaff } from "../../utils/CommonMethods.js";
import { GetCurrentStaffId } from "../../services/client/client.js";
import { useSelector } from "react-redux";
import { NavigationButton } from "../../components/detail-page/Navigation.jsx";
export async function StaffAction({ request, params }) {
    const formData = await request.formData();
    const updates = Object.fromEntries(Array.from(formData.keys()).map(key => [key, formData.getAll(key).length > 1 ? formData.getAll(key) : formData.get(key)]));
    const validation = ValidateStaff(updates);
    if (!validation)
        return false;

    var response = await StaffSubmitApi(updates, params.id, updates.profileName, updates.documentNames);
    if (response?.response?.status == 400 && response?.response?.data?.isSuccess == false) {
        var messageText = GetErrorMessageForStaff(response?.response?.data?.description);
        alertBox({
            show: true,
            title: 'Message',
            proceed: undefined,
            confirmation: messageText
        });
        return false;
    }


    if (response.status == 200)
        return redirect(`/staff/managestaffs`);
    else
        return false;
}
export async function StaffLoader({ params }) {
    var staffData = undefined;
    if (params?.id != undefined) {
        staffData = await GetStaffApi(params.id);
        return { staffData };
    }
    return { staffData };
}
function EditStaff() {
    const { clinic } = useSelector((state) => state.currentClinic);
    const blob_URL = `${import.meta.env.VITE_BLOB_URL}${import.meta.env.VITE_CLINICBLOB_CONTAINER_PREFIX}${clinic._id
        }/staff/`;
    var { staffData } = useLoaderData();
    const [autoId, SetAutoId] = useState(staffData?.staffId);
    const [isAdmin, setIsAdmin] = useState(staffData?.isAdmin ?? false);
    const [isDoctor, setIsDoctor] = useState(staffData?.isDoctor ?? false);
    if (staffData == undefined) {
        staffData = {};
    }
    const settingsArray = ["Status", "Personal-details", "Contact-details", "Bank-details",  "Available-time-slot", "Documents"];
    const [activeTab,
        setActiveTab] = useState(0);
    var isLoadingAutoid = false;

    useEffect(() => {
        if (staffData?.staffId == null && !isLoadingAutoid) {
            isLoadingAutoid = true;
            const fetchAutoId = async () => {
                let data = await GetCurrentStaffId();
                let generatedId = "";
                if (data?.staffId?.prefix != null && data?.staffId?.prefix != "") {
                    generatedId = data.staffId.prefix + "_"
                }
                generatedId = generatedId + (data.staffId.currentStaffId);
                if (data?.staffId?.suffix != null && data.staffId.suffix != "") {
                    generatedId = generatedId + "_" + data.staffId.suffix;
                }
                SetAutoId(generatedId);
            }
            fetchAutoId();
        }
    }, [isLoadingAutoid]);
    const handleTabClick = (tabNumber) => {
        setActiveTab(tabNumber);
    };
    return (<div className="h-full ">
        <Form className="flex flex-row h-full pb-4 gap-3" method="post" encType="multipart/form-data" noValidate>

            <nav className="tabs flex-col border rounded-lg items-start p-3 font-primary text-base font-medium gap-3 w-xs" aria-label="Tabs" role="tablist" data-tabs-vertical="true" aria-orientation="horizontal" >
                {settingsArray.map((data, index) => {
                    var hiddenClass= !isDoctor && index==4?" hidden":"";
                    return (
                        <button type="button" key={`tab_button-${index}`} 
                        className={`btn btn-text btn-secondary active-tab:bg-secondary/10 active-tab:text-black hover:text-secondary hover:bg-secondary/10 ${index == 0 ? "active" : ""} w-full px-10 py-7 justify-start ${hiddenClass}`}
                            id={`tab-${data}`}
                            data-tab={`#tab-${data}-data`}
                            aria-controls={`tab-${data}-data`}
                            role="tab"
                            aria-selected="true" >
                            {data.toString().replace("-", " ")}
                        </button>)
                })}
            </nav>

            <div className='w-full h-full ml-2'>
                <div id="tab-Status-data" className=" flex flex-col h-full px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-Status">
                    <Status
                        setIsDoctor={setIsDoctor}
                        isDoctor={isDoctor}
                        setIsAdmin={setIsAdmin}
                        isAdmin={isAdmin} />
                    <NavigationButton
                        next={() => {
                            window.HSTabs.open(document.querySelector('#tab-Personal-details'));
                            return 1;
                        }}
                    />
                </div>
                <div id="tab-Personal-details-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-Personal-details">
                    <StaffPersonalDetails staffId={autoId} firstName={staffData.firstName} lastName={staffData.lastName}
                        age={staffData.age} gender={staffData.gender} birthday={staffData.birthday} profilePic={staffData.profilePic}
                        specialization={staffData.specialization}
                        hprId={staffData.hprId}
                        isDoctor={isDoctor}
                        prefix={staffData.prefix}
                    />
                    <NavigationButton
                        prev={() => {
                            window.HSTabs.open(document.querySelector('#tab-Status'));
                            return 1;
                        }}
                        next={() => {
                            window.HSTabs.open(document.querySelector('#tab-Contact-details'));
                            return 1;
                        }}
                    />
                </div>
                <div id="tab-Contact-details-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-Contact-details">
                    <ContactDetails mobile={staffData.mobile} email={staffData.email} address={staffData.address} />
                    <NavigationButton
                        prev={() => {
                            window.HSTabs.open(document.querySelector('#tab-Personal-details'));
                            return 1;
                        }}
                        next={() => {
                            window.HSTabs.open(document.querySelector('#tab-Bank-details'));
                            return 1;
                        }}
                    />
                </div>
                <div id="tab-Bank-details-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-Bank-details">
                    <BankDetails bankName={staffData.bankName} ifsc={staffData.ifsc} account={staffData.account} accountName={staffData.accountName} />
                    <NavigationButton
                        prev={() => {
                            window.HSTabs.open(document.querySelector('#tab-Contact-details'));
                            return 1;
                        }}
                        next={() => {
                            isDoctor &&  window.HSTabs.open(document.querySelector('#tab-Available-time-slot'));
                            !isDoctor && window.HSTabs.open(document.querySelector('#tab-Documents'))
                            return 1;
                        }}
                    />
                </div>
                <div id="tab-Available-time-slot-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-Available-time-slot">
                <AvailableTimeSlot
                            savedTimeSlot={staffData.availableTimeSlot}
                        />
                    <NavigationButton
                        prev={() => {
                            window.HSTabs.open(document.querySelector('#tab-Bank-details'));
                            return 1;
                        }}
                        next={() => {
                            window.HSTabs.open(document.querySelector('#tab-Documents'));
                            return 1;
                        }}
                    />
                </div>

                <div id="tab-Documents-data" className="hidden h-full flex flex-col px-8 py-6 bg-backcolor_detailpage rounded-lg" role="tabpanel" aria-labelledby="tab-Documents">
                <Documents documentType={staffData.documentType} documentNames={staffData.documents}
                        documentNo={staffData.documentNumber} blob_url={blob_URL} click={() => { }}
                    />
                    
                </div>

            </div>
        </Form>
    </div>)
}

export default EditStaff