import mongoose from "mongoose";
import mongoosePaginate from "mongoose-paginate-v2";
import { CLIENT_COLLECTION, PATIENT_COLLECTION } from "../../mongodb.collection.name.js";

const masterMedicationSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
    },
    description: {
      type: String,
    },
    is_deleted: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

masterMedicationSchema.plugin(mongoosePaginate);

// const masterMedicationModel = mongoose.model(
//   "masterMedication",
//   masterMedicationSchema
// );

const pastMedicationSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    duration: {
      value: Number,
      unit: String,
    },
    notes: {
      type: String,
    },
    patient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: PATIENT_COLLECTION
    },
    clinic: {
      type: mongoose.Schema.Types.ObjectId,
      ref: CLIENT_COLLECTION
    },
    created: {
      on: {
        type: Date,
        default: Date.now,
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255,
        },
      },
    }
  },
  {
    versionKey: "1.0",
    timestamps: true,
  }
);

pastMedicationSchema.index({name:1, clinic: 1, patient: 1 });

// const MedicationHistories = mongoose.model("history_medications", pastMedicationSchema);

export { pastMedicationSchema,masterMedicationSchema };
