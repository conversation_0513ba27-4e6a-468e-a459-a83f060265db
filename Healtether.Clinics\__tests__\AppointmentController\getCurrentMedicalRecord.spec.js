import { jest } from "@jest/globals";
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
mockApointmentHelper();
const { getCurrentMedicalRecord } = await import("../../controllers/appointments/appointment.controller.js"); 
const { getCurrentAppointmentRecord } = await import("../../helpers/appointment/appointment.helper.js");

describe("getCurrentMedicalRecord", () => {
  let req, res;

  beforeEach(() => {
    req = {
      query: {
        id: "patientId123",
      },
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  });

  it("should return 200 with the current medical record data", async () => {
    const mockAppointments = [
      {
        _id: "appointmentId123",
        medicalRecords: [
          {
            fileName: "diabetes_report.pdf",
            blobName: "45678_diabetes_report",
          },
        ],
        procedureRecords: [],
        prescriptionRecords: [
          {
            fileName: "diabetes_prescription.pdf",
            blobName: "45678_diabetes_prescription",
          },
        ],
      },
    ];

    getCurrentAppointmentRecord.mockResolvedValue(mockAppointments);
    await getCurrentMedicalRecord(req, res);
    expect(getCurrentAppointmentRecord).toHaveBeenCalledWith(req.query.id);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(mockAppointments);
  });

  // it("should return a 400 error if no  Id  is provided", async () => {
  //   req.query = {};
  //   await getCurrentMedicalRecord(req, res);
  //   expect(res.status).toHaveBeenCalledWith(400);
  //   expect(res.json).toHaveBeenCalledWith({
  //     success: false,
  //     error: "Id is required",
  //   });
  // });

  // it("should handle errors and return a 500 status code", async () => {
  //   const mockError = new Error("An error occurred");
  //   getCurrentAppointmentRecord.mockRejectedValue(mockError);
  //   await getCurrentMedicalRecord(req, res);

  //   expect(res.status).toHaveBeenCalledWith(500);
  //   expect(res.json).toHaveBeenCalledWith({
  //     success: false,
  //     error: mockError.message,
  //   });
  // });
});
