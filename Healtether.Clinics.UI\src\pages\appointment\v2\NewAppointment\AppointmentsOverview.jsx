import dayjs from "dayjs";
import { useEffect, useRef, useState } from "react";
import QueueDetails from "./queuedetail/QueueDetails.jsx";
import { BookNewAppointment } from "./consultationdetail/BookNewAppointment.jsx";
import {
  BookedDetails,
  AppointmentDetails,
} from "./consultationdetail/AppointmentDetails.jsx";
import moment from "moment";
import { DateSelector } from "../../../../components/detail-page/new appointment/DateSelector.jsx";
import { GetDoctorsWithAvailableTimeSlotApi } from "../../../../services/staff/staff.js";
import { useLocation } from "react-router-dom";
import { GetAppointmentOverview } from "../../../../services/appointment/appointment.js";

export default function AppointmentsOverview() {
  let loadingDoctor = false;
  const location = useLocation();
  const [tab, setTab] = useState(0);
  const [doctors, setDoctors] = useState(() => []);
  const tabTitle = ["Book New", "Booked", "Follow up", "Cancelled"];

  useEffect(() => {
    console.log("location", location.state);
    if (location.state && typeof location.state.defaultTab === "number") {
      setTab(location.state.defaultTab);
      setTimeout(() => {
        const tabId = `tabs-${
          ["booknew", "booked", "follow-up", "cancelled"][
            location.state.defaultTab
          ]
        }`;
        const tabElement = document.getElementById(tabId);
        if (tabElement) {
          tabElement.click();
        }
      }, 0);
    }
  }, [location.state]);

  const [bookedAppointmentDate, setBookedAppointmentDate] = useState(
    moment().format("YYYY-MM-DD")
  );

  useEffect(() => {
    if (!loadingDoctor) {
      loadingDoctor = true;
      const fetchDoctor = async () => {
        var doctors = await GetDoctorsWithAvailableTimeSlotApi();
        setDoctors(doctors);
      };

      fetchDoctor();
    }
  }, []);

  const [queueHasMore, setQueueHasMore] = useState(true);
  const [pg, setPg] = useState(1);
  const [data, setData] = useState([]);
  const [selectedAppointmentDate, setselectedAppointmentDate] = useState(
    moment().format("YYYY-MM-DD")
  );
  const [selectedDoctorId, setSelectedDoctorId] = useState(0);
  const [appointmentstatus, setAppointmentStatus] = useState("Upcoming");

  const [defaultKeywords, setDefaultKeywords] = useState({
    text: "",
    option: {
      status: "Upcoming",
      appDate: { date: dayjs().format("YYYY-MM-DD") },
      doctor: null,
    },
  });

  let loadingQueue = false;

  useEffect(() => {
    if (!loadingQueue) {
      loadingQueue = true;
      fetchQueueData(true);
    }
  }, [selectedDoctorId, appointmentstatus, selectedAppointmentDate]);

  const fetchQueueData = async (reset = false) => {
    const currentPage = reset ? 1 : pg + 1;
    if (reset) {
      resetQueueData();
    }

    let query = `&date=${selectedAppointmentDate}&status=${appointmentstatus}`;
    if (selectedDoctorId !== 0) query += `&doctor=${selectedDoctorId}`;

    const res = await GetAppointmentOverview(currentPage, 6, query);

    if (res.status === 200) {
      loadingQueue = false;
      const fetchedData = res.data?.data || [];
      setPg(currentPage);
      setQueueHasMore(fetchedData.length === 6);
      setData((prev) => [...prev, ...fetchedData]);
    }
  };

  const resetQueueData = () => {
    setPg(1);
    setData([]);
    setQueueHasMore(true);
  };

  return (
    <main className="flex gap-3 h-[98%]">
      <div className="w-[63%] border rounded-xl shadow-xs p-3 h-full">
        <h5 className="font-primary font-normal text-lg">Patient Queue</h5>

        <div className="h-[97%]">
          <QueueDetails
            //selectedAppointmentDate={queueAppointmentDate}
            doctors={doctors}
            setDefaultKeywords={setDefaultKeywords}
            setSelectedDoctorId={setSelectedDoctorId}
            setAppointmentStatus={setAppointmentStatus}
            setselectedAppointmentDate={setselectedAppointmentDate}
            selectedAppointmentDate={selectedAppointmentDate}
            defaultKeywords={defaultKeywords}
            setData={setData}
            data={data}
            fetchQueueData={fetchQueueData}
            queueHasMore={queueHasMore}
           

          />
        </div>
      </div>

      <div className="w-[37%] h-full bg-backcolor_light py-5 px-4 rounded-xl">
        <div className="w-full h-full rounded-md font-primary relative">
          <div className="h-[4%] flex justify-between items-center mb-2">
            <div className="text-lg font-primary font-semibold text-black ">
              Appointments
            </div>
            <DateSelector
              label="booked"
              onChange={(date) => setBookedAppointmentDate(date)}
            />
          </div>

          <nav
            className="tabs tabs-bordered font-semibold "
            aria-label="Tabs"
            role="tablist"
            aria-orientation="horizontal"
          >
            <button
              type="button"
              className={`tab ${
                tab === 0 ? "active tab-active w-full" : "w-full"
              } text-ellipsis`}
              onClick={() => setTab(0)}
              id="tabs-booknew"
              data-tab="#tabs-booknew-data"
              aria-controls="tabs-booknew-data"
              role="tab"
              aria-selected={tab === 0}
            >
              Book New
            </button>
            <button
              type="button"
              className={`tab ${
                tab === 1 ? "active tab-active w-full" : "w-full"
              } text-ellipsis`}
              onClick={() => setTab(1)}
              id="tabs-booked"
              data-tab="#tabs-booked-data"
              aria-controls="tabs-booked-data"
              role="tab"
              aria-selected={tab === 1}
            >
              Booked
            </button>
            <button
              type="button"
              className={`tab ${
                tab === 2 ? "active tab-active w-full" : "w-full"
              } text-ellipsis`}
              onClick={() => setTab(2)}
              id="tabs-follow-up"
              data-tab="#tabs-follow-up-data"
              aria-controls="tabs-follow-up-data"
              role="tab"
              aria-selected={tab === 2}
            >
              Follow up
            </button>
            <button
              type="button"
              className={`tab ${
                tab === 3 ? "active tab-active w-full" : "w-full"
              } text-ellipsis`}
              onClick={() => setTab(0)}
              id="tabs-cancelled"
              data-tab="#tabs-cancelled-data"
              aria-controls="tabs-cancelled-data"
              role="tab"
              aria-selected={tab === 3}
            >
              Cancelled
            </button>
          </nav>

          {/* <div className="h-[5%] flex items-center justify-between gap-3 my-4 border-b border-color_muted/30 px-4 mx-4 overflow-x-auto overflow-y-hidden custom-scrollbar">
            {tabTitle.map((item, idx) => (
              <div
                key={idx}
                onClick={() => setTab(idx)}
                className={`${tab === idx ? "text-black" : "text-color_muted"
                  } min-w-[100px] p-2 flex items-center justify-center cursor-pointer whitespace-nowrap duration-300 text-base font-semibold relative`}
              >
                {tab === idx && "+"}
                {item}
                <div
                  className={`${tab === idx ? "w-full" : "w-0"
                    } absolute -bottom-[1px] left-1/2 -translate-x-1/2 h-0.5 bg-primary_light duration-300`}
                />
              </div>
            ))}
          </div> */}

          <div className="h-[90%] overflow-y-auto px-1 custom-scrollbar mt-1">
            <div
              id="tabs-booknew-data"
              className={`${tab === 0 ? "" : "hidden"} mt-3`}
              role="tabpanel"
              aria-labelledby="tabs-booknew"
            >
              <BookNewAppointment
                fetchQueueData={()=>fetchQueueData(true)}
                setTab={setTab}
                doctors={doctors}
              />
            </div>
            <div
              id="tabs-booked-data"
              className={`${tab === 1 ? "" : "hidden"}`}
              role="tabpanel"
              aria-labelledby="tabs-booked"
            >
              <AppointmentDetails
                status="Booked"
                setTab={setTab}
                filterDate={bookedAppointmentDate}
              />
            </div>
            <div
              id="tabs-follow-up-data"
              className={`${tab === 2 ? "" : "hidden"}`}
              role="tabpanel"
              aria-labelledby="tabs-follow-up"
            >
              <AppointmentDetails
                filterDate={bookedAppointmentDate}
                status="FollowUp"
                setTab={setTab}
              />
            </div>
            <div
              id="tabs-cancelled-data"
              className={`${tab === 3 ? "" : "hidden"}`}
              role="tabpanel"
              aria-labelledby="tabs-cancelled"
            >
              <AppointmentDetails
                status="Cancelled"
                filterDate={bookedAppointmentDate}
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
