import { getProperMobile } from "../../utils/common.js";

export const getPaymentLinkTemplate = (to, code, clinicName, patientName, doctorName, paymentLink) => {
    var toMobile = getProperMobile(to);
    var summaryTemplate = {
        messaging_product: "whatsapp",
        recipient_type: "individual",
        to: toMobile,
        type: "template",
        template: {
            name: process.env.WHATSAPP_TEMPLATE_PAYMENT_LINK,
            language: {
                code: (code != null && code != ""
                    ? code
                    : "en")
            },
            components: [
                {
                    type: "body",
                    parameters: [
                        {
                            type: "text",
                            text: clinicName
                        }, {
                            type: "text",
                            text: patientName
                        }, {
                            type: "text",
                            text: doctor<PERSON><PERSON>
                        },
                        //  {
                        //     type: "text",
                        //     text: scheduleDate + (isOnline
                        //         ? " (online)"
                        //         : "")
                        // }, {
                        //     type: "text",
                        //     text: timeSlots
                        // }
                    ]

                },
                {
                    type: "button",
                    sub_type: "URL",
                    index: "0",
                    parameters: [
                        {
                            type: "TEXT",
                            text: paymentLink
                        }
                    ]
                }
            ]
        }
    };

    return summaryTemplate;
}
