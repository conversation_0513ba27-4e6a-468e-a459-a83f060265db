import mongoose from "mongoose";
import { CLIENT_COLLECTION } from "../../mongodb.collection.name.js";

const abhaConsentSchema = new mongoose.Schema(
  {
    patientId: {
      type: mongoose.Schema.Types.ObjectId,
      index: true,
    },
    clinicPatientId: {
      type: String,
      maxLength: 255,
    },
    clinicId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: CLIENT_COLLECTION,
      index: true,
    },
    abhaId: {
      type: String,
      maxLength: 255,
    },
    abhaAddress: {
      type: String,
      maxLength: 255,
    },
    patientName: {
      type: String,
    },
    consentStatus: {
      type: String,
      enum: ["pending", "approved", "rejected"],
      maxLength: 20,
    },
    reason: {
      type: String,
    },
    purpose: {
      code: { type: String, required: true },
      text: { type: String, required: true },
      refUri: { type: String },
    },
    hiuId: {
      type: String,
    },
    consentRequestId:{
    type:String,
    },
    careContexts: [
      {
        patientReference: { type: String },
        careContextReference: { type: String },
      },
    ],
    consentArtefacts:[
      {
        id:{
          type:String,
        }
      }
    ],
    hiTypes: [{ type: String }],
    requester: {
      name: { type: String, required: true },
      identifier: {
        type: { type: String },
        value: { type: String },
        system: { type: String },
      },
    },
    requestId: {
      type: String,
      maxLength: 255,
    },
    consentArtifactId: {
      type: String,
      maxLength: 255,
      index: true,
    },
    permission: {
      dateRange: {
        from: { type: Date, required: true },
        to: { type: Date, required: true },
      },
      frequency: {
        unit: { type: String },
        value: { type: Number },
        repeats: { type: Number },
      },
      accessMode: { type: String },
      dataEraseAt: { type: Date },
    },
    requestedToAbha: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true },
  { versionKey: "1.0" }
);

abhaConsentSchema.index({ patientId: 1, clinicId: 1 });
abhaConsentSchema.index({ requestId: 1 });

export { abhaConsentSchema };
