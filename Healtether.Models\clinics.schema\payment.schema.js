import mongoose from "mongoose";
import { CLIENT_COLLECTION, INVOICE_COLLECTION } from "../mongodb.collection.name.js";

const paymentSchema = new mongoose.Schema({
    transactionId: {
        type: String,
        required: true,
        maxLength: 40,
        index: true,
        unique: true
    },
    transactionAmount: {
        type: mongoose.Types.Decimal128,
        required: true
    },
    paidOn: {
        type: Date
    },
    amountPaid: {
        type: mongoose.Types.Decimal128
    },
    isSuccess: {
        type: Boolean,
        default: false
    },
    payload: {
        type: String
    },
    created: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            }
        }
    },
    referenceId: {
        type: String
    },
    responsed: {
        type: Boolean,
        default:false
    },
    isOnline: {
        type: Boolean
    },
    clinic:{
        type: mongoose.Schema.Types.ObjectId,
        ref: CLIENT_COLLECTION,
        index:true
    },
    invoice:{
        type: mongoose.Schema.Types.ObjectId,
        ref: INVOICE_COLLECTION,
        index:true
    },
    paymentMode:{
        type:String
    }
});

//const Payment = new mongoose.model("Payment", paymentSchema);
export {paymentSchema};