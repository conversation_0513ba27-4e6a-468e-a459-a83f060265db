import React from "react";
import { Icons } from "../icons";

export default function UploadFile({ file, setFile }) {
  console.log(file);

  return (
    <section>
      {file?.name ? (
        <main className=" relative h-fit w-fit">
          <Icons.cancel
            color="#F44444"
            onClick={() => setFile({ name: "" })}
            className="absolute -top-1.5 -right-1 cursor-pointer"
          />
          {file?.type?.includes("pdf") ? (
            <iframe
              src={URL.createObjectURL(file)}
              className=" w-24 h-24 rounded-md mt-5"
            />
          ) : (
            <img
              src={URL.createObjectURL(file)}
              alt=""
              className=" w-24 h-24 rounded-md mt-5"
            />
          )}
        </main>
      ) : (
        <label
          for="dropzone-file"
          className="flex flex-col items-center justify-center w-full h-fit py-2 border-2 border-gray border-dashed bg-light rounded-lg cursor-pointer"
        >
          <Icons.upload className="w-10 text-primary" />
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            <span className="font-semibold">Click to upload</span> or drag and
            drop
          </p>
        </label>
      )}
      <input
        type="file"
        id="dropzone-file"
        className="hidden"
        onChange={(e) => setFile(e.target.files[0])}
      />
    </section>
  );
}
