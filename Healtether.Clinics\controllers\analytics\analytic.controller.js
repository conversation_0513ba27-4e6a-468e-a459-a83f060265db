import
{
    getPatientsAppointmentsGenderRatio,
    getAgeGroupAnalyasis,
    patientAnalyasis,
    patientWeeklyAnalysis,
    patientCustomAnalysis,
    getCustomAgeGroupAnalysis,
    getPatientsAppointmentsCustomGenderRatio,
    getAgeGroupWeeklyAnalysis,
    getWeeklyPatientsAppointmentsGenderRatio
}
from "../../helpers/analytics/analytics.helper.js"



export const getPatientGenderRatio = async(req, res) => {
        let appointments;
        if(req.body.type=="monthly"){
            appointments = await getPatientsAppointmentsGenderRatio();
        }else if(req.body.type=="weekly"){
            console.log("in age group weekly")
            appointments = await getWeeklyPatientsAppointmentsGenderRatio();
        }else if(req.body.startDate&&req.body.endDate){
            appointments = await  getPatientsAppointmentsCustomGenderRatio(req.body.startDate, req.body.endDate);
        }
     
        res
            .json(appointments)
            .status(200);
};
export const getAgeRatio = async(req, res) => {
       let param=req.body.type;
       let ageGroup;
       if(param=="monthly"){
        ageGroup=await getAgeGroupAnalyasis();
       }else if(req.body.type=="weekly"){
        ageGroup = await getAgeGroupWeeklyAnalysis()
       } else if(req.body.startDate && req.body.endDate){
        ageGroup=await  getCustomAgeGroupAnalysis(req.body.startDate, req.body.endDate);
            
       }
        res
            .json(ageGroup)
            .status(200);
};

export const getPatientAnalysis = async (req, res) => {
    let param = req.body.type;
    let patients;
    if (param == "monthly") {
      patients = await patientAnalyasis();
    } else if (param == "weekly") {
      patients = await patientWeeklyAnalysis();
    } else if (req.body.startDate && req.body.endDate) {
      patients = await patientCustomAnalysis(
        req.body.startDate,
        req.body.endDate
      );
    }

    console.log("patients", patients);
    res.json(patients).status(200);
};

export const appointmentBooking = async (req, res) => {
    const { period } = req.body;

    // Initialize result arrays
    let result;

    // Get current date
    const currentDate = new Date();

    if (period === "monthly") {
      // Fetching appointment data for the current year and selecting only the appointmentDate field
      const startOfYear = new Date(currentDate.getFullYear(), 0, 1);
      const endOfYear = new Date(currentDate.getFullYear(), 11, 31);

      let data = await Appointment.find({
        appointmentDate: {
          $gte: startOfYear,
          $lte: endOfYear,
        },
      }).select({ appointmentDate: 1 });

      // Initialize an array to hold the count of appointments for each month
      result = Array(12).fill(0);

      // Iterate through the fetched appointment data
      data.forEach((appointment) => {
        // Ensure appointmentDate is a Date object
        const appointmentDate = new Date(appointment.appointmentDate);

        // Get the month from the appointment date (0 = January, 11 = December)
        const month = appointmentDate.getMonth();

        // Increment the count for the respective month
        result[month]++;
      });
    } else if (period === "weekly") {
      // Fetching appointment data for the current week and selecting only the appointmentDate field
      const startOfWeek = new Date(
        currentDate.setDate(currentDate.getDate() - currentDate.getDay())
      );
      const endOfWeek = new Date(
        currentDate.setDate(currentDate.getDate() - currentDate.getDay() + 6)
      );

      let data = await Appointment.find({
        appointmentDate: {
          $gte: startOfWeek,
          $lte: endOfWeek,
        },
      }).select({ appointmentDate: 1 });

      // Initialize an array to hold the count of appointments for each day of the week
      result = Array(7).fill(0);

      // Iterate through the fetched appointment data
      data.forEach((appointment) => {
        // Ensure appointmentDate is a Date object
        const appointmentDate = new Date(appointment.appointmentDate);

        // Get the day of the week from the appointment date (0 = Sunday, 6 = Saturday)
        const dayOfWeek = appointmentDate.getDay();

        // Increment the count for the respective day of the week
        result[dayOfWeek]++;
      });
    } else {
      return res.status(400).json({
        status: "error",
        message:
          "Invalid period specified. Please specify 'weekly' or 'monthly'.",
      });
    }

    // Respond with the result array
    res.status(200).json({
      status: "success",
      result,
    });
};

export const appointmentAnalysis = async (req, res) => {
    const { period } = req.body;

    if (!["weekly", "monthly"].includes(period)) {
      return res.status(400).json({
        status: "error",
        message: "Invalid period. Must be 'weekly' or 'monthly'.",
      });
    }

    let startDate, endDate;

    const now = new Date();

    if (period === "monthly") {
      // Set the date range for the current month
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    } else if (period === "weekly") {
      // Set the date range for the current week (assuming week starts on Sunday)
      const firstDayOfWeek = now.getDate() - now.getDay();
      startDate = new Date(now.setDate(firstDayOfWeek));
      endDate = new Date(now.setDate(firstDayOfWeek + 6));
    }

    // Fetch the appointments within the date range
    let data = await Appointment.find({
      appointmentDate: { $gte: startDate, $lte: endDate },
    }).select({
      rescheduled: 1,
      isCanceled: 1,
      ended: 1,
    });

    const result = [0, 0, 0];

    // Count canceled appointments
    result[0] = data.filter(
      (appointment) => appointment.isCanceled === true
    ).length;

    // Count rescheduled appointments
    result[1] = data.filter(
      (appointment) =>
        appointment.rescheduled && appointment.rescheduled.previousDate
    ).length;

    // Count ended appointments
    result[2] = data.filter(
      (appointment) => appointment.ended && appointment.ended.yes === true
    ).length;

    res.status(200).json({
      status: "success",
      result,
    });
};
