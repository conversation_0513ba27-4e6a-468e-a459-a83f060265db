import { useEffect, useState } from 'react'
import { Outlet, useLocation, useNavigation } from 'react-router-dom'
import Topbar from 'layout/topbar/Topbar'
import Breadcrumb from '../breadcrumb/Breadcrumb';
import Sidebar from '../sidebar/Sidebar';
import { useDispatch } from 'react-redux';
import { notificationMessaging } from '../../google-firebase/firebaseConfig';
import { getToken, onMessage } from 'firebase/messaging';
import { setNotificationKey } from '../../store/slice/NotificationKeySlice';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Message from '../../components/toast/Message';
import { alertBox } from '../../components/dialog/prompt';
import PageLoader from '../../components/loader/PageLoader';
import MenuBar from './MenuBar';
// https://tailwindcomponents.com/component/dashboard-administrador-2

async function loadFlyonUI() {
  return import('flyonui/flyonui');
}
export default function Layout() {
  const { VITE_APP_VAPID_KEY } = import.meta.env;
  const dispatch = useDispatch();
  const [openSideBar, setOpenSideBar] = useState(false);

  // reinitializes the components every time when app is mounted or page was changed
  // this is to avoid the error of flyonui components not being initialized
  // when the page is changed or app is mounted
    const location = useLocation();
  
    useEffect(() => {
      const initFlyonUI = async () => {
        await loadFlyonUI();
        setTimeout(() => {
          if (
            window.HSStaticMethods &&
            typeof window.HSStaticMethods.autoInit === 'function'
          ) {
            window.HSStaticMethods.autoInit();
          }
        }, 100);
      };
  
      initFlyonUI();
    }, []);
    useEffect(() => {
      setTimeout(() => {
        if (
          window.HSStaticMethods &&
          typeof window.HSStaticMethods.autoInit === 'function'
        ) {
          window.HSStaticMethods.autoInit();
        }
      }, 100);
    }, [location.pathname]);
  

  async function requestPermission() {
    //requesting permission using Notification API
    const permission = await Notification.requestPermission();

    if (permission === "granted") {
      const token = await getToken(notificationMessaging, {
        vapidKey: VITE_APP_VAPID_KEY,
      });
      dispatch(setNotificationKey({ device: token }));
    } else if (permission === "denied") {
      //notifications are blocked
      // alert("You denied for the notification");
    }
    // var txt=<Message notification={ {
    //   title:"Test",
    //   body:"check test message"
    // }} />;
    // toast.success(txt);
    onMessage(notificationMessaging, (payload) => {
      var txt = <Message notification={payload.notification} />;
      if (payload?.data?.notificationType == "success") {
        toast.success(txt);
      } else if (payload?.data?.notificationType == "error") {
        toast.error(txt);
      } else {
        toast(txt);
      }
    });
  }
  let isTokenLoading = false;
  useEffect(() => {
    if (!isTokenLoading) {
      isTokenLoading = true;
      requestPermission();
    }
  }, []);

  let ToggleSidebar = () => setOpenSideBar((prevState) => !prevState);
  const [viewSidebarxs,setViewSidebarxs]=useState(false);
  const navigation = useNavigation();

  return (
    <div className="flex flex-col w-screen h-screen font-primary font-medium antialiased tracking-normal">
      <div className="flex-1 flex h-full">
        <div className='relative flex flex-1 flex-col h-full overflow-y-auto overflow-x-hidden'>
              <Topbar setViewSidebarxs={()=>setViewSidebarxs(x=>!x)}/>
              <MenuBar/>
            <div className="mx-4 font-medium text-md" id="breadcrumbLayout" style={{ height: '30px' }}>
              <Breadcrumb />
            </div>
            <div id="detailOutlet" className="scroll-smooth hover:scroll-auto px-[4] font-secondary font-normal pr-3 pl-4 overscroll-y-auto overflow-y-scroll mt-2 bg-base-100" style={{ height: '100%' }}>
           {(navigation.state == "loading") ? <PageLoader /> : <Outlet />} 
            </div>
        </div>
      </div>
      <ToastContainer
        position="bottom-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
}
