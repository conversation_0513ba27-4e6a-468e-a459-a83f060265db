# Node.js Express Web App to Linux on Azure
# Build a Node.js Express app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- version/*
- release/*

variables:
  - group: pipeline-variable

  - name: 'azureSubscription'
    value: 'd134d55b-a55d-46fc-bd34-f256c1f3dcb2'

  - name: 'webAppName'
    value: 'api-chats'

  - name: 'environmentName'
    value: 'api-chats'

  - name: 'vmImageName'
    value: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)

    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '18.x'
      displayName: 'Install Node.js'

    - script: |
        npm config set registry https://pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/
        npm config set //pkgs.dev.azure.com/healtether/HealtetherApps/_packaging/healtether_clinics/npm/registry/:_authToken $personal_Token
        npm install
        npm run build --if-present
    #    npm run test --if-present

      displayName: 'npm install, build, test'
      env:
        personal_Token: $(package_token)

    - task: CopyFiles@2
      displayName: "Copy files"
      inputs:
        SourceFolder: "$(System.DefaultWorkingDirectory)"
        Contents: |
          **/*
          !*.md
          !*.yml
          !.git/**
          !*.gitignore
        TargetFolder: "$(System.DefaultWorkingDirectory)/tmp"

    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)/tmp'
        includeRootFolder: false
        archiveType: zip
        archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
        replaceExistingArchive: true

    - upload: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
      artifact: drop