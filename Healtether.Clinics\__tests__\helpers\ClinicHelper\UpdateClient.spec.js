import mongoose from 'mongoose';
import { jest } from "@jest/globals";
import { updateClient } from '../../../helpers/clinic/client.helper.js';
import { Client } from '../../../model/clinics.model.js';

import { setup, teardown } from '../../../setup.js';

jest.setTimeout(30000);

beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe('updateClient Function Test', () => {
  let clientId;
  let user;
let groupId=new mongoose.Types.ObjectId();
let adminUserId=new mongoose.Types.ObjectId();

  beforeEach(async () => {
    // Create a test client
    const client = new Client({
      clinicName: 'Test Clinic',
      groupId:groupId,
      logo: 'logo.png',
      adminUserId:adminUserId,
      address: '123 Main St',
      isActive: true,
      deleted: false,
    });
    await client.save();
    clientId = client._id;

    user = { id: "test_user", name: 'Test User' };
  });

  it('should update a client and return the updated client', async () => {
    const data = {
      clinicName: 'Updated Clinic Name',
      logo: 'logo2.png',
      address: '123 Main St',
      isActive: true,
    };

    const result = await updateClient(clientId, data, user);

    expect(result.clinicName).toBe('Updated Clinic Name');
    expect(result.logo).toBe('logo2.png');
    expect(result.address).toBe('123 Main St');
    expect(result.isActive).toBe(true);
    expect(result.modified.on).toBeInstanceOf(Date);
    expect(result.modified.by).toEqual(user);
  });

  // it('should return an error if the update fails', async () => {
  //   const data = {
  //     clinicName: 'Updated Clinic Name',
  //     logo: 'logo.png',
  //     address: '123 Main St',
  //     isActive: true,
  //     groupId: new mongoose.Types.ObjectId(),
  //     adminUserId: new mongoose.Types.ObjectId(),
  //   };

  //   jest.spyOn(Client, 'findByIdAndUpdate').mockImplementationOnce(() => {
  //     throw new Error('Mock error');
  //   });

  //   const result = await updateClient(clientId, data, user);

  //   expect(result).toBeInstanceOf(Error);
  // });
});