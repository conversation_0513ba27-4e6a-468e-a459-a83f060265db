import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { QueueActions } from "./QueueActions";
import { Icons } from "../../../../../components/detail-page/icons";

export function PatientQueueInfo({ info, endConsultation }) {
  const [appointment, SetAppointment] = useState(info);
  const navigate = useNavigate();

  useEffect(() => {
    SetAppointment(info);
  }, [info]);

  return (
    <>
      <div className="card card-sm card-border shadow-none mt-2 rounded-lg  hover:bg-accent/1" onClick={(event) => {
        event.stopPropagation();
        navigate(`/appointment/${appointment._id}/consultation`);
      }}>

        <div className="card-body">
          <section className="w-full flex flex-row items-center justify-between text-base font-primary">
            <div className="text-sm">
              #Token {appointment?.tokenNumber && appointment.tokenNumber}
            </div>
            <div className="capitalize whitespace-nowrap flex flex-row text-md font-medium">
              {appointment?.name} &nbsp;
              <span className="text-color_muted text-sm content-center">
                {" "}
                {appointment?.age} Years, {appointment?.gender}{" "}
              </span>
            </div>
            <aside className="h-7 flex items-center  gap-2 text-Primary cursor-pointer font-medium">
              <Icons.doctor_profile className="h-5 w-5" />
              Dr.{appointment?.doctorName}
            </aside>
          </section>

          <div className="flex flex-row h-full item-center justify-between">
           
            <section className="font-normal text-color_muted flex  flex-col gap-3">
              <div className="flex items-center gap-2 truncate">
                <Icons.profile className="h-5 w-5" />
                <div className=" truncate"> {appointment?.clinicPatientId}</div>
              </div>
              <div className="flex items-center gap-2 truncate">
                <Icons.address className="h-5 w-5" /> {appointment.abhaAddress}
              </div>
            </section>

            <section className="font-normal text-color_muted flex flex-col mx-2">
              <div className=" flex items-center gap-2 ">
                <Icons.phone className="h-5 w-5" />
                {appointment.mobile}
              </div>
              <div className=" flex items-center gap-2 mt-2">
                <Icons.abha_num className="h-5 w-5" />
                <div className="mt-0.5">{appointment.abhaNumber}</div>
              </div>
            </section>

            {QueueActions(appointment, true, "", "", endConsultation, navigate).map(
              ({ label, Icon, handleClick }, idx) =>
                idx < 6 && (
                  <div
                    key={idx}
                    onClick={handleClick}
                    className="text-center text-xs  flex flex-col items-center gap-1 mx-2 font-primary fond-medium"
                  >
                    <div className="p-2 rounded-md bg-backcolor_primary cursor-pointer flex items-center justify-center mb-1">
                      <Icon />
                    </div>
                    {label}
                  </div>
                )
            )}
          </div>
        </div>
      </div>
      {/* <div className=" flex justify-evenly w-full rounded-xl px-2 item-center border h-32 overflow-x-auto overflow-y-hidden custom-scrollbar mb-2" >
        <div className="flex flex-col w-2/12 gap-1 items-center h-full justify-center text-color_muted">
          <div>#Token</div>
          <div className="text-wrap text-xs">{appointment?.timeSlot} </div>
          {appointment?.virtualConsultation && (
            <span
              className="text-Secondary text-sm cursor-pointer"
              onClick={(e) => {
                e.preventDefault();
                window.open(
                  appointment.googleLink.link,
                  "_blank",
                  "rel=noopener noreferrer"
                );
                e.stopPropagation();
              }}
            >
              Join call
            </span>
          )}
        </div>

        <div className="flex flex-col w-10/12 ">

        </div>
      </div> */}
    </>
  );
}


function AppointmentButton(){
  return(
  <button class="btn btn-square btn-soft btn-primary" aria-label="Soft Icon Button"> 
  <span class="icon-[tabler--star] size-4.5 shrink-0"></span>
  </button>
  )
}