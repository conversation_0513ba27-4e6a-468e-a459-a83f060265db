import {UserOTPModel} from "../../model/clinics.model.js";
import {getExpiryByMinutes} from "../../utils/common.utils.js";

export const upsertUserOTP = async(data) => {
        var userOtpModel = {};

        var otpModel = await UserOTPModel
            .findOne({mobile: data.mobile})
            .exec();

        if (otpModel != null && otpModel.mobile != "") {
            otpModel.mobile = data.mobile,
            otpModel.email = data.email,
            otpModel.otp = data.otp,
            otpModel.timestamp = new Date().toISOString(),
            otpModel.expireAt = getExpiryByMinutes(15)

            await otpModel.save();
            userOtpModel = otpModel;

        } else {
            userOtpModel = new UserOTPModel({
                mobile: data.mobile,
                email: data.email,
                otp: data.otp,
                timestamp: new Date().toISOString(),
                expireAt: getExpiryByMinutes(15)

            });

            await userOtpModel.save();
        }
        return userOtpModel;
};

export const verifyUserOTPByEmail = async(email, otp) => {
    var otpModel = await UserOTPModel
        .findOne({email: email})
        .exec();
    if (otpModel != null && otpModel.otp == otp) {
        return true;
    }
    return false;

}
export const verifyUserOTPByMobile = async(mobile, otp) => {
    var otpModel = await UserOTPModel
        .findOne({mobile: mobile})
        .exec();
    if (otpModel != null && otpModel.otp == otp) {
        return true;
    }
    return false;
}
