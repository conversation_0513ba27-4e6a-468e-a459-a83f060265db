import { jest } from "@jest/globals";
const { mockApointmentHelper } = await import("../mocks/mock.appointment.helper.js");
mockApointmentHelper();
const { getTodayAppointment } = await import("../../controllers/appointments/appointment.controller.js"); 
const { getAppointmentCount } = await import("../../helpers/appointment/appointment.helper.js");

describe('getTodayAppointment', () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: {
                clinicId: 'clinic123'
            }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should return 200 with the correct received and completed counts', async () => {
        const mockAppointments = [
            { paymentStatus: true },   // Completed appointment
            { paymentStatus: false },  // Not completed appointment
            { paymentStatus: false }   // Not completed appointment
        ];

        getAppointmentCount.mockResolvedValue(mockAppointments);

        await getTodayAppointment(req, res);

        // Verify the date handling
        const expectedDate = new Date(new Date().toISOString().split("T")[0] + "T00:00");
        expect(getAppointmentCount).toHaveBeenCalledWith(req.query.clinicId, expectedDate);
        
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            received: 2,      // Total (3) - Completed (1) = 2
            completed: 1,      // Number of completed appointments
            data: mockAppointments  // The controller includes the data in response
        });
    });

    it('should use provided date from query if available', async () => {
        const testDate = '2023-01-01T00:00';
        req.query.date = testDate;
        
        const mockAppointments = [{ paymentStatus: true }];
        getAppointmentCount.mockResolvedValue(mockAppointments);

        await getTodayAppointment(req, res);

        expect(getAppointmentCount).toHaveBeenCalledWith(req.query.clinicId, testDate);
        expect(res.status).toHaveBeenCalledWith(200);
    });

});