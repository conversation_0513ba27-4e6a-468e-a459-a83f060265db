import { MedicationHistories,FamilyHistories,Allergies,Patient,PastProcedure,PersonalHistories,PastHistories} from "../../../model/clinics.model.js";
import mongoose from "mongoose";

export const updateMedicalHistory = async (data, user, patientId, clinicId) => {
  if (data?.allergies?.length > 0) {
    await upsertAllergies(data?.allergies, user, patientId, clinicId);
  }
  if (data?.medication?.length > 0) {
    await upsertMedication(data?.medication, user, patientId, clinicId);
  }
  if (data?.familyHistory?.length > 0) {
    await upsertFamilyHistory(data?.familyHistory, user, patientId, clinicId);
  }
  if (data?.pastHistory?.length > 0) {
    await upsertPastHistory(data?.pastHistory, user, patientId, clinicId);
  }
  if (data?.pastProcedureHistory?.length > 0) {
    await upsertPastProcedure(
      data?.pastProcedureHistory,
      user,
      patientId,
      clinicId
    );
  }
  if (data?.personalHistory?.length > 0) {
    await upsertPersonalHistory(
      data?.personalHistory,
      user,
      patientId,
      clinicId
    );
  }
};
export const getMedicalHistoryForPatient = async (patientId) => {
  const patientHistories = await Patient.findById(patientId)
    .populate({
      path: "allergies",
      select: {
        name: 1,
        duration: 1,
        notes: 1,
      },
    })
    .populate({
      path: "medication",
      select: {
        name: 1,
        duration: 1,
        notes: 1,
      },
    })
    .populate({
      path: "familyHistory",
      select: {
        name: 1,
        duration: 1,
        notes: 1,
      },
    })
    .populate({
      path: "pastHistory",
      select: {
        name: 1,
        duration: 1,
        notes: 1,
      },
    })
    .populate({
      path: "pastProcedureHistory",
      select: {
        name: 1,
        duration: 1,
        notes: 1,
      },
    })
    .populate({
      path: "personalHistory",
      select: {
        activity: 1,
        nature: 1,
        notes: 1,
      },
    })
    .select({
      allergies: 1,
      medication: 1,
      familyHistory: 1,
      pastHistory: 1,
      pastProcedureHistory: 1,
      personalHistory: 1,
    })
    .exec();
  return patientHistories;
};

const upsertAllergies = async (allergies, user, patientId, clinicId) => {
  var filterAllergies = allergies?.filter((a) => a?.name?.trim() != "");

  if (filterAllergies != null && filterAllergies.length > 0) {
    var allergies = await Allergies.deleteMany({
      clinic: new mongoose.Types.ObjectId(clinicId),
      patient: new mongoose.Types.ObjectId(patientId),
    });

    for (const allergieObj of filterAllergies) {
        const allergiesCollection = new Allergies({
            name: allergieObj.name,
            duration: allergieObj.duration,
            notes: allergieObj.notes,
            created: {
              on: new Date().toISOString(),
              by: {
                id: user.id,
                name: user.name,
              },
            },
            clinic: new mongoose.Types.ObjectId(clinicId),
            patient: new mongoose.Types.ObjectId(patientId),
          });
          await allergiesCollection.save();
    }
  }
};

const upsertMedication = async (medication, user, patientId, clinicId) => {
  let filterMedication = medication?.filter((a) => a?.name?.trim() != "");

  if (filterMedication != null && filterMedication.length > 0) {
    let medications = await MedicationHistories.deleteMany({
      clinic: new mongoose.Types.ObjectId(clinicId),
      patient: new mongoose.Types.ObjectId(patientId),
    });

    for (const medicationObj of filterMedication) {
        const medicationCollection = new MedicationHistories({
            name: medicationObj.name,
            duration: medicationObj.duration,
            notes: medicationObj.notes,
            created: {
              on: new Date().toISOString(),
              by: {
                id: user.id,
                name: user.name,
              },
            },
            clinic: new mongoose.Types.ObjectId(clinicId),
            patient: new mongoose.Types.ObjectId(patientId),
          });
          await medicationCollection.save();
    }
  }
};

const upsertFamilyHistory = async (
  familyHistory,
  user,
  patientId,
  clinicId
) => {
  let filterFamilyHistory = familyHistory?.filter((a) => a?.name?.trim() != "");

  if (filterFamilyHistory != null && filterFamilyHistory.length > 0) {
    let medications = await FamilyHistories.deleteMany({
      clinic: new mongoose.Types.ObjectId(clinicId),
      patient: new mongoose.Types.ObjectId(patientId),
    });

    for (const familyHistoryObj of filterFamilyHistory) {
        const familyHistoryCollection = new FamilyHistories({
            name: familyHistoryObj.name,
            duration: familyHistoryObj.duration,
            notes: familyHistoryObj.notes,
            created: {
              on: new Date().toISOString(),
              by: {
                id: user.id,
                name: user.name,
              },
            },
            clinic: new mongoose.Types.ObjectId(clinicId),
            patient: new mongoose.Types.ObjectId(patientId),
          });
          await familyHistoryCollection.save();
    }
   
  }
};

const upsertPastHistory = async (pastHistory, user, patientId, clinicId) => {
  let filterPastHistory = pastHistory?.filter((a) => a?.name?.trim() != "");

  if (filterPastHistory != null && filterPastHistory.length > 0) {
    let medications = await PastHistories.deleteMany({
      clinic: new mongoose.Types.ObjectId(clinicId),
      patient: new mongoose.Types.ObjectId(patientId),
    });

    for (const pastHistoryObj of filterPastHistory) {
        const pastHistories = new PastHistories({
            name: pastHistoryObj.name,
            duration: pastHistoryObj.duration,
            notes: pastHistoryObj.notes,
            created: {
              on: new Date().toISOString(),
              by: {
                id: user.id,
                name: user.name,
              },
            },
            clinic: new mongoose.Types.ObjectId(clinicId),
            patient: new mongoose.Types.ObjectId(patientId),
          });
          await pastHistories.save();
    }
  }
};

const upsertPastProcedure = async (
  pastProcedure,
  user,
  patientId,
  clinicId
) => {
  let filterPastProcedure = pastProcedure?.filter((a) => a?.name?.trim() != "");

  if (filterPastProcedure != null && filterPastProcedure.length > 0) {
    let medications = await PastProcedure.deleteMany({
      clinic: new mongoose.Types.ObjectId(clinicId),
      patient: new mongoose.Types.ObjectId(patientId),
    });

    for (const pastProcedureObj of filterPastProcedure) {
        const procedure = new PastProcedure({
            name: pastProcedureObj.name,
            duration: pastProcedureObj.duration,
            notes: pastProcedureObj.notes,
            created: {
              on: new Date().toISOString(),
              by: {
                id: user.id,
                name: user.name,
              },
            },
            clinic: new mongoose.Types.ObjectId(clinicId),
            patient: new mongoose.Types.ObjectId(patientId),
          });
          await procedure.save();
    }
  }
};

export const upsertPersonalHistory = async (
  personalHistories,
  user,
  patientId,
  clinicId
) => {
  let filterPersonalHistories = personalHistories?.filter(
    (a) => a?.activity?.trim() != ""
  );

  if (filterPersonalHistories != null && filterPersonalHistories.length > 0) {
    let medications = await PersonalHistories.deleteMany({
      clinic: new mongoose.Types.ObjectId(clinicId),
      patient: new mongoose.Types.ObjectId(patientId),
    });

    for (const personalProcedureObj of filterPersonalHistories) {
      const histories = new PersonalHistories({
        activity: personalProcedureObj.activity,
        nature: personalProcedureObj.nature,
        notes: personalProcedureObj.notes,
        created: {
          on: new Date().toISOString(),
          by: {
            id: user.id,
            name: user.name,
          },
        },
        clinic: new mongoose.Types.ObjectId(clinicId),
        patient: new mongoose.Types.ObjectId(patientId),
      });

      let result = await histories.save();
    }
  }
};
