import React, { useEffect, useState } from "react";
import <PERSON><PERSON> from "../modal";
import <PERSON><PERSON><PERSON> from "./link-abha";
import { AbhaVerification } from "./abha-verification";
import Verify<PERSON>umber from "./verify-number";
import <PERSON><PERSON><PERSON><PERSON> from "./create-abha";
import ABHACard from "./ABHACard";
import {
  abhaIndexRequestotp,
  fetchAbhaCard,
  getUserProfile,
  searchAbha,
  verifyIndexAbhaOtp,
} from "../../../services/appointment/abha-m1";
import {
  CalculateAge,
  formatDateToUTC,
  startTimer,
  updateResendAttempts,
} from "../../../utils/CommonMethods";
import { PatientProfileWithAbhaDetails } from "../..//../services/patient/patient";
import Spinner from "../../loader/Spinner";
import { Input } from "../input";
import PatientUpdateAbort from "./PatientUpdateAbort";
// Define constants for flow steps
const Steps = {
  FIND_ABHA: "findAbha",
  ABHA_PRESENT: "AbhaPresent",
  VERIFY_ABHA_OTP: "verifyAbhaOtp",
  ABHA_NOT_PRESENT: "AbhaNotPresent",
  LINK_ABHA: "linkAbha",
  AADHAAR_VERIFICATION: "aadhaarVerification",
  MOBILE_VERIFICATION: "mobileVerification",
  NEW_ADDRESS: "newAddress",
  VIEW_ABHA_CARD: "viewAbhaCard",
  NUMBER_NOT_SAME: "numberNotSAME",
  CREATE_ABHA: "createAbha",
  PATIENT_EXIST: "patientexist",
};

export default function AddAbhaUsingMobile(propValues) {
  const {
    addAbhaUsing,
    setAddAbhaUsing,
    patientMobile,
    setpatientMobile,
    setAddress,
    address,
    setAbhaNumber,
    abhaNumber,
    OnSelectPatient,
    isCreatingAddress,
    setIsCreatingAddress,
  } = propValues;
  const [busy, setBusy] = useState(false);
  const [currentStep, setCurrentStep] = useState(Steps.VERIFY_NUMBER);
  const [otp, setOtp] = useState(Array(6).fill(""));
  const [aadhaar, setAadhaar] = useState(Array(3).fill(""));
  const [error, setError] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [patientData, setPatientData] = useState(null);
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const [abhaData, setAbhaData] = useState(null);
  const [abhaCard, setAbhaCard] = useState(null);
  const [timer, setTimer] = useState(60);
  const [selectedAbha, setSelectedAbha] = useState(null);
  const [resendAttemptsMap, setResendAttemptsMap] = useState(new Map());

  const resetState = () => {
    setCurrentStep(Steps.VERIFY_NUMBER);
    setOtp(Array(6).fill(""));
    setAadhaar(Array(3).fill(""));
    setError(false);
    setAddress("");
    setAddAbhaUsing("");
  };

  const props = {
    otp,
    setOtp,
    aadhaar,
    setAadhaar,
    error,
    setError,
    address,
    setAddress,
    setAbhaNumber,
    abhaNumber,
    patientMobile,
    setpatientMobile,
    setIsCreatingAddress,
    isCreatingAddress,
    busy,
    setBusy,
    nextStep: setCurrentStep,
  };

  const AbhaCard = async () => {
    try {
      setBusy(true);
      let result = await fetchAbhaCard();
      setBusy(false);
      setAbhaCard(result);
      setCurrentStep(Steps.VIEW_ABHA_CARD);
    } catch (error) {
      setBusy(false);
      error?.response?.data?.error
        ? handleError(error.response.data.error || "Unexpected error occurred")
        : handleError(error.message);
    }
  };

  const handleAddPatientProfile = async (update) => {
    let data;
    try {
      let birthDay;
      if (profileData.dob) {
        birthDay = formatDateToUTC(profileData.dob);
      } else if (profileData.dateOfBirth) {
        birthDay = new Date(Date.UTC(profileData.dateOfBirth));
      } else if (
        profileData.yearOfBirth &&
        profileData.monthOfBirth &&
        profileData.dayOfBirth
      ) {
        birthDay = new Date(
          Date.UTC(
            parseInt(profileData.yearOfBirth),
            parseInt(profileData.monthOfBirth) - 1, // Month is zero-based
            parseInt(profileData.dayOfBirth)
          )
        );
      } else {
        throw new Error("Insufficient data to calculate birth date.");
      }
      let addresshouse = {
        house: profileData.address,
        city: profileData.districtName,
        district: profileData.districtName,
        state: profileData.stateName,
        pincode: profileData.pinCode || profileData.pincode,
        landmarks: profileData.stateName,
      };
      data = {
        firstName:
          `${profileData.firstName} ${profileData.middleName || ""}`.trim() ||
          profileData.name?.split(" ")[0],

        lastName: profileData.lastName || profileData.name?.split(" ")[1],
        abhaAddress:
          address ||
          profileData?.preferredAbhaAddress ||
          profileData?.phrAddress[0],
        gender: profileData.gender?.toUpperCase() === "M" ? "Male" : "Female",

        birthday: birthDay,
        address: addresshouse,
        age: CalculateAge(birthDay),
        profilePic: `data:image/jpeg;base64,${
          profileData?.photo ||
          profileData.profilePic ||
          profileData.profilePhoto
        }`,
        abhaNumber: profileData.ABHANumber,
        mobile: profileData.mobile || patientMobile,
      };
      setBusy(true);
      let result = await PatientProfileWithAbhaDetails(data, update);
      if (result.success) {
        if (result.isExist && !update) {
          setCurrentStep(Steps.PATIENT_EXIST);
          setPatientData(result.data);
        } else {
          OnSelectPatient(result.data);
          resetState();
        }
      }
      setBusy(false);
    } catch (error) {
      setBusy(false);
      handleError(error.message);
    }
  };
  useEffect(() => {
    if (
      addAbhaUsing === "mobile number" &&
      patientMobile &&
      patientMobile.length == 10
    ) {
      setCurrentStep(Steps.FIND_ABHA);
    }
  }, [addAbhaUsing, patientMobile]);

  const handleError = (message) => {
    setError(message);
    setTimeout(() => setError(""), 10000);
  };

  const findAbha = async () => {
    console.log("findAbha", patientMobile);
    try {
      setBusy(true);
      let result = await searchAbha(patientMobile);
      console.log("findAbha", result);
      setBusy(false);
      if (result?.isSuccess) {
        localStorage.setItem("txnId", result.response[0].txnId);
        setAbhaData(result.response);
        setSelectedAbha(result.response[0].ABHA[0]);

        setCurrentStep(Steps.ABHA_PRESENT);
      } else {
        if (
          result?.response?.error &&
          result.response?.error?.code == "ABDM-1114"
        ) {
          setCurrentStep(Steps.ABHA_NOT_PRESENT);
        } else if (result.response.code == 900901) {
          handleError(result.response.message);
          return;
        }
      }
    } catch (error) {
      setBusy(false);
      handleError(error.message || "Unexpected error occurred");
    }
  };

  const handleAbhaIndexLogin = async (isResend = false) => {
    const { canResend, updatedMap, message } = updateResendAttempts(
      patientMobile,
      isResend,
      resendAttemptsMap
    );
    try {
      if (!canResend) {
        handleError(message);
        return;
      }
      setBusy(true);
      let result = await abhaIndexRequestotp(selectedAbha.index);
      console.log("abhaIndexLogin", result);
      setBusy(false);
      if (result.isSuccess) {
        setResendAttemptsMap(updatedMap);
        startTimer(60, setTimer, setIsResendDisabled);
        localStorage.setItem("verifyIndexOtp", result.response.txnId);
        setCurrentStep(Steps.VERIFY_ABHA_OTP);
      } else {
        handleError(result.response.message || "Unexpected error occurred");
      }
    } catch (error) {
      setBusy(false);
      handleError(error.message || "Unexpected error occurred");
    }
  };
  const abhaIndexLogin = () => handleAbhaIndexLogin(false);
  const resendAbhaIndexOtp = () => handleAbhaIndexLogin(true);
  const verifyAbhaOtp = async () => {
    try {
      setBusy(true);
      let result = await verifyIndexAbhaOtp(otp.join(""));
      setBusy(false);
      if (result.isSuccess) {
        if (result.response.authResult === "success") {
          localStorage.setItem("verifyUserToken", result.response.token);
          let profile = await getUserProfile();
          if (profile.isSuccess) {
            setCurrentStep(Steps.CREATE_ABHA);
            setProfileData(profile.response);
          }
        } else {
          setOtp(Array(6).fill(""));
          handleError(result.response.message || "Unexpected error occurred");
        }
      } else {
        setOtp(Array(6).fill(""));
        handleError(result.response.message || "Unexpected error occurred");
      }
    } catch (error) {
      setBusy(false);
      setOtp(Array(6).fill(""));
      handleError(error.message || "Unexpected error occurred");
    }
  };
  const renderStep = () => {
    switch (currentStep) {
      case Steps.FIND_ABHA:
        return (
          <article className=" flex flex-col gap-2">
            <div className=" text-sm font-semibold text-dark">
              Mobile Number*{" "}
            </div>
            <Input.text
              placeholder={"**********"}
              name="patientMobile"
              value={patientMobile}
              readOnly={false}
              maxLength="10"
              onChange={(e) => {
                setpatientMobile(e.target.value);
              }}
            />

            <button
              onClick={findAbha}
              className={`mt-7 mx-auto h-10 w-20 flex items-center justify-center text-white bg-Primary rounded-lg ${
                patientMobile.length !== 10 && "pointer-events-none opacity-50"
              }`}
            >
              {busy ? <Spinner show={true} /> : "Submit"}
            </button>
          </article>
        );
      case Steps.ABHA_PRESENT: {
        return (
          <LinkAbha
            {...props}
            abhaData={abhaData}
            setSelectedAbha={setSelectedAbha}
            selectedAbha={selectedAbha}
            LinkAbha={abhaIndexLogin}
            handleBack={() => {
              setCurrentStep(Steps.FIND_ABHA);
            }}
            onCreate={() => setAddAbhaUsing("abha creation")}
          />
        );
      }
      case Steps.VERIFY_ABHA_OTP: {
        return (
          <VerifyNumber
            {...props}
            verify={verifyAbhaOtp}
            timer={timer}
            handleBack={() => setCurrentStep(Steps.ABHA_PRESENT)}
            isResendDisabled={isResendDisabled}
            handleCancel={() => resetState()}
            onResendOtp={resendAbhaIndexOtp}
            error={error}
            resendAttemptsCount={resendAttemptsMap.get(patientMobile)}
          />
        );
      }
      case Steps.ABHA_NOT_PRESENT:
        return (
          <AbhaVerification
            {...props}
            verify={() => setAddAbhaUsing("abha creation")}
          />
        );
      case Steps.CREATE_ABHA:
        return (
          <CreateAbha
            {...props}
            handleChancel={resetState}
            onComplete={() => handleAddPatientProfile(false)}
            profileData={profileData}
            onViewAbhaCard={AbhaCard}
          />
        );
      case Steps.VIEW_ABHA_CARD:
        return (
          <ABHACard
            {...props}
            abhaCard={abhaCard}
            onCancel={() => setCurrentStep(Steps.CREATE_ABHA)}
          />
        );
      case Steps.PATIENT_EXIST:
        return (
          <PatientUpdateAbort
            handlePatientAutoFill={(value) => {
              OnSelectPatient(value);
              resetState();
            }}
            handleBack={() => setCurrentStep(Steps.CREATE_ABHA)}
            address={address}
            onComplete={() => handleAddPatientProfile(true)}
            patientData={patientData}
            profileData={profileData}
          />
        );
    }
  };

  return (
    <>
      <Modal
        isOpen={addAbhaUsing === "mobile number"}
        setIsOpen={
          currentStep == Steps.VIEW_ABHA_CARD
            ? () => setCurrentStep(Steps.CREATE_ABHA)
            : resetState
        }
        isCard={currentStep == Steps.VIEW_ABHA_CARD}
        classname={`font-primary ${
          currentStep == Steps.VIEW_ABHA_CARD
            ? "w-[400px] "
            : "min-w-[350px]! min-h-96!"
        }`}
      >
        {renderStep()}
      </Modal>
    </>
  );
}
