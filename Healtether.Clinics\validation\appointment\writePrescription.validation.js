import { checkSchema, validationResult } from "express-validator";
import mongoose from "mongoose";

export const validateUpdatePrescription = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    patientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient Id must be a valid ObjectId",
      },
    },
    appointmentId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment Id must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateUpsertMedicalHistory = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    patientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient Id must be a valid ObjectId",
      },
    },
    "medication.*.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
  'medication.*.duration': {
      in: ['body'],
      optional: true,
      custom: {
        options: (value) => {
          if (value && typeof value === 'object') {
            if (!Number.isInteger(value.value)) {
              throw new Error('Duration value must be an integer');
            }
            if (typeof value.unit !== 'string') {
              throw new Error('Duration unit must be a string');
            }
          }
          return true;
        },
      },
    },
    "medication.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },

    "allergies.*.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
   'allergies.*.duration': {
      in: ['body'],
      optional: true,
      custom: {
        options: (value) => {
          if (value && typeof value === 'object') {
            if (!Number.isInteger(value.value)) {
              throw new Error('Duration value must be an integer');
            }
            if (typeof value.unit !== 'string') {
              throw new Error('Duration unit must be a string');
            }
          }
          return true;
        },
      },
    },
    "allergies.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },

    "familyHistory.*.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
    'familyHistory.*.duration': {
      in: ['body'],
      optional: true,
      custom: {
        options: (value) => {
          if (value && typeof value === 'object') {
            if (!Number.isInteger(value.value)) {
              throw new Error('Duration value must be an integer');
            }
            if (typeof value.unit !== 'string') {
              throw new Error('Duration unit must be a string');
            }
          }
          return true;
        },
      },
    },
    "familyHistory.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },

    "pastHistory.*.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
    'pastHistory.*.duration': {
      in: ['body'],
      optional: true,
      custom: {
        options: (value) => {
          if (value && typeof value === 'object') {
            if (!Number.isInteger(value.value)) {
              throw new Error('Duration value must be an integer');
            }
            if (typeof value.unit !== 'string') {
              throw new Error('Duration unit must be a string');
            }
          }
          return true;
        },
      },
    },
    "pastHistory.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },

    "pastProcedureHistory.*.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
   'pastProcedureHistory.*.duration': {
      in: ['body'],
      optional: true,
      custom: {
        options: (value) => {
          if (value && typeof value === 'object') {
            if (!Number.isInteger(value.value)) {
              throw new Error('Duration value must be an integer');
            }
            if (typeof value.unit !== 'string') {
              throw new Error('Duration unit must be a string');
            }
          }
          return true;
        },
      },
    },
    "pastProcedureHistory.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },
  }).run(req);
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateVitals = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    patientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient Id must be a valid ObjectId",
      },
    },
    "vitals.bloodPressure.systolic": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.bloodPressure.diastolic": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.interpretation": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.spo2": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.temperature": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.height": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.weight": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.pulseRate": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.rbs": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.heartRate": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
    "vitals.respiratoryRate": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional: true,
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateLabTests = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    patientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient Id must be a valid ObjectId",
      },
    },
    appointmentId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment Id must be a valid ObjectId",
      },
    },
    labTests: {
      trim:true,
      escape:true,
      in: ["body"],
      isArray: {
        errorMessage: "Lab tests must be an array",
      },
    },
    "labTests.*.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
    "labTests.*.repeat": {
      trim:true,
      escape:true,
      in: ["body"],
      isBoolean: {
        errorMessage: "Repeat must be a boolean value",
      },
    },
    "labTests.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateDrugs = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    patientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient Id must be a valid ObjectId",
      },
    },
    appointmentId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment Id must be a valid ObjectId",
      },
    },

    "drugs.*.drugName": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
    "drugs.*.isBeforeMeal": {
      trim:true,
      escape:true,
      in: ["body"],
      isBoolean: {
        errorMessage: "isBeforeMeal must be a boolean value",
      },
    },
    "drugs.*.dosage": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional:true
    },
    "drugs.*.frequency": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      optional:true
    },
    'drugs.*.duration': {
      trim:true,
      escape:true,
      in: ['body'],
      optional: true,
      custom: {
        options: (value) => {
          if (value && typeof value === 'object') {
            if (!Number.isInteger(value.value)) {
              throw new Error('Duration value must be an integer');
            }
            if (typeof value.unit !== 'string') {
              throw new Error('Duration unit must be a string');
            }
          }
          return true;
        },
      },
    },
    "drugs.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSymptomDiagnosis = async (req, res, next) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    patientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient Id must be a valid ObjectId",
      },
    },
    appointmentId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment Id must be a valid ObjectId",
      },
    },
    symptoms: {
      in: ["body"],
      isArray: {
        errorMessage: "Lab tests must be an array",
      },
    },
    "symptoms.*.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
    'symptoms.*.duration': {
      trim:true,
      escape:true,
      in: ['body'],
      optional: true,
      custom: {
        options: (value) => {
          if (value && typeof value === 'object') {
            if (!Number.isInteger(value.value)) {
              throw new Error('Duration value must be an integer');
            }
            if (typeof value.unit !== 'string') {
              throw new Error('Duration unit must be a string');
            }
          }
          return true;
        },
      },
    },
    "symptoms.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },

    "diagnosis.*.name": {
      trim:true,
      escape:true,
      in: ["body"],
      isString: true,
      notEmpty: {
        errorMessage: "Name is required",
      },
    },
    "diagnosis.*.notes": {
      trim:true,
      escape:true,
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Notes must be a string",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetWholePrescriptionAndVitalsForAppointment = async (
  req,
  res,
  next
) => {
  await checkSchema({
    clientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Clinic ID must be a valid ObjectId",
      },
    },
    appointment: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment Id must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateGetWholeMedicalHistoryForPatient = async (
  req,
  res,
  next
) => {
  await checkSchema({
    patientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "patient Id must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const validateSearchMasterAllergies = async (req, res, next) => {
  await checkSchema({
    name: {
      trim:true,
      escape:true,
      in: ["query"],
      isString: true,
      errorMessage: "Search text is required",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
export const validateSearchMasterMedication = async (req, res, next) => {
  await checkSchema({
    name: {
      trim:true,
      escape:true,
      in: ["query"],
      isString: true,
      errorMessage: "Search text is required",
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};


export const validateGetVitalsPersonalHistory = async (req, res,next) => {
  await checkSchema({
    patientId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Patient Id must be a valid ObjectId",
      },
    },
    appointmentId: {
      in: ["query"],
      custom: {
        options: (value) => mongoose.Types.ObjectId.isValid(value),
        errorMessage: "Appointment Id must be a valid ObjectId",
      },
    },
  }).run(req);

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
}