import crypto from "crypto";
import fs from "fs";
import path from "path";


export function getContentFromWebhookMessage(msg) {
    if (msg != null && msg.entry[0].changes[0].value.messages != undefined && msg.entry[0].changes[0].value.messages.length > 0) {
        let webhookMessage = msg.entry[0].changes[0].value.messages[0];

        let phone_number_id = msg.entry[0].changes[0].value.metadata.phone_number_id;
        let displayName = msg.entry[0].changes[0].value.contacts[0]
            ?.profile
                ?.name;
        let from = msg.entry[0].changes[0].value.messages[0].from; // extract the phone number from the webhook payload
        // extract the message text from the webhook payload

        switch (webhookMessage.type) {
            case "text":
                {
                    let msg_body = webhookMessage.text.body;
                    return {mobile: from, message: msg_body, phoneNumberId: phone_number_id, type: "text", displayName: displayName}
                }
            case "interactive":
                {
                    
                    let interactiveType = webhookMessage.interactive.type;
                    var replyMessageId = null;
                    if (webhookMessage.context != null) {
                        replyMessageId = webhookMessage.context.id;

                        switch(interactiveType)
                        {
                            case "button_reply":{
                                return {
                                    mobile: from,
                                    phoneNumberId: phone_number_id,
                                    type: "reply",
                                    displayName: displayName,
                                    messageId: replyMessageId,
                                    replyText: webhookMessage.interactive.button_reply.id
                                };

                            }break;
                            case "nfm_reply":{

                                var response_json=  webhookMessage.interactive[interactiveType]["response_json"];
                                var replyJson= JSON.parse(response_json);
                             switch(replyJson["flow_token"])
                             {
                                 case process.env.WHATSAPP_SCHEDULE_APPOINTMENT_FLOWTOKEN:
                                     {
                                         var selectedDoctor=null;
                                         for (let index = 0; index < replyJson.data_doctors.length; index++) {
                                             var element = replyJson.data_doctors[index];
                                             if(element.id==replyJson.doctor)
                                             {
                                                 selectedDoctor=element;
                                                 break;
                                             }
                                         }
                                         var selectedPatient=null;
                                         for (let index = 0; index < replyJson.data_patient.length; index++) {
                                             var element = replyJson.data_patient[index];
                                             if(element.id==replyJson.patient)
                                             {
                                                 selectedPatient=element;
                                                 break;
                                             }
                                         }
                                         var scheduleDate=new Date(parseInt(replyJson.scheduleDate));
                                           
                                         return {
                                             mobile: from,
                                             phoneNumberId: phone_number_id,
                                             type: "ScheduleReply",
                                             displayName: displayName,
                                             messageId: replyMessageId,
                                             reply: {
                                                 doctor:selectedDoctor,
                                                 patient:selectedPatient,
                                                 timeslot:replyJson.scheduleTime,
                                                 scheduleDate:scheduleDate,
                                                 isOnline:replyJson.isOnline
                                             }
                                         };
                                     }
                             }

                            }break;
                        }

                     
                      
                    }

                }
                break;
            case "reaction":
                {}
                break;
            case "image":
                {}
                break;
            case "sticker":
                {}
                break;
            case "unknown":
                {}
                break;
            case "button":
                {
                    var replyMessageId = null;
                    if (webhookMessage.context != null) {
                        replyMessageId = webhookMessage.context.id;
                        return {
                            mobile: from,
                            phoneNumberId: phone_number_id,
                            type: "reply",
                            displayName: displayName,
                            messageId: replyMessageId,
                            replyText: webhookMessage.button.payload
                        }
                    }

                }

        }

    }

    return null;
}


export function decryptWhatsappRequest(body){
    const { encrypted_aes_key, encrypted_flow_data, initial_vector } = body;

    // Decrypt the AES key created by the client
    const privateKeyPath = path.join(process.cwd(), 'keys/whatsapp/private.pem');
    const privateKey = crypto.createPrivateKey({
        key: fs.readFileSync(privateKeyPath,'utf8'),
        format: 'pem',  // Ensure the format is correct
        passphrase: 'H#e@ltet#her@'  // Include if the key is encrypted
      });

    const decryptedAesKey = crypto.privateDecrypt(
      {
        key: privateKey,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: "sha256",
      },
      Buffer.from(encrypted_aes_key, "base64"),
    );
   
    // Decrypt the Flow data
    const flowDataBuffer = Buffer.from(encrypted_flow_data, "base64");
    const initialVectorBuffer = Buffer.from(initial_vector, "base64");
  
    const TAG_LENGTH = 16;
    const encrypted_flow_data_body = flowDataBuffer.subarray(0, -TAG_LENGTH);
    const encrypted_flow_data_tag = flowDataBuffer.subarray(-TAG_LENGTH);
  
    const decipher = crypto.createDecipheriv(
      "aes-128-gcm",
      decryptedAesKey,
      initialVectorBuffer,
    );
    decipher.setAuthTag(encrypted_flow_data_tag);
  
    const decryptedJSONString = Buffer.concat([
      decipher.update(encrypted_flow_data_body),
      decipher.final(),
    ]).toString("utf-8");
  
    return {
      decryptedBody: JSON.parse(decryptedJSONString),
      aesKeyBuffer: decryptedAesKey,
      initialVectorBuffer,
    };

}

export function encryptResponse  (
    response,
    aesKeyBuffer,
    initialVectorBuffer
  )
   {
    // Flip the initialization vector
    const flipped_iv = [];
    for (const pair of initialVectorBuffer.entries()) {
      flipped_iv.push(~pair[1]);
    }
    // Encrypt the response data
    const cipher = crypto.createCipheriv(
      "aes-128-gcm",
      aesKeyBuffer,
      Buffer.from(flipped_iv),
    );
    return Buffer.concat([
      cipher.update(JSON.stringify(response), "utf-8"),
      cipher.final(),
      cipher.getAuthTag(),
    ]).toString("base64");
  }

  export function checkItIsPing(msg){

    return msg.action==="ping"

  }