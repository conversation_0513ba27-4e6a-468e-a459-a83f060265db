const documentReferencesValidator = {
  documentReferences: {
    in: ["body"],
    isArray: true,
    errorMessage: "documentReferences must be an array",
  },
  "documentReferences.*.status": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "status is required and must be a non-empty string",
  },
  "documentReferences.*.docStatus": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "docStatus is required and must be a non-empty string",
  },
  "documentReferences.*.type": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "type is required and must be a non-empty string",
  },
  "documentReferences.*.content": {
    in: ["body"],
    isArray: true,
    errorMessage: "content must be an array",
  },
  "documentReferences.*.content.*.attachment": {
    in: ["body"],
    isObject: {
      errorMessage: "attachment must be an object",
    },
  },
  "documentReferences.*.content.*.attachment.contentType": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "contentType is required and must be a non-empty string",
  },
  "documentReferences.*.content.*.attachment.language": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "language is required and must be a non-empty string",
  },
  "documentReferences.*.content.*.attachment.data": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "data is required and must be a base64-encoded string",
  },
  "documentReferences.*.content.*.attachment.title": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "title is required and must be a non-empty string",
  },
  "documentReferences.*.content.*.attachment.creation": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "creation must be a valid ISO 8601 date-time string",
  },
};

export default documentReferencesValidator;
