import {useState} from "react";
import PropTypes from 'prop-types'; 

export default function Action({ value, column, row }) {
    
    return (
        <span className="flex gap-1">
            {column.actionAccessor.map((item) => (
                <button className="btn btn-circle btn-text btn-sm" aria-label="Action button">
                <span key={row.original[column.idAccessor]+"_"+item.name}
                    className={item.iconClass + " "}
                    data-tooltip-id="grid-tooltip"
                    role="button"
                    data-tooltip-content={item.name}
                    data-tooltip-place="top"
                    onClick={(e) => item.callBack(e,row.original)}></span>
                    </button>
            ))}
        </span>
    )
}
// Action.propTypes={
//     actionProp:PropTypes.array,
// }
// Action.defaultProps ={
//     actionProp:[{
//         name: 'Email',
//         iconClass: '',
//         callBack: () => {}
//     }]
// }