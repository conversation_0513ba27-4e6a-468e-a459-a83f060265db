import mongoose from "mongoose";
import { Appointment, Client } from "../../model/clinics.model.js";
import { createInvoiceWithConsultationCharge } from "../payment/payment.helper.js";
import {
  cancelMeeting,
  createGoogleMeetLink,
  scheduleMeeting,
} from "../googleMeet/googleMeet.js";
import { createPatientWithAutoPatientIdInScheduleAppointment } from "../patient/patient.helper.js";
import { sendAppointmentGoogleLinkInWhatsapp } from "../whatsapp/whatsapp.helper.js";
import { generateToken, getAppointmentInUtcDateWithTime } from "../../utils/common.utils.js";

export const overview = async (
  clientId,
  pg,
  size,
  keyword,
  filterDate,
  sortby,
  direction,
  status,
  doctor
) => {
  const regex = new RegExp(keyword, "i"); // i for case insensitive

  const findObj =
    keyword != null
      ? {
          $or: [
            {
              mobile: {
                $regex: regex,
              },
            },
            {
              name: {
                $regex: regex,
              },
            },
          ],
          isDeleted: false,
          clinic: new mongoose.Types.ObjectId(clientId),
        }
      : {
          isDeleted: false,
          clinic: new mongoose.Types.ObjectId(clientId),
        };
  if (filterDate) {
    const date = new Date(filterDate);
    const startOfDay = new Date(date);
    startOfDay.setUTCHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setUTCHours(23, 59, 59, 999);
    findObj["created.on"] = {
      $gte: startOfDay,
      $lte: endOfDay,
    };
  }

  if (doctor) {
    findObj.doctorId = new mongoose.Types.ObjectId(doctor);
  }

  const sortObj =
    sortby != null
      ? {
          sortby: direction === "desc" ? -1 : 1,
        }
      : {
          appointmentDate: 1,
          created: -1,
        };
  switch (status) {
    case "Upcoming":
      {
        findObj["isCanceled"] = false;
        findObj["paymentStatus"] = false;
        findObj["ended"] = { $exists: false };
      }
      break;
    case "Cancelled":
      findObj["isCanceled"] = true;
      break;
    case "Completed":
      // findObj["paymentStatus"] = true;
      findObj["ended"] = { $exists: true };
      break;
    case "FollowUp":
      findObj["isFollowUp"] = true;
      break;
    case "Reschedule":
      findObj["rescheduled"] = { $ne: null };
      break;
    default:
      break;
  }

  const appointmentCollection = await Appointment.find(findObj)
    .sort(sortObj)
    .skip((pg - 1) * size)
    .limit(size)
    .select({
      _id: 1,
      name: 1,
      mobile: 1,
      virtualConsultation: 1,
      appointmentDate: 1,
      timeSlot: 1,
      address: 1,
      pincode: 1,
      doctorName: 1,
      patientId: 1,
      gender: 1,
      age: 1,
      clinicPatientId: 1,
      googleLink: 1,
      paymentStatus: 1,
      abhaNumber: 1,
      abhaAddress: 1,
      prescriptionFinished: 1,
      tokenNumber: 1,
    })
    .exec();
  const appointmentCount = await Appointment.find(findObj).count();
  return { data: appointmentCollection, totalCount: appointmentCount };
};

export const upsertAppointment = async (data, id, user) => {
  let appointment = {};
  let isVirtual = false;
  let oldEventId = null;
  let tokenNumber = data?.tokenNumber;
  if (id != null && id) {
    appointment = await Appointment.findById(id).exec();

    // const oldTimeSlot = appointment.timeSlot;
    const oldVirtualConsultation = appointment.virtualConsultation;
    // const oldAppointmentDate = appointment.appointmentDate;

    appointment.reason = data.reason;
    // appointment.timeSlot = data.timeSlot;
    // appointment.appointmentDate = getAppointmentInUtcDateWithTime(data.appointmentDate, data.timeSlot);
    appointment.virtualConsultation =
      data.virtualConsultation === "true" || data.virtualConsultation === true;

    if (oldVirtualConsultation) {
      isVirtual = true;
      oldEventId = appointment.googleLink.id;
    }
    appointment.doctorId = new mongoose.Types.ObjectId(data.doctorId);
    appointment.doctorName = data.doctorName;
  } else {
    if (!tokenNumber) {
      tokenNumber = await generateToken(data);
    }
    let patientData = null;
    if (data.patientId == null || data.patientId === "") {
      patientData = await createPatientWithAutoPatientIdInScheduleAppointment(
        data.prefix,
        data.name,
        data.age,
        data.mobile,
        data.gender,
        data.birthDate,
        data.clientId,
        user
      );
    }

    appointment = new Appointment({
      name: data.name,
      mobile: data.mobile,
      age: data.age,
      birthDate: data.birthDate,
      gender: data.gender,
      appointmentDate: data.appointmentDate,
      doctorId: new mongoose.Types.ObjectId(data.doctorId),
      doctorName: data.doctorName,
      reason: data.reason,
      timeSlot: data.timeSlot,
      isFollowUp: data.isFollowUp,
      address: data.address,
      pincode: data.pincode,
      abhaNumber: data.abhaNumber,
      abhaAddress: data.abhaAddress,
      tokenNumber: tokenNumber,
      speciality: data.speciality,
      clinic: new mongoose.Types.ObjectId(data.clientId),
      patientId:
        patientData?._id || new mongoose.Types.ObjectId(data.patientId),
      virtualConsultation:
        data.virtualConsultation === "true" ||
        data.virtualConsultation === true,
      clinicPatientId: patientData?.patientId || data.clinicPatientId,
      isDeleted: false,
      created: {
        on: new Date(),
        by: user,
      },
    });
  }

  if (data.virtualConsultation) {
    appointment.googleLink = data.googleLink;
  }

  await appointment.save();
  return [true, appointment];
};

export const getAppointmentCount = async (clientId, filterDate) => {
  const startOfDay = new Date(filterDate);
  const endOfDay = new Date(filterDate);
  endOfDay.setHours(23, 59, 59, 999); // Set the end of the day to 23:59:59
  return await Appointment.find({
    isDeleted: false,
    clinic: new mongoose.Types.ObjectId(clientId),
    "created.on": {
      $gte: startOfDay, // Greater than or equal to the start of the day
      $lte: endOfDay, // Less than or equal to the end of the day
    },
  })
    .select({
      _id: 1,
      name: 1,
      paymentStatus: 1,
      mobile: 1,
      age: 1,
      gender: 1,
      patientId: 1,
    })
    .exec();
};

export const getAppointment = async (id) => {
  return await Appointment.findById(id).exec();
};

export const getAppointmentWithPatient = async (id) => {
  return await Appointment.findById(id)
    .populate({
      path: "patientId",
      perDocumentLimit: 1,
      select: {
        firstName: 1,
        lastName: 1,
        age: 1,
        birthday: 1,
        gender: 1,
        mobile: 1,
        email: 1,
        patientId: 1,
        abhaNumber: 1,
        abhaAddress: 1,
        address: 1,
        height: 1,
        weight: 1,
        documentType: 1,
        documentNumber: 1,
        documents: 1,
        appointments: 1,
      },
    })
    .select({
      mobile: 1,
      name: 1,
      gender: 1,
      age: 1,
      birthDate: 1,
      appointmentDate: 1,
      timeSlot: 1,
      reason: 1,
      virtualConsultation: 1,
      doctorName: 1,
      doctorId: 1,
      patientId: 1,
      abhaAddress: 1,
      abhaNumber: 1,
      address: 1,
      pincode: 1,
      speciality: 1,
      clinic: 1,
      medicalRecords: 1,
      procedureRecords: 1,
      prescriptionRecords: 1,
      started: 1,
      ended: 1,
    })
    .exec();
};

export const getCurrentAppointmentRecord = async (id) => {
  return await Appointment.findById(id)
    .select({ medicalRecords: 1, procedureRecords: 1, prescriptionRecords: 1 })
    .exec();
};

export const setStarted = async (id, user) => {
  const appointment = await Appointment.findById(id).exec();
  if (!appointment) {
    throw new Error("appointment not found");
  }
  appointment.started = {
    on: new Date().toISOString(),
    yes: true,
  };
  appointment.modified = {
    on: new Date().toISOString(),
    by: user,
  };
  await appointment.save();
  return appointment;
};

export const setEnded = async (data, id, user) => {
  const appointment = await Appointment.findById(id).exec();
  if (!appointment) {
    throw new Error("appointment not found");
  }
  appointment.medicalRecords = data.medicalRecords;
  appointment.procedureRecords = data.procedureRecords;
  appointment.prescriptionRecords = data.prescriptionRecords;

  appointment.ended = {
    on: new Date().toISOString(),
    yes: true,
  };
  appointment.modified = {
    on: new Date().toISOString(),
    by: user,
  };
  await appointment.save();

  await createInvoiceWithConsultationCharge(
    user.id,
    user.name,
    id,
    data.clientId
  );
  return appointment;
};

export const setFollowUp = async (data, id, user) => {
  const appointment = await Appointment.findById(id).exec();
  if (!appointment) {
    throw new Error("appointment not found");
  }
  var newAppointment = new Appointment({
    name: appointment.name,
    mobile: appointment.mobile,
    age: appointment.age,
    birthDate: appointment.birthDate,
    gender: appointment.gender,
    appointmentDate: data.appointmentDate,
    doctorId: appointment.doctorId,
    doctorName: appointment.doctorName,
    reason: appointment.reason,
    timeSlot: data.timeSlot,
    clinic: appointment.clinic,
    patientId: appointment.patientId,
    virtualConsultation: appointment.virtualConsultation,
    isDeleted: false,
    isFollowUp: true,
    created: {
      on: new Date().toISOString(),
      by: user,
    },
  });
  newAppointment.save();

  return newAppointment;
};

export const reschedule = async (data, id, user) => {
  const appointment = await Appointment.findById(id).exec();
  if (!appointment) {
    throw new Error("appointment not found");
  }
  if (appointment?.started?.yes) {
    return "Already in Started";
  } else {
    var prevDate = appointment.appointmentDate;
    var prevTimeSlot = appointment.timeSlot;
    var isVirtual = appointment.virtualConsultation;
    var oldEventId = appointment?.googleLink?.id;

    appointment.rescheduled = {
      previousDate: prevDate,
      previousTimeSlot: prevTimeSlot,
    };
    appointment.appointmentDate = getAppointmentInUtcDateWithTime(
      data.appointmentDate,
      data.timeSlot
    );
    appointment.timeSlot = data.timeSlot;
    appointment.modified = {
      on: new Date().toISOString(),
      by: user,
    };

    var resGoogleMeet = await createGoogleMeetLink(appointment, isVirtual);

    if (resGoogleMeet?.isSuccess) appointment.googleLink = resGoogleMeet.data;

    if (oldEventId) await cancelMeeting(oldEventId);

    await appointment.save();

    if (isVirtual && !!appointment?.googleLink?.link) {
      await sendAppointmentGoogleLinkInWhatsapp(appointment);
    }
  }

  return appointment;
};
export const cancelled = async (id, user) => {
  const appointment = await Appointment.findById(id).exec();
  if (!appointment) {
    throw new Error("appointment not found");
  }

  if (appointment?.started?.yes) {
    return "Already in Started";
  } else {
    appointment.isCanceled = true;
    appointment.modified = {
      on: new Date().toISOString(),
      by: user,
    };
    appointment.save();

    if (!!appointment?.googleLink?.id)
      await cancelMeeting(appointment?.googleLink?.id);
  }
  return appointment;
};

export const updateRecords = async (data, id, user) => {
  const appointment = await Appointment.findById(id).exec();
  if (!appointment) {
    throw new Error("appointment not found");
  }
  appointment.medicalRecords = data.medicalRecords;
  appointment.procedureRecords = data.procedureRecords;
  appointment.prescriptionRecords = data.prescriptionRecords;
  appointment.prescriptionReport = data.prescriptionReport;
  appointment.invoiceReport = data.invoiceReport;
  appointment.vaccineCertificate = data.vaccineCertificate;

  appointment.modified = {
    on: new Date().toISOString(),
    by: user,
  };
  const result = await Appointment.findByIdAndUpdate(
    id,
    { $set: appointment },
    { new: true }
  );

  return result;
};

export const updateAdviceNotes = async (data, id, user) => {
  const appointment = await Appointment.findById(id).exec();
  if (!appointment) {
    throw new Error("appointment not found");
  }
  appointment.advice = data.advice;
  appointment.notes = data.notes;
  appointment.modified = {
    on: new Date().toISOString(),
    by: user,
  };
  const result = await Appointment.findByIdAndUpdate(
    id,
    { $set: appointment },
    { new: true }
  );
  return appointment;
};

export const setPaymentStatus = async (appointmentId, status) => {
  const appointment = await Appointment.findById(appointmentId).exec();
  appointment.paymentStatus = status;
  await appointment.save();
};
