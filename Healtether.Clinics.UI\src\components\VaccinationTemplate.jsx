import moment from 'moment';
import { useRef } from 'react';

export const VaccinationCertificateModal = ({ 
  isOpen, 
  onClose, 
  certificateData,
  onPrint, 
  onSave 
}) => {
  if (!isOpen) return null;
  
  const contentToPrint = useRef(null);
  
  if (!certificateData) return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-8 rounded-lg shadow-xl">
        <p className="text-xl font-semibold">Loading certificate data...</p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mt-4"></div>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 overflow-auto">
      <div className="bg-white rounded-lg shadow-xl w-11/12 max-w-6xl max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-bold">Vaccination Certificate Preview</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="p-4">
          <div
            ref={contentToPrint}
            data-certificate-content="true"
            className="bg-white border border-gray-300 rounded"
          >
            {/* Header */}
            <div className="text-black p-4 text-center border-b border-gray-300">
              <h1 className="text-lg font-bold">Certificate for Vaccination</h1>
            </div>
            
            <div className="p-6">
              {/* Beneficiary Details Section */}
              <div className="mb-6">
                <h2 className="text-base font-bold text-black border-b border-gray-400 pb-1 mb-4">
                  Beneficiary Details :
                </h2>
                
                <div className="space-y-3">
                  <div className="flex">
                    <span className="font-medium w-40">Beneficiary Name :</span>
                    <span>{certificateData.patientName}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">ABHA Address :</span>
                    <span>{certificateData.patientAbhaAddress}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">ABHA Number :</span>
                    <span>{certificateData.patientAbhaNumber}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Date of birth :</span>
                    <span>{moment(certificateData.patientBirthDate).format("DD/MM/YYYY")}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Age :</span>
                    <span>{certificateData.patientAge}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Mobile No. :</span>
                    <span>{certificateData.patientMobile}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Gender :</span>
                    <span>{certificateData?.patientGender}</span>
                  </div>
                  {/* <div className="flex">
                    <span className="font-medium w-40">Aadhaar Number :</span>
                    <span>{certificateData.beneficiary?.aadhaarNumber}</span>
                  </div> */}
                </div>
              </div>

              {/* Vaccination Details Section */}
              <div className="mb-6">
                <h2 className="text-base font-bold text-black border-b border-gray-400 pb-1 mb-4">
                  Vaccination Details :
                </h2>
                {
                  certificateData?.immunizations?.map((immunization, index) => (
                        <div className="space-y-3">
                  <div className="flex">
                    <span className="font-medium w-40">Vaccination Name :</span>
                    <span>{immunization?.drugName}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Vaccination Lot Number :</span>
                    <span>{immunization.lotNumber}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Vaccine Given On :</span>
                    <span>{moment(immunization.occurrenceDateTime).format("DD/MM/YYYY hh:mm A")}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Dose Number :</span>
                    <span>{immunization.dosage}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Vaccine Given By :</span>
                    <span>{certificateData.doctorName}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-40">Vaccine Given At :</span>
                    <span>{certificateData?.clinic?.clinicName}</span>
                  </div>
                </div>

                  ))
                }
                
            
              </div>

              {/* Footer with contact details */}
              <div className="mt-8 pt-4 border-t border-gray-300 flex justify-between items-end text-sm">
                <div>
                  <p>Ph: {certificateData?.clinic?.adminUserId?.mobile}</p>
                  <p>email: {certificateData?.clinic?.adminUserId?.email}</p>
                </div>
                <div className="text-right">
                  <p>{certificateData?.clinic?.address}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end p-4 gap-3 border-t">
          <button
            onClick={onClose}
            className="border border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-100"
          >
            Close
          </button>
          <button
            onClick={() => onPrint(contentToPrint.current)}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Print
          </button>
          <button
            onClick={() => onSave(contentToPrint.current)}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            Save PDF
          </button>
        </div>
      </div>
    </div>
  );
};

// Demo component to show the modal in action
export default function VaccinationCertificateDemo() {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  
  const sampleCertificateData = {
    beneficiary: {
      name: 'John Fernandes G',
      age: '24',
      dateOfBirth: '12/03/2000',
      gender: 'Male',
      aadhaarNumber: '1234 4567 2239'
    },
    vaccination: {
      name: 'Hepatitis A',
      type: 'Protects against Havrix',
      givenOn: '11/01/2023',
      doseNumber: '1/2',
      nextDose: '12/02/2023',
      batchNumber: '12344567',
      givenBy: 'Dr. Manisha',
      location: 'Bhalla Clinic, Hyderabad, Telangana'
    },
    certificateId: '905894471',
    email: '<EMAIL>'
  };

  const handlePrint = (content) => {
    console.log('Print function called with content:', content);
    // Add your print logic here
  };

  const handleSave = (content) => {
    console.log('Save PDF function called with content:', content);
    // Add your PDF save logic here
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100 p-4">
      <button
        onClick={() => setIsModalOpen(true)}
        className="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 font-semibold"
      >
        Open Vaccination Certificate
      </button>

      <VaccinationCertificateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        certificateData={sampleCertificateData}
        onPrint={handlePrint}
        onSave={handleSave}
      />
    </div>
  );
}