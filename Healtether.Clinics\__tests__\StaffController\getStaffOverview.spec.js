import { jest } from "@jest/globals";
const { mockStaffHelper } = await import("../mocks/mock.staff.helper");
mockStaffHelper();
const { getStaffOverview } = await import('../../controllers/staffs/staffs.controller.js'); 
const { staffOverview }= await import('../../helpers/staff/staff.helper.js');


describe('getStaffOverview', () => {
    let req, res;

    beforeEach(() => {
        req = {
            query: {}
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    it('should return staff overview data successfully', async () => {
        const mockData = {
            staffList: [{ id: 1, name: '<PERSON>' }],
            total: 1
        };

        staffOverview.mockResolvedValue(mockData);

        req.query = {
            clientId: 'client1',
            page: 1,
            size: 10,
            keyword: '<PERSON>',
            sortby: 'name',
            direction: 'asc',
            status: 'active'
        };

        await getStaffOverview(req, res);

        expect(staffOverview).toHaveBeenCalledWith(
            'client1',
            1,
            10,
            '<PERSON>',
            'name',
            'asc',
            'active'
        );
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith(mockData);
    });

});
