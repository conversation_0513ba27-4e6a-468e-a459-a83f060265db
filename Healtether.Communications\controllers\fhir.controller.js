import { handleApisComingFromClinicForFhir } from "../helper/fhir/main.bundle.fhir.helper.js";

export const fhirRequestController = async (req, res) => {
    try {
        console.log("in");
        const result = await handleApisComingFromClinicForFhir(req);

        if (!result) {
            console.error("Invalid result received:", result);
            return res.status(400).json({
                error: { code: 'ABDM-9999', message: 'Invalid response from handler' }
            });
        }

        return res.status(result.isSuccess ? 200 : 500).json(result);

    } catch (error) {
        console.error("Error in fhirRequestController:", error);

        const status = error.status || 500;
        const data = error.data || { error: { code: 'ABDM-9999', message: 'Internal Server Error' } };

        return res.status(status).json(data);
    }
};

