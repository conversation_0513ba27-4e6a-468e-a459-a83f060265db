import mongoose from "mongoose";
import { CLIENT_COLLECTION, INVOICE_COLLECTION, PATIENT_COLLECTION, PRESCRIPTIONS_COLLECTION, STAFF_COLLECTION, VITALS_COLLECTION } from "../../mongodb.collection.name.js";

const queuedAppointmentSchema = new mongoose.Schema({
    mobile: {
        type: String,
        index: true,
        required: true,
        maxLength: 10
    },
    name: {
        type: String,
        required: true,
        maxLength: 255
    },
    gender: {
        type: String,
        maxLength: 20
    },
    age: {
        type: Number,
        min: 1,
        max: 100
    },
    birthDate: {
        type: Date
    },
    appointmentDate: {
        type: Date,
        index: true
    },
    timeSlot: {
        type: String,
        index: true,
        maxLength: 50
    },
    reason: {
        type: String,
        maxLength: 1000
    },
    virtualConsultation: {
        type: Boolean,
        default: false
    },
    googleLink: {
        link: { type: String },
        id: { type: String }
    },
    paymentStatus: {
        type: Boolean,
        default: false
    },
    created: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            }
        }
    },
    modified: {
        on: {
            type: Date
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            }
        }
    },
    medicalRecords: [
        {
            fileName: {
                type: String,
                maxLength: 255
            },
            blobName: {
                type: String,
                maxLength: 255
            }
        }
    ],
    procedureRecords: [
        {
            fileName: {
                type: String,
                maxLength: 255
            },
            blobName: {
                type: String,
                maxLength: 255
            }
        }
    ],
    prescriptionRecords: [
        {
            fileName: {
                type: String,
                maxLength: 255
            },
            blobName: {
                type: String,
                maxLength: 255
            }
        }
    ],
    prescriptionReport: [
        {
            fileName: {
                type: String,
                maxLength: 255
            },
            blobName: {
                type: String,
                maxLength: 255
            }
        }
    ],

    invoiceReport: [
        {
            fileName: {
                type: String,
                maxLength: 255
            },
            blobName: {
                type: String,
                maxLength: 255
            }
        }
    ],
    vaccineCertificate: [
        {
            fileName: {
                type: String,
                maxLength: 255
            },
            blobName: {
                type: String,
                maxLength: 255
            }
        }
    ],


    isDeleted: {
        type: Boolean,
        default: false
    },
    doctorName: {
        type: String,
        maxLength: 255
    },
    clinicPatientId: {
        type: String,
        maxLength: 255
    },
    doctorId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: STAFF_COLLECTION
    },
    patientId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: PATIENT_COLLECTION
    },
    clinic: {
        type: mongoose.Schema.Types.ObjectId,
        ref: CLIENT_COLLECTION,
        index: true
    },
    started: {
        on: Date,
        yes: Boolean
    },
    ended: {
        on: Date,
        yes: Boolean
    },
    isCanceled: {
        type: Boolean,
        default: false
    },
    rescheduled: {
        previousDate: {
            type: Date,
        },
        previousTimeSlot: {
            type: String,
            maxLength: 25
        }
    },
    isFollowUp: {
        type: Boolean,
        default: false
    },
    abhaNumber: {
        type: String,
        maxLength: 50,
    },
    abhaAddress: {
        type: String,
        maxLength: 50,
    },
    speciality: {
        type: String,
        maxLength: 50,
    },
    address: {
        type: String,
        maxLength: 225
    },
    pincode: {
        type: String,
        maxLength: 10
    },
    district: {
        type: String,
        maxLength: 225
    },
    state: {
        type: String,
        maxLength: 100 
    },
    tokenNumber:{
     type: Number,
    },
    prescriptionFinished: {
        type: Boolean,
        default: false
    },
}, { versionKey: '1.7', toJSON: { virtuals: true }, toObject: { virtuals: true } });

queuedAppointmentSchema.virtual('invoicedetail', {
    ref: INVOICE_COLLECTION,
    localField: '_id',
    foreignField: 'appointmentId'
});
queuedAppointmentSchema.virtual('prescriptions', {
    ref: PRESCRIPTIONS_COLLECTION,
    localField: '_id',
    foreignField: 'appointment'
});
queuedAppointmentSchema.virtual('vitals', {
    ref: VITALS_COLLECTION,
    localField: '_id',
    foreignField: 'appointment'
});
// Create the user model
//const Appointment = new mongoose.model("Appointment", appointmentSchema);
export { queuedAppointmentSchema };