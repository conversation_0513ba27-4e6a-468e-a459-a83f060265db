
import { jest } from "@jest/globals";
import { Staff } from '../../../model/clinics.model.js';
import { staffById } from '../../../helpers/staff/staff.helper.js'; 
import { setup, teardown } from "../../../setup.js"; 
import mongoose from "mongoose";
jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); // Set up MongoDB connection
});

afterAll(async () => {
  await teardown(); // Tear down MongoDB connection
});

describe('staffById function', () => {
  let testStaffId;

  beforeEach(async () => {
    await Staff.deleteMany({}); 
    const staff = new Staff({
      firstName: 'John',
      lastName: 'Doe',
      staffId: "testStaffId1",
      mobile: '1234567890',
      isAdmin: false,
      prefix:"Mr.",
      isDoctor: true,
      email: '<EMAIL>',
      createdOn: new Date(),
      modifiedOn: new Date(),
    });

    const savedStaff = await staff.save();
    testStaffId = savedStaff._id; 
  });

  afterEach(async () => {
    await Staff.deleteMany({}); 
  });

  it('should return staff member when a valid ID is provided', async () => {
    const result = await staffById(testStaffId);
    expect(result).toBeTruthy(); 
    expect(result.firstName).toBe('John'); 
    expect(result.lastName).toBe('Doe');
  });

  it('should return null when an invalid ID is provided', async () => {
    const invalidId =new mongoose.Types.ObjectId(); 
    const result = await staffById(invalidId);
    expect(result).toBeNull(); 
  });

});
