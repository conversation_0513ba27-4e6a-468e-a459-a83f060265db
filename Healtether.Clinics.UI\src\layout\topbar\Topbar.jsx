import React, { useEffect, useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { logout } from "store/slice/AuthSlice";
import { confirm } from "components/dialog/prompt";
import { removeUser } from "store/slice/UserSlice";
import { removeCurrentClinic } from "store/slice/ClinicSlice";
import moment from "moment/moment";

export default function Topbar({ setViewSidebarxs }) {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [currentDate, setCurrentDate] = useState(moment());
  const { user } = useSelector((state) => state.user);
  const { clinic } = useSelector((state) => state.currentClinic);
  let SuperAdmin = user.isSuperAdmin;
  let admin = user.isSuperAdmin;

  for (let index = 0; index < user.linkedClinics.length && !admin; index++) {
    const linkedClinic = user.linkedClinics[index];

    var isCurrent = linkedClinic?.clinic?._id == clinic._id;

    if (isCurrent) {
      admin = linkedClinic.isAdmin;
      break;
    }
  }

  const LogoutUser = async () => {
    confirm({
      show: true,
      title: "Logout",
      proceed: async () => {
        dispatch(removeUser());
        dispatch(removeCurrentClinic());
        return dispatch(logout());
      },
      confirmation: "Are you sure you want to logout?",
    });
  };



  const dropDownMenu = [
    {
      _id: 1,
      icon: "icon-[mdi--settings]",
      Heading: "Settings",
      role: "Admin",
      click: (e) => {
        e.preventDefault();

        navigate("clinic/setting");
      },
    },
    {
      _id: 2,
      icon: "icon-[zondicons--date-add]",
      Heading: "Manage Clinic",
      role: "SuperAdmin",
      click: (e) => {
        e.preventDefault();

        navigate("clinic/manageclinic");
      },
    },
    {
      _id: 3,
      icon: "icon-[mdi--account-switch-outline]",
      Heading: "Switch Clinic",
      role: "All",
      click: (e) => {
        e.preventDefault();

        navigate("select-clinic");
      },
    },
  ];

  const dateInputRef = useRef(null);
  const handleButtonClick = () => {
    dateInputRef.current.showPicker();
    // setActiveTab("custom");
  };
  return (
    <nav className="navbar bg-base-100  gap-4 border  " style={{ height: '50px' }}>
      <div className="navbar-start">
        <div className="dropdown relative inline-flex [--auto-close:inside] [--offset:9]">
          <button id="dropdown-name" type="button" className="btn btn-text btn-circle" aria-haspopup="dialog" aria-expanded="false" aria-controls="healtether-menubar" data-overlay="#healtether-menubar">
            <span className="icon-[tabler--menu-2] size-7"></span>
          </button>

        </div>
      </div>
      <div className="navbar-end flex items-center gap-4">
        <button className="btn btn-sm btn-text btn-circle size-8.5 md:hidden">
          <span className="icon-[tabler--search] size-5.5"></span>
        </button>
        <div className="input max-md:hidden rounded-full max-w-56">
          <span className="icon-[tabler--search] text-base-content/80 my-auto me-3 size-5 shrink-0"></span>
          <label className="sr-only" htmlFor="searchInput">Full Name</label>
          <input type="search" className="grow" placeholder="Search" id="searchInput" />
        </div>

        <button id="dropdown-scrollable" type="button" className="btn btn-text btn-circle  size-10">

          <Link to="notification" className="indicator">
            <span className="indicator-item bg-error size-2 rounded-full"></span>
            <span className="icon-[tabler--bell] text-base-content size-5.5"></span>
          </Link>
        </button>


        <div className="dropdown relative inline-flex [--auto-close:inside] [--offset:8] [--placement:bottom-end]">
          <button id="dropdown-scrollable" type="button" className="dropdown-toggle flex items-center" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
            <div className="avatar">
              <div className="size-9.5 rounded-full">
                <img src="https://mdbootstrap.com//img/Photos/Square/1.jpg" alt="avatar 1" />
              </div>
            </div>
          </button>
          <ul className="dropdown-menu dropdown-open:opacity-100 hidden min-w-60" role="menu" aria-orientation="vertical" aria-labelledby="dropdown-avatar">
            <li className="dropdown-header gap-2">
              <div className="avatar">
                <div className="w-10 rounded-full">
                  <img src="https://mdbootstrap.com//img/Photos/Square/1.jpg" alt="avatar" />
                </div>
              </div>
              <div>
                <h6 className="text-base-content text-base font-semibold truncate"> {user?.firstName + " " + user?.lastName}</h6>
                <small className="text-base-content/50">{SuperAdmin ? "Super Admin" : admin ? "Admin" : ""} </small>
              </div>
            </li>
            {dropDownMenu.map((item) => {
              if (
                (item.role == "Admin" && admin) ||
                item.role == "All" ||
                (item.role == "SuperAdmin" && SuperAdmin)
              )
                return (

                  <li key={item._id} className="cursor-pointer">
                    <div className="dropdown-item hover:border-r-2 hover:border-r-[#110C2C] !rounded-none"
                      onClick={(e) => {
                        item.click(e);
                      }}>
                      <span className={item.icon + " "}></span>
                      {item.Heading}
                    </div>
                  </li>


                );
              else return <></>;
            })}


            <li className="dropdown-footer gap-2">
              <div className="btn btn-error btn-soft btn-block " onClick={(e) => {
                e.preventDefault();
                LogoutUser();
              }}>
                <span className="icon-[tabler--logout]"></span>
                Sign out
              </div>
            </li>
          </ul>
        </div>
      </div>
    </nav>


  );
}
