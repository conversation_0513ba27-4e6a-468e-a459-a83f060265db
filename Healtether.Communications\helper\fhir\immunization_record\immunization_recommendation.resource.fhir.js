import {
  getSnomedCtCode,
  ImmunizationRecommendation,
} from "../../../utils/fhir.constants.js";
import { v4 as uuidv4 } from "uuid";
import { generateSnomedCtCode } from "../common_resources/snomed_ct_code.generator.fhir.js";
export const generateImmunizationRecommendationResource = async (
  type,
  recommendation,
  patientResource,
  organizationResource
) => {
  const id = uuidv4();
  const getSnomedData = await generateSnomedCtCode(type);
  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "ImmunizationRecommendation",
      id,
      patient: { reference: patientResource.fullUrl },
      date: recommendation.date,
      recommendation: recommendation.recommendation.map(
        (recommendationDetail) => ({
          vaccineCode: [
            getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term),
          ],
          doseNumber: recommendationDetail.doseNumber,
          seriesDoses: recommendationDetail.seriesDoses,
          forecastStatus: {
            coding: [
              {
                system:
                  "http://terminology.hl7.org/CodeSystem/immunization-recommendation-status",
                code: "due",
                display: recommendationDetail.forecastStatus,
              },
            ],
          },

          dateCriterion: [
            {
              code: {
                coding: [
                  {
                    system: "http://loinc.org",
                    code: "30980-7",
                    display: "Date vaccine due",
                  },
                ],
              },
              value: recommendationDetail.dateCriterion,
            },
          ],
        })
      ),
      authority: organizationResource
        ? { reference: organizationResource.fullUrl }
        : undefined,
      text: ImmunizationRecommendation(),
    },
  };
};
