import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { upsertDrugs } from "../../../helpers/appointment/write-prescription/prescription.helper.js";
import { Patient } from "../../../model/clinics.model.js";
import { Appointment } from "../../../model/clinics.model.js";
import { Client } from "../../../model/clinics.model.js";
import { setup, teardown } from "../../../setup.js";
import { PatientPrescriptions } from "../../../model/clinics.model.js"; // Add this import for the prescription model

jest.setTimeout(30000);
beforeAll(async () => {
  await setup();
});

afterAll(async () => {
  await teardown();
});

describe("UpsertVitals function", () => {
  let patientId;
  let appointmentId;
  let clinicId;

  afterEach(async () => {
  });
  it("should add new vitals when no existing record is found", async () => {
    const patient = new Patient({
      firstName: "<PERSON>",
      lastName: "Doe",
      mobile: "**********",
      prefix:"Mr.",
      patientId: "PAT001",
      deleted: false,
    });
    patientId = (await patient.save())._id;

    const clinic = await Client.create({
      clinicName: "TEST",
      address: "18 veera alagamman koil street",
      created: { on: new Date("2024-04-27T06:52:20.110Z") },
      isDeleted: false,
      logo: "",
      modified: { on: new Date("2024-09-17T09:15:18.594Z") },
      patientId: { prefix: "SD", suffix: "" },
    });
    clinicId = clinic._id;

    const appointment = new Appointment({
      mobile: "**********",
      name: "First Patient",
      gender: "Male",
      age: 33,
      patientId: new mongoose.Types.ObjectId(patientId),
      clinic: new mongoose.Types.ObjectId(clinicId),
      doctorName: "Dr. Smith",
      appointmentDate: new Date(),
      timeSlot: "10:00 AM",
      reason: "Checkup",
      paymentStatus: true,
      virtualConsultation: false,
      isCanceled: false,
    });
    appointmentId = (await appointment.save())._id;

    let drugs = [
      {
        isBeforeMeal: true,
        drugName: "drugname",
        dosage: "1",
        frequency: "1-1-1",
        duration: {
          value: 2,
          unit: "Days"
        }          
      }
    ]
    
    const user = { id: "user1", name: "User One" }
    const result = await upsertDrugs(
      {}, // data
      drugs, // drugs
      user,
      patientId,
      clinicId,
      appointmentId
    );
    expect(result.drugPrescriptions[0].drugName).toBe("drugname");
  });

  it("should update existing symptoms and diagnosis when a record is found", async () => {
    // Create an initial prescription record
    const existingPrescription = new PatientPrescriptions({
      clinic: clinicId,
      patient: patientId,
      appointment: appointmentId,
      drugPrescriptions: [
        {
          isBeforeMeal: true,
          drugName: "drugname",
          dosage: "1",
          frequency: "1-1-1",
          duration: {
            value: 2,
            unit: "Days"
          }          
        }
      ],
      diagnosis: [{ name: "flu", notes: "Drink plenty of fluids" }]
    });

    await existingPrescription.save();

    let drugs = [
      {
        isBeforeMeal: true,
        drugName: "drugname2",
        dosage: "1",
        frequency: "1-1-1",
        duration: {
          value: 2,
          unit: "Days"
        }          
      }
    ]
    
    const user = { id: "user2", name: "User Two" }
    const result = await upsertDrugs(
      {}, // data
      drugs, // drugs
      user, 
      patientId, 
      clinicId, 
      appointmentId
    );

    expect(result.drugPrescriptions[0].drugName).toBe("drugname2");
  });
});
