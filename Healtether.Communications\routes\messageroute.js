import { Router } from "express";
import { sendOTP, send, } from "../controllers/message.controller.js";
import { validateSend, validateSendAppointmentLink, validateSendAppointmentSummary, validateSendOTP, validateSendPaymentLink } from "../validation/message/message.validation.js";
import { sendAppointmentLink, sendAppointmentSummary, sendPaymentLink } from "../controllers/whatsapp.send.template.controller.js";
import { listen, verifyHook } from "../controllers/whatsapp.hook.js";

const messageRouter = Router();

/**
 * @swagger
 * /api/message/send:
 *   post:
 *     summary: Send a message via WhatsApp
 *     description: Sends a WhatsApp message to a recipient using the Facebook API.
 *     tags:
 *       - Messaging
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   mobile:
 *                     type: string
 *                     description: The recipient's mobile number.
 *                     example: "1234567890"
 *                   message:
 *                     type: string
 *                     description: The message to be sent.
 *                     example: "Hello, this is a test message."
 *                   clientId:
 *                     type: Objectid
 *                     description: The ID of the client.
 *                     example: "662ca0a41a2431e16c41ebaa"
 *                   tableName:
 *                     type: string
 *                     description: The name of the table.
 *                     example: "messages"
 *     responses:
 *       200:
 *         description: Message sent successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Invalid data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid data"
 */
messageRouter.route("/send").post(validateSend, async (req, res, next) => {
    try {
        return await send(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /api/message/sendappointmentsummary:
 *   post:
 *     summary: Sends an appointment summary via WhatsApp
 *     description: This endpoint sends an appointment summary message to a patient using the WhatsApp template.
 *     tags:
 *       - Messaging
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   to:
 *                     type: string
 *                     description: The recipient's phone number.
 *                   clinicName:
 *                     type: string
 *                     description: The name of the clinic.
 *                   patientName:
 *                     type: string
 *                     description: The name of the patient.
 *                   doctorName:
 *                     type: string
 *                     description: The name of the doctor.
 *                   scheduleDate:
 *                     type: string
 *                     description: The date of the scheduled appointment.
 *                   timeSlots:
 *                     type: string
 *                     description: The time slot of the appointment.
 *     responses:
 *       200:
 *         description: Appointment summary sent successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Server error.
 */
messageRouter.route("/sendappointmentsummary").post(validateSendAppointmentSummary, async (req, res, next) => {
    try {
        return await sendAppointmentSummary(req, res);
    }
    catch (e) {
        next(e)
    }
});
/**
 * @swagger
 * /api/message/sendappointmentlink:
 *   post:
 *     summary: Sends an appointment link via WhatsApp
 *     description: This endpoint sends an appointment link message to a patient using the WhatsApp template.
 *     tags:
 *       - Messaging
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   to:
 *                     type: string
 *                     description: The recipient's phone number.
 *                   clinicName:
 *                     type: string
 *                     description: The name of the clinic.
 *                   patientName:
 *                     type: string
 *                     description: The name of the patient.
 *                   doctorName:
 *                     type: string
 *                     description: The name of the doctor.
 *                   scheduleDate:
 *                     type: string
 *                     description: The date of the scheduled appointment.
 *                   timeSlots:
 *                     type: string
 *                     description: The time slot of the appointment.
 *                   googleMeetLink:
 *                     type: string
 *                     description: The Google Meet link for the appointment.
 *     responses:
 *       200:
 *         description: Appointment link sent successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Server error.
 */
messageRouter.route("/sendappointmentlink").post(validateSendAppointmentLink, async (req, res, next) => {
    try {
        return await sendAppointmentLink(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /api/message/sendpaymentlink:
 *   post:
 *     summary: Sends a payment link via WhatsApp
 *     description: This endpoint sends a payment link to the user's mobile number using WhatsApp.
 *     tags:
 *       - Messaging
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   to:
 *                     type: string
 *                     description: The recipient's mobile number.
 *                     example: "**********"
 *                   clinicName:
 *                     type: string
 *                     description: The name of the clinic.
 *                     example: "Health Clinic"
 *                   patientName:
 *                     type: string
 *                     description: The name of the patient.
 *                     example: "John Doe"
 *                   doctorName:
 *                     type: string
 *                     description: The name of the doctor.
 *                     example: "Dr. Smith"
 *                   paymentLink:
 *                     type: string
 *                     description: The payment link to be sent.
 *                     example: "https://paymentlink.com"
 *                   scheduleDate:
 *                     type: string
 *                     description: The date of the scheduled appointment.
 *                     example: "2024-10-22"
 *                   timeSlots:
 *                     type: string
 *                     description: The time slots for the appointment.
 *                     example: "10:00 AM - 11:00 AM"
 *     responses:
 *       200:
 *         description: Payment link sent successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Error sending payment link.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   description: Error message.
 */
messageRouter.route("/sendpaymentlink").post(validateSendPaymentLink, async (req, res, next) => {
    try {
        return await sendPaymentLink(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /api/message/sendotp:
 *   post:
 *     summary: Sends an OTP via WhatsApp
 *     description: This endpoint sends a One-Time Password (OTP) to the user's mobile number using WhatsApp.
 *     tags:
 *       - Messaging
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   mobile:
 *                     type: string
 *                     description: The recipient's mobile number.
 *                     example: "**********"
 *                   email:
 *                     type: string
 *                     description: The recipient's email id.
 *                     example: "<EMAIL>"
 *                   otp:
 *                     type: string
 *                     description: The One-Time Password to be sent.
 *                     example: "123456"
 *     responses:
 *       200:
 *         description: OTP sent successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Error sending OTP.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   description: Error message.
 */
messageRouter.route("/sendotp").post(validateSendOTP, async (req, res, next) => {
    try {
        return await sendOTP(req, res);
    }
    catch (e) {
        next(e)
    }
});


//Dont change
messageRouter.route("/listen").post(async (req, res, next) => {
    try {
        return await listen(req, res);
    }
    catch (e) {
        next(e)
    }
});
messageRouter.route("/listen").get((req, res, next) => {
    try {
        return verifyHook(req, res);
    }
    catch (e) {
        next(e)
    }
});


// messageRouter.route("/sendemail").post((req, res, next) => {
//     console.log("Headers:", req.headers); // Log request headers
//     console.log("Body:", req.body); // Log request body
//     try {
//         return sendOtpOnEmail(req, res);
//     } catch (e) {
//         console.error("Error in /sendemail:", e);
//         next(e);
//     }
// });


export default messageRouter;
