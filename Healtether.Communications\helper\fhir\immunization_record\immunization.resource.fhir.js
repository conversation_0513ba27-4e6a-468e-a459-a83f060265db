import { getSnomedCtCode, immunizationDiv, immunizationMetaData } from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "../common_resources/snomed_ct_code.generator.fhir.js";
import { v4 as uuidv4 } from "uuid";
import portalDb from "../../../config/clinics.collections.config.js";
const Prescription = portalDb.model("Prescription");

export const generateImmunizationResource = async (type,immunization, patientResource, patientId) => {
    const id = uuidv4();
     const getSnomedData = await generateSnomedCtCode(type);
    const prescription =await Prescription.findOne({ patient: patientId })
    .sort({ "created.on": -1 });
    const note=prescription.drugPrescriptions.filter((drug)=>drug.drugName===getSnomedData.term).map((drug)=>drug.notes).join(" ")
    const duration=prescription.drugPrescriptions.filter((drug)=>drug.drugName===getSnomedData.term).map((diag)=>`${diag.duration.value} ${diag.duration.unit}`).join(" ")
    const notes =`(Notes:${note}) (Duration:${duration})`
    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'Immunization',
            id,
            meta: immunizationMetaData(),
            status: immunization.status,
            vaccineCode:getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term,notes),
            patient: { 
                reference: patientResource.fullUrl,
                display : "Patient"
             },
            // encounter: { reference: immunization.encounter ? `urn:uuid:${immunization.encounter}` : undefined },
            occurrenceDateTime: immunization.occurrenceDateTime,
            lotNumber: immunization.lotNumber,
            // text:immunizationDiv(),
        }
    };
};
