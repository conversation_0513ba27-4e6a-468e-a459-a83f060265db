
import {  FrequentSymptoms, FrequentDiagnosis, FrequentLabtest, FrequentDrugs } from '../../model/clinics.model.js';
import mongoose from 'mongoose';


export const upsertSymptoms = async (symptoms, clinicId) => {
  for (let symptomData of symptoms) {
    const { name } = symptomData;
    if (name) {
      let symptom = await FrequentSymptoms.findOne({ name: name?.toLowerCase() });

      if (symptom) {
        symptom.count++;
        await symptom.save();
      } else {
        symptom = new FrequentSymptoms({ name: name?.toLowerCase(), count: 1, clinic: new mongoose.Types.ObjectId(clinicId) });
        await symptom.save();
      }
    }
  }
};

export const upsertDiagnosis = async (diagnosis, clinicId) => {
  console.log("end");
  for (let diagnosisData of diagnosis) {
    const { name } = diagnosisData;
    if (name) {
      let diagnosis = await FrequentDiagnosis.findOne({ name: name?.toLowerCase() });

      if (diagnosis) {
        diagnosis.count++;
        await diagnosis.save();
      } else {
        diagnosis = new FrequentDiagnosis({ name: name?.toLowerCase(), count: 1, clinic: new mongoose.Types.ObjectId(clinicId) });
        await diagnosis.save();
      }
    }

  }
}
export const upsertLabtest = async (labtest, clinicId) => {
  for (let labtestData of labtest) {
    const { name } = labtestData;
    if (name) {
      let labtest = await FrequentLabtest.findOne({ name: name?.toLowerCase() });
      if (labtest) {
        labtest.count++;
        await labtest.save();
      } else {
        labtest = new FrequentLabtest({ name: name?.toLowerCase(), count: 1, clinic: new mongoose.Types.ObjectId(clinicId) });
        await labtest.save();
      }
    }
  }
}
export const upsertDrugs = async (drugs, clinicId) => {
  for (let drugData of drugs) {
    const { drugName } = drugData;
    if (drugName) {
      let drugs = await FrequentDrugs.findOne({ name: drugName?.toLowerCase() });
      if (drugs) {
        drugs.count++;
        await drugs.save();
      } else {
        drugs = new FrequentDrugs({ name: drugName?.toLowerCase(), count: 1, clinic: new mongoose.Types.ObjectId(clinicId) });
        await drugs.save();
      }
    }

  }
}

export const getSymptom = async (clinicId) => {
  const getData = await FrequentSymptoms.find({ clinic:new mongoose.Types.ObjectId(clinicId) }).limit(5).sort({ count: -1 });
  return getData;
};
export const getDiagnosis = async (clinicId) => {
  const getData = await FrequentDiagnosis.find({ clinic:new mongoose.Types.ObjectId(clinicId) }).limit(5).sort({ count: -1 });
  return getData;
};

export const getLabTest = async (clinicId) => {
  const getData = await FrequentLabtest.find({ clinic:new mongoose.Types.ObjectId(clinicId) }).limit(5).sort({ count: -1 });
  return getData;
};


export const getDrugs = async (clinicId) => {
  const getData = await FrequentDrugs.find({clinic:new mongoose.Types.ObjectId(clinicId) }).limit(5).sort({ count: -1 });
  return getData;
};
