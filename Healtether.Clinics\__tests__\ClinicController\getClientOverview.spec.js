import { jest } from "@jest/globals";
const { mockClientHelper } = await import("../mocks/mock.client.helper.js");
mockClientHelper();
const { getClientOverview } = await import("../../controllers/clinic/client.controller.js");
const { overview } = await import("../../helpers/clinic/client.helper.js");

const mockClientData = [
  {
    _id: '62a000000000000000000001',
    clinicName: 'Test Clinic 1',
    adminUserId: {
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      mobile: '1234567890',
    },
  },
  {
    _id: '62a000000000000000000002',
    clinicName: 'Test Clinic 2',
    adminUserId: {
      firstName: 'Jane',
      lastName: 'Doe',
      email: '<EMAIL>',
      mobile: '0987654321',
    },
  },
];

// Update mockQuery to match what the controller expects
const mockQuery = {
  query: {  // Add this nested query object
    page: 1,
    size: 10,
    keyword: ""
  }
};

const res = {
  json: jest.fn().mockReturnThis(),
  status: jest.fn().mockReturnThis(),
};

describe('getClientOverview', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return overview data successfully', async () => {
    overview.mockResolvedValue({
      data: mockClientData,
      totalCount: 2,
    });

    await getClientOverview(mockQuery, res);

    expect(overview).toHaveBeenCalledWith(
      mockQuery.query.page,
      mockQuery.query.size,
      mockQuery.query.keyword
    );
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      data: mockClientData,
      totalCount: 2,
    });
  });

});