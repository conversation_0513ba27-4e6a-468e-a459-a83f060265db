import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { searchStaff } from '../../../helpers/staff/staff.helper.js'; 
import { Staff } from '../../../model/clinics.model.js'; 
import { setup, teardown } from "../../../setup.js"; 

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown(); 
});

describe('searchStaff function', () => {
  it('should return staff matching the given name', async () => {
    let clientId=new mongoose.Types.ObjectId();
    // Insert test staff data
    const staff1 = new Staff({
      firstName: 'John',
      lastName: 'Doe',
      staffId: "testStaffId1",
      mobile: '1234567890',
      isAdmin: false,
      prefix:"Mr.",
      isDoctor: true,
      email: '<EMAIL>',
      clinic:clientId

    });

    const staff2 = new Staff({
      firstName: 'Jane',
      lastName: 'Doe',
      staffId: "testStaffId2",
      mobile: '0987654321',
      isAdmin: true,
      isDoctor: false,
      prefix:"Mr.",
      email: '<EMAIL>',
      clinic:clientId
    });

    await staff1.save();
    await staff2.save();

    const result = await searchStaff(clientId,'Doe',5); 

    expect(result.staffCollection.length).toBe(2); 
    // expect(result.staffCollection[0].firstName).toBe('John');
    // expect(result.staffCollection[1].firstName).toBe('Jane');
  });

  it('should limit the number of staff returned based on the size parameter', async () => {
    let clientId=new mongoose.Types.ObjectId();
    const staff1 = new Staff({
      firstName: 'John',
      lastName: 'Doe',
      staffId: "testStaffId1",
      mobile: '1234567890',
      isAdmin: false,
      prefix:"Mr.",
      isDoctor: true,
      email: '<EMAIL>',
      clinic:clientId
    });

    const staff2 = new Staff({
      firstName: 'Jane',
      lastName: 'Doe',
      staffId: "testStaffId2",
      mobile: '0987654321',
      isAdmin: true,
      prefix:"Mr.",
      isDoctor: false,
      email: '<EMAIL>',
      clinic:clientId
    });

    const staff3 = new Staff({
      firstName: 'Jim',
      lastName: 'Smith',
      staffId: "testStaffId3",
      mobile: '1122334455',
      isAdmin: false,
      prefix:"Mr.",
      isDoctor: true,
      email: '<EMAIL>',
      clinic:clientId
    });

    await staff1.save();
    await staff2.save();
    await staff3.save();

    const result = await searchStaff(clientId,'Doe',5); 
    console.log(result); // Debug log

    expect(result.staffCollection).toHaveLength(2); 
    // expect(result.staffCollection[0].firstName).toBe('John');
  });

  // it('should return an empty array if no staff match the search term', async () => {
  //   const result = await searchStaff('Nonexistent');
  //   expect(result.staffCollection.length).toBe(0); 
  // });

});
