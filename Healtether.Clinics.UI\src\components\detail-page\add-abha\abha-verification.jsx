import { Input } from "../input";
import { Buttons } from "../appointment/button";

export function AbhaVerification({ verify, handleCancel,patientMobile }) {
  return (
    <article className=" h-80 flex flex-col gap-4 font-primary">
      <div className="">
        <div className="mb-2 text-sm font-semibold text-dark">
          Mobile number *
        </div>
        <Input.number
          handleChange={(e) => console.log(e)}
          placeholder="**********"
          value={patientMobile}
        />
      </div>
      {/* {mobileNumber && ( */}
        <p className="text-sm text-color_muted-foreground">
          The mobile number you have entered does not match with any of the records. Please enter a different number
        </p>
      {/* )} */}
      <footer className=" flex justify-end gap-2 mt-auto">
        <Buttons.light
          onClick={handleCancel}
          title="Cancel"
          classname="w-fit font-semibold"
        />
        <Buttons.primary
          onClick={verify}
          title="Create ABHA"
          classname="w-fit font-semibold"
        />
      </footer>
    </article>
  );
}
