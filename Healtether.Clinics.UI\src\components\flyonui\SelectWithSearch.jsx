export default function SelectWithSearch({ name,placeholder, options,selectedValue }) {

  const detailsSelect = {
    placeholder: placeholder,
    toggleTag: `<button type="button" class="hover:outline border-none ring-0 text-secondary" aria-expanded="false"></button>`,
    toggleClasses: "advance-select-toggle select-disabled:pointer-events-none select-disabled:opacity-40",
    hasSearch: true,
    searchClasses:"rounded-full w-full border-accent ring-0",
    dropdownClasses: "advance-select-menu max-h-52 pt-0 overflow-y-auto",
    optionClasses: "advance-select-option selected:select-active",
    optionTemplate: `<div class="flex justify-between items-center w-full">
      <span data-title></span><span class="icon-[tabler--check] shrink-0 size-4 text-primary hidden selected:block">
      </span>
    </div>`,
    extraMarkup: `<span class="icon-[tabler--caret-down] shrink-0 size-4 text-base-content absolute top-1/2 end-3 -translate-y-1/2"></span>`
  } 

  return (
    <select
      data-select={JSON.stringify(detailsSelect)}
      name={name}
      id={name}
      defaultValue={selectedValue}
      className="hidden"
    >
      <option value="">Choose</option>
      {options}
    </select>
  )
}