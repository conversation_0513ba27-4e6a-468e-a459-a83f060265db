import { UTCDate } from "@date-fns/utc";
import axios from "axios";
import { addHours, addMinutes, subHours, subMinutes } from "date-fns";
import moment from "moment";
import { tokenModel } from "../schema/token.schema.js";
import mongoose from "mongoose";
import { BlobServiceClient } from "@azure/storage-blob";

export function getDecimal(value) {
    if (typeof value !== 'undefined') {
        return parseFloat(value.toString());
    }
    return value;
}

export function getFileTimeNow() {
    return new Date().getTime() * 1e4 + 116444736e9;
}

export function calculateDiscount(amount, disc) {
    return Math.round((parseFloat(disc) / 100) * parseFloat(amount));
}

export function calculateAmountAfterDisc(quantity, amount, discRate) {
    var qty = quantity != null && quantity !== ""
        ? parseFloat(quantity)
        : 0;
    var amt = amount != null && amount !== ""
        ? parseFloat(amount)
        : 0;
    var disc = discRate != null && discRate !== ""
        ? parseFloat(discRate)
        : 0;

    var amtWithQuantity = Math.round(qty * amt);
    var discount = calculateDiscount(amtWithQuantity, disc);
    return Math.round(parseFloat(amtWithQuantity) - parseFloat(discount));
}

export function getExpiryByMinutes(mins) {
    let today = new Date();
    return today.setMinutes(today.getMinutes() + parseInt(mins));
}

export function getProperMobile(mobile) {

    if (mobile != null && mobile.toString().length > 10) {
        return mobile;
    } else if (mobile != null && mobile.toString().length === 10) {
        return ("91" + mobile);
    }

    return null;
}

export function resultObject(code, description, isSucess, data) {
    return {code: code, description: description, isSuccess: isSucess, data: data};
}

export function getTodayDate() {
    var today = new Date()
        .toISOString()
        .split("T")[0];
    return new Date(today + "T00:00");
}

export function getCurrentTime() {
    const currentDate = new Date();
    const options = {
        hour: '2-digit',
        minute: '2-digit'
    };
    return currentDate.toLocaleString('en-US', options);
}

export function buildNotificationText(pre, middle, post, user) {
    var txt = (pre != null
        ? pre
        : "");

    txt += (middle != null
        ? (" " + middle)
        : "");
    txt += (post != null
        ? (" " + post)
        : "");
    txt += (user?.name != null ? (" by " + user.name) : "");

    return txt;
}

export function formatTodayDate(today) {
    const yyyy = today.getFullYear();
    let mm = today.getMonth() + 1; // Months start at 0!
    let dd = today.getDate();

    if (dd < 10) dd = '0' + dd;
    if (mm < 10) mm = '0' + mm;

    return dd + '/' + mm + '/' + yyyy;
}

export function checkStringEmptyAndReturnNull(data) {
    if (data != null && data?.trim() !== "") {
        return data;
    } else {
        return undefined;
    }
}

export function lastPathInUrl(url){
    if(!!url)
    return url.substring(url.lastIndexOf('/') + 1);
}

export function getAppointmentInUtcDateWithTime(appointmentDate,timeSlot)
{
    const timeParts = timeSlot.match(/(\d{1,2}):(\d{2})\s*(am|pm)?/i);
    if (!timeParts) {
        throw new Error('Invalid time slot format');
    }

    const hours = parseInt(timeParts[1], 10);
    const minutes = timeParts[2];
    const ampm = timeParts[3]?.toLowerCase();
    let hour24;

    if (ampm === 'pm' && hours !== 12) {
        hour24 = hours + 12;
    } else if (ampm === 'am' && hours === 12) {
        hour24 = 0;
    } else {
        hour24 = hours;
    }
    const utcDate = new UTCDate(appointmentDate.getFullYear(),appointmentDate.getMonth(),appointmentDate.getDate());
    var isoDate = addMinutes(addHours(utcDate, hour24),minutes);
    var isoString=subMinutes(subHours(isoDate,5),30).toISOString();
    return isoString;
}



export function toTitleCase(str) {
    return str.replace(/\w\S*/g, (txt) => {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
}


export async function fetchImageAsBase64(blobName, clinicId) {
  try {
    const connStr = process.env.AZURE_CONNECTIONSTRING;
    console.log("Fetching image from Azure Blob Storage...",blobName);
  const blobServiceClient = BlobServiceClient.fromConnectionString(connStr);

    // Construct container name based on clinic ID
    const containerName = `${process.env.CLINICBLOB_CONTAINER_PREFIX}${clinicId}`;
    
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(`patient/${blobName}`);
    
    // Download the blob content
    const downloadBlockBlobResponse = await blobClient.download();
    
    // Convert the stream to buffer
    const buffer = await streamToBuffer(downloadBlockBlobResponse.readableStreamBody);
    
    // Convert to base64
    const base64 = buffer.toString('base64');
    return base64;
  } catch (error) {
    console.error("Error fetching image from Azure Blob:", error);
    throw error;
  }
}

// Helper function to convert stream to buffer
async function streamToBuffer(readableStream) {
  return new Promise((resolve, reject) => {
    const chunks = [];
    readableStream.on("data", (data) => {
      chunks.push(typeof data === "string" ? Buffer.from(data) : data);
    });
    readableStream.on("end", () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on("error", reject);
  });
}

// export async function fetchImageAsBase64(url) {
//     try {
//       const response = await axios.get(url, { responseType: "arraybuffer" });
//       const base64 = Buffer.from(response.data, "binary").toString("base64");
//       return base64;
//     } catch (error) {
//     //   console.error("Error fetching image:",url, error);
//     }
//   }
  export function CalculateAge(dob) {
    var today = new Date();
    var birthDate = new Date(dob);
    var age_now = today.getFullYear() - birthDate.getFullYear();
    var m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age_now--;
    }
    return age_now;
}


 export const resetDailyCounter= async(clinicId)=> {
    const today = moment().startOf('day').toDate();
    await tokenModel.findOneAndUpdate(
      {clinic: clinicId },
      { value: 0, date: today },
      { upsert: true }
    );
    console.log(`Token counter reset for clinic ${clinicId} on ${today}`);
  }
  
  
  export const generateToken = async (data) => {
    let tokenNumber;
  
    const appointmentDate = data.appointmentDate;
  
      const currentTokenNumber = await getCurrentTokenNumber(
        data.clientId,
        appointmentDate
      );
        tokenNumber = await getNextTokenNumber(data.clientId, appointmentDate);
  
      await tokenModel.findOneAndUpdate(
        {
          clinic: data.clientId,
          date: moment(appointmentDate).startOf("day").toDate(),
        },
        { value: Math.max(tokenNumber, currentTokenNumber) },
        { upsert: true }
      );
    
  
    return tokenNumber;
  };
  
  export const getNextTokenNumber = async (clinicId, appointmentDate) => {
    let newmongooseclinicId = clinicId && new mongoose.Types.ObjectId(clinicId);
    const dateOnly = moment(appointmentDate).startOf("day").toDate();
  
    const counter = await tokenModel.findOneAndUpdate(
      {
        clinic: new mongoose.Types.ObjectId(newmongooseclinicId),
        date: dateOnly,
      },
      { $inc: { value: 1 } },
      { new: true, upsert: true }
    );
  
    return counter.value;
  };
  
  export const getCurrentTokenNumber = async (clinicId, appointmentDate) => {
    const dateOnly = moment(appointmentDate).startOf("day").toDate();
    const counter = await tokenModel.findOne({
      clinic: clinicId,
      date: dateOnly,
    });
    return counter ? counter.value : 0;
  };