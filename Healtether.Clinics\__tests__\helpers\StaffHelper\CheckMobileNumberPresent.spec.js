import mongoose from "mongoose";
import { jest } from "@jest/globals";
import { checkMobileNumberPresent } from '../../../helpers/staff/staff.helper.js'; 
import { Staff } from '../../../model/clinics.model.js'; 
import { ClientUser } from '../../../model/clinics.model.js';
import {setup,teardown}  from "../../../setup.js"

jest.setTimeout(30000);

beforeAll(async () => {
  await setup(); 
});

afterAll(async () => {
  await teardown();
});

describe('checkMobileNumberPresent function', () => {
  let testMobile, doctorId, linkedClinicId;

  beforeEach(async () => {
    await Staff.deleteMany({});
    await ClientUser.deleteMany({});

    const doctor = new Staff({
      firstName: 'Test',
      lastName: 'Doctor',
      prefix:"Mr.",
      staffId:"teststaffid",
      mobile: '1234567890',
      specialization: 'General',
      userId: new mongoose.Types.ObjectId(), 
      isDoctor: true,
      deleted: false,
    });
    doctorId = doctor._id;
    testMobile = doctor.mobile;
    await doctor.save();

    const linkedClinic = new ClientUser({
      userId: doctor.userId, // Linking doctor to the user
      clinic: new mongoose.Types.ObjectId(), 
      isAdmin: false,
      isdeleted: false,
    });
    linkedClinicId = linkedClinic.clinic;
    await linkedClinic.save();
  });

  afterEach(async () => {
    await Staff.deleteMany({});
    await ClientUser.deleteMany({});
  });

  it('should return true if mobile number is already present and id is different', async () => {
    const newId = new mongoose.Types.ObjectId(); 

    const result = await checkMobileNumberPresent(testMobile, newId);

    expect(result).toBe(true); 
  });

  it('should return false if mobile number is not present', async () => {
    const newMobile = '0987654321'; 
    const result = await checkMobileNumberPresent(newMobile, null);

    expect(result).toBe(false); 
  });

//   it('should return false if mobile number matches but id is the same', async () => {
//     const result = await checkMobileNumberPresent(testMobile, doctorId);

//     expect(result).toBe(false); 
//   });

  it('should return false if mobile number is present but no id is provided', async () => {
    const result = await checkMobileNumberPresent(testMobile, null);

    expect(result).toBe(true); 
  });


});
