import { format } from "date-fns";
import useFormatPhoneNumber from "../../../utils/hooks/useFormatPhoneNumber";
export const appointment_details = (appoitmentDetails) => [
  { label: "Patient ID :", value: appoitmentDetails?.patientId },
  { label: "Patient Name :", value: appoitmentDetails?.name },
  { label: "Age : ", value: appoitmentDetails?.age },
  { label: "Gender : ", value: appoitmentDetails?.gender },
  { label: "ABHA Address :", value: "PatientA123@abdm" },
  { label: "Time :", value: appoitmentDetails?.timeSlot },
  { label: "Attending Doctor :", value: appoitmentDetails?.doctorName },
  { label: "Appointment Brief :", value: appoitmentDetails?.reason },
];

export const patient_details = (data) => {
  const { formatNumber } = useFormatPhoneNumber();
  return [
    {
      title: "Personal Details",
      content: [
        {
          label: "Birthday",
          value: data?.birthday
          ? format(new Date(data?.birthday), "yyyy/MM/dd")
          : "N/A", 
        },
        { label: "Age", value: `${data?.age}yrs` },
        { label: "Gender", value: data?.gender },
      ],
    },
    {
      title: "contact details",
      content: [
        {
          label: "Mobile",
          value: formatNumber(data?.mobile),
        },
        {
          label: "whatsapp",
          value: formatNumber(data?.mobile),
        },
        { label: "Email", value: data?.email },
        {
          label: "Address ",
          value: `${data?.address?.house} ${data?.address?.street} ${data?.address?.landmarks} ${data?.address?.city} ${data?.address?.pincode}`,
        },
      ],
    },
    {
      title: "Documents",
      content: [
        { label: "ID type", value: data?.documentType },
        { label: "ID no.", value: data?.documentNumber },
      ],
    },
  ];
};


export const months = [
  { name: "Jan" },
  { name: "Feb" },
  { name: "Mar" },
  { name: "April" },
  { name: "May" },
  { name: "June" },
  { name: "July" },
  { name: "Aug" },
  { name: "Sept" },
  { name: "Oct" },
  { name: "Nov" },
  { name: "Dec" },
];
