{"name": "healtether-portal-ui", "private": true, "version": "0.0.0", "type": "module", "homepage": ".", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@iconify-json/iconamoon": "^1.1.1", "@iconify/json": "^2.2.119", "@reduxjs/toolkit": "^1.9.5", "apexcharts": "^4.7.0", "axios": "^1.6.8", "babel-plugin-macros": "^3.1.0", "date-fns": "^2.30.0", "dayjs": "^1.11.12", "dom-to-image-more": "^3.5.0", "firebase": "^10.10.0", "flyonui": "^2.1.0", "framer-motion": "^10.12.22", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "match-sorter": "^6.3.1", "moment": "^2.30.1", "nanoid": "^5.0.2", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-autosuggest": "^10.1.0", "react-chartjs-2": "^5.2.0", "react-confirm": "^0.3.0-7", "react-data-table-component": "^7.5.3", "react-dom": "^18.2.0", "react-icons": "^4.10.1", "react-infinite-scroll-component": "^6.1.0", "react-inputs-validation": "^4.9.10", "react-live-clock": "^6.1.18", "react-loader-spinner": "^5.3.4", "react-query": "^3.39.3", "react-redux": "^8.1.3", "react-router-dom": "^6.26.2", "react-table": "^7.8.0", "react-to-print": "^2.15.1", "react-toastify": "^10.0.5", "react-tooltip": "^5.21.5", "redux": "^4.2.1", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.5", "superstruct": "^1.0.3", "yup": "^1.2.0"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.23.4", "@iconify/tailwind": "^0.1.3", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/postcss": "^4.1.3", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@vitejs/plugin-react": "^4.0.1", "eslint": "^8.44.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "serve": "^14.2.4", "tailwindcss": "^4.1.3", "vite": "^4.4.0"}}